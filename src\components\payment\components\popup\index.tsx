import { createRoot } from 'react-dom/client'
import ReactDOM from 'react-dom'
import React from 'react'
import Align from './align'
import type { AlignProps } from './align'

export type ModalProps = {
  options: {
    timeout?: number
    wrapperStyle?: React.CSSProperties
    position: AlignProps['position']
    mask?: boolean
    maskClosable?: boolean
    onClose?: () => void
  }
  children: React.ReactNode
  destory: () => void
}

class Modal extends React.Component<ModalProps> {
  componentDidMount() {
    const { options } = this.props
    if (typeof options?.timeout === 'number') {
      window.setTimeout(() => {
        this.close()
      }, options.timeout)
    }
  }

  close() {
    const { options, destory } = this.props
    if (typeof options?.onClose === 'function') {
      options.onClose()
    }
    destory()
  }

  render() {
    const { options, children } = this.props

    const _options = { ...options }
    let style: React.CSSProperties = {}

    if (_options.mask) {
      style.backgroundColor = 'rgba(0,0,0,.7)'
    }

    if (_options.wrapperStyle) {
      style = { ...style, ..._options.wrapperStyle }
    }

    const content = React.isValidElement(children)
      ? React.cloneElement(children as React.ReactElement<any>, {
          _close: () => {
            this.close()
          },
        })
      : children

    return (
      <>
        {ReactDOM.createPortal(
          <Align
            style={style}
            position={_options.position}
            onClick={options?.maskClosable ? () => this.close() : undefined}
          >
            {content}
          </Align>,
          document.body,
        )}
      </>
    )
  }
}

const destory_funcs_store: Function[] = []

export function popup(content: React.ReactNode, options: ModalProps['options']) {
  const div = document.createElement('div')
  document.body.appendChild(div)
  const root = createRoot(div)

  root.render(
    <Modal destory={destory} options={{ ...options }}>
      {content}
    </Modal>,
  )

  function destory() {
    root.unmount()
    if (div.parentNode) {
      div.parentNode.removeChild(div)
    }
    const index = destory_funcs_store.indexOf(destory)
    if (index !== -1) {
      destory_funcs_store.splice(index, 1)
    }
  }
  
  destory_funcs_store.push(destory)

  return destory
}

export function destory_all_popup() {
  const functionsToDestroy = destory_funcs_store.slice()
  destory_funcs_store.length = 0
  functionsToDestroy.forEach((fn) => fn())
  functionsToDestroy.length = 0
}
