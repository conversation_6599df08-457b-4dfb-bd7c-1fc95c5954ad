import React from 'react'
import { popup } from '../popup'
import type { ModalProps } from '../popup'
import styles from './index.module.scss'

const defaultOptions: ModalProps['options'] = {
  mask: false,
  timeout: 3e3,
  position: 'center-center',
  wrapperStyle: { zIndex: 100 },
}

type ToastProps = Partial<{
  title: string
}>

const Toast: React.FC<ToastProps> = ({ title }) => {
  return <div className={styles.toast}>{title}</div>
}

function showToast(props?: ToastProps, options?: ModalProps['options']) {
  return popup(<Toast {...props} />, { ...defaultOptions, ...options })
}

export default showToast
