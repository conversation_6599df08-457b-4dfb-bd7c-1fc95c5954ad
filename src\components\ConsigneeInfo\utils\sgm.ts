type SgmCustomReportParam = { type: number; code: string; msg: string } | object
import colorApi from './colorApi'
/**
 * sgm自定义上报
 * https://sgm-web-help.jd.com/development/h5.html
 * type: 自定义类型（1-error,2-warn,3-info, 4-log, 5-debug, 6-biz, 7-element)
 * type: 自定义类型失败用的info，如果用info要提前和wanglili76问一下，业务尽量用biz
 * @param data { type: number, code: string, message: string, cost1, cost2, cost3 }
 */
const customReport = (data: SgmCustomReportParam) => {
  // if (process.env.TARO_ENV === "h5") {
  //     try {
  //         window['__sgm__']?.custom(data, "9HwAEg@K/AYduPHT039F5B/")
  //     } catch (e) {
  //         console.log('sgm上报异常', e)
  //     }
  // }
  colorApi.postDraData(716, 'success不为真', '地址组件area', '', data, '')
}
const sgm = {
  customReport,
}
export default sgm
