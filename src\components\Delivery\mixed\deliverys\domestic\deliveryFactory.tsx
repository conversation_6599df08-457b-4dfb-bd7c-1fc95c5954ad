/**
 * @file: deliveryFactory.tsx
 * @description: 国内配送工厂，用于根据不同类型组件返回不同组件
 */
import { PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp'
import { DeliveryDataIniter } from '../../initDeliveryData'
import Selector2 from '../../../ui/TimeSelector/selector2'
import TimePicker from '../../../ui/TimeSelector/timePicker'
import Smzt from './smzt'
import Mdzt from './mdzt'
import { SaveType } from '../../saveDelivery'
import { useEffect, useMemo, useCallback, useRef } from 'react'
import Normal from './normal'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import { Tips2 } from '../../../ui/Tips/tips'
import { useDelivery } from '../../../context'
import { DeliveryDataProvider } from '../shared/DeliveryDataProvider'
import { useLatest } from '../../hooks'

// 文案定义
const TIPS = {
  DELIVERY: ['我们会努力按照您指定的时间配送，但因天气、交通等各类因素影响，您的订单有可能会有延误现象！'],
  TIME_PICKER: [
    '1、您选择的时间可能会因库存不足等因素导致订单延迟，请您谅解！',
    '2、我们会在您选定提货日期的前一天处理您的订单，在此之前您的订单处于暂停状态。',
  ],
}

const STYLES = {
  SPACER: { paddingTop: '20px' },
  INSTALL_LINK: { lineHeight: '20px' },
  INSTALL_TEXT: { color: '#888B94', fontSize: '12px', paddingRight: '10px' },
  INSTALL_LINK_STYLE: { color: '#FF0F23', fontSize: '12px' },
}

export type Props = {
  promise: PromiseListItem
  deliveryDataIniter: DeliveryDataIniter
  tipComponent?: React.ReactNode
  onSelectValue: (t: SaveType | null, v: any) => void
}

export default function DeliveryFactory(props: Props) {
  const { promise, deliveryDataIniter, onSelectValue, tipComponent } = props
  const { deliveryLogger } = useDelivery()

  // 统一数据获取
  const dataProvider = useMemo(() => new DeliveryDataProvider(deliveryDataIniter), [deliveryDataIniter])
  const { type, data } = useMemo(() => dataProvider.getComponentTypeAndData(promise), [dataProvider, promise])

  // 日志回调
  const logShipmentClick = useCallback(
    (s: string) => {
      deliveryLogger.shipmentlayer('3', '', s)
      deliveryLogger.shipmentPOPClick({ date: s })
    },
    [deliveryLogger],
  )
  const logShipmentLayer = useCallback(
    (s: string) => {
      deliveryLogger.shipmentlayer('3', '', s)
    },
    [deliveryLogger],
  )

  const onSelectValueRef = useLatest(onSelectValue)

  const lastPromiseRef = useRef<PromiseListItem | null>(null)

  useEffect(() => {
    if (lastPromiseRef.current !== promise) {
      lastPromiseRef.current = promise
      onSelectValueRef.current(null, null)
    }
  }, [promise, onSelectValueRef])

  const thisSelectValue = useCallback(
    (t: SaveType | null, v: any) => {
      lastPromiseRef.current = promise
      onSelectValueRef.current(t, v)
    },
    [promise, onSelectValueRef],
  )

  // 2. type/data 变化时，赋新值
  useEffect(() => {
    if (type === 'simple' && data) {
      thisSelectValue('default', (data as { displayedString: string }).displayedString)
    }
  }, [type, data, thisSelectValue])

  // 错误监控
  useEffect(() => {
    if (!type) {
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.Delivery,
        msg: {
          promise,
          wrapData: deliveryDataIniter.initState,
          info: 'deliveryTypeInfo 为空',
          type: '配送模块',
        },
      })
    }
  }, [type, promise, deliveryDataIniter.initState])

  if (!type) return tipComponent || null

  const reducer = () => {
    // 渲染分发
    switch (type) {
      case 'timeList':
        return (
          <>
            <Normal tipComponent={tipComponent} promise={promise} onSelectValue={thisSelectValue} />
            <div style={STYLES.SPACER} />
            <Tips2 title="温馨提示：" contexts={TIPS.DELIVERY} />
          </>
        )
      case 'simple': {
        // 断言 data 类型
        const simpleData = data as { displayedString: string } | null
        if (!simpleData) return null
        return (
          <>
            <Selector2
              onClick={(s) => {
                logShipmentClick(s)
              }}
              showStr={simpleData.displayedString}
            />
            <div style={STYLES.SPACER} />
            <Tips2 title="温馨提示：" contexts={TIPS.DELIVERY} />
          </>
        )
      }
      case 'timePicker': {
        const timePickerData = data as any[] | null
        if (!timePickerData) return null
        return (
          <>
            <TimePicker
              onClick={(s) => logShipmentLayer(s)}
              calendarData={timePickerData}
              onChange={(date) => thisSelectValue('timePicker', date)}
            />
            <div style={STYLES.SPACER} />
            <Tips2 title="温馨提示：" contexts={TIPS.TIME_PICKER} />
          </>
        )
      }
      case 'loading':
        return <div>加载中...</div>
      case 'smzt':
        return <Smzt promise={promise} onChange={(e) => thisSelectValue('smzt', e)} />
      case 'mdzt':
        return (
          <Mdzt
            storeInfo={promise.venderSelfDeliveryStoreInfo}
            venderId={deliveryDataIniter.initState.venderId}
            onChange={(e) => thisSelectValue('mdzt', e)}
          />
        )
      case 'install': {
        const installData = data as any[] | null
        if (!installData) return null
        return (
          <>
            <TimePicker calendarData={installData} onChange={(date) => thisSelectValue('install', date)} />
            <div style={STYLES.INSTALL_LINK}>
              <span style={STYLES.INSTALL_TEXT}>大家电如何预约安装</span>
              <a target="_blank" href="https://help.jd.com/user/issue/942-67.html" style={STYLES.INSTALL_LINK_STYLE}>
                查看详情
              </a>
            </div>
          </>
        )
      }
      default:
        return null
    }
  }
  return (
    <>
      {tipComponent && type !== 'timeList' && <>{tipComponent}</>}
      {reducer()}
    </>
  )
}
