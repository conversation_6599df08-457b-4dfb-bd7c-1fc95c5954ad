/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-02 18:05:53
 * @LastEditors: ext.wangchao120
 * @Description:
 * @FilePath: /pc_settlement/src/components/VirtualAsset/components/Tabs/index.tsx
 */
import React, { useState, useEffect, ReactNode } from 'react'
import Button from '../Button'
import styles from './index.module.scss'
import classNames from 'classnames'
interface PanelProps {
  /**
   * 面板的唯一标识
   */
  tabKey: string

  /**
   * 面板的title
   */
  tab: ReactNode

  /**
   * 是否禁用该面板
   * 如果为true，则该面板不可点击
   * @default false
   */
  disabled?: boolean

  /**
   * 面板内容
   */
  children?: ReactNode

  /**
   * 是否隐藏该面板
   * @default false
   */
  hiden?: boolean

  /**
   * 是否显示小红点
   * @default false
   */
  dot?: boolean

  /**
   * 面板样式
   */
  panelClass?: string
}

interface TabsProps {
  /**
   * 当前激活的面板的key
   */
  activeKey?: string

  /**
   * 默认激活的面板的key
   * 如果activeKey未定义，则使用defaultActiveKey
   */
  defaultActiveKey?: string

  /**
   * 面板切换时的回调函数
   * @param key 当前激活的面板的key
   */
  onChange?: (key: string) => void

  /**
   * 子面板
   */
  children: React.ReactElement<PanelProps> | React.ReactElement<PanelProps>[]
  /**
   * tabs的类型
   * 可选值：'button' | 'common'
   * @default 'common'
   */
  type?: 'button' | 'common'

  /**
   * 最大高度
   * 当设置时，内容区域超出会有滚动条
   */
  maxHeight?: string

  /**
   * 右上角提示
   */
  rightTip?: ReactNode

  /**
   * 自定义样式
   */
  tabClass?: string

  /**
   * 是否保存tabs的名称
   */
  isTabsName?: boolean
}

const Panel: React.FC<PanelProps> = ({ children }) => <>{children}</>

const Tabs: React.FC<TabsProps> & { Panel: React.FC<PanelProps> } = ({
  activeKey: propsActiveKey,
  defaultActiveKey,
  onChange,
  children,
  type = 'common',
  maxHeight,
  rightTip,
  tabClass,
}) => {
  // 优先取父组件的activeKey，其次父组件传的默认值，最后读取第一个节点的key
  const [internalActiveKey, setInternalActiveKey] = useState<string>(
    propsActiveKey || defaultActiveKey || (React.Children.toArray(children)[0] as React.ReactElement)?.props.tabKey || '',
  )
  useEffect(() => {
    // 父组件控制子组件
    if (propsActiveKey !== undefined) {
      setInternalActiveKey(propsActiveKey)
    }
  }, [propsActiveKey])

  // tab 切换
  const handleTabChange = (key: string) => {
    // propsActiveKey为undefined时，内部控制
    if (propsActiveKey === undefined) {
      setInternalActiveKey(key)
    }
    // 父组件的onChange回调
    onChange?.(key)
  }

  const tabs =
    React.Children.map(children, (child) => {
      if (!child || child.type !== Panel) return null
      return {
        key: child.props.tabKey,
        tab: child.props.tab,
        content: child.props.children,
        disabled: child.props.disabled,
        hiden: child.props.hiden,
        dot: child.props.dot,
        panelClass: child.props.panelClass,
      }
    })?.filter((item) => !item?.hiden) || []

  return (
    <div className={`${styles.tabs} ${styles['tabs-' + type]}`}>
      <div className={styles.rightTip}>{rightTip}</div>
      <div className={styles.tabsTitle}>
        {/* 通用tab选项 */}
        {type == 'common'
          ? tabs.map(
              ({ key, tab, disabled, hiden, dot }) =>
                !hiden && (
                  <div
                    key={key}
                    className={classNames(
                      styles.tabTitleItem,
                      key === internalActiveKey && styles.active,
                      disabled && styles.disabled,
                      dot && styles.dot,
                    )}
                    onClick={() => !disabled && handleTabChange(key)}
                  >
                    {tab}
                  </div>
                ),
            )
          : // Button tab选项
            tabs.map(
              ({ key, tab, disabled, hiden }) =>
                !hiden && (
                  <Button
                    key={key}
                    type={key === internalActiveKey ? 'primary' : 'default'}
                    onClick={() => !disabled && handleTabChange(key)}
                    className={styles.button}
                  >
                    {tab}
                  </Button>
                ),
            )}
      </div>
      {/* tab内容 */}
      <div className={classNames(styles.tabsContent, tabClass && tabClass)}>
        {tabs.map(
          ({ key, content, hiden, panelClass }) =>
            !hiden && (
              <div
                key={key}
                style={maxHeight ? { maxHeight: `${maxHeight}px` } : {}}
                className={classNames(
                  styles.tabPane,
                  key === internalActiveKey && styles.active,
                  maxHeight && styles.scroll,
                  panelClass && panelClass,
                )}
              >
                {content}
              </div>
            ),
        )}
      </div>
    </div>
  )
}

Tabs.Panel = Panel

export default Tabs
