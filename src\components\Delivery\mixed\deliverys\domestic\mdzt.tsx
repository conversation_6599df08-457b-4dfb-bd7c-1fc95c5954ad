/**
 * @file: mdzt.tsx
 * @description: 门店自提组件，用于展示门店自提等逻辑
 */
import { useMasterData } from '@app/context/masterContext'
import { api_balance_getDeliveryStoreList_pc } from '@app/services/api'
import { useEffect, useState } from 'react'
import AddrSelector from '../../../ui/TimeSelector/addrSelector'
import { UiAddrSelector } from '../../../ui/TimeSelector/types'
import { DeliveryStoreItem } from '@app/typings/delivery_api'
import { StoreInfoType } from '@app/typings/api_getBundleShipmentList_resp'

export default function Mdzt({
  venderId,
  onChange,
  storeInfo,
}: {
  venderId: number
  storeInfo?: StoreInfoType
  onChange: (v: { addr: any; time: any }) => void
}) {
  const { balanceAddress } = useMasterData()?.body || {}

  let defaultStoreId = 0
  let initAreaValue = undefined
  if (storeInfo && storeInfo.storeId) {
    defaultStoreId = storeInfo.storeId
    if (storeInfo.idProvince && storeInfo.idCity && storeInfo.idArea) {
      initAreaValue = {
        provinceId: storeInfo.idProvince.toString(),
        cityId: storeInfo.idCity.toString(),
        districtId: '',
      }
    }
  }

  const [shopList, setShopList] = useState<UiAddrSelector.InitState<any, any>['addrList']>([])
  const [areaValue, setAreaValue] = useState<UiAddrSelector.InitState<any, any>['areaValue']>(
    initAreaValue
      ? initAreaValue
      : {
          provinceId: balanceAddress?.provinceId?.toString() || '',
          cityId: balanceAddress?.cityId?.toString() || '',
          districtId: '',
        },
  )
  const [initState, setInitState] = useState<UiAddrSelector.InitState<any, any>>({
    addrList: shopList,
    // pickTimeList: [],
    areaValue: areaValue,
    hasAddrInput: true,
    inLoading: true,
    noDataStr: '抱歉，该区域暂无门店或门店缺货，您可以选择邮寄上门～',
  })

  useEffect(() => {
    setInitState((s) => {
      return {
        ...s,
        addrList: shopList,
        areaValue: areaValue,
      }
    })
  }, [shopList, areaValue])

  const setCurrentSelectValue = (value: { addr: any; time: any }) => {
    onChange(value)
  }

  // console.log(shopList, areaValue,"areaValue");

  useEffect(() => {
    // console.log(venderId, data, balanceAddress, "mdzt");

    if (!venderId || !areaValue) return

    setInitState((s) => {
      return {
        ...s,
        inLoading: true,
      }
    })
    api_balance_getDeliveryStoreList_pc({
      venderId: venderId,
      provinceId: areaValue?.provinceId || '',
      cityId: areaValue?.cityId || '',
      areaId: '',
      orderLat: '',
      orderLng: '',
      // addrId: balanceAddress?.id?.toString() || '',
      addrId: '',
    })
      .then((res) => {
        const list: UiAddrSelector.addInfo<any>[] = res?.body
          .map((item: DeliveryStoreItem) => {
            return {
              pickAddress: item.storeAddress,
              pickName: item.name,
              businessHours: item.businessHours,
              targetData: item,
              distance: item.distance,
              isSelected: defaultStoreId !== 0 && item.storeId === defaultStoreId,
              isDisabled: !item.stockStatus,
            }
          })
          .sort((a, b) => {
            if (a.isSelected === b.isSelected) {
              return 0
            }
            return a.isSelected ? -1 : 1
          })
        setShopList(list || [])
        if (list.length > 0) {
          // setAreaValue({provinceId: balanceAddress?.provinceId?.toString(), cityId: balanceAddress.cityId?.toString(), districtId: balanceAddress.countyId?.toString()})
        }
      })
      .finally(() => {
        setInitState((s) => {
          return {
            ...s,
            inLoading: false,
          }
        })
      })
  }, [areaValue, balanceAddress?.id, defaultStoreId, venderId])

  return (
    <>
      <div style={{ paddingBottom: '20px' }}></div>
      {
        <AddrSelector<string, string>
          onChange={(e) => setCurrentSelectValue(e)}
          initState={initState}
          onAreaChange={(v) => {
            console.log(v, areaValue, 'onAreaChange')
            if (
              !areaValue ||
              v.cityId.toString() !== areaValue.cityId ||
              v.provinceId.toString() !== areaValue.provinceId ||
              v.districtId.toString() !== areaValue.districtId
            ) {
              setAreaValue({
                provinceId: v.provinceId.toString(),
                cityId: v.cityId.toString(),
                districtId: v.districtId.toString(),
              })
            }
          }}
        />
      }
    </>
  )
}
