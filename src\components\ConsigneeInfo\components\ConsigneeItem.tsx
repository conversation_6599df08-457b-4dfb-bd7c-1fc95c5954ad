/*
 * @Author: ext.xuchao26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息地址卡片
 */
import { forwardRef, memo, useRef, useImperativeHandle, useEffect } from 'react'
import { EditBtns, ConsigneeItemDetail, GBAreaNestTip } from '../components'
import classNames from 'classnames'
import styles from '../index.module.scss'
import { reportExpose } from '@app/utils/event_tracking'
import useIntersectionObserverOnce from '@app/hooks/useIntersectionObserverOnce'
import { useMasterData } from '@app/context/masterContext'

const ConsigneeItem = memo(
  forwardRef((props: any, ref: any) => {
    const innerRef = useRef<HTMLDivElement>(null)
    const tipRef = useRef<HTMLDivElement>(null)
    const { addressInfoList, onShow, onSelectAddress, isAddressChunked, isShow } = props
    const masterData = useMasterData()

    useImperativeHandle(ref, () => {
      if (innerRef.current) {
        return { innerRef: innerRef.current, tipRef }
      }
    })

    const observerRef = useIntersectionObserverOnce(
      async (data) => {
        const { provinceId, cityId, countyId, townId, addressId } = data || ({} as any)
        const addr = [provinceId, cityId, countyId, townId + '.' + addressId].join('-')
        await new Promise<void>((resolve) => {
          setTimeout(() => resolve(), 500)
        })
        await Promise.resolve().then(() => reportExpose('writeAddressEXPO', { addrid: `${addressId}`, addr }))
      },
      { root: innerRef.current, threshold: 0.5 },
    )

    const content = (addr: any) => {
      return (
        <div className="is-flex items-center">
          <i className={`h-16 w-16 is-inline-block mr-8 ${styles['tip-area-icon']}`} />
          <div>
            当前详细地址不在省市对应范围内
            <span
              className={`${styles['tip-area-btn']} ftx12 font-11 bg-white is-inline-block cursor-pointer w-49 h-24 leading-24 a-center round-4 mx-8`}
              onClick={() => {
                onShow({ ...addr, type: 'edit', idx: 0 })
                tipRef.current?.onClose()
              }}
            >
              去修改
            </span>
          </div>
        </div>
      )
    }

    useEffect(() => {
      !isShow && tipRef.current?.onShow()
    }, [isShow, masterData?.body])

    return (
      <div ref={innerRef} className={`is-flex ${styles['consignee-items']}`}>
        {addressInfoList.map((addr: any, index: number) => (
          <div
            key={addr.addressId}
            ref={observerRef(addr)}
            className={styles['consignee-item-wrap']}
            onClick={() => onSelectAddress(addr, index)}
          >
            {addr.addressUpdateType === '3' && addr.addressErrorMessage && (
              <div className={classNames('font-12', 'leading-12', styles['consignee-item-warning'])}>{addr.addressErrorMessage}</div>
            )}
            <div className={`${styles['edit-btns-wrap']}`}></div>
            {index === 0 && isAddressChunked && <GBAreaNestTip ref={tipRef} content={content(addr)} width={339} />}
            <div className={classNames(`${styles['consignee-item']} is-flex`, { [styles['active']]: !!addr.selected })}>
              <i className={`${styles['consignee-item-icon']} w-20 h-20`} />
              <ConsigneeItemDetail addr={addr} />
              <EditBtns
                onSelectAddress={onSelectAddress}
                onShow={(v) => {
                  onShow(v)
                  tipRef.current?.onClose()
                }}
                addressInfoList={addressInfoList}
                addr={addr}
                idx={index}
              />
            </div>
          </div>
        ))}
      </div>
    )
  }),
)

export default ConsigneeItem
