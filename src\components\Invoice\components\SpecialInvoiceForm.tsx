import { FC, useState, useRef } from 'react'
import { Area } from '@app/components/ConsigneeInfo/components'
import { Select, Input, Form, Toast2 } from '@app/common/legao'
import apis from '@app/components/ConsigneeInfo/api'
import { VatEditVO } from '@app/typings/invoice.d'
import InvoiceContentSelector from './InvoiceContentSelector'
import InvoicePutTypeSelector from './InvoicePutTypeSelector'
import { handleInvoicePutTypeChangeAtom, updateFieldAtom } from '@app/components/Invoice/atom/invoiceAction'
import { useSetAtom } from 'jotai'
import CompanySelect from './CompanySelect'
import { vatInvoiceFields } from '@app/components/Invoice/form/formFieldsConfig'
import { isOverSea } from '@app/components/ConsigneeInfo/utils'
import { invoiceValidationService } from '@app/components/Invoice/services/invoice.validation.service'

/**
 * 专用发票表单组件
 */
interface SpecialInvoiceFormProps {
  specialInvoiceStep: number
  // 表单字段
  vat: Partial<VatEditVO>
  // 表单事件处理
  onVatRegAddrChange: (val: string) => void
  onVatRegPhoneChange: (val: string) => void
  onVatRegBankChange: (val: string) => void
  onVatRegBankAccountChange: (val: string) => void
  onVatEmailChange: (val: string) => void
  onConsigneeNameChange: (val: string) => void
  onConsigneePhoneChange: (val: string) => void
  onConsigneeAddressChange: (val: string) => void
  onVatCompanyNameChange: (val: string) => void
  onVatCodeChange: (val: string) => void
}

const SpecialInvoiceForm: FC<SpecialInvoiceFormProps> = ({
  specialInvoiceStep,
  vat,
  onVatRegAddrChange,
  onVatRegPhoneChange,
  onVatRegBankChange,
  onVatRegBankAccountChange,
  onVatEmailChange,
  onConsigneeNameChange,
  onConsigneePhoneChange,
  onConsigneeAddressChange,
  onVatCompanyNameChange,
  onVatCodeChange,
}) => {
  const { companyName, regAddr, regPhone, regBank, regBankAccount, email, invoiceConsigneeEditVO } = vat

  // 地址相关状态
  const [form, setForm] = useState({
    provinceId: invoiceConsigneeEditVO?.provinceId || 0,
    cityId: invoiceConsigneeEditVO?.cityId || 0,
    countyId: invoiceConsigneeEditVO?.countyId || 0,
    townId: invoiceConsigneeEditVO?.townId || 0,
    provinceName: invoiceConsigneeEditVO?.provinceName || '',
    cityName: invoiceConsigneeEditVO?.cityName || '',
    countyName: invoiceConsigneeEditVO?.countyName || '',
    townName: invoiceConsigneeEditVO?.townName || '',
    addressDetail: invoiceConsigneeEditVO?.address || '',
    shortAddress: '',
  })

  // 判断是否为海外地址
  const isOverSeaArea = isOverSea(form.provinceId)

  // 详细地址搜索相关状态
  const [suggestAddr, setSuggestAddr] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const handleInvoicePutTypeChange = useSetAtom(handleInvoicePutTypeChangeAtom)
  const setUpdateField = useSetAtom(updateFieldAtom)
  // 创建引用
  const areaRef = useRef<any>(null)
  const addressDetailRef = useRef<any>(null)
  const initArea = [form.provinceId, form.cityId, form.countyId, form.townId].join('-')

  /**
   * 生成动态地址验证规则
   */
  const generateAddressValidationRules = () => {
    const label = '收票人地址'
    const required = true

    return [
      {
        required: required,
        message: `请输入${label}`,
        trigger: ['blur', 'change'],
      },
      {
        validator: (_rule: any, value: string, callback: (error?: Error) => void) => {
          if (!value && !required) {
            callback()
            return
          }
          if (!value && required) {
            callback(new Error(`${label}不能为空`))
            return
          }

          // 根据是否为海外地址选择不同的验证方法
          const result = isOverSeaArea
            ? invoiceValidationService.validateOverseaAddress(value, required, label)
            : invoiceValidationService.validateDomesticAddress(value, required, label)

          if (!result.isValid) {
            callback(new Error(result.message))
          } else {
            callback()
          }
        },
        trigger: ['blur', 'change'],
      },
    ]
  }

  /**
   * 处理区域变更
   */
  const handleAreaChange = (area: any, local: any) => {
    console.log(area, 'area')
    console.log(local, 'local')
    if (!local) return

    const { provinceId = 0, cityId = 0, countyId = 0, townId = 0, provinceName = '', cityName = '', countyName = '', townName = '' } = local

    // 检查是否切换到海外地址，如果是则清空详细地址
    const isNewOverSeaArea = isOverSea(provinceId)
    const shouldClearAddress = isNewOverSeaArea !== isOverSeaArea

    // 更新表单状态
    setForm((prev) => ({
      ...prev,
      provinceId,
      cityId,
      countyId,
      townId,
      provinceName,
      cityName,
      countyName,
      townName,
      shortAddress: '',
      // 如果切换海外/国内状态，清空详细地址
      addressDetail: shouldClearAddress ? '' : prev.addressDetail,
    }))

    // 构建地址文本并更新父组件
    const addressParts = [provinceName, cityName, countyName, townName].filter(Boolean)
    setUpdateField({ field: 'vat.invoiceConsigneeEditVO.provinceId', value: String(provinceId) })
    setUpdateField({ field: 'vat.invoiceConsigneeEditVO.cityId', value: String(cityId) })
    setUpdateField({ field: 'vat.invoiceConsigneeEditVO.countyId', value: String(countyId) })
    setUpdateField({ field: 'vat.invoiceConsigneeEditVO.townId', value: String(townId) })
    setUpdateField({ field: 'vat.invoiceConsigneeEditVO.provinceName', value: String(provinceName) })
    setUpdateField({ field: 'vat.invoiceConsigneeEditVO.cityName', value: String(cityName) })
    setUpdateField({ field: 'vat.invoiceConsigneeEditVO.countyName', value: String(countyName) })
    setUpdateField({ field: 'vat.invoiceConsigneeEditVO.townName', value: String(townName) })

    const fullAddress = [...addressParts, shouldClearAddress ? '' : form.addressDetail].filter(Boolean).join(' ')
    onConsigneeAddressChange(fullAddress)

    // 清空地址搜索建议
    setSuggestAddr([])
  }

  /**
   * 搜索地址
   */
  const onSearch = async (keyword: string) => {
    // 海外地址不进行搜索
    if (isOverSeaArea) {
      setSuggestAddr([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const { provinceId, cityId, countyId, provinceName, cityName, countyName } = form

      const region = [provinceName, cityName, countyName].filter(Boolean).join('|')

      const result = await apis.searchAddress({
        region,
        keyword,
        provinceId,
        cityId,
        countyId,
        latitudeString: '',
        longitudeString: '',
        supportNewParamEncode: true,
        layerFlag: true,
      })

      const suggestAddrList = result.addressSuggestionVOList || []
      setSuggestAddr(suggestAddrList)
    } catch (error) {
      console.error('搜索地址失败:', error)
      Toast2.create({
        type: 'fail',
        text: '地址搜索失败，请稍后重试',
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理详细地址变更
   */
  const handleAddressDetailChange = (addressDetail: any) => {
    if (!addressDetail) {
      // 清空详细地址表单，并通知父组件
      setForm((prev) => ({
        ...prev,
        addressDetail: '',
        latitudeString: '',
        longitudeString: '',
      }))
      onConsigneeAddressChange('')
      return
    }

    // 海外地址直接更新，不进行匹配
    if (isOverSeaArea) {
      console.log(addressDetail)
      const addrText = typeof addressDetail === 'string' ? addressDetail : addressDetail.title || ''
      setForm((prev) => ({
        ...prev,
        addressDetail: addrText,
        latitudeString: '',
        longitudeString: '',
      }))
      onConsigneeAddressChange(addrText)
      return
    }

    // 国内地址：从suggestAddr中查找匹配项
    const matchedAddr = suggestAddr?.find(
      (m) =>
        (typeof addressDetail === 'string' && (addressDetail?.includes(m.title) || m.title?.includes(addressDetail))) ||
        addressDetail.id === m.id,
    )

    if (!matchedAddr) {
      // 没有匹配项，直接更新地址并清空经纬度
      const addrText = typeof addressDetail === 'string' ? addressDetail : addressDetail.title || ''
      setForm((prev) => ({
        ...prev,
        addressDetail: addrText,
        latitudeString: '',
        longitudeString: '',
      }))
      onConsigneeAddressChange(addrText)
    } else {
      // 有匹配项，更新地址同时设置对应的区县ID和经纬度
      const title = matchedAddr.title || ''
      setForm((prev) => ({
        ...prev,
        addressDetail: title,
        countyId: matchedAddr.countyId || prev.countyId,
        latitudeString: matchedAddr.latitude || '',
        longitudeString: matchedAddr.longitude || '',
      }))

      // 更新相关字段
      setUpdateField({ field: 'vat.invoiceConsigneeEditVO.countyId', value: String(matchedAddr.countyId || form.countyId) })

      // 通知父组件
      onConsigneeAddressChange(title)
    }
  }
  return (
    <>
      <InvoicePutTypeSelector onChange={(invoicePutType) => handleInvoicePutTypeChange(invoicePutType)} />

      {specialInvoiceStep === 1 && <InvoiceContentSelector contentType="normal" />}

      <div className="step-indicator">
        <div className={`step ${specialInvoiceStep === 1 ? 'active' : ''} ${specialInvoiceStep === 2 ? 'completed' : ''}`}>
          <span className="step-number">{specialInvoiceStep === 2 ? '' : '1'}</span>
          <span className="step-text">填写或核对公司信息</span>
        </div>
        <div className={`step-line ${specialInvoiceStep === 2 ? 'active' : ''}`}></div>
        <div className={`step ${specialInvoiceStep === 2 ? 'active' : ''}`}>
          <span className="step-number">2</span>
          <span className="step-text">填写收票人信息</span>
        </div>
      </div>

      {specialInvoiceStep === 1 ? (
        // 步骤1：公司信息
        <>
          <Form.Item {...vatInvoiceFields['vat.companyName']}>
            <CompanySelect
              value={companyName}
              onChange={(company) => {
                onVatCompanyNameChange(company.name)
                onVatCodeChange(company.creditCode)
              }}
              disabled
              placeholder="请填写单位"
            />
          </Form.Item>

          <Form.Item {...vatInvoiceFields['vat.regAddr']}>
            <Input type="text" placeholder="请填写单位注册地址" value={regAddr} disabled onChange={onVatRegAddrChange} />
          </Form.Item>

          <Form.Item {...vatInvoiceFields['vat.regPhone']}>
            <Input type="text" placeholder="请填写注册电话" value={regPhone} disabled onChange={onVatRegPhoneChange} />
          </Form.Item>

          <Form.Item {...vatInvoiceFields['vat.regBank']}>
            <Input type="text" placeholder="请填写开户银行名称" value={regBank} disabled onChange={onVatRegBankChange} />
          </Form.Item>

          <Form.Item {...vatInvoiceFields['vat.regBankAccount']}>
            <Input type="text" placeholder="请填写银行账户号码" value={regBankAccount} disabled onChange={onVatRegBankAccountChange} />
          </Form.Item>

          <Form.Item {...vatInvoiceFields['vat.email']}>
            <Input type="text" placeholder="选填，用来接收数电票和电子发票邮件" value={email} onChange={onVatEmailChange} />
          </Form.Item>
        </>
      ) : (
        // 步骤2：收票人信息
        <>
          <Form.Item {...vatInvoiceFields['vat.invoiceConsigneeEditVO.consigneeName']}>
            <Input
              type="text"
              placeholder="请填写收票人姓名"
              value={invoiceConsigneeEditVO?.consigneeName}
              onChange={onConsigneeNameChange}
            />
          </Form.Item>

          <Form.Item {...vatInvoiceFields['vat.invoiceConsigneeEditVO.phone']}>
            <Input
              type="text"
              placeholder="请填写11位手机号"
              value={invoiceConsigneeEditVO?.phone}
              onChange={(val: string) => {
                // 过滤非数字字符
                const numericValue = val.replace(/\D/g, '')
                onConsigneePhoneChange(numericValue)
              }}
              maxLength={11}
            />
          </Form.Item>

          <Form.Item
            label="收票人地区"
            prop="provinceId"
            rules={[
              {
                required: true,
                message: '请选择收票人地区',
                trigger: 'change',
                validator: (_rule: any, _value: any, callback: (error?: Error) => void) => {
                  if (!form.provinceId) {
                    callback(new Error('请选择完整的省市区信息'))
                  } else {
                    callback()
                  }
                },
              },
            ]}
          >
            <Area
              ref={areaRef}
              topClassName="jd-invoice"
              value={initArea}
              scopeLevel={4}
              writeCookie={false}
              placement="bottom"
              isUpScroll={false}
              reLevel={false}
              showWaitData="all"
              onChange={handleAreaChange}
            />
          </Form.Item>
          <Form.Item
            label="收票人地址"
            prop="vat:invoiceConsigneeEditVO:address"
            rules={generateAddressValidationRules()}
            key="consignee-address-step2"
          >
            <Select
              ref={addressDetailRef}
              className="w-full addressDetail"
              type="textarea"
              autosize={{ maxRows: 4, minRows: 7 }}
              value={form.addressDetail}
              filterable
              reserveKeyword
              remote
              onChange={handleAddressDetailChange}
              remoteMethod={isOverSeaArea ? undefined : onSearch}
              autoComplete="off"
              loading={loading}
              maxLength={150}
              placeholder={
                isOverSeaArea ? '请填写详细地址信息（仅支持英文、数字及常用符号）' : '请填写道路、小区、单元楼、门牌号等详细信息'
              }
            >
              {suggestAddr?.map((el) => {
                return (
                  <Select.Option key={el.id || el.title} label={el.title} value={el}>
                    <div>
                      <div className="is-ellipsis" style={{ maxWidth: 390 }} title={el.title}>
                        {el.title}
                      </div>
                      <div className="is-ellipsis" style={{ fontSize: 12, color: '#888B94', maxWidth: 390 }}>
                        {el.shortAddress || el.title}
                      </div>
                    </div>
                  </Select.Option>
                )
              })}
            </Select>
          </Form.Item>
        </>
      )}
    </>
  )
}

export default SpecialInvoiceForm
