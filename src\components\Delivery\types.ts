import { BundleType, NewRemarkVOMap } from '@app/typings/master_api_response'
import DeliveryLogger from './deliveryLogger'

export namespace DeliveryWrap {
  export type Props = {
    initState: InitState
    hasDelivery: boolean
  }
  export type InitState = {
    jdcombineStoreId?: number // 用于留言模块
    storeId?: number // 店铺id
    bundle: BundleType // 包裹信息
    venderId: number // 商家id
    newRemarkVOMap?: NewRemarkVOMap // 留言信息
    isSelfVendor: boolean // 是否为自营商家
  }
}

export namespace Context {
  export type DeliveryState = {
    hasOpen: boolean
    isOpen: boolean
    wrapInitState: DeliveryWrap.InitState
  }

  export type Action = {
    type: string
    isOpen?: boolean
    wrapInitState?: DeliveryWrap.InitState
  }

  // 3. 定义 Context 值类型
  export type DeliveryContextValue = {
    state: DeliveryState
    deliveryLogger: DeliveryLogger
    dispatch: (action: Action) => void
  }
}
