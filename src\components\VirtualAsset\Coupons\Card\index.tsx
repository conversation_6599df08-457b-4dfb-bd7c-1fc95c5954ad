/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-24 18:18:40
 * @LastEditTime: 2025-07-03 18:43:33
 * @LastEditors: ext.wangchao120
 * @Description: 优惠券卡片
 * @FilePath: /pc_settlement/src/components/VirtualAsset/Coupons/Card/index.tsx
 */
import React, { useEffect, useRef, FC } from 'react'
import styles from './index.module.scss'
import classnames from 'classnames'
import Tooltip from '@app/common/Tooltip'
import { GoodsPlaceholderImage, SELECTED_ICON } from '../../constants'
import getImgUrl from '@app/utils/images'
import type { CouponItem, ShowSkuVO } from '@app/typings/virtualAsset'
interface CardProps {
  onClick?: () => void
  item: CouponItem
  disabled?: boolean
  loadingDom?: (id: string) => void
  tabKey?: string
  activeKey?: string
  checked?: boolean
}
const Card: FC<CardProps> = ({ onClick, item, disabled, loadingDom, tabKey, activeKey, checked }) => {
  // console.log('item', tabKey)
  const ref = useRef<HTMLDivElement | null>(null)
  const handleClick = () => {
    onClick && onClick()
  }

  /**
   * 下拉框商品列表
   */
  const dropDownCont = (skuList: ShowSkuVO[]) => {
    // console.log('skuList')
    return (
      <div>
        <div className={styles.tooltipTit}>该券可用商品列表</div>
        <div className={styles.dropDownCont}>
          {skuList &&
            skuList.map((item: ShowSkuVO, index: number) => {
              return <img src={item?.imgUrl ? getImgUrl(item?.imgUrl) : GoodsPlaceholderImage} key={index}></img>
            })}
        </div>
      </div>
    )
  }

  /**
   * 消费劵标识
   */
  const xfq = () => {
    if (!item?.couponIconClass?.desc) return
    return <div className={classnames(styles.xfq, styles[item?.couponIconClass?.iconClass])}>{item?.couponIconClass?.desc}</div>
  }

  /**
   * 优惠劵顶部信息
   */
  const cardTopInfo = () => {
    return (
      <div className={styles.cardTop}>
        {item?.discountInfos?.length > 0 ? (
          <span className={`${styles.yen}`}>
            {item?.discountInfos[0]?.discount}
            <i>折</i>
          </span>
        ) : (
          <span className={`${styles.yen}`}>&yen;{item?.discount}</span>
        )}
        <span className={styles.quota}>
          满<i>{item?.quota}</i>
          {item?.discountInfos?.length > 0 ? (
            <Tooltip arrow={true} maxWidth={360} padding={'12px'} content={`最多减¥${item?.highDiscount}元`}>
              <div className={classnames('icon-info', styles.icon)}></div>
            </Tooltip>
          ) : (
            <></>
          )}
        </span>
        <div>有效期至{item.endTime}</div>
      </div>
    )
  }

  /**
   * 优惠劵底部信息
   */
  const cardBottomInfo = () => {
    return (
      <div className={styles.cardBottom}>
        {item?.dongOverlapStr &&
          item?.dongOverlapStr.map((type: string, index: number) =>
            !disabled && type.includes('品类') ? (
              <Tooltip
                key={index}
                innerRef={ref}
                minWidth={200}
                maxWidth={496}
                padding={'16px'}
                // onChange={getDownContList}
                content={dropDownCont(item?.supportSkus)}
                placement="bottomLeft"
                type="popover"
                distance={4}
              >
                <span>{type}</span>
              </Tooltip>
            ) : (
              <span key={index}>{type}</span>
            ),
          )}
      </div>
    )
  }

  /**
   * 底部提示
   */
  const cardDesc = () => {
    return (
      <div className={classnames(styles['cardDesc'])}>
        {item?.selected ? (
          <div className={styles.discount}>
            <i>已减</i>
            <span className={`${styles.yen}`}>&yen;{item?.discount}</span>
          </div>
        ) : (
          <div className={styles.limitDesc}>{item.limitDesc}</div>
        )}
      </div>
    )
  }
  useEffect(() => {
    if (tabKey === 'coupons') {
      if (activeKey !== 'redeemCode' || checked) {
        loadingDom && loadingDom(item?.id)
      }
    }
  }, [tabKey, activeKey, checked])
  return (
    <div className={classnames(`${disabled ? 'un' : ''}coupon`, styles['flex'])} data-point-id={item?.id}>
      <div className={styles['cardWrap']}>
        <div
          ref={ref}
          className={classnames(styles['card'], item?.selected && styles['active'], (disabled || item?.readOnly) && styles['disabled'])}
          style={{
            background: item?.coupon200 ? `url(${getImgUrl(item?.backgroundImg)})  center/100% 100% no-repeat` : '',
          }}
          onClick={handleClick}
        >
          {/* 已勾选 */}
          {item?.selected && <img src={SELECTED_ICON} />}

          {/* 消费劵标识 */}
          {xfq()}

          {/* 优惠劵顶部信息 */}
          {cardTopInfo()}

          {/* 优惠劵底部信息 */}
          {cardBottomInfo()}
        </div>
      </div>
      {/* 底部提示 */}
      {cardDesc()}
    </div>
  )
}

export default Card
