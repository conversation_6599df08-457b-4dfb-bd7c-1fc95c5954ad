/** 
 * @file: api.ts
 * @description: 结算页数据接口调用声明
 */
import { ApiGetBundleShipmentListResponse } from '@app/typings/api_getBundleShipmentList_resp'
import rca from './request_color_api'
import type { MasterApiQueryParams, SaveConsigneeAddressApiQueryParams } from '@app/typings/master_api_params'
import type { MasterApiResponse, BalancePayment } from '@app/typings/master_api_response'
import type { SubmitOrderApiQueryParams } from '@app/typings/submitOrder_api_params'
import type { SubmitOrderApiResponse } from '@app/typings/submitOrder_api_response'
import type { SuccessApiQueryParams, SuccessApiResponse } from '@app/typings/success_api'
import type {
  GiftCardListResponseBody,
  IGetGiftCardListParams,
  // IVirtualAssetCaptchaParams,
  IQueryGiftCardInfoParasm,
  IBindGiftCardParams,
  IGiftCardSelectionParams,
} from '@app/typings/virtualAsset'
import { DeliveryPickSiteResponse, DeliveryStoreResponse } from '@app/typings/delivery_api'
import {
  InvoiceInfoEditVO,
  GetInvoiceForm,
  CompanyInvoiceInfoDO,
  SearchCreditParam,
  DeleteInvoiceFromUsualForm,
  InvoiceInfoSaveVO,
  SaveInvoiceForm,
  InvoiceResult,
  InvoiceToUsualForm,
} from '@app/typings/invoice.d'

/**
 * 获取结算页主数据, 进入结算页时调用
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function fetchMasterData(params?: MasterApiQueryParams, linkParams?: Record<string, string | number>) {
  return rca<MasterApiResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_getCurrentOrder_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data
  })
}

/**
 * 结算页切换地址,保存地址ID 到结算单
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function saveConsigneeAddress(params?: SaveConsigneeAddressApiQueryParams, linkParams?: Record<string, string | number>) {
  return rca<MasterApiResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_saveConsigneeAddress_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data
  })
}

/**
 * 结算页编辑或增加地址,校验规则
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function addAddressPagePc(params?: { configType: string }, linkParams?: Record<string, string | number>) {
  return rca<MasterApiResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_cmpnt_addAddressPage_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data
  })
}

/**
 * 更新结算页主数据, 页面信息变更时调用
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function updateMasterData(params?: MasterApiQueryParams, linkParams?: Record<string, string | number>) {
  return rca<MasterApiResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_getCurrentOrderWithNewRule_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data
  })
}

/**
 * 提交订单
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function submitOrder(params?: SubmitOrderApiQueryParams, linkParams?: Record<string, string | number>) {
  return rca<SubmitOrderApiResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_submitOrder_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    if (data?.code == '0') {
      return data.body
    } else {
      throw new Error(data?.message)
    }
  })
}

/**
 * 提交订单成功后获取跳转地址
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function getSuccessUrl(params?: SuccessApiQueryParams, linkParams?: Record<string, string | number>) {
  return rca<SuccessApiResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_success_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data.body
  })
}

/**
 * 取消结算促销价格
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function cancelSkuScaleBuyPrice(params?: { scaleSkuIds: number[] }, linkParams?: Record<string, string | number>) {
  return rca<Omit<MasterApiResponse, 'body'>>({
    method: 'POST',
    params: {
      functionId: 'balance_cancelSkuScaleBuyPrice_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data
  })
}

/**
 * 无货剔除
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */

type SkuItem = {
  skuId: number
  num?: number
  storeId?: number
  cartUuid?: string
  giftSkuId?: number
  itemType?: number // todo：服务端抹平卡单skuList和移除接口skuItems差异后可删除
  skuUuid?: string // todo：服务端抹平卡单skuList和移除接口skuItems差异后可删除
}

export function removeItems(params?: { skuItems: SkuItem[] }, linkParams?: Record<string, string | number>) {
  return rca<
    Omit<MasterApiResponse, 'body'> & {
      body: {
        /** 当前sku添加成功后实际数量 */
        num?: number
      }
    }
  >({
    method: 'POST',
    params: {
      functionId: 'balance_removeItems_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data.body
  })
}

/**
 * 切换支付方式
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function selectPayment(params?: Pick<BalancePayment, 'paymentId' | 'subPaymentId'>, linkParams?: Record<string, string | number>) {
  return rca<Omit<MasterApiResponse, 'body'>>({
    method: 'POST',
    params: {
      functionId: 'balance_selectPayment_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data
  })
}

/**
 * 获取配置文案
 * @param params 请求接口时所需的业务参数
 * @param linkParams 接口地址链接上所需的额外参数
 * @returns
 */
export function fetchCommonConfig(params?: { configType: string[] }, linkParams?: Record<string, string | number>) {
  return rca<Omit<MasterApiResponse, 'body'> & { body: Record<string, any> }>({
    method: 'POST',
    params: {
      functionId: 'balance_getConfig_pc',
      ...linkParams,
    },
    data: { ...params },
  }).then(({ data }) => {
    return data
  })
}

/** 查询绑定Sid */
export async function api_queryBindSid(): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'queryBindSid',
    },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/** 查询绑定礼品卡 */
export async function api_queryBindGiftCardComPc<T extends IQueryGiftCardInfoParasm | IBindGiftCardParams>(params: T): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'queryBindGiftCardComPc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/** 获取礼品卡列表 */
export async function api_getGiftCardList(params: IGetGiftCardListParams): Promise<GiftCardListResponseBody> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_getGiftCardByPage_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/** 使用礼品卡 */
export async function api_useGiftCard(params: IGiftCardSelectionParams): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_useOrCancelGiftCard_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/** 取消礼品卡 */
export async function api_cancelGiftCard(params: IGiftCardSelectionParams): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_cancelGiftCard_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/**
 * 优惠劵列表
 */
export function api_getPageCouponList(): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_getPageCouponList_pc',
    },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/**
 * 最优优惠劵
 */
export function api_getBestVertualCoupons(): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_getBestVertualCoupons_pc',
    },
  }).then(({ data }) => {
    return data ?? {}
  })
}
interface SelectOrderCoupon {
  couponKey: string
  couponId: string
  selectCoupon: boolean
}
/**
 * 使用优惠劵/取消优惠劵
 * @param {SelectOrderCoupon} params
 * @param {string} params.couponKey 优惠劵key
 * @param {string} params.couponId 优惠劵id
 * @param {boolean} params.selectCoupon 是否选中
 * @returns {Promise<any>}
 */
export function api_selectOrderCoupon(params: SelectOrderCoupon): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_selectOrderCoupon_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

interface ChangeCoupons {
  couponKey: string
}
/**
 * 兑换优惠劵
 * @param params couponKey 优惠劵号码
 */
export function api_exchangeCoupons(params: ChangeCoupons): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_exchangeCoupons_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/**
 * 获取京豆接口
 */
export function api_getJdBean(): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_getJdBean_pc',
    },
  }).then(({ data }) => {
    return data ?? {}
  })
}
interface UseJdBean {
  jdBeanNum: number
  pointSelectFlag: string
}
/**
 * 使用京豆接口
 * @param {UseJdBean} params
 * @param {number} params.jdBeanNum 京豆数量
 * @param {string} params.pointSelectFlag 是否选择京豆 1-一般整数抵扣 ｜ 2:最大抵扣 ｜3:暂不使用京豆 ｜ 4-自定义数量
 */
export function api_useJdBean(params: UseJdBean): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_useCancelEditJdBean_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/**
 * 使用红包接口
 * @param {boolean} params.use 是否使用红包
 */
export function api_useRedPacket(params: { use: boolean }): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_selectRedPacket_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}
/**
 * 使用余额接口
 * @param {boolean} params.use 是否使用红包
 */
export function api_changeBalance(params: { use: boolean }): Promise<any> {
  return rca({
    method: 'POST',
    params: {
      functionId: 'balance_changeBalanceStatus_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

// 配送列表
export async function api_GetBundleShipmentList(params: {
  bundleUuid: string
  venderId: number
  transportCtrl: boolean
}): Promise<ApiGetBundleShipmentListResponse> {
  return rca<ApiGetBundleShipmentListResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_getBundleShipmentList_pc',
    },
    data: {
      bundleUuid: params.bundleUuid,
      venderId: params.venderId,
      balanceCommonOrderForm: {
        overseasTransport: params.transportCtrl,
      },
    },
  }).then(({ data }) => {
    return data ?? {}
  })
}

// 国外自提点列表
export async function api_balance_transportPickSiteListBundle_pc(params: {
  transportCode?: string
  formatBundleUUID: string
  overseasTransport: boolean
}): Promise<DeliveryPickSiteResponse> {
  console.log(params, 'params')
  return rca<DeliveryPickSiteResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_transportPickSiteListBundle_pc',
    },
    data: {
      transportCode: params.transportCode, // 国内 del
      bundleUuid: params.formatBundleUUID,
      balanceCommonOrderForm: {
        overseasTransport: params.overseasTransport,
        overseaMerge: true,
        international: false,
        checkJdNewRule: true,
        invoicedNewRule: true,
        supportCalendarExtendDaySwitch: true,
        jdBean: true,
        stateSubsidiesSwitch: true,
        govSubsidyDateSwitch: true,
        useBestCoupon: true,
      },
    },
  }).then(({ data }) => {
    return data ?? {}
  })
}

// 门店自提点列表
export async function api_balance_getDeliveryStoreList_pc(params: {
  venderId: number
  provinceId: string
  cityId: string
  areaId: string
  orderLat: string
  orderLng: string
  addrId: string
}): Promise<DeliveryStoreResponse> {
  return rca<DeliveryStoreResponse>({
    method: 'POST',
    params: {
      functionId: 'balance_getDeliveryStoreList_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

// 定期购时间列表接口
export async function api_balance_getRegularBuyPromise_pc(params: {
  /**
   * 用户选择的首次配送时间
   */
  firstDeliveryDate: string
  /**
   * 配送计划
   */
  sendCycle: number
  /**
   * 活动id
   */
  regularBuyActivityId: string
  /**
   * 期数
   */
  sectionNum: number
  /**
   * 每期配送数量
   */
  buyNum: number
  /**
   * 包裹UUID
   */
  bundleUUID: string
  /**
   * 总期数
   */
  totalSectionNum: number
}): Promise<{
  code: string
  message: string
  timestamp: number
  body: {
    buyNum: string
    periodNumber: string
    sendDate: string
  }[]
}> {
  return rca<{
    code: string
    message: string
    timestamp: number
    body: {
      buyNum: string
      periodNumber: string
      sendDate: string
    }[]
  }>({
    method: 'POST',
    params: {
      functionId: 'balance_getRegularBuyPromise_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

//保存时效
export async function balance_saveBundleShipment_pc(params: {
  venderId: number
  bundleUuid: string
  addrId: string
  saveShipmentParam: any
  isZyVender: boolean
  transportCode?: string
  balanceCommonOrderForm?: object
}): Promise<any> {
  console.log(params, 'params')
  return rca<any>({
    method: 'POST',
    params: {
      functionId: 'balance_saveBundleShipment_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

// 上门自提点时效选择列表
export async function balance_getBundlePickDateList_pc(params: { bundleUuid: string; pickSiteId: number }): Promise<{
  code: string
  message: string
  timestamp: number
  body: {
    pickDateList: string[]
    isAvailable: boolean
  }
  traceId: string
  requestId: string
}> {
  return rca<{
    code: string
    message: string
    timestamp: number
    body: {
      pickDateList: string[]
      isAvailable: boolean
    }
    traceId: string
    requestId: string
  }>({
    method: 'POST',
    params: {
      functionId: 'balance_getBundlePickDateList_pc',
    },
    data: { ...params },
  }).then(({ data }) => {
    return data ?? {}
  })
}

/**
 * @description 保存发票信息
 * @param {SaveInvoiceForm} body - 保存发票参数
 * @returns {Promise<Result<InvoiceInfoSaveVO>>} 保存结果
 */
export async function saveInvoice(body: SaveInvoiceForm): Promise<InvoiceResult<InvoiceInfoSaveVO>> {
  const response = await rca({
    method: 'post',
    params: {
      functionId: 'balance_saveInvoice_pc',
    },
    data: body,
  })

  return response.data as InvoiceResult<InvoiceInfoSaveVO>
}

/**
 * @description 新增常用发票
 * @param {AddUsualInvoiceParams} params - 新增常用发票参数
 * @returns {Promise<{usualInvoiceId: number}>} 新增结果
 */
export async function addInvoiceToUsual(body: InvoiceToUsualForm): Promise<InvoiceResult<number>> {
  return rca<InvoiceResult<number>>({
    method: 'POST',
    params: {
      functionId: 'balance_addInvoiceToUsual_pc',
    },
    data: body,
  }).then(({ data }) => {
    return data
  })
}

/**
 * @description 编辑常用发票
 * @param {AddUsualInvoiceParams} params - 编辑常用发票参数
 * @returns {Promise<{usualInvoiceId: number}>} 编辑结果
 */
export async function editInvoiceToUsual(body: InvoiceToUsualForm): Promise<InvoiceResult<number>> {
  return rca<InvoiceResult<number>>({
    method: 'POST',
    params: {
      functionId: 'balance_editInvoiceToUsual_pc',
    },
    data: body,
  }).then(({ data }) => {
    return data
  })
}

/**
 * @description 删除常用发票
 * @param {DeleteUsualInvoiceParams} params - 删除常用发票参数
 * @returns {Promise<{success: boolean}>} 删除结果
 */
export async function deleteInvoiceFromUsual(body: DeleteInvoiceFromUsualForm): Promise<InvoiceResult<boolean>> {
  return rca<InvoiceResult<boolean>>({
    method: 'POST',
    params: {
      functionId: 'balance_deleteInvoiceFromUsual_pc',
    },
    data: body,
  }).then(({ data }) => {
    return data
  })
}

/**
 * @description 查询发票信息
 * @param {GetInvoiceInfoParams} params - 查询参数
 * @returns {Promise<InvoiceQueryResult>} 发票信息
 */
export async function getInvoice(body: GetInvoiceForm): Promise<InvoiceResult<InvoiceInfoEditVO>> {
  return rca<InvoiceResult<InvoiceInfoEditVO>>({
    method: 'POST',
    params: {
      functionId: 'balance_getInvoice_pc',
    },
    data: body,
  }).then(({ data }) => {
    return data
  })
}
/**
 * @description 查询企业发票联想
 * @param {GetInvoiceInfoParams} body - 查询参数
 * @returns {Promise<CompanyInvoiceInfoDO[]>} 发票信息
 */
export async function searchCredit(body: SearchCreditParam): Promise<InvoiceResult<CompanyInvoiceInfoDO[]>> {
  return rca<InvoiceResult<CompanyInvoiceInfoDO[]>>({
    method: 'POST',
    params: {
      functionId: 'balance_getCodeByName_pc',
    },
    data: body,
  }).then(({ data }) => {
    return data
  })
}
