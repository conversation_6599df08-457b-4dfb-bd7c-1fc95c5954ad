/**
 * @file: useRootClassName.ts
 * @description: 窄屏模式下的付款详情展开与收起逻辑
 */
import classNames from 'classnames'
import { useState, useMemo } from 'react'
import useIsNarrowScreen from '@app/hooks/useIsNarrowScreen'

function useRootClassName() {
  const matches = useIsNarrowScreen()
  const [isOpened, setIsOpened] = useState<boolean>(false)
  const toggle = useMemo(() => () => setIsOpened((s) => !s), [])

  const className = (() => {
    if (matches) {
      return classNames({ 'payment--expanded': isOpened })
    } else {
      return ''
    }
  })()

  return [className, toggle] as const
}

export default useRootClassName
