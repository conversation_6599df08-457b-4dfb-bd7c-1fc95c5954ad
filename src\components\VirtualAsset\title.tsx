/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-02 17:37:13
 * @LastEditors: ext.wangchao120
 * @Description: 虚拟资产顶部标题
 * @FilePath: /pc_settlement/src/components/VirtualAsset/title.tsx
 */
import React, { FC } from 'react'
import styles from './index.module.scss'
import RightTip from './components/RightTip'
import useMasterData from '@app/hooks/useMasterData'
import type { BalanceUser } from '@app/typings/master_api_response'
import { reportClick } from '@app/utils/event_tracking'
import type { VirtualPropertyVO } from '@app/typings/master_api_response'

interface Props {
  tabKey?: string
}
const Title: FC<Props> = ({ tabKey }) => {
  const masterData = useMasterData()?.body ?? {}
  const virtualPropertyVO = (masterData?.virtualPropertyVO as VirtualPropertyVO) ?? {}
  const balanceUser = (masterData?.balanceUser as BalanceUser) ?? {
    // 默认进来不显示
    fundsPwd: true,
    realName: true,
  }
  const total = masterData?.balanceTotal

  const onClick = () => {
    // 余额上报点击埋点
    if (tabKey === 'balance') {
      reportClick('virtualasset_balanceClick', {
        tgstatus: virtualPropertyVO?.useBalance ? '1' : '2', // 1:勾选，2:取消勾选
        hlttext: '开启支付密码',
      })
    }
  }
  return (
    <div className={styles.title}>
      <h5>使用优惠/虚拟资产</h5>
      {!balanceUser?.fundsPwd || !balanceUser?.realName ? (
        <div className={styles.tips}>
          为保障您的账户资金安全，首次使用虚拟资产请
          {!balanceUser?.fundsPwd && <RightTip type="paymentPassword" onClick={onClick} />}
          {!balanceUser?.fundsPwd && !balanceUser?.realName && '及'}
          {!balanceUser?.realName && <RightTip type="realNameAuthentication" />}
        </div>
      ) : (
        Number(total?.virtualDiscount) > 0 && (
          <div className={styles.discount}>
            共抵 <i className={`${styles.yen}`}>-&yen;</i>
            <span>{total?.virtualDiscount}</span>
          </div>
        )
      )}
    </div>
  )
}

export default Title
