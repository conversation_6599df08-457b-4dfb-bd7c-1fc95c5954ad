/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-10 15:50:37
 * @LastEditors: ext.wangchao120
 * @Description: 礼品卡、领货码相关逻辑
 * @FilePath: /pc_settlement/src/components/VirtualAsset/hooks/useGift.ts
 */

import { useRef, useState, useCallback } from 'react'
import Cookie from 'js-cookie'
import { confirm, CommonConfirmDialogs } from '@app/components/payment/components/confirm_dialog'
import { api_queryBindSid, api_queryBindGiftCardComPc, api_getGiftCardList, api_useGiftCard } from '@app/services/api.ts'
import { type codeRef } from '../components/RedeemCode/index'
import {
  type IGiftCardSelectionParams,
  type IQueryGiftCardInfoParasm,
  type IBindGiftCardParams,
  type cardListQueryType,
} from '@app/typings/virtualAsset'
import useUpdateMasterData from '@app/hooks/useUpdateMasterData'
import useMasterData from '@app/hooks/useMasterData'
import type { BalanceUser } from '@app/typings/master_api_response'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import showToast from '@app/components/payment/components/toast'
import { reportClick } from '@app/utils/event_tracking'
import { GIFT_CARD_TABS, GIFT_CODE_TABS } from '../constants'
import { useAtom } from 'jotai'
import { virtualAssetLoading, giftTabKey, deliveryCodeTabKey } from '@app/components/VirtualAsset/atoms'
const useGift = () => {
  const codeRef = useRef<codeRef>(null)
  const [giftCardList, setGiftCardList] = useState<any[]>([])
  const [notGiftCardList, setNotGiftCardList] = useState<any[]>([])
  const _updateMasterData = useUpdateMasterData()
  const balanceUser = (useMasterData()?.body?.balanceUser as BalanceUser) ?? {}
  const [, setLoading] = useAtom(virtualAssetLoading)
  const [, setGiftTabKey] = useAtom(giftTabKey)
  const [, setDeliveryCodeTabKey] = useAtom(deliveryCodeTabKey)

  /**
   * 更新数据
   */
  const updateMasterData = useCallback(
    (params: any = undefined) => {
      _updateMasterData(params, 'virtualAsset')
    },
    [_updateMasterData],
  )

  /**
   * 点击兑换按钮
   * @param {string} giftCardPwd 礼品卡code
   * @param {boolean} valid 是否校验通过
   * @param {string} type 'gift'|'accesskey'
   * @returns
   */
  const handleSubmit = (giftCardPwd: string, type: string) => {
    // 是否实名
    if (!balanceUser?.realName) {
      CommonConfirmDialogs?.confirmRealName()
      return
    }
    // 未设置密码
    if (!balanceUser?.fundsPwd) {
      CommonConfirmDialogs?.confirmPaymentPassword()
      return
    }
    const queryGiftCardType = type === 'gift' ? 0 : 1
    // 兑换码格式化
    giftCardPwd = giftCardPwd.replace(/(.{4})(?!$)/g, '$1-')
    // console.log(giftCardPwd, valid)
    setLoading(true)
    api_queryBindSid()
      .then((result) => {
        try {
          // console.log('result', result)
          if (result?.code === 'success') {
            const option = {
              sessionId: result?.data,
              language: '1', //1-中文简体 2-中文繁体 3-英文 4-泰文 可不传 默认中文
              onSuccess: function (ret: any) {
                //验证成功后回调（ret.vt为后台需要再次验证的vt）
                console.log('验证成功后回调')
                queryGiftCardInfo(giftCardPwd, result?.data, ret.vt, queryGiftCardType)
              },
              onLoad: function (data: any) {
                // 验证码初始化完毕时会被调用
                console.log('验证码初始化完毕时会被调用')
              },
              onFailure: function (ret: any) {
                // sid失效，vt过期需重新申请sid，调用reset方法
                //验证失败后回调（接口异常）
                console.log('验证失败后回调')
              },
              onCancel: function () {
                console.log('验证码已取消')
              },
            }
            // 初始化验证码，此函数只需初始化一次，重新创建请调用Jcap.create()
            window?.captchaLoadJS(option, function (obj: any) {
              if (obj?.create) {
                obj.create({ sessionId: result?.data, ccode: 86 })
              } else {
                console.log('验证码加载失败')
              }
            })
          } else {
            showToast({ title: result?.echo || result?.msg || '请稍后再试！' })
          }
        } catch (e) {
          console.error('error', e)
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'queryBindSid',
            error_type_txt: 'queryBindSid接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
      .finally(() => {
        setLoading(false)
      })
  }
  /**
   * 查询礼品卡
   * @param giftCardPwd 礼品卡code
   * @param sessionId sessionId
   * @param verifyCode vt
   * @param queryGiftCardType 0-礼品卡 1-领货码
   */
  const queryGiftCardInfo = (giftCardPwd: string, sessionId: string, verifyCode: string, queryGiftCardType: number) => {
    const params: IQueryGiftCardInfoParasm = {
      giftCardPwd: giftCardPwd,
      sessionId: sessionId,
      verifyCode: verifyCode,
      eid: Cookie.get('jd_eid') || '',
      doBindFlag: 0,
      queryGiftCardType: queryGiftCardType,
      origin: 'settlement',
    }
    api_queryBindGiftCardComPc(params)
      .then((result) => {
        // console.log('res', result)
        try {
          if (result?.code === 'nobind' && result?.data?.isRechargeCard != '1') {
            console.log('查询卡基础信息')
            bindGiftCard(result?.data?.pwdKey, giftCardPwd, queryGiftCardType)
          } else if (result.code === 'nobind' && result.data.isRechargeCard == '1') {
            console.log('暂不支持充值卡充值，请前往我的京东-礼品卡进行充值')
            confirm({
              title: '提示',
              description: '暂不支持充值卡充值，请前往我的京东-礼品卡进行充值',
              okText: '确定',
              cancelText: '',
              onOk: () => {
                console.log('OK')
              },
            })
          } else if (result.code == '-1') {
            confirm({
              title: '提示',
              description: '网路异常，请稍后再试！',
              okText: '确定',
              cancelText: '',
              onOk: () => {
                console.log('OK')
              },
            })
          } else {
            confirm({
              title: '提示',
              description: result.msg,
              okText: '确定',
              cancelText: '',
              onOk: () => {
                console.log('OK')
              },
            })
          }
        } catch (e) {
          console.error('error', e)
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'queryBindSid',
            error_type_txt: 'queryBindSid接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
  }

  /**
   * 绑卡
   * @param pwdKey pwdKey
   * @param giftCardPwd 礼品卡code
   * @param queryGiftCardType 0-礼品卡 1-领货码
   */
  const bindGiftCard = (pwdKey: string, giftCardPwd: string, queryGiftCardType: number) => {
    const params: IBindGiftCardParams = {
      pwdKey: pwdKey,
      giftCardPwd: giftCardPwd,
      eid: Cookie.get('jd_eid') || '',
      doBindFlag: 1,
      queryGiftCardType: queryGiftCardType,
      origin: 'settlement',
    }
    api_queryBindGiftCardComPc(params)
      .then((result) => {
        // console.log('result', result)
        // 绑定成功
        if (result?.code === 'success') {
          showToast({ title: '绑定成功' })
          // console.log('绑定成功')
          // 清空输入框
          codeRef?.current?.clearInput()
          // 跳转列表页
          if (queryGiftCardType === 0) {
            // 跳转礼品卡
            setGiftTabKey('giftList')
          } else {
            // 跳转领货码
            setDeliveryCodeTabKey('available')
          }
        } else if (result.msg != undefined && result.msg != null) {
          confirm({
            title: '提示',
            description: result.msg,
            okText: '确定',
            cancelText: '',
            onOk: () => {
              console.log('OK')
            },
          })
        } else {
          console.log('网络异常，请稍后再试')
          confirm({
            title: '提示',
            description: '网络异常，请稍后再试',
            okText: '确定',
            cancelText: '',
            onOk: () => {
              console.log('OK')
            },
          })
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'queryBindGiftCardComPc',
            error_type_txt: 'queryBindGiftCardComPc接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
  }

  /** 获取礼品卡列表
   * @param {string} giftType 礼品卡类型 gift-礼品卡  accesskey-领货码
   * @param {string} type 类型 update-礼品卡更新 ｜ available-领货码可用列表  ｜ unavailable-领货码不可用列表
   */
  const getGiftCardList = (giftType = '', type: string = '') => {
    // 可用礼品卡列表
    if (giftType === 'gift') {
      list(1)
      // 使用和取消后不更新不可用列表
      if (type === 'update') return
      list(-1)
    } else {
      // 可用领货码列表
      if (type === 'available') {
        list(2)
      } else {
        // 不可用领货码列表
        list(-2)
      }
    }
  }

  /**
   * 获取列表
   * @param {cardListQueryType } cardListQueryType
   * @returns
   */
  const list = (cardListQueryType: cardListQueryType) => {
    const params = {
      cardListPageSize: 100,
      cardListPageNo: 1,
      // presaleStockSign: 1,
      cardListQueryType: cardListQueryType,
    }
    setLoading(true)
    api_getGiftCardList(params)
      .then((result) => {
        try {
          if (result?.code === '0') {
            if (cardListQueryType > 0) {
              setGiftCardList(result?.body?.giftCardList ?? [])
            } else {
              setNotGiftCardList(result?.body?.giftCardList ?? [])
            }
          } else {
            showToast({ title: result?.message })
          }
        } catch (e) {
          console.error('error', e)
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'balance_getGiftCardByPage_pc',
            error_type_txt: 'balance_getGiftCardList_pc接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
      .finally(() => {
        setLoading(false)
      })
  }
  /**
   * 点击礼品卡
   * @param {string} giftType 礼品卡类型 gift-礼品卡  accesskey-领货码
   * @param item
   */
  const cardClick = (giftType: string, item: any) => {
    const cardListQueryType = giftType === 'gift' ? 1 : 2
    const params: IGiftCardSelectionParams = {
      giftCardKey: item?.key,
      giftCardId: item?.id,
      selected: !item?.selected,
      cardListQueryType: cardListQueryType as cardListQueryType,
      cardListPageSize: 100,
      cardListPageNo: 1,
    }
    setLoading(true)
    // 使用礼品卡
    api_useGiftCard(params)
      .then((res) => {
        if (res?.code == '0') {
          // 更新融合接口
          updateMasterData()
          setGiftCardList(res?.body?.giftCardList ?? [])
          // 礼品卡点击埋点
          if (giftType === 'gift') {
            reportClick('virtualasset_CardClick', {
              cardid: item?.id,
              second_tab_name: GIFT_CARD_TABS,
            })
          } else {
            // 领货码点击埋点
            reportClick('virtualasset_RedeemcodeClick', {
              getstock_id: item?.id,
              clerk: item?.cardBrandName,
              // TODO 无参数
              // button_name: '',
              second_tab_name: GIFT_CODE_TABS,
            })
          }
        } else {
          showToast({ title: res?.message || '请稍后再试' })
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'balance_useOrCancelGiftCard_pc',
            error_type_txt: 'balance_useGiftCard_pc接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return {
    handleSubmit,
    codeRef,
    giftCardList,
    notGiftCardList,
    cardClick,
    getGiftCardList,
    updateMasterData,
  }
}

export default useGift
