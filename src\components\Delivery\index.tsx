/**
 * @file: index.tsx
 * @description: 配送模块入口
 */
import React, { Component } from 'react'
import { DeliveryProvider } from './context'
import { DeliveryWrap } from './types'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import DeliveryModule from './mixed/main/DeliveryModule'
import SelectorV2 from './mixed/selector/SelectorV2'
import DingQiGouModule from './mixed/main/DingQiGouModule'

class ErrorBoundary extends Component {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError() {
    // 更新 state 使下一次渲染能够显示降级 UI
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 可以将错误日志上报到服务器
    console.log('错误信息:', error)
    console.log('错误详情:', errorInfo)
    monitoring({
      name: monitorName.Settlement,
      code: monitorCode.Delivery,
      msg: {
        error: errorInfo,
        info: 'delivery 组件错误',
        type: '配送模块',
      },
    })
  }

  render() {
    if ((this.state as { hasError: boolean }).hasError) {
      // 你可以自定义降级 UI
      return null
    }

    return (this.props as { children: any }).children
  }
}

export default ({ initState, hasDelivery }: DeliveryWrap.Props) => {
  const { newRemarkVOMap, venderId } = initState
  let input = false
  if (venderId !== null && venderId !== undefined && newRemarkVOMap && newRemarkVOMap[venderId]?.show) {
    input = true
  }
  return (
    <ErrorBoundary>
      <DeliveryProvider initState={initState}>
        {/* 111 */}
        {!hasDelivery && <SelectorV2 input={input} />}
        {hasDelivery && <DeliveryModule />}
        {hasDelivery && <DingQiGouModule />}
        {/* <Selector
          initState={{
            jdcombineStoreId: jdcombineStoreId, //店铺 id 用于 留言功能
            storeId: storeId, //店铺 id  用于 留言功能
            bundle: bundle, //配送信息
            delivery: true, //显示配送模块
            input: input, //是否显示留言模块
            install: true, //时候显示安装模块
            venderId: venderId,
          }}
        /> */}
      </DeliveryProvider>
    </ErrorBoundary>
  )
}
