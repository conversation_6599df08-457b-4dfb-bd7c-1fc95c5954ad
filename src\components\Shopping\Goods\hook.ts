import { useEffect, useRef } from 'react'

type IntersectionObserverOptions = {
  root?: Element | Document | null
  rootMargin?: string
  threshold?: number | number[]
}

const useIntersectionObserverOnce = (callback: () => void, options?: IntersectionObserverOptions) => {
  const targetRef = useRef<Element | null>(null)
  const hasTriggered = useRef(false)
  const observerRef = useRef<IntersectionObserver | null>(null)

  // 设置目标元素引用
  const setTargetRef = (node: Element | null) => {
    targetRef.current = node
  }

  useEffect(() => {
    const target = targetRef.current
    if (!target || hasTriggered.current) return

    const handleIntersect: IntersectionObserverCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !hasTriggered.current) {
          hasTriggered.current = true
          callback()

          // 执行后立即断开观察
          if (observerRef.current) {
            observerRef.current.disconnect()
            observerRef.current = null
          }
        }
      })
    }

    observerRef.current = new IntersectionObserver(handleIntersect, options)
    observerRef.current.observe(target)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [callback, options])

  return setTargetRef
}

export { useIntersectionObserverOnce }
