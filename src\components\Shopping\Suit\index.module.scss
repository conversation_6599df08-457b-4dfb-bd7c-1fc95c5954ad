.suit {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 12px;

  .tag {
    padding: 2px 5px;
    border-radius: 2px;
    border: 0.33px solid #ffa199;
    color: #fa2c19;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .name {
    color: #1a1a1a;
    font-size: 14px;
    font-weight: 600;
    margin: 0 8px 0 6px;
  }

  .price {
    margin-left: 40px;
  }

  .quantity {
    color: #888b94;
    font-size: 14px;
  }

  .suit-price {
    .value {
      color: #ff0f23;
      font-family: JDZhengHeiVRegular2-1;
      font-size: 16px;
      font-weight: 600;
    }

    .unit {
      color: #ff0f23;
      font-size: 14px;
      margin-left: 4px;
    }
  }

  .jd-price {
    color: #888b94;
    font-family: JDZhengHeiVRegular2-1;
    font-size: 14px;
    margin-top: 8px;
  }
}

/* 窗口宽度在 1440px 到 1660px 之间 */
@media (max-width: 1659px) and (min-width: 1440px) {
  .suit {
    .name {
      max-width: 500px;
    }
  }
}

/* 窗口宽度小于 1240px */
@media (max-width: 1239px) {
  .suit {
    .name {
      max-width: 500px;
    }
  }
}
