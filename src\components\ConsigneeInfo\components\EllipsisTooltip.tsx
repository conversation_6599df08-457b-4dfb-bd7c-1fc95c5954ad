import JDTooltip from '@app/common/Tooltip'
import { useCallback, cloneElement, ReactElement, ReactNode, useMemo, isValidElement } from 'react'
import useEllipsisDetect from '@app/hooks/useEllipsisDetect'
import classNames from 'classnames'

interface TooltipProps {
  children: ReactNode
  content: string
  className?: string
}

const Tooltip = ({ children, content, className }: TooltipProps) => {
  const { ref, hasEllipsis } = useEllipsisDetect()
  const tooltipContent = useMemo(() => <div className="leading-20" dangerouslySetInnerHTML={{ __html: content }} />, [content])

  // 渲染带Tooltip的子元素
  const renderChildren = useCallback(() => {
    if (!isValidElement(children)) return children

    const childElement = cloneElement(children as ReactElement, {
      ref,
    })

    return hasEllipsis ? (
      <JDTooltip
        key={content}
        content={tooltipContent}
        placement="top"
        width={232}
        padding="12px"
        className={classNames(className, 'is-flex', 'is-ellipsis')}
        arrow
        renderElement
      >
        {childElement}
      </JDTooltip>
    ) : (
      childElement
    )
  }, [hasEllipsis, children, className, content, tooltipContent, ref])

  return <>{renderChildren()}</>
}

export default Tooltip
