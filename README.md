# PC 结算页

## 开发环境
  - NodeJS版本：`>=20.x.x`
  - 包管理器：`pnpm`
  - 构建工具：[Vite](https://cn.vite.dev/)
  - host配置：`127.0.0.1 dev.jd.com`
  - color网关：链接上的`ECFlag`参数可用来切换网关域名环境，`1`测试、`2`beta，列如：`https://dev.jd.com:5173/?ECFlag=2`
  - 预售商品结算页：添加`?source=presale`参数，如：`https://dev.jd.com:5173/?ECFlag=2&source=presale`

## 构建部署
  - 代码构建和发布：`npm run build:pre`
  - 项目部署：头尾系统 [tradePc](http://header.jd.com/#/file/my_file)
  - 版本号升级：package.json文件的`version`字段
  - 预发预览：
    - http协议，绑定host`************* trade.jd.com`，访问 http://trade.jd.com/shopping/order/getOrderInfo.action?ECFlag=2
    - https协议，绑定host`************* trade4.jd.com`，访问 https://trade4.jd.com/shopping/order/getOrderInfo.action?ECFlag=2

## 依赖开发库
  - UI框架：[React](https://react.dev/)
  - Hooks库：[ahooks](https://ahooks.js.org)
  - 状态管理：[jotai](https://jotai.org/docs)
  - 网络请求：[Axios](https://axios-http.com/docs/intro)

## 目录结构
<pre>
│____public
│ │____init.js                             // 全局初始化配置
│____script                                // 构建脚本
│____src                                   // 源码目录
│ │____assets/
│ │____atoms/                              // Jotai的原子(atom)定义，进行全局React状态管理
│ │____common/                             // 公共组件
│ │____components/                         // 业务组件 
│ │____helpers/
│ │____hooks/
│ │____layouts/
│ │____services/                           
│  │____api.ts                              // 接口调用声明
│  │____const.ts                            // 与网络请求相关的常量
│  │____parameters.ts                       // 融合接口参数处理相关
│  │____request_color_api.ts                // 发起网络求
│ │____typings/
│ │____global.d.ts
│ │____app.tsx                              // 应用根组件，业务组件在此引入
│ │____main.tsx                             // 应用入口文件，全局声明或初始化在此引入
│____tsconfig.app.json
│____tsconfig.json
│____tsconfig.node.json
│____vite.config.ts
│____README.md

</pre>

## 分支管理

## 公共组件（公共头、侧边栏）[接入指南](http://xingyun.jd.com/codingRoot/mall_fe/mall-common-component/blob/feat-search/README.md)
1. 在HTML中插入相应的插槽元素：
```html
  <!-- 公共头插槽 -->
  <div id="shortcut-2024"></div>
  <!-- 侧边栏插槽 -->
  <div id="elevator-2024"></div>
```

2. 在HTML中引入相应的CSS和JS：
```html
  <!-- head元素中引入CSS -->
  <link href="//storage.360buyimg.com/retail-mall/mall-common-component/pre/0.0.6/css/index.aba7814f.css" rel="stylesheet"/>
  <!-- body元素中引入JS -->
  <!-- 事件总线 -->
  <script src="https://storage.360buyimg.com/retail-mall/lib/eventemiter.js?t=1"></script>
  <!-- 无障碍辅助功能 -->
  <script src="https://static.360buyimg.com/item/assets/oldman/wza1/aria.js?appid=bfeaebea192374ec1f220455f8d5f952"></script>
  <!-- 公共组件：头尾、侧边栏  -->
  <script src="//storage.360buyimg.com/retail-mall/mall-common-component/pre/0.0.6/js/index.aba7814f.js" defer></script>
```

3. 在HTML中初始化配置数据：
```html
  <script>
    // window.commonAppId = "trade-jd-com-v5"; 
      window.pageConfig = {
        // 头部导航“手机京东”数据配置
        shortcutMobileData: [
          {
            title: '\u624b\u673a\u4eac\u4e1c',
            desc: '\u65b0\u4eba\u4e13\u4eab\u5927\u793c\u5305',
            img: 'jfs\/t1\/84498\/39\/9486\/28110\/5d72189bEee8e7110\/773e5a267ab3786c.png',
            url: 'https:\/\/app.jd.com\/',
            devices: [
              { type: 'iphone', src: 'https:\/\/itunes.apple.com\/cn\/app\/id414245413' },
              { type: 'android', src: 'https:\/\/storage.jd.com\/jdmobile\/JDMALL-PC2.apk' },
              { type: 'ipad', src: 'https:\/\/itunes.apple.com\/cn\/app\/jing-dong-hd\/id434374726' },
            ],
          },
          {
            title: '\u5173\u6ce8\u4eac\u4e1c\u5fae\u4fe1',
            desc: '\u5fae\u4fe1\u626b\u4e00\u626b\u5173\u6ce8\u4eac\u4e1c\u670d\u52a1\u53f7\u8ba2\u9605\u66f4\u591a\u4fc3\u9500\u4f18\u60e0\u798f\u5229',
            img: 'jfs\/t1\/74865\/18\/9334\/11346\/5d7218c7Ecccb8ff0\/34095b63503fc108.jpg',
            url: '',
            devices: [],
          },
          {
            title: '\u4eac\u4e1c\u91d1\u878d\u5ba2\u6237\u7aef',
            desc: '\u65b0\u4eba\u4e13\u4eab\u5927\u793c\u5305',
            img: 'jfs\/t1\/77329\/31\/9360\/15408\/5d7218f7Efb5248cb\/ac1de226dafd0455.jpg',
            url: 'https:\/\/m.jr.jd.com\/integrate\/download\/html\/pc.html',
            devices: [
              {
                type: 'iphone',
                src: 'https:\/\/itunes.apple.com\/cn\/app\/jing-dong-jin-rong-hui-li\/id895682747?mt=8',
              },
              { type: 'android', src: 'http:\/\/************\/downapp\/jrapp_jr188.apk' },
            ],
          },
          {
            title: '\u4eac\u4e1c\u5065\u5eb7\u5ba2\u6237\u7aef',
            desc: '',
            img: 'jfs\/t1\/171539\/38\/9328\/28300\/60404decE2ee00c60\/297e613c999daada.png',
            url: 'https:\/\/www.jd.com',
            devices: [
              { type: 'iphone', src: 'https:\/\/hlc.m.jd.com\/download\/?downloadSource=jdh_JDcom' },
              { type: 'android', src: 'https:\/\/hlc.m.jd.com\/download\/?downloadSource=jdh_JDcom' },
            ],
          },
          {
            title: '\u5173\u6ce8\u4eac\u4e1c\u5c0f\u7a0b\u5e8f',
            desc: '\u65b0\u4eba0.1\u5143\u8d2d',
            img: 'jfs\/t1\/154335\/38\/20291\/19657\/60404de0Ece970bda\/917415e2e3b10628.jpg',
            url: '',
            devices: [],
          },
        ],
        // 侧边栏导航配置
        cartIcons: [
          { name: '购物车', poiSuffix: '051', icon: 'Cart' },
          { name: '我的', poiSuffix: '052', icon: 'Me' },
          { name: '客服', poiSuffix: '05', icon: 'ConsumerService' },
          { name: '反馈', poiSuffix: '06', icon: 'Feedback' },
        ],
        product: {
          // 侧边栏配置“反馈”链接配置
          FEEDBACK: 'https://wj-dongjian.jd.com/q/61961d8186206f00b9b1bae0'
        },
        headShiLaoHua: true, // 是否显示“网站无障碍”导航
        headServiceType: 'default'
      }
  </script>
```
4. [ColorAPI市场](https://color.jd.com/webapi/market/#/api-market)申请接口授权：   
  4.1 [color网关 appid和functionId介绍](https://joyspace.jd.com/pages/p6PHOYMBupcRxw8oj7TX)  
  4.2 接口列表：[公共组件相关的接口](http://xingyun.jd.com/codingRoot/mall_fe/mall-common-component/blob/feat-search/README.md)部分以`functionId`开头标识的就是要申请的接口，根据使用的公共组件，选择对应的接口进行申请。  
  4.3 本应用申请的ColorAPI：pcCart_jc_getCartNum,pcCart_jc_gate,checkChat,pcorder_getOrderListCountJson,pc_getMyJdAnswerCount,pctradesoa_queryTopbar,pctradesoa_followCommodity_count,pctradesoa_query_ace,pingou_webmonitor_biz,getcouponcount,btshowjsf  

## 烛龙使用方式
```javascript
enum monitorCode {
  // 收货地址
  Address = 800,
  // 赠品
  Gift = 801,
  // 订单信息
  Order = 802,
  // 顺手买
  HandBuy = 803,
  // 发票
  Invoice = 804,
  // 虚拟资产
  VirtualAsset = 805,
  // 付款详情
  PaymentDetail = 806,

  // 其他
  Other = 900,
}
// 引入monitoring
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
// 监控上报
monitoring({
  name: monitorName.Settlement,
  code: monitorCode.VirtualAsset,
  msg: {
    functionId: 'functionId',
    error_type_txt: 'error_type_txt',
    request: '请求参数'，
    error_msg: '错误信息',
  },
})
```

## 相关文档
需求文档：https://joyspace.jd.com/pages/UNo7hFpqBaj2e1cF2P2e  
设计稿：https://relay.jd.com/file/design?id=1880234483899174913&page_id=0%3A18083&node_id=494%3A1&mode=dev  
模块拆分：https://joyspace.jd.com/sheets/GaUUM0VNaK8I0UbXxnTi  
埋点文档：https://joyspace.jd.com/sheets/gpaHiF5rEjhVOKrrGCmh  
接口文档：https://j-api.jd.com/fe-app-view/demandManage/32330?interfaceType=1   
结算页入口详细设计: https://joyspace.jd.com/pages/vxBvEQAVBEm1Sd2sTeEB
