.root {
    display: flex;
    flex-direction: column;
    padding: 24px;
    max-height: 400px;
}

.header {
    line-height: 26px;
    font-size: 18px;
    font-weight: 600;
    margin-top: 12px;
    display: flex;
    i {
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 12px;
        &.warning {
            background: url('https://img12.360buyimg.com/img/jfs/t1/320563/8/11038/1571/68586b8bFce694af5/977ca928d6008a1d.png') center / cover no-repeat;
        }
        &.success {
            background: url('https://img13.360buyimg.com/img/jfs/t1/311233/22/10398/2168/6851caedF893d49e8/c88710c2a63e6c89.png') center / cover no-repeat
            
        }
        &.fail {
            background: url('https://img20.360buyimg.com/img/jfs/t1/320838/39/10094/1938/6851cb11F7504b069/12b37683612ee23e.png') center / cover no-repeat;
        }
    }
    
}

.body {
    flex: 1;
    overflow: auto;
    margin-top: 8px;
    // padding: 0 9px;

    .content {
        width: fit-content;
        line-height: 20px;
        font-size: 14px;
        white-space: pre-wrap;
        word-break: break-all;
        text-align: justify;
        margin: auto;
        &.contentArr {
            margin: 0;
        }
    }
}

.footer {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    margin-top: 28px;
}

.confirm {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    height: 32px;
    min-width: 80px;
    background: linear-gradient(90deg, rgba(255, 71, 93, 1) 0%, rgba(255, 15, 35, 1) 100%);
    border-radius: 6px;
    line-height: 1.5;
    font-size: 14px;
    color: #fff;
    padding: 9px 12px;
    margin: 0;
    box-sizing: border-box;
    white-space: nowrap;
    &:hover {
        color: #fff;
    }

    &::after {
        border: none;
    }
}

.cancel {
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    height: 32px;
    min-width: 80px;
    background: rgba(255, 255, 255, 1);
    border-radius: 6px;
    line-height: 1.5;
    font-size: 14px;
    padding: 9px 12px;
    margin: 0;
    border: 1px solid #C2C4CC;
    box-sizing: border-box;
    white-space: nowrap;
    transform: scale(1);
    // margin-right: 8px;

    &::after {
        border: none;
    }
}

.modal {
    position: fixed;
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 200ms ease-in;
    z-index: 10000;
    overflow: hidden;
    color: #1A1A1A;

    .overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 150ms ease-in;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 80%;
        min-width: 300px;
        border-radius: 12px;
        background-color: #ffffff;
        overflow: hidden;
    }
}

.active {
    opacity: 1;
    height: 100%;

    .overlay,
    .container {
        opacity: 1;
    }
}
