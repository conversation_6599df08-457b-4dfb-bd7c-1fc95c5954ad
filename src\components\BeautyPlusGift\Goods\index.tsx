import { BalanceSkuType } from '@app/typings/master_api_response'
import getCDNImgUrl from '@app/utils/images'
import { DEFAULT_IMG } from './constant'
import scope from './index.module.scss'

interface IProps {
  sku: BalanceSkuType
}

const Goods: React.FC<IProps> = ({ sku }) => {
  // 跳转商品详情
  const skipItem = () => {
    window.open(`https://item.jd.com/${sku.id}.html`)
  }

  // 渲染主图
  const renderGoodsImg = (url?: string) => {
    let imgUrl = DEFAULT_IMG

    if (url) {
      imgUrl = getCDNImgUrl(url)
    }

    return imgUrl
  }

  return (
    <div className={`${scope.goods} flex`}>
      <div className={scope.left}>
        <div className={scope['goods-img']}>
          {/* 商品主图 */}
          <img src={renderGoodsImg(sku.imgUrl)} className={scope.img} onClick={skipItem} />
          {/* 库存状态 */}
          {sku.stockStateText && <div className={`${scope['stock-text']} flex-center2`}>{sku.stockStateText}</div>}
        </div>
      </div>
      <div className={scope.center}>
        <div className={scope.block}>
          <div className={`${scope.title} flex-center`} onClick={skipItem}>
            {/* <div className={scope.tag}>美妆加赠</div> */}
            {/* 商品标签 */}
            {sku?.skuTitleIconList?.map((i, x) => <img src={i.url} className={scope.icon} key={x} />)}
            {/* 商品名称 */}
            <div className={`text-ellipsis ${scope.text}`}>
              {sku.titleTag || ''}
              {sku.name}
            </div>
          </div>

          {/* 规格 */}
          <div className={scope.sku}>
            {sku.color && <div className={scope.item}>{sku.color}</div>}
            {sku.size && <div className={scope.item}>{sku.size}</div>}
            {/* 规格不存在 占位防止掉楼 */}
            {!sku.color && !sku.size && <div className={scope.item}>&nbsp;</div>}
          </div>
          {/* 价格｜数量 */}
          <div className={scope['price-quantity']}>
            <div className="flex flex-1">
              {/* 赠品价格 */}
              <div className={`${scope['received-price']}`}>&yen;{sku.jdPrice}</div>

              {/* 原始价格 */}
              {sku.primitivePrice && <div className={`${scope['jd-price']}`}>￥{sku.primitivePrice}</div>}
            </div>

            {/* 购买数量 */}
            <div className={scope.quantity}>&times;{sku.buyNum}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Goods
