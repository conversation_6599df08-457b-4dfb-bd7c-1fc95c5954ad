export type SubmitOrderApiQueryParams = Partial<SubmitOrderForm>

export interface SubmitOrderForm {
  /**订单联盟参数*/
  orderUnionParam?: OrderUnionParam
  /**支付密码*/
  payPassword?: string
  /**验证码，用户输入的答案*/
  checkcodeTxt?: string
  /**验证码真实的答案*/
  checkCodeAnswer?: string
  /**浏览器UA*/
  userAgent?: string
  /**订单备注*/
  remark?: string
  /**sop是否开发票*/
  sopNotPutInvoice?: string
  /**预售订单所填用户手机号*/
  presaleMobile?: string
  /**预售订单支付类型*/
  presalePayType: number
  /**礼品购是否隐藏价格*/
  giftBuyHidePrice: boolean
  /**验证码随机数*/
  checkCodeRid?: string
  /**扩展属性Map*/
  orderExtInfoMap?: any
  /**入口vip*/
  submitOrderVip?: string
  /**是否忽略价格变化*/
  ignorePriceChange: number
  /**卡信息*/
  cardInfo?: string
  /**支付前置白条参数*/
  plan?: string
  /**优惠券数量*/
  couponNum?: string
  /**活动ID*/
  activityId?: string
  /**优惠金额*/
  discountAmount?: string
  /**是否支持白条*/
  btSupport?: string
  /**支付前置京东卡还款日期*/
  repayDate?: string
  /**支付前置京东卡ID*/
  cardid?: string
  /**支付前置京东卡Key*/
  cardkey?: string
  /**支付前置京东卡签名*/
  cardsign?: string
  /**支付前置京东卡有效期*/
  cardvalid?: string
  /**是否最优优惠券*/
  isBestCoupon: number
  /**追踪ID*/
  trackId?: string
  /**短信验证码*/
  msgCode?: string
  /**短信验证码Uuid*/
  msgUuid?: string
  /**赠品无货移除*/
  giftRemove?: string
  /**提单刷子限制标识*/
  limitUserFlag?: string
  /**采购清单ID*/
  qygId?: string
  /**白条分期数*/
  fq?: string
  /**店铺留言parseJson*/
  vendorRemarks?: {
    remark: string
    venderId: number
    jdcombineStoreId?: number
  }[]
  /**国际站初始化cookie*/
  overseaPurchaseCookies?: string
  /**随手买商品*/
  purchaseCasualSkuIds?: string[]
  /**奥莱外部单号*/
  jdOutLetsOrderId?: string
  /**User-Agent*/
  agent?: string
  /**配送一级地址*/
  provinceId?: string
  /**配送二级地址*/
  cityId?: string
  /**配送三级地址*/
  areaId?: string
  /**配送四级地址, 例如：1-72-2799*/
  locationId?: string
  /**客户端Ip*/
  ip?: string
  /**BalanceClientInfo class*/
  balanceClientInfo?: ClientInfoQuery
  /**通用入参-信息
  https://joyspace.jd.com/pages/LtfuLiGG84RNEa3xT6XJ
  https://cf.jd.com/display/H5/orderFrom
  融合Biz：com.jd.core.server.balance.domain.common.Biz*/
  balanceCommonOrderForm?: OrderFormQuery
  /**cookieMapStr*/
  cookieMapStr?: string
  /**headerUserAgent*/
  headerUserAgent?: string
  /**color参数：根据用户ip计算出的附加信息*/
  location?: string
  /**orderFrom*/
  orderFrom?: number
  /**android/ios/pc*/
  client?: string
  /**白条、京东支付前置类型：mzwx：m站微信环境，mzsq：m站手q环境*/
  payChannelType?: string
  /**广告参数（m站区分广告来源，--暂无功能使用，用于大数据上报统计）*/
  utmMedium?: string
  /**进入系统时间*/
  startTime?: number
  /**color应用app id*/
  appid?: string
  /**设备指纹信息*/
  eid?: string
  /**风控 fp*/
  fp?: string
  /**百亿补贴屏蔽入参标识*/
  bbtf?: string
  /**来源：主要和商详保持一致，能区分后续来源*/
  sourceType?: string
  /**业务类型*/
  businessType?: string
  /**刷新结算页的时候，判断是否不需要重置默认地址（大家电换区、轻松购不需要重置）*/
  notResetDefaultAddress: boolean
  /**进入结算的整单uuid*/
  orderUuid?: string
  /**对应的子单uuid*/
  combinationBundleUuid?: string
  /**中台地址全站化使用，由soa传入，只在首次进结算使用，作为进结算时的默认地址*/
  addressId?: number

  needIdentity: number
  /**用户pin*/
  pin?: string
  /**uuid*/
  uuid?: string
  /**设备ID*/
  deviceUUID?: string
  /**应用ID*/
  appId?: string
  /**插件ID，应用类型为2（插件）时，必填*/
  pluginId?: string
  /**鉴权token，加密生成。*/
  token?: string
  /**请求来源*/
  source?: string
  /**全链路压测标识*/
  forcebot?: string
  /**登陆模式*/
  loginType?: string
  /**扩展参数（可选，按需传值）*/
  extParam?: any
}

export interface OrderUnionParam {
  /**联盟ID*/
  unionId: number
  /**联盟站点ID*/
  unionSiteId?: string
  /**联盟用户名*/
  unionUserName?: string
  /**联盟时间*/
  unionTime?: string
  /**联盟扩展信息*/
  unionEx?: string
  /**新联盟ID*/
  mt_xid?: string
  /**新联盟子站点*/
  mt_subsite?: string
  /**新联盟扩展信息*/
  mt_ext?: string
  /**DMP参数*/
  dmpjs?: string
  /**联盟参数*/
  unpl?: string
}

export interface ClientInfoQuery {
  /**全链路测试标识*/
  forcebot?: string
  /**流量终端*/
  ua?: number
  /**客户端类型*/
  client?: string
  /**客户端版本*/
  clientVersion?: string
  /**客户端小版本号*/
  build?: number
  /**客户端操作系统版本*/
  osVersion?: string
  /**客户端设备号*/
  uuid?: string
  /**用户pin*/
  pin?: string
  /**用户会员级别*/
  umg?: string
  /**用户风险级别*/
  urg?: string
  /**plus级别*/
  upg?: string
  /**合作伙伴*/
  partner?: string
  /**客户端屏幕尺寸*/
  screen?: string
  /**设备品牌*/
  dBrand?: string
  /**设备型号*/
  dModel?: string
  /**请求网络类型*/
  networkType?: string
  /**用户ip*/
  ip?: string
  /**服务器ip*/
  serverIp?: string
  /**访问京东时用户ip的原始端口*/
  port?: number
  /**根据用户ip计算出的附加信息*/
  location?: string
  /**四级地址*/
  area?: string
  /**前端请求携带的HTTP Referer*/
  referer?: string
  /**前端请求携带的User-Agent*/
  agent?: string
  /**前端请求携带的cookie*/
  cookie?: string
  /**红包所需服务名称*/
  serverName?: string
  /**红包所需用户IP*/
  userIP?: string
  /**红包所需countKey*/
  countKey: number
  /**红包所需netBuySourceType*/
  netBuySourceType: number
  /**调用发票基础服务invokeInvoiceBasicService*/
  invokeInvoiceBasicService: boolean
  /**客户端浏览器名称*/
  browserName?: string
  /**客户端浏览器版本号*/
  browserVersion?: string
  /**操作系统版本号*/
  osName?: string
}

export interface OrderFormQuery {
  /**流程*/
  action?: number
  /**传参传递的 购物车选中的sku数量*/
  selectedSkuNum?: string
  /**是否支持多地址功能： 需要选完多地址功能后才能标记，
     并且要配套给结算中台传tokenKey："51"；     * tokenValue："multiaddress"*/
  supportMultipleProductAddress?: boolean
  /**新增字段：微信端隔离支付方式传 1 ,京购小程序透传字段*/
  clientPartStoreId?: string
  /**微信先用后付流程标识，京购小程序透传字段*/
  weChatCreditPayFlag?: boolean
  /**扩展map 用于补充新增的各类字段传递*/
  extMap?: any
  /**请求来源页面*/
  webOriginId?: number
  /**流程标示*/
  orderFlowType?: number
  /**站点标示*/
  siteId?: number
  /**来源标示  小程序/pc/app
      1 = pc /对应中台 useragentid = 1(UA.PC(1))
      2 = app /对应中台 useragentid = 2(UA.APP(2))
      3 = 极速版小程序 /对应中台 useragentid = 2(UA.APP(2))
      4 = 门祥小程序 /对应中台 useragentid = 2(UA.APP(2))
      5 = 京东 京车会 /对应中台 useragentid = 2(UA.APP(2))
      6 = 微信小程序 /对应中台 useragentid = 5(UA.WX(5))
      7 = M站 /对应中台 useragentid = 3(UA.WAP(3))*/
  originId?: number
  /**渠道价格标示*/
  businessOrigin?: string
  /**扩展Map*/
  orderExtAttrMap?: any
  /**流程标示(作为区分订单流程使用，用来区分不同的存储)*/
  flowId: number
  /**流程ID,用以标识各业务线的流程.
      结算页中台见字典 com.jd.trade.sdk.common.dict.order.FlowTypeDict
      购物车中台见字典 com.jd.core.server.balance.constants.CartFlowTypeEnum*/
  flowType: number
  /**是否是轻松购流程*/
  easybuy?: boolean
  /**是否是分支购物流程，目前暂有本地生活服务的分支购物流程*/
  locBuy?: boolean
  /**是否是礼品购流程*/
  giftbuy?: boolean
  /**是否是实体礼品卡流程*/
  lipinkaPhysical?: boolean
  /**是否是合约机流程*/
  contractMachine?: boolean
  /**白条支付流程*/
  whiteBar?: boolean
  /**是否将地址设置为全站默认地址(只有在getCurrentOrder页面才会用到)
      大家电换区的时候getCurrentOrder不需要将地址设置为全站默认地址，否则会导致死循环
      手机端、微信端新增地址后，会重新调用getCurrentOrder，这时不需要将地址设置为全站默认地址*/
  resetDefaultAddress?: boolean
  /**是否重置311*/
  resetResevsion?: boolean
  /**厂商直送发码的门店ID*/
  factoryShopId: number
  /**厂商直送发码的门店ID*/
  factoryRegionId?: string
  /**预售流程*/
  presale?: boolean
  /**是否调用新优惠券接口(ps:优惠券切分流量后删除)*/
  invokeNewCouponInterface?: boolean
  /**是否重置标记，类似sendpay方式，可以支持多个重置业务逻辑*/
  resetFlag?: string
  /**缓存中的地址id。如key:ipLoc-djd;value:3-51036-2984-0*/
  areaIdFromCookie?: string
  /**是否调用新 虚拟资产*/
  isNewVertual?: boolean
  /**是否是汽车OTO*/
  carO2O?: boolean
  /**getAdd方法需要返回日历信息，即使是自提的时候,给手机端用，以后可优化成和pc端一样，自提时不需要调日历*/
  needCalender?: boolean
  /**true：rpc校验是否需要显示订单备注 false：无需调用rpc*/
  checkShowOrderRemark?: boolean
  /**getAdd方法是否需要更新orderStore缓存，给手机端使用,默认为0需要更新，其他值不需要更新*/
  needUpdateStore: number
  /**透传促销属性*/
  requestConditionType: number
  /**传递是否已经校验过验证码的标记，只是内部标记，不对外*/
  captchaHasChecked?: boolean
  /**是否使用礼品盒子.<br>
      在结算页展示传true，否则false，为了解决其他各端在结算页不展示却往下传节点的问题*/
  useGiftService?: boolean

  requestConditionTypes?: number[]
  /**移动端调用，移动端的版本是否为新版本，在计算礼品卡数量时使用*/
  newVersion4m?: boolean

  useBestCoupon?: boolean

  returnSelectedCouponNum?: boolean

  notReturnTotalCouponNum?: boolean
  /**风控标记*/
  riskControl?: string
  /**是否需要送装一体
      送装一体兼容手机端老版本，老版本默认为false，新版本为true；  手机端老版本废弃后考虑是否去掉（包括pc 手机  微信）*/
  needShipInstall?: boolean
  /**购物车换区标示*/
  noPartitionProduct?: boolean
  /**是否走购物车换区新流程*/
  newReplacedFlow?: boolean
  /**是否检查换区*/
  checkPartitionProduct?: boolean
  /**虚拟组套是否叠加总价判断（兼容新老版本） @@date 2017-2-15*/
  vsuitToMan?: boolean
  /**是否是飞单 tiantian11*/
  isFlyOrder?: boolean
  /**是否第一次访问getAdd*/
  typeFlag: number
  /**是否app切量*/
  appUp?: boolean
  /**标签类型普通标准达：1000;普通京准达：2000;半小时京准达：2005; 一小时京准达：2010; 两小时京准达：2020*/
  promiseTagType?: string
  /**忽略类型*/
  ignorePickType: number
  /**版本逻辑兼容参数*/
  versionParams?: Map<string, number>
  /**一号店订单来源类型*/
  yhdSourceType: number
  /**0:礼品卡;1:领货卡*/
  bindGiftType: number
  /**自提入口*/
  pickSource: number
  /**是否重置为最近自提*/
  resetNearestSite?: boolean

  cartType: number

  newInvoiceContent?: boolean

  preSalePaymentTypeInOptional: number

  isResetGsd?: boolean

  selfPickFlag: number

  orderNeedPay?: string

  tags?: Map<string, number>
  /**businessMark*/
  businessMark?: Map<string, string>

  needIdentityInfo?: boolean

  userLevelYhd: number

  isNewVersion: number

  appVersion?: string

  eid?: string

  userAgent?: string

  isNeedMsgCheck: number

  flowTypeMap?: Map<string, boolean>
  /**otc进主流程*/
  otcMerge?: boolean
  /**订单额外map(当前处理优惠券接口开普勒渠道价appid=businessOrigin和orgType=5字段)*/
  orderExtMapParam?: Map<string, string>
  /**是否包含京东国际的商品*/
  overseaMerge?: boolean
  /**是否全球购流程*/
  international?: boolean
  /**全球售多物流 是否需要全球售多物流数据，app、小程序在用*/
  supportTransport?: boolean
  /**全球售多物流 版本控制*/
  transportCtrl?: boolean

  useRedPackage?: boolean
  /**优惠券反勾选*/
  reverseSelect?: boolean
  /**支持到家流程*/
  supportJDCombine?: boolean
  /**到家需要剔除的门店ID*/
  invalidStoreIds?: string[]
  /**支持的门店ID*/
  validStoreIds?: string[]
  /**是否支持电子增值税发票*/
  needEleVatInvoice?: boolean
  /**获取包裹化数据标识*/
  needBundle?: boolean
  /**预售尾款流程*/
  endPaymentPresaleSign?: boolean
  /**预售现货买流程标记*/
  presaleStockSign?: boolean
  /**黄金保险柜流程*/
  goldSafeBox?: boolean
  /**众筹流程*/
  crowdFunding?: boolean
  /**配送到店流程*/
  supportDeliveryToStore?: boolean
  /**经纬度*/
  orderLng?: string
  /**经纬度*/
  orderLat?: string
  /**是否提单校验药品疫情登记*/
  checkMedicineEpidemicRegistration?: boolean
  /**全球购预售尾款进主流程*/
  globalPreSaleEndPayment?: boolean
  /**一键购买后获取的推荐品skuid集合,soa是从transferJson拿的,首次进结算并行调用可能获取不到*/
  recommendSkuIds?: string[]
  /**是否选中包装礼盒*/
  giftBoxSelected?: boolean
  /**是否刷新结算页，true 刷新结算页 false 第一次进入结算页；supportJDPrePay 为true时生效*/
  refreshPage?: boolean
  /**是否支持京东大支付，true 支持， false 不支持。 优先级最高！*/
  supportJDPrePay?: boolean
  /**是否需要小时购包装费*/
  needPackagingFeeForHourBuy?: boolean
  /**业务标识appId（预发：betajdmallapp；线上：jdmallapp；以数科给结算分配为准）	新增*/
  prePayAppId?: string
  /**平台编码（固定值“SDK_SHOPPING”，以数科给结算分配为准）	新增*/
  platformCode?: string
  /**来源类型（结算前置传“2”-移动端，以数科给结算分配为准）*/
  sourceType?: string
  /**是否需要获取前置支付工具列表信息, true需要， false不需要
      获取时效接口新增
      v9.0.0*/
  needPrePayInfo?: boolean
  /**收银中台来源类型（主站收银台：jdapp，结算前置：jdappPre）*/
  cashierSourceType?: string
  /**支付唤起来源（商城APP结算前台透传 “mallSettlePre”）,supportJDPrePay 为true时生效*/
  bizScene?: string
  /**0	白条新、老数据
      1	引导开启京东快付
      2	不下发*/
  baitiaoCutFlag?: string
  /**0	一键付银行卡数据+引导开启京东快付
      1	引导开启京东快付
      2 银行卡前置
      3 新绑卡*/
  bankCardCutFlag?: string
  /**1 命中新绑卡，0未命中*/
  bindBankCardCutFlag?: string
  /**收银台完成页Url*/
  successUrl?: string
  /**scf需要剔除的店铺ID*/
  invalidVenderIds?: string[]
  /**scf支持的店铺ID*/
  validVenderIds?: string[]
  /**微信渠道的 netBuySourceType*/
  netBuySourceType?: number
  /**业务标识*/
  bizTags?: string[]
  /**透传给结算中台Tags*/
  addIntoMainFlowTags?: string[]
  /**透传给结算中台tradeEntranceList*/
  tradeEntranceList?: string[]
  /**透传给结算中台miniCartParamMap*/
  miniCartParamMap?: any
  /**订单中台tags*/
  orderCenterTags?: number[]
  /**透传给结算中台tradeCollectionParam域*/
  functionTags?: string[]
  /**以旧换新域tags*/
  tradeInTags?: string[]
  /**是否支持百亿补贴商品下单， 默认不支持，app-soa的场景是商详立即购买进结算支持，其余不支持，其余模块按需传递*/
  supportTenBillion?: boolean
  /**是否包含百亿补贴商品下单、透传给中台用于判断提单前后百亿补贴商品促销是否消失*/
  hasTenBillion?: boolean
  /**是否包含百亿补贴商品下单、并且用户点了继续下单，同意百亿补贴促销消失，价格升高后继续提单*/
  agreeContinueSubmit?: boolean
  /**以旧换新券的相关操作标识*/
  tradeInCouponOperation?: boolean
  /**是否支持一键付，true 支持， false 不支持。 优先级最高！*/
  supportOneKeyPay?: boolean
  /**是否支持快付四期，v10.2.0*/
  supportFastPay?: boolean
  /**是否支持快付四期合规整改v10.2.0
      1：支持 、0：不支持*/
  supportShowOneClickPayAndWhiteBar?: string
  /**一键付类型，银行卡、白条还是普通在线支付，其实银行卡和白条都是在线支付*/
  onlinePayType?: string
  /**透传给数科的requeireUUID*/
  requireUUID?: string
  /**v9.4.6 是否刚开通一键付，如果这个字段为true，中台会默认选中一键付*/
  openOkp?: string
  /**v9.4.6
      新老数据标识 返回数据是展示一键付还是展示京东快付 1：一键付 2：京东快付*/
  oneClickPayType?: string
  /**加车流程标识
      接口文档https://cf.jd.com/pages/viewpage.action?pageId=345385961
      替换非组件化mainFlowId，如立即购买流程tokenKey=8*/
  tokenKey?: string
  /**加车流程标识
      接口文档https://cf.jd.com/pages/viewpage.action?pageId=345385961
      替换非组件化mainFlowId，如立即购买流程tokenValue=nowbuy*/
  tokenValue?: string
  /**V954 处方药 进主流程标识*/
  prescriptionMerge?: boolean
  /**V10.2.6 处方药 新规*/
  prescriptionNewRule?: boolean
  /**医药一盘货*/
  supportDrugShip?: boolean
  /**OTC进京东自营药房*/
  supportOtcSelfVender?: boolean
  /**10.0.2 是否需要券包*/
  needCouponPackage?: boolean
  /**10.4.4 是否支持多物流标准达*/
  supportTransportPromise?: boolean
  /**10.4.4 是否支持上门服务*/
  supportServicePlus?: boolean
  /**10.5.0是否勾选鉴定协议*/
  authenticateServiceSelected?: boolean
  /**给结算中台让新增传参tradeEntranceList的判断条件*/
  clientSourceType: number
  /**结算页面类型:21：A版短结算，22：B版短结算，23：B版长结算*/
  balancePageType?: string
  /**国补与履约时效互斥skuUUID map*/
  govSubsidyAndPromiseRepelSkuUUIDMap?: Map<string, string>
  /**信用试  cds决策结果*/
  cdsActiveResult?: string
  /**信用试  流程标识*/
  creditUseFlag?: boolean
  /**updateTags,  重置京品试为普通商品标识*/
  updateTags?: string[]
  /**网关透传风控标识*/
  userRiskSignMap?: Map<string, string>
  /**是否不需要下发orderInfo(对应orderXml）和 cartInfo（对应cartXml) 10.3.2版本优化xml解析新增*/
  unNeedOrderOrCartXml?: boolean
  /**极速版，小程序，接入券包，新增给中台的入参, 渠道标识*/
  orgType?: string
  /**极速版，小程序，接入券包，新增给中台的入参*/
  appId?: string
  /**口岸自提点距离*/
  portRadius?: string
  /**京喜自营 拆分店铺mark 版本控制标识*/
  supportJxzy?: boolean
  /**V10.4.4 宠物处方药*/
  petPrescription?: boolean
  /**是否是一单多地址*/
  oneOrderMultipleAddress?: boolean
  /**是否支持医保卡*/
  supportMedicalInsurance?: boolean
  /**是否支持plus预售*/
  supportPlusPresale?: boolean
  /**是否支持以旧换新拉通*/
  supportAllOldToNew?: boolean
  /**是否需要支持以旧换新业务*/
  needTradeInFlag?: boolean
  /**是否支持plus超级补贴改递进叠加*/
  usePlusCoupon?: boolean
  /**是否需要小时购包装费二期*/
  packingFeePhaseTwo?: boolean
  /**是否需要超市卡*/
  supportSuperMarketCard?: boolean
  /**搭售商品是否删除*/
  recommendRemove?: boolean
  /**移除礼品盒子商品*/
  giftBoxRemove?: boolean
  /**新的商品结构标识*/
  newProductVOFlag?: boolean
  /**轻餐饮保存信息*/
  tableWareInfo?: Map<string, string>[]

  trialProductVersionFlag?: boolean
  /**是否支持先享后付楼层*/
  xxhfCutFlag?: boolean
  /**支持活动类型 - 由 v12.0.10 礼品卡买实物赠E卡需求引入*/
  incentiveRequestTypes?: number[]

  sdkToken?: string

  baitiaoQuickCutFlag?: string
  /**是否是整车交易流程*/
  wholeCar?: boolean
  /**多地址复制车tokenKey*/
  copyTokenKey?: string
  /**多地址复制车tokenValue*/
  copyTokenValue?: string
  /**是否开启用户隐私保护*/
  supportUserPrivacy?: boolean
  /**用户隐私保护勾选/反勾选行为使用。默认不勾选
      1、null or false，反勾选
      2、true，勾选*/
  userPrivacyChecked?: boolean
  /**全球售-台湾韩国实名认证 是否需要给中台传身份证号，保存身份证号、提单接口需要传，在融合服务中赋值*/
  needJicc?: boolean
  /**全球售-台湾韩国实名认证 地址id*/
  jiccAddressId?: number
  /**全球售-台湾韩国实名认证 身份证号*/
  jiccIdentityId?: string
  /**是否计算波次运费*/
  supportBatchFreight?: boolean
  /**GPS四级地址对应的经度*/
  locationLongitude?: string
  /**GPS四级地址对应的纬度*/
  locationLatitude?: string
  /**协议签纸标识*/
  frontProtocolSlice?: string
  /**支付工具的扩展字段*/
  payToolExtInfo?: Map<string, string>
  /**当前选中微信支付工具id(1.微信支付 2.云闪付)*/
  wxPayToolsId?: string
  /**无感换货 签约协议号*/
  agreementNo?: string
  /**是否支持推荐日历波次*/
  supportRecommendCalendar?: boolean
  /**购物车UUIDs*/
  cartItemUUIDs?: string[]
  /**立即购买（商详进入结算）*/
  immediatelyBuy?: boolean
  /**灰度控制全球购多物流配送*/
  overseasTransport?: boolean
  /**是否支持省省卡*/
  needPackageCoupon?: boolean
  /**福粒虚拟资产*/
  welfareCard?: boolean
  /**包装服务*/
  packingFeeService?: boolean
  /**仓网变革二期*/
  cwbgEnable?: boolean
  /**自营入仓中小件履约日历标识开关*/
  supportCalendarExtendDaySwitch?: boolean
  /**pop强制包邮*/
  popForceFree?: boolean
  /**京豆开关*/
  jdBean?: boolean
  /**包邮二期支持平台供给关系*/
  platformSupply?: boolean
  /**是否支持礼金*/
  giftMoney?: boolean
  /**跨店铺满减*/
  supportStoreReduction?: boolean
  /**处方药开关*/
  prescription?: boolean
  /**PLUS会员补贴*/
  plusMember?: boolean
  /**京豆新规则标识*/
  checkJdNewRule?: boolean
  /**京豆规则类型*/
  selectedJdbeanType?: string
  /**国补时效移除卡单list*/
  govSubsidyRemoveSkuUuidList?: string[]
  /**正品鉴定*/
  zpjdSwitch?: boolean
  /**pop强制包邮*/
  c2mSwitch?: boolean
  /**配件商品*/
  partsItem?: boolean
  /**发票升级*/
  invoiceUpgrade?: boolean
  /**国补时效开关*/
  govSubsidyDateSwitch?: boolean
  /**国补促销立减开关*/
  stateSubsidiesSwitch?: boolean
  /**PLUS会籍SKU标准化开关*/
  plusStandardBuildSwitch?: boolean
  /**purchaseSkuIdList ?*/
  purchaseSkuIdList?: string[]
  /**进车场景开关*/
  sourceSwitch?: boolean
  /**云闪付功能开关*/
  cloudPaySwitch?: boolean
  /**新加坡税费 灰度开关*/
  gstTaxAmount?: boolean
  /**企业采购通过采购清单下单时，source=qyg,需要sendepay第500位打标
      PC用：bybt、zfbt、qyg、dqg、common*/
  source?: string
  /**白条分期数
      fq=1 不分期，fq=3 分3期*/
  fq?: string
  /**渠道卡单降级灰度白名单开关*/
  sourceBlock?: boolean
}
