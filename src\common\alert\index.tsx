import './index.scss'
import type { FC } from 'react'
import { useState } from 'react'
import classnames from 'classnames'

type Props = {
  className?: string
  showIcon?: boolean
  closable?: boolean
  onClose?: () => void
  children?: React.ReactNode
}

const Alert: FC<Props> = ({ showIcon = true, closable = true, className, onClose, children }) => {
  const [isVisible, setIsVisible] = useState(true)
  const handleClick = () => {
    setIsVisible(false)
    onClose?.()
  }

  return (
    isVisible && (
      <div className={classnames('alert', className)}>
        {showIcon && <div className="alert-icon" />}
        <div className="alert-content">{children}</div>
        {closable && <div className="alert-close" onClick={handleClick} />}
      </div>
    )
  )
}

export default Alert
