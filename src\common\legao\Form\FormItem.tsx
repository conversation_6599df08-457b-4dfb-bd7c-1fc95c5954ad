import React from 'react'
import AsyncValidator from '../libs/async-validator'
import { Component, PropTypes, Transition } from '../libs'
import FormContext from '@app/common/legao/Context'

type State = {
  error: string
  valid: boolean
  validating: boolean
}

export default class FormItem extends Component {
  state: State

  static contextType = FormContext // 使用静态contextType

  constructor(props: object) {
    super(props)

    this.state = {
      error: '',
      valid: false,
      validating: false,
    }
  }

  componentDidMount() {
    const { prop } = this.props

    if (prop) {
      typeof this.parent().addField === 'function' && this.parent().addField(this) // 通过context访问Form实例
      this.initialValue = this.getInitialValue()
    }
  }

  componentWillUnmount(): void {
    typeof this.parent().removeField === 'function' && this.parent().removeField(this)
  }

  parent(): object {
    return this.context.component || {}
  }

  isRequired(): boolean {
    const rules = this.getRules()
    let isRequired = false

    if (rules && rules.length) {
      rules.every((rule) => {
        if (rule.required) {
          isRequired = true
          return false
        }
        return true
      })
    }

    return isRequired
  }

  onFieldBlur(): void {
    this.validate('blur')
  }

  onFieldChange(): void {
    if (this.validateDisabled) {
      this.validateDisabled = false
      return
    }

    setTimeout(() => {
      this.validate('change')
    })
  }

  validate(trigger: string, cb?: Function): boolean | void {
    const rules = this.getFilteredRule(trigger)
    if (!rules || rules.length === 0) {
      if (cb instanceof Function) {
        cb()
      }
      return true
    }

    this.setState({ validating: true })

    const descriptor = { [this.props.prop]: rules }
    const validator = new AsyncValidator(descriptor)
    const model = { [this.props.prop]: this.fieldValue() }

    validator.validate(model, { firstFields: true }, (errors) => {
      this.setState(
        {
          error: errors ? errors[0].message : '',
          validating: false,
          valid: !errors,
        },
        () => {
          if (cb instanceof Function) {
            cb(errors)
          }
        },
      )
    })
  }

  getInitialValue(): string | void {
    const value = this.parent().props?.model?.[this.props.prop]
    if (value === undefined) {
      return value
    } else {
      return JSON.parse(JSON.stringify(value))
    }
  }

  resetField(): void {
    this.setState({ valid: true, error: '' })
    const value = this.fieldValue()
    const { onChange } = this.props?.children?.props || {}
    if (Array.isArray(value) && value.length > 0) {
      this.validateDisabled = true
      this.parent().props.model[this.props.prop] = []
      typeof onChange === 'function' && onChange(this.initialValue)
    } else if (value) {
      this.validateDisabled = true
      this.parent().props.model[this.props.prop] = this.initialValue
      typeof onChange === 'function' && onChange(this.initialValue)
    }
  }

  getRules(): Array<any> {
    let formRules = this.parent().props.rules
    const selfRuels = this.props.rules

    formRules = formRules ? formRules[this.props.prop] : []
    return [].concat(selfRuels || formRules || [])
  }

  getFilteredRule(trigger: string): Array<any> {
    const rules = this.getRules()
    return rules
      .filter((rule) => {
        if (!rule.trigger || trigger === '') return true
        if (Array.isArray(rule.trigger)) {
          return rule.trigger.indexOf(trigger) > -1
        } else {
          return rule.trigger === trigger
        }
      })
      .map((rule) => Object.assign({}, rule))
  }

  labelStyle(): { width?: number | string } {
    const ret = {}

    if (this.parent().props.labelPosition === 'top') return ret

    const labelWidth = this.props.labelWidth || this.parent().props.labelWidth

    if (labelWidth) {
      ret.width = parseInt(labelWidth)
    }

    return ret
  }

  contentStyle(): { marginLeft?: number | string } {
    const ret = {}

    if (this.parent().props?.labelPosition === 'top' || this.parent().props?.inline) return ret

    const labelWidth = this.props.labelWidth || this.parent().props.labelWidth

    if (labelWidth) {
      ret.marginLeft = parseInt(labelWidth)
    }

    return ret
  }
  // 原方法
  fieldValue2(): mixed {
    const model = this.parent().props.model
    if (!model || !this.props.prop) return
    const [key, nestedKey] = this.props.prop.split(':')
    return nestedKey ? model[key]?.[nestedKey] : model[this.props.prop]
  }

  // 支持多层嵌套
  fieldValue1(): mixed {
    const model = this.parent().props.model
    if (!model || !this.props.prop) return

    // 支持多层嵌套
    const parts = this.props.prop.split(':')
    if (parts.length === 1) {
      return model[this.props.prop]
    }

    // 递归获取嵌套值
    let value = model
    for (const part of parts) {
      if (!value) return undefined
      value = value[part]
    }
    return value
  }
  // 支持多层嵌套 - 优化
  fieldValue(): mixed {
    const model = this.parent()?.props.model
    const prop = this.props.prop

    // 初始输入验证
    if (!model || !prop) {
      return undefined
    }

    // 创建一个安全的函数来处理单层属性获取
    const getSingleLevelValue = (obj: Object, key: string): mixed => {
      return obj[key] !== undefined ? obj[key] : undefined
    }

    // 创建一个递归函数来处理嵌套属性
    const getNestedValue = (currentObj: Object, keys: string[]): mixed => {
      if (keys.length === 0) return undefined

      const currentKey = keys.shift()
      const currentValue = getSingleLevelValue(currentObj, currentKey)

      if (keys.length === 0 || currentValue === undefined) {
        return currentValue
      }

      return getNestedValue(currentValue, keys)
    }

    // 处理多层嵌套情况
    const parts = prop.split(':')

    if (parts.length === 1) {
      return getSingleLevelValue(model, prop)
    }

    return getNestedValue(model, [...parts])
  }

  render(): React.DOM {
    const { error, validating } = this.state
    const { label, required } = this.props

    return (
      <FormContext.Provider value={{ component: this.context.component, form: this }}>
        <div
          style={this.style()}
          className={this.className('el-form-item', {
            'is-error': error !== '',
            'is-validating': validating,
            'is-required': this.isRequired() || required,
          })}
          onBlur={this.onFieldBlur.bind(this)}
          onChange={this.onFieldChange.bind(this)}
        >
          {label && (
            <label className="el-form-item__label" style={this.labelStyle()}>
              {typeof label === 'string' ? label + this.parent().props.labelSuffix : label}
            </label>
          )}
          <div className="el-form-item__content" style={this.contentStyle()}>
            {this.props.children}
            <Transition name="el-zoom-in-top">{error && <div className="el-form-item__error">{error}</div>}</Transition>
          </div>
        </div>
      </FormContext.Provider>
    )
  }
}

FormItem.propTypes = {
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  labelWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  prop: PropTypes.string,
  required: PropTypes.bool,
  rules: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
}
