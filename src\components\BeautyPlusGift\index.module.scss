.beauty-plus-gift {
  margin: 16px 0;
  line-height: 1;
  box-sizing: border-box;
  padding: 20px 16px 20px 16px;
  background: #fff;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  border: 1px solid #fff;
  box-sizing: border-box;

  &::after {
    content: ' ';
    position: absolute;
    width: 200px;
    height: 68px;
    left: 0;
    top: 0;
    background: url(https://img12.360buyimg.com/imagetools/jfs/t1/299508/18/13242/25456/68415a9bF32df9cf5/e6a7b6c87f782415.png) left top /
      200px 68px no-repeat;
    z-index: 1;
  }

  :global {
    .flex {
      display: flex;
    }

    .flex-column-center {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .flex-column-center2 {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .flex-end {
      display: flex;
      align-items: flex-end;
    }

    .flex-center {
      display: flex;
      align-items: center;
    }

    .flex-between {
      display: flex;
      justify-content: space-between;
    }

    .flex-around {
      display: flex;
      justify-content: space-around;
    }

    .flex-content-center {
      display: flex;
      justify-content: center;
    }

    .flex-center2 {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .flex-center-between {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .text-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .text-ellipsis-2 {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .flex-1 {
      flex: 1;
    }
  }
}

.list {
  .item {
    margin-bottom: 12px;
  }

  & > div:last-child {
    margin-bottom: 0;
  }
}

.toolbar {
  margin-bottom: 16px;
  z-index: 2;
  position: relative;

  .icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }

  .tip {
    color: #1a1a1a;
    font-size: 16px;
    font-weight: 600;
    flex: 1;
  }
}

.none {
  display: none;
}
