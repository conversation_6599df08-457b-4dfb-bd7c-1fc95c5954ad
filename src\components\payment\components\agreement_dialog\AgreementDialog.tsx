import './index.scss'
import React from 'react'
import Button from '@app/common/button'

export type AgreementDialogProps = {
  title?: string
  description?: string
  content?: React.ReactNode
  children?: React.ReactNode
  okText?: string
  onOk?: (_close?: Function) => void
  autoOkClose?: boolean
  cancelText?: string
  onCancel?: () => void
  _close?: () => void
}

const AgreementDialog: React.FC<AgreementDialogProps> = (props) => {
  const {
    title,
    description,
    content,
    children,
    okText = '同意并继续',
    onOk,
    autoOkClose = true,
    cancelText,
    onCancel,
    _close,
  } = props

  return (
    <div className="agreement-dialog">
      <div className="agreement-dialog-title">
        {title}
        <span className="iconfont close" onClick={_close} />
      </div>
      <div className="agreement-dialog-description">{description}</div>
      <div className="agreement-dialog-content">{children ? children : content}</div>
      <div className="agreement-dialog-footer">
        {cancelText && (
          <Button
            onClick={() => {
              onCancel?.()
              _close?.()
            }}
          >
            {cancelText}
          </Button>
        )}
        {okText && (
          <Button
            variant="primary"
            onClick={() => {
              onOk?.(_close)
              if (autoOkClose) _close?.()
            }}
          >
            {okText}
          </Button>
        )}
      </div>
    </div>
  )
}

export default AgreementDialog
