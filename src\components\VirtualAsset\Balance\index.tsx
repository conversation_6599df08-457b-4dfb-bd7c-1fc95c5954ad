/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-26 18:00:57
 * @LastEditTime: 2025-07-10 14:32:36
 * @LastEditors: ext.wangchao120
 * @Description: 虚拟资产-余额
 * @FilePath: /pc_settlement/src/components/VirtualAsset/Balance/index.tsx
 */

import React, { FC, useEffect } from 'react'
import type { ChangeEvent } from 'react'
import Checkbox from '@app/common/checkbox'
import styles from './index.module.scss'
import { api_changeBalance } from '@app/services/api'
import useUpdateMasterData from '@app/hooks/useUpdateMasterData'
import useMasterData from '@app/hooks/useMasterData'
import showToast from '@app/components/payment'
import { reportClick, reportExpose } from '@app/utils/event_tracking'
import type { VirtualPropertyVO } from '@app/typings/master_api_response'
import getImgUrl from '@app/utils/images'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import type { BalanceUser } from '@app/typings/master_api_response'
import { CommonConfirmDialogs } from '@app/components/payment/components/confirm_dialog'

interface Props {
  tabKey: string
}
const Balance: FC<Props> = ({ tabKey }) => {
  // 主数据
  const masterData = useMasterData()?.body ?? {}
  const virtualPropertyVO = (masterData?.virtualPropertyVO as VirtualPropertyVO) ?? {}
  const balanceUser = (masterData?.balanceUser as BalanceUser) ?? {}
  const updateMasterData = useUpdateMasterData()
  const onChange = (e: ChangeEvent<HTMLInputElement>) => {
    // 是否实名
    if (!balanceUser?.realName) {
      CommonConfirmDialogs?.confirmRealName()
      return
    }
    // 未设置密码
    if (!balanceUser?.fundsPwd) {
      CommonConfirmDialogs?.confirmPaymentPassword()
      return
    }
    const params = {
      use: e?.target?.checked,
    }
    api_changeBalance(params)
      .then((res) => {
        // console.log('res', res)
        if (res?.code == '0') {
          // 更新融合数据
          updateMasterData(undefined, 'virtualAsset')
          // 上报点击
          reportClick('virtualasset_balanceClick', {
            tgstatus: e?.target?.checked ? '1' : '2', // 1:勾选，2:取消勾选
          })
        } else {
          showToast({ title: res?.message || '请稍后再试' })
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'balance_selectRedPacket_pc',
            error_type_txt: 'balance_selectRedPacket_pc接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
  }
  // 曝光
  useEffect(() => {
    if (tabKey !== 'balance') return
    reportExpose('virtualasset_balanceEXPO', {
      tatal_balance: virtualPropertyVO?.userRemainderBanlance,
      // TODO 不可用的skuid，服务端没下发数据
      // skuid: []
    })
  }, [tabKey])

  return (
    <>
      <div className={styles.balance}>
        <Checkbox onChange={onChange} checked={virtualPropertyVO?.useBalance} />
        <div className={styles.text}>
          使用余额（账户当前余额：&yen; {virtualPropertyVO?.userRemainderBanlance || 0}, 本次可用：&yen;
          {virtualPropertyVO?.canUseBalance || 0}）
        </div>
        {virtualPropertyVO?.unSupportSkuImgList && virtualPropertyVO?.unSupportSkuImgList.length > 0 ? (
          <div className={styles.unSupportSkuImgList}>
            <div className={styles.title}>以下商品不可使用余额</div>
            <div className={styles.flex}>
              {virtualPropertyVO?.unSupportSkuImgList &&
                virtualPropertyVO?.unSupportSkuImgList.map((imgUrl: string, index: number) => {
                  return <img src={getImgUrl(imgUrl)} key={index}></img>
                })}
            </div>
          </div>
        ) : null}
      </div>
    </>
  )
}
export default Balance
