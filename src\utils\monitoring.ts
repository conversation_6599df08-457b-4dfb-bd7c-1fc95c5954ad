/**
 * @file: monitoring.ts
 * @description: 烛龙监控上报
 */
import { safeStringify } from '@app/helpers'

/**
 * code 用于烛龙维度展示
 *
 */
export enum monitorCode {
  /** 收货地址 */
  Address = 800,
  /** 赠品 */
  Gift = 801,
  /** 订单信息 */
  Order = 802,
  /** 顺手买 */
  HandBuy = 803,
  /** 发票 */
  Invoice = 804,
  /** 虚拟资产 */
  VirtualAsset = 805,
  /** 付款详情 */
  PaymentDetail = 806,
  // 配送信息
  Delivery = 807,

  /** 其他 */
  Other = 900,
}

/**
 * name 对应 烛龙 监控标识
 */
export enum monitorName {
  Settlement = 'settlement',
}

interface MonitorParams {
  /** 上报标识，对应烛龙 埋点标识 */
  name: monitorName
  /** 上报码，用于维度展示 */
  code: monitorCode
  /** 自定义上报字段 */
  msg: Record<string, any>
  /** 其它扩展字段 */
  extra?: Record<string, any>
}

/**
 * @param {MonitorParams} values
 * @param {monitorName} values.name - 上报标识，monitorName 对应烛龙 埋点标识
 * @param {monitorCode} values.code - 上报码，monitorCode 用于维度展示
 * @param {Record<string, any>} valuse.msg - 自定义上报字段
 * @param {Record<string, any>} values.extra - 其它扩展字段
 *
 * @returns
 */
const monitoring: (values: MonitorParams) => void = (values) => {
  try {
    const { name, code, msg, extra } = values
    const type = extra?.type
    // 有 type 走 sendEvent
    if (type) {
      window?.dra?.sendEvent({
        type,
        payload: values,
      })
      return
    }

    // 否则一律走 customEvent
    window?.dra?.sendCustomEvent({
      name,
      metrics: {
        // 不报这个报不上
        error_code: code,
      },
      context: {
        // msg 解构上报，用于各字段 通过该字段维度 可视化展示
        ...msg,
        code,
        // safeStringify msg 保存 msg 的一个快照
        msg: safeStringify(msg),
      },
    })
  } catch (e) {
    console.error('上报异常：', e)
  }
}

export default monitoring
