.common-input {
  position: relative;

  .common-input__inner {
    color: #1A1A1A;
    font-size: 14px;
    font-weight: 400;
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    display: inline-block;
    font-size: inherit;
    height: 32px;
    line-height: 32px;
    outline: none;
    padding: 0 14px;
    transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
    width: 100%;
    &::placeholder {
      color: #C2C4CC;
      font-size: 14px;
      font-weight: 400;
    }
  }

  &.common-input--small {
    font-size: 13px;

    .common-input__inner {
      height: 32px;
      line-height: 32px;
    }
  }

  &.common-input--mini {
    font-size: 12px;

    .common-input__inner {
      height: 28px;
      line-height: 28px;
    }
  }

  &.common-input--large {
    font-size: 14px;
    .common-input__inner {
      height: 36px;
      line-height: 36px;
    }
  }

  &.common-input--prefix {
    .common-input__inner {
      padding-left: 25px;
    }

    .common-input__prefix {
      line-height: 0;
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  &.input-disabled input{
    cursor: not-allowed;
  }
}