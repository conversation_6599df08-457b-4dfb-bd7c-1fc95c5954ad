/**
 * @file: initDeliveryData.ts
 * @description: 初始化配送数据，用于初始化配送数据
 */
import {
  ApiGetBundleShipmentListResponse,
  CalendarListItem,
  ComShipmentType,
  PromiseListItem,
  ShipmentInfo,
} from '@app/typings/api_getBundleShipmentList_resp'
import { DeliveryWrap } from '../types'
import { BaseItem, List as HeaderList, ListItem as TopHeaderBaseItem } from '../ui/header'
import { DeliveryComponentType, DeliveryEnum, DeliveryTypeInfo } from './types'
import { UiAddrSelector, UiTimePicker, UiTimeSelector } from '../ui/TimeSelector/types'

/**
 * 国内配送数据初始化类
 */
export class DeliveryDataIniter {
  public initState: DeliveryWrap.InitState
  public otherData = {
    transportCtrl: false,
  }
  public isInstall: boolean = false
  public apiData: ApiGetBundleShipmentListResponse

  private shipmentType_promise_map: Map<ComShipmentType, PromiseListItem[]> = new Map<ComShipmentType, PromiseListItem[]>()

  public getDeliveryInfo(): object {
    return this.apiData.body.deliveryInfo
  }

  public getErrorMessage(): string | void {
    const resp = this.apiData
    if (resp.code !== '0') {
      return (resp as any).echo || (resp as any).message || (resp as any).msg || '获取数据失败'
    }
  }

  /**
   * @description: 初始化 shipmentType_promise_map 配送方式与时效列表的映射关系
   */
  private init_shipmentType_promise_map(): void {
    const resp = this.apiData
    if (resp.code !== '0') {
      return
    }

    if (!resp.body.deliveryInfo?.promiseList) {
      return
    }

    if (!resp.body.comShipmentTypeList || resp.body.comShipmentTypeList.length === 0) {
      return
    }
    resp.body.comShipmentTypeList.forEach((item) => {
      this.shipmentType_promise_map.set(
        item,
        resp.body.deliveryInfo?.promiseList.filter((promiseItem) => promiseItem.shipmentType === item.shipmentType) || [],
      )
    })
  }

  constructor(initState: DeliveryWrap.InitState, apiData: ApiGetBundleShipmentListResponse, isInstall: boolean = false) {
    this.initState = initState
    this.apiData = apiData
    this.isInstall = isInstall
    this.init_shipmentType_promise_map()
    console.log(this.shipmentType_promise_map, 'this.shipmentType_promise_map')
  }

  /**
   * @description: 检查时效是否为安装
   * @param promise 时效数据
   * @returns 是否为安装
   */
  private checkIsInstall(promise: PromiseListItem): boolean {
    return [
      DeliveryEnum.DeliveryFloorType.BIG_ITEM_INSTALL,
      DeliveryEnum.DeliveryFloorType.SMALL_ITEM_INSTALL,
      DeliveryEnum.DeliveryFloorType.ZSD_BIG_ITEM_INSTALL,
    ].includes(promise.deliveryFloorType as DeliveryEnum.DeliveryFloorType)
  }

  /**
   * @description: 配送方式标题列表(用于弹窗的头部组件)
   * @returns 配送方式标题列表
   */
  public getHeaderList(): HeaderList<ComShipmentType, PromiseListItem> {
    const getHeaderItem = (promise: PromiseListItem) => {
      const getPromiseTypeName = (promise: PromiseListItem) => {
        if (promise.promiseType === 0) {
          return promise.deliveryFloorTypeName || promise.promiseTypeName
        }
        if (promise.promiseTypeName) {
          return promise.promiseTypeName
        }
        if (promise.promiseType === 4) {
          return '标准达'
        }
        if (promise.promiseType === 6) {
          return '京准达'
        }
        return promise.deliveryFloorTypeName
      }
      const item: BaseItem<PromiseListItem> = {
        text: getPromiseTypeName(promise),
        isSelected: promise.selected,
        targetValue: promise,
      }
      return item
    }
    const headerList: HeaderList<ComShipmentType, PromiseListItem> = []

    //是否需要二级标题
    const needSonHeaderItem = (promiseList: PromiseListItem[], shipment: ComShipmentType): boolean => {
      if (promiseList.length === 1) {
        return false
      }
      return true
    }
    this.shipmentType_promise_map.forEach((promiseList, shipmentType) => {
      //区分安装和配送
      const promiseList2 = promiseList.filter((promise) => this.checkIsInstall(promise) === this.isInstall)
      //过滤空的数据
      if (promiseList2.length === 0) {
        return
      }
      const topItem: TopHeaderBaseItem<ComShipmentType, PromiseListItem> = {
        text: this.isInstall && this.shipmentType_promise_map.size === 1 ? '安装时间' : shipmentType.shipmentTypeName,
        isSelected: shipmentType.selected,
        targetValue: shipmentType,
        sonList: needSonHeaderItem(promiseList2, shipmentType) ? promiseList2.map((promise) => getHeaderItem(promise)) : [],
      }
      headerList.push(topItem)
    })
    if (this.isInstall) {
      headerList.forEach((item) => {
        if (item.isSelected && item.sonList.length > 0) {
          if (!item.sonList.find((sonItem) => sonItem.isSelected)) {
            item.sonList[0].isSelected = true
          }
        }
      })
    }
    return headerList
  }

  /**
   * 根据配送方式获取下面的时效列表
   * @param shipmentType 配送方式
   * @returns 时效列表
   */
  public getPromiseByShipmentType(shipmentType: ComShipmentType): PromiseListItem[] {
    return this.shipmentType_promise_map.get(shipmentType)?.filter((p) => this.checkIsInstall(p) === this.isInstall) || []
  }

  /**
   * 根据时效项 获取对应的时效, (配送对应安装时效,或者安装对应的配送时效)
   * @param promise 配送或者安装时效
   * @returns 时效
   */
  public getTargetPromise(promise: PromiseListItem): PromiseListItem | null {
    let retPromise: PromiseListItem | null = null
    this.shipmentType_promise_map.forEach((promiseList) => {
      if (promiseList.includes(promise)) {
        promiseList.forEach((p) => {
          if (p.promiseType === promise.promiseType && this.checkIsInstall(p) !== this.isInstall) {
            const bigSet = [DeliveryEnum.DeliveryFloorType.BIG_ITEM, DeliveryEnum.DeliveryFloorType.BIG_ITEM_INSTALL]
            const smallSet = [DeliveryEnum.DeliveryFloorType.SMALL_ITEM, DeliveryEnum.DeliveryFloorType.SMALL_ITEM_INSTALL]
            if (
              bigSet.includes(p.deliveryFloorType as DeliveryEnum.DeliveryFloorType) &&
              bigSet.includes(promise.deliveryFloorType as DeliveryEnum.DeliveryFloorType)
            ) {
              retPromise = p
            } else if (
              smallSet.includes(p.deliveryFloorType as DeliveryEnum.DeliveryFloorType) &&
              smallSet.includes(promise.deliveryFloorType as DeliveryEnum.DeliveryFloorType)
            ) {
              retPromise = p
            } else if (
              !bigSet.includes(promise.deliveryFloorType as DeliveryEnum.DeliveryFloorType) &&
              !smallSet.includes(promise.deliveryFloorType as DeliveryEnum.DeliveryFloorType)
            ) {
              console.log('大件安装 意外数据处理情况 ! getTargetPromise error')
              // 这种情况应该不存在预防万一 给个默认值
              retPromise = p
            }
          }
        })
      }
    })
    return retPromise
  }

  /**
   * 根据时效获取其对应应该使用的子组件类型
   */
  public getDeliveryTypeInfo(promise: PromiseListItem): DeliveryTypeInfo | null {
    const getResult = (componentType: DeliveryComponentType) => {
      return {
        promiseType: promise.promiseType,
        deliveryFloorType: promise.deliveryFloorType,
        shipmentType: promise.shipmentType,
        componentType: componentType,
      }
    }

    if (this.checkIsInstall(promise)) {
      return getResult('install')
    }

    const deliveryFloorTypes: string[] = [
      DeliveryEnum.DeliveryFloorType.SMALL_ITEM,
      DeliveryEnum.DeliveryFloorType.BIG_ITEM,
      DeliveryEnum.DeliveryFloorType.PICK_ITEM,
      DeliveryEnum.DeliveryFloorType.DELIVERY_ITEM,
      DeliveryEnum.DeliveryFloorType.COMBINE_ITEM,
      DeliveryEnum.DeliveryFloorType.CONSOLIDATOR_ITEM,
      DeliveryEnum.DeliveryFloorType.ZSD_BIG_ITEM,
    ]

    if (!deliveryFloorTypes.includes(promise.deliveryFloorType)) {
      //不是配送的数据过滤掉
      return null
    }

    if (
      [DeliveryEnum.DeliveryFloorType.PICK_ITEM, DeliveryEnum.DeliveryFloorType.DELIVERY_ITEM].includes(
        promise.deliveryFloorType as DeliveryEnum.DeliveryFloorType,
      )
    ) {
      if (promise.shipmentType === DeliveryEnum.ShipmentTypeEnum.SMZT) {
        return getResult('smzt')
      }
      if (promise.deliveryFloorType === DeliveryEnum.DeliveryFloorType.DELIVERY_ITEM) {
        return getResult('mdzt')
      }
      // 进行地址选择器的处理
      if (promise.pickAddress && promise.pickName) {
        return getResult('address')
      }
    }

    if (promise.promiseType === DeliveryEnum.PromiseEnum.JSD) {
      // 进行极速达的处理
      return getResult('simple')
    }

    if ([DeliveryEnum.PromiseEnum.BZD, DeliveryEnum.PromiseEnum.JZD, DeliveryEnum.PromiseEnum.GSD].includes(promise.promiseType)) {
      if (!promise?.calendarList || promise.calendarList.length === 0) {
        if (promise.promiseMsg) {
          return getResult('simple')
        }
        return null
      }
      //如果有timeList 就使用常规的, 如果没有就展示简化版本(比方说大件安装)
      if ('timeList' in promise.calendarList[0] && promise.calendarList[0].timeList?.length !== 0) {
        return getResult('timeList')
      }
      return getResult('timePicker')
    }

    return getResult('simple')
  }
}

/**
 * 处理每个类型的组件 应该获取的组件数据类
 */
export class promiseToComponentData {
  getAddrSelector = (promiseListItem: PromiseListItem, hasAddrInput: boolean = false): UiAddrSelector.InitState<any, any> => {
    return {
      hasAddrInput: hasAddrInput,
      addrList: promiseListItem.pickName
        ? [
            {
              pickAddress: promiseListItem.pickAddress || '',
              pickName: promiseListItem.pickName || '',
              pickPhone: promiseListItem.pickPhone || '',
              targetData: promiseListItem as unknown as string,
              distance: '',
              businessHours: '',
            },
          ]
        : [],
      pickTimeList:
        promiseListItem.pickDateList && promiseListItem.pickName
          ? promiseListItem.pickDateList?.map((time) => {
              return {
                date: time,
                weekName: new Date(time).toLocaleDateString('zh-CN', { weekday: 'long' }),
                targetData: time,
              }
            })
          : undefined,
      inLoading: false,
    }
  }

  getSimple = (promiseListItem: PromiseListItem) => {
    if (!promiseListItem.promiseMsg) {
      return {
        displayedString: '',
      }
    }
    return {
      displayedString: promiseListItem.promiseMsg + (promiseListItem.carriageMoneyMsg || ''),
    }
  }

  getTimeList = (promiseListItem: PromiseListItem): UiTimeSelector.TimeList<any, any> => {
    const timeList: UiTimeSelector.TimeList<any, any> = []
    promiseListItem.calendarList?.forEach((calendar) => {
      const itemSons: {
        textList: {
          style: 'none' | 'gray'
          text: string
        }[]
        targetValue: any
        isSelected: boolean
      }[] = []
      calendar.timeList?.forEach((time) => {
        const itemSon: {
          textList: {
            style: 'none' | 'gray'
            text: string
          }[]
          targetValue: any
          isSelected: boolean
        } = {
          textList: [
            { style: 'none', text: time.timeRange },
            // { style: "gray", text: "需加运费3元" }
          ],
          targetValue: time,
          isSelected: time.selected,
        }
        if (time.freight) {
          itemSon.textList.push({ style: 'gray', text: `需加运费${time.freight}元` })
        }
        itemSons.push(itemSon)
      })

      const item: UiTimeSelector.TimeItem<any, any> = {
        isSelected: calendar.selected,
        // text: calendar.dateStr && calendar.weekString ? calendar.dateStr + `[${calendar.weekString}]` : calendar.displayedString,
        text: calendar.displayedString,
        targetValue: calendar,
        items: itemSons,
      }
      timeList.push(item)
    })
    return timeList
  }

  getSimpleTimeList = (promiseListItem: PromiseListItem): UiTimePicker.ListState<any> => {
    const list: UiTimePicker.ListState<any> = []
    const map = new Map<string, any[]>()
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

    days.forEach(() => {
      // map.set(day, [])
    })

    function getDayOfWeek(dateString: string): string {
      const date = new Date(dateString)
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return ''
      }
      const dayIndex = date.getDay()
      return days[dayIndex]
    }

    function compareDates(dateStr1: string, dateStr2: string): -1 | 0 | 1 | null {
      const date1 = new Date(dateStr1)
      const date2 = new Date(dateStr2)
      const time1 = isNaN(date1.getTime()) ? 0 : date1.getTime()
      const time2 = isNaN(date2.getTime()) ? 0 : date2.getTime()
      if (time1 < time2) {
        return -1
      } else if (time1 > time2) {
        return 1
      } else {
        return 0
      }
    }

    // 获取下一个周几的函数
    function getNextDayOfWeek(currentDay: string): string {
      const index = days.indexOf(currentDay)
      if (index === -1) {
        return ''
      }
      const nextIndex = (index + 1) % days.length
      return days[nextIndex]
    }

    let maxDate = '',
      lastWeekString = '',
      delayCalendarItem: CalendarListItem | null = null
    promiseListItem.calendarList.forEach((calendar) => {
      const weekString = getDayOfWeek(calendar.dateStr)
      if (calendar.timeOffset === -2) {
        delayCalendarItem = calendar
      }
      if (!weekString) {
        return
      }
      if (compareDates(calendar.dateStr, maxDate)) {
        maxDate = calendar.dateStr
        lastWeekString = weekString
      }
      const arr = map.get(weekString) || []
      arr.push(calendar)
      map.set(weekString, arr)
    })
    if (lastWeekString && delayCalendarItem) {
      const nextWeekString = getNextDayOfWeek(lastWeekString)
      if (nextWeekString) {
        const arr = map.get(nextWeekString) || []
        arr.push(delayCalendarItem)
        map.set(nextWeekString, arr)
      }
    }
    map.forEach((arrs, key) => {
      const item: UiTimePicker.ListItem<any> = {
        weekName: key,
        items: arrs.map((calendar) => {
          const date = new Date(calendar.dateStr)
          const month = date.getMonth() + 1 // 月份从 0 开始，需要加 1
          const day = date.getDate()
          const formattedDate = calendar.weekString === '今天' ? '今天' : `${month}.${day}`
          return {
            text: calendar.timeOffset === -2 ? calendar.displayedString : formattedDate,
            type: 'none',
            targetData: calendar,
          }
        }),
      }
      list.push(item)
    })
    return list
  }

  getInstallData = (promiseListItem: PromiseListItem, targetPromise: PromiseListItem): UiTimePicker.ListState<any> => {
    let listState: UiTimePicker.ListState<any> = []
    const timeOffset = targetPromise.timeOffset || targetPromise.promiseDate
    if (!timeOffset && timeOffset !== 0) {
      return listState
    }
    if (['3', '4', '10'].includes(promiseListItem.deliveryFloorType)) {
      if (!promiseListItem.bigItemInstallDateMap) {
        return listState
      }
      // 为避免隐式 any 类型错误，先检查 bigItemInstallDateMap 是否存在，再使用类型断言确保 timeOffset 可以作为索引
      const calendarListMap: (string | number)[] =
        promiseListItem.bigItemInstallDateMap[timeOffset as keyof typeof promiseListItem.bigItemInstallDateMap]
      const calendarList: CalendarListItem[] = []
      promiseListItem.calendarList.forEach((calendar) => {
        if (!calendar.timeOffset) {
          return
        }
        if (calendarListMap.includes(calendar.timeOffset)) {
          calendarList.push(calendar)
        }
      })
      listState = this.getSimpleTimeList({ ...promiseListItem, calendarList: calendarList })
    }
    return listState
  }
}

/**
 * 海外配送数据初始化类
 */
export class DeliveryHWJyDataIniter {
  public otherData = {
    transportCtrl: false,
  }
  public initState: DeliveryWrap.InitState
  public apiData: ApiGetBundleShipmentListResponse
  constructor(initState: DeliveryWrap.InitState, apiData: ApiGetBundleShipmentListResponse) {
    this.initState = initState
    this.apiData = apiData
  }

  public getErrorMessage(): string | void {
    const resp = this.apiData
    if (resp.code !== '0' && !resp.body?.seasShipmentTypeList) {
      return (resp as any).echo || (resp as any).message || (resp as any).msg || '获取数据失败'
    }
  }

  public getHeaderList(): HeaderList<ShipmentInfo, null> {
    const resp = this.apiData
    const headerList: HeaderList<ShipmentInfo, null> = []

    if (resp.body.seasShipmentTypeList) {
      resp.body.seasShipmentTypeList.forEach((item) => {
        const hItem = {
          text: item.shipmentTypeName,
          isSelected: item.selected,
          targetValue: item,
          sonList: [],
        }
        headerList.push(hItem)
      })
    }
    return headerList
  }

  public getData(item: ShipmentInfo): string[] {
    const strList = []
    if (['TCS', 'TCO', 'TCA'].includes(item.shipmentType)) {
      strList.push('包裹从商家配送至集运仓后，在"我的京东-我的集运"再次支付跨境运费后，由跨境集运商将包裹统一打包寄送，可省运费。')
    }
    if (item.freight) {
      strList.push(DeliveryHWJyDataIniter.getTipByFreight(item.freight))
    }
    strList.push('送达时间：' + item.promiseMsg)
    return strList
  }

  static getTipByFreight(freight: number): string {
    if (freight === 0) {
      return ''
    }
    return '运费说明：' + `大陆段免运费，跨境段运费${freight}元起。本次仅支付大陆段运费，跨境段运费请在订单页支付。`
  }
}
