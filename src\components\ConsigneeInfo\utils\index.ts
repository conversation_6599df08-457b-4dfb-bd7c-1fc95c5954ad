import { Check, CheckItem } from '../atoms/address'
export { default as crypto } from './crypto'
export { default as sgm } from './sgm'
export { default as appNetwork } from './appNetwork'
export { default as env } from './env'
export { default as colorApi } from './colorApi'

// 手机号脱敏
export function desensitizePhone(phone: string) {
  // 1. 移除所有非数字字符
  const cleaned = phone.replace(/\D/g, '')

  // 2. 验证是否为11位有效手机号
  if (cleaned.length !== 11) return phone // 非标准长度返回原值

  // 3. 正则脱敏：前三后四保留，中间四位变星号
  return cleaned.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
}

export function trimTxt(txt = '') {
  return txt?.replace(/\s+/g, ' ')?.trim()
}

// 定义地区代码映射类型
type AreaCode = '86' | '53283' | '852' | '0853' | '886'
type AddressType = 'addressCheck' | 'foreignAddressCheck' | 'taiWanAddressCheck' | 'gangAoAddressCheck'

// 创建地区代码到地址类型的映射
const AREA_CODE_MAPPING: Record<AreaCode, AddressType> = {
  '86': 'addressCheck',
  '53283': 'foreignAddressCheck',
  '852': 'gangAoAddressCheck',
  '0853': 'gangAoAddressCheck',
  '886': 'taiWanAddressCheck',
}

// 定义支持的检查类型
type CheckType = keyof Check

export const getRuleValue = <T extends keyof CheckItem>(params: {
  type: CheckType // 不再需要泛型参数 K，因为它不影响返回值类型
  areaCode?: AreaCode
  key: T // 使用泛型参数 T 来表示 CheckItem 的属性名
  obj: {
    [K in AddressType]: Check
  }
}): CheckItem[T] => {
  // 返回 CheckItem 中属性 T 的类型
  const { type, areaCode = '86', key, obj } = params
  const val = AREA_CODE_MAPPING[areaCode] || 'foreignAddressCheck'

  // 使用类型断言确保返回值类型安全
  return obj[val]?.[type]?.[key] as CheckItem[T]
}

export const getMapKey = (provinceId: number, key: string) => {
  switch (provinceId) {
    case 53283:
      return { Check: 'foreignAddressCheck', DarkGrain: 'foreignAddressDarkGrain' }[key] // 海外
    case 52993:
      return { Check: 'gangAoAddressCheck', DarkGrain: 'gangAoAddressDarkGrain' }[key] // 香港澳门

    case 32:
      return { Check: 'taiWanAddressCheck', DarkGrain: 'taiWanAddressDarkGrain' }[key] // 台湾

    default:
      return { Check: 'addressCheck', DarkGrain: 'addressDarkGrain' }[key] // 大陆
  }
}

export const isOverSea = (provinceId: number) => [53283, 52993, 32].includes(provinceId)

// 判断是否是直辖市
export function isMunicipality(provinceId: number) {
  return [1, 2, 3, 4].includes(provinceId)
}

// 获取详细的地址信息
export const full = (address: any) => {
  const { provinceName = '', cityName = '', countyName = '', townName = '' } = address
  return [provinceName, cityName, countyName, townName].concat(address.addressDetail || '').join('')
}

export function originRules() {
  return {
    provinceId: [
      {
        required: true,
        validator: (rule, value, callback) => {
          const isMun = isMunicipality(+value)
          if (isMun) {
            if (value == 0 || this.state.form.cityId == 0) {
              callback(new Error('请选择完整的地区信息'))
            } else {
              callback()
            }
          } else {
            if (value == 0 || this.state.form.cityId == 0 || this.state.form.countyId == 0) {
              callback(new Error('请选择完整的地区信息'))
            } else {
              callback()
            }
          }
        },
      },
    ],
    addressDetail: [
      { required: true, message: '请输入详细的地址信息，不能包含表情符号' },
      {
        validator: (rule, value, callback) => {
          const regex =
            /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac]/g
          const regexNew =
            /^(?:[^\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}])*$/u
          value = trimTxt(value)
          if (!value) {
            callback(new Error('请输入详细的地址信息，不能包含表情符号'))
          } else if (regex.test(value) || !regexNew.test(value)) {
            callback('请输入详细的地址信息，不能包含表情符号')
          } else {
            callback()
          }
        },
      },
    ],
    name: [
      { required: true, message: '请输入收货人姓名，不超过50个字符' },
      {
        validator: (rule, value, callback) => {
          const regex = /[*\\'$^;<>"={}@%~&]/
          value = trimTxt(value)
          if (value && regex.test(value)) {
            callback('收货人姓名中含有非法字符')
          } else {
            callback()
          }
        },
      },
    ],
    mobile: [
      { required: true, message: '请输入格式正确的手机号' },
      {
        validator: (rule, value, callback) => {
          const regex = /^\d+$/
          value = trimTxt(value)
          if (!regex.test(value)) {
            callback(new Error('请输入格式正确的手机号'))
          } else {
            callback()
          }
        },
      },
    ],
    phone: [
      {
        validator: (rule, value, callback) => {
          const regex = /^\d+$/
          value = trimTxt(value)
          if (value && !regex.test(value)) {
            callback(new Error('请输入格式正确的固定电话'))
          } else {
            callback()
          }
        },
      },
    ],
    postCode: [
      { required: false, message: '请输入格式正确的验证邮政编码' },
      {
        validator: (rule, value, callback) => {
          callback()
        },
      },
    ],
    email: [
      { required: false, message: '请输入格式正确的邮箱地址' },
      {
        validator: (rule, value, callback) => {
          callback()
        },
      },
    ],
  }
}

export const areaCodes = {
  '86': '中国大陆',
  '0086': '中国大陆',
  '852': '中国香港',
  '0853': '中国澳门',
  '886': '中国台湾',
}

export const formData = {
  provinceId: '所在地区',
  addressDetail: '详细地址',
  name: '收货人姓名',
  mobile: '手机号码',
  phone: '固定电话',
  postCode: '邮政编码',
  email: '邮箱地址',
}

export const addrTags = [
  {
    label: '学校',
    value: 1,
  },
  {
    label: '家',
    value: 2,
  },
  {
    label: '公司',
    value: 3,
  },
]

/**
 * @createCookie
 */
type OptsType = { name?: string; value?: string | number; days?: number; domain?: string }
export function createCookie(opts?: OptsType) {
  const { name, value, days = 30, domain = '.jd.com' } = opts || {}
  let expires: string | undefined
  if (days) {
    const date = new Date()
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
    expires = '; expires=' + date.toUTCString()
  }
  document.cookie = name + '=' + value + expires + '; path=/;domain=' + domain
}

/**
 * @readCookie
 */
export function readCookie(name = '') {
  const prefix = `${name}=`
  return (
    document.cookie
      .split(';')
      .map((c) => c.trimStart())
      .find((cookie) => cookie.startsWith(prefix))
      ?.slice(prefix.length) || null
  )
}
