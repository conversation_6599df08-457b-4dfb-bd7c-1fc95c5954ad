@charset "UTF-8";

.lg-message-content:after,
.lg-message-content:before {
    content: "";
    display: block;
    clear: both
}

.lg-message {
    position: relative;
    margin-top: 16px;
    margin-right: 24px;
    padding: 9px 18px;
    line-height: 1.5;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    color: #434343;
    background-color: #FFF;
    box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.1)
}

.lg-message-container {
    z-index: 99;
    position: fixed;
    box-sizing: border-box
}

.lg-message-container *,
.lg-message-container :after,
.lg-message-container :before {
    box-sizing: border-box
}

.lg-message-cont {
    display: inline-block;
    margin-left: 24px
}

.lg-message-cont-close {
    margin-right: 24px
}

.lg-message-icon {
    position: absolute;
    top: 12px;
    display: inline-block;
    font-size: 16px
}

.lg-message-icon.lg-icon-info-s1:before {
    color: #0083FF
}

.lg-message-icon.lg-icon-success-s1:before {
    color: #55B837
}

.lg-message-icon.lg-icon-warn-s1:before {
    color: #FAA21E
}

.lg-message-icon.lg-icon-error-s1:before,
.lg-message-icon.lg-icon-wrong-s1:before {
    color: #E1251B
}

.lg-message-close {
    position: absolute;
    right: 16px;
    top: 13px;
    font-size: 14px;
    color: #7c7c7c
}

.lg-message-close:hover {
    cursor: pointer
}

.lg-message--top-center {
    top: 8px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%)
}

.lg-message--top-center .lg-message {
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0
}

.message--out {
    -webkit-animation: messageOut .4s ease-in-out;
    animation: messageOut .4s ease-in-out
}

.message--in {
    -webkit-animation: messageIn .4s ease-in-out;
    animation: messageIn .4s ease-in-out
}

@-webkit-keyframes messageIn {
    0% {
        -webkit-transform: scale(0.2);
        transform: scale(0.2)
    }

    95% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes messageIn {
    0% {
        -webkit-transform: scale(0.2);
        transform: scale(0.2)
    }

    95% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    100% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes messageOut {
    0% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    20% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    100% {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0)
    }
}

@keyframes messageOut {
    0% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    20% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    100% {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0)
    }
}