import { popup } from '../popup'
import Block from './Block'
import type { ModalProps } from '../popup'
import type { BlockProps } from './types/block'

const defaultOptions: ModalProps['options'] = {
  mask: true,
  position: 'center-center',
  wrapperStyle: { zIndex: 100 },
}

function showBlockModal(props: BlockProps, options?: ModalProps['options']) {
  return popup(<Block {...props} />, { ...defaultOptions, ...options })
}

export default showBlockModal
