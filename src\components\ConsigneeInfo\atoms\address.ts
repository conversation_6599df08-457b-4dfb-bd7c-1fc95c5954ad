import { SelectAddressType } from '../constant/bizTypes'
import { IABResult } from '../utils/abTest'

export interface RedTipsProps {
  type?: string
  color?: string
  background?: string
  icon?: string
  content?: string
  resultCode?: string // 保存、更新接口返回的resultCode，用于控制小黄条显示，点击跳转反馈面板
}

interface AddressDarkGrain {
  area: string
  addressDetail: string
  searchAddress: string
  houseNumber: string //  门牌号
  phone: string
  name: string
  mobile: string
  postCode: string
  email: string
  mapSelect?: string
}
export interface CheckItem {
  // 位数限制
  limit: number
  message: string
  rule: string
  textLimit: number
}
export interface Check {
  addressDetail: CheckItem
  houseNumber: CheckItem
  email: CheckItem
  mobile: CheckItem
  name: CheckItem
  phone: CheckItem
  postCode: CheckItem
}
interface AllAddressDetailCheckMap {
  foreignAddressCheck: string
  addressCheck: string
  emailCheck: string
  nameCheck: string
  mobileCheck: string
  postCodeCheck: string
  foreignNameCheck: string
}
export interface AddPageRuleData {
  addressDarkGrain: AddressDarkGrain
  allAddressDetailCheckMap: AllAddressDetailCheckMap
  foreignAddressDarkGrain: AddressDarkGrain
  gangAoAddressDarkGrain: AddressDarkGrain
  taiWanAddressDarkGrain: AddressDarkGrain
  /**
   * 是否展示顶部定位引导黄条
   * 默认展示
   */
  isShowTopTip?: boolean
  /**
   * 是否展示标签楼层
   */
  showAddressTag?: boolean
  /**
   * 是否展示语音输入
   */
  isSupportVoice?: boolean
  /**
   * 是否展示智能识别
   */
  supportIntelliGenText?: boolean
  /**
   * 是否展示-表单项中的定位按钮
   */
  supportGis?: boolean
  /**
   * 是否展示历史收货人推荐
   */
  supportSuggestion?: boolean
  /**
   * 是否隐藏邮编
   */
  hidePostCode?: boolean
  addressCheck: Check
  foreignAddressCheck: Check
  taiWanAddressCheck: Check
  gangAoAddressCheck: Check
  /**
   * 定位推荐地址信息
   */
  recommendedAddressVO?: AddressStandard.RecommendedAddressVO
  /**
   * 粘贴板智能识别地址配置信息
   */
  addressIntelligentReminder?: {
    /**
     * "地址粘贴板"
     */
    outTextMessage?: string
    /**
     * 聚焦时展示文案："试试粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息",
     */
    focusMessage?: string
    /**
     * 默认文案："试试粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息",
     */
    defaultMessage?: string
  }
  /**
   * 顶部定位条提示文案等相关配置
   */
  customMap?: {
    /**
     * 背景颜色
     */
    bgColor?: string
    /**
     * 按钮边框颜色
     */
    buttonBorderColor?: string
    /**
     * tip标题
     */
    titleString?: string
    /**
     * tip 副文案
     */
    detailString?: string
    /**
     * 按钮文案
     */
    buttonString?: string
    changeB2CToast?: string
    /**
     * icon链接
     */
    iconUrl?: string
    /**
     * 文字颜色
     */
    textColor?: string
  }
  /**
   * 是否使用poi,决定表单页是否把详细地址换成门牌号
   */
  usePoiAddress?: boolean
  /**
   * 默认组件标题
   */
  defaultAddressTitle?: string
  /**
   * 默认组件文案
   */
  defaultAddressMsg?: string
  /**
   * 是否展示默认地址开关楼层
   */
  showDefaultSet?: boolean
  /**
   * areaInfo在新建地址时并且不是从定位地址(fromLocationAddress === 'false')过来的，会自动往表单填充一些数据，从addpagesaddress中下发的字段areainfo
   */
  areaInfo?: AreaInfo
  /**
   * 无地址用户新增地址时，接口根据IP推荐的地址
   */
  addressLocateDetailVO?: AddressLocateDetailVO
  /**
   * tags
   */
  tags?: Array<Tags>
  /**
   * 降级控制数组
   */
  abResult?: IABResult[]
}

/**
 * 标签信息
 */
interface Tags {
  report: string
  retTag: number
  tagName: string
  tagSource: number
}

/**
 * 区域信息
 */
interface AreaInfo {
  /**
   * 地址ID
   */
  addrId?: number
  /**
   * 区号
   */
  areaCode?: string
  diaoYuDao?: boolean
  /**
   * 是否为海外
   */
  foreignOverSea?: boolean
  gangAoNewUISwitch?: boolean
  /**
   * 是否港澳台
   */
  gangAoTai?: boolean
  /**
   * 是否隐藏邮编
   */
  hidePostCode?: boolean
  isSupCOD?: boolean
  /**
   * 一级名称，如：中国
   */
  name?: string
  parentId?: number
  parentName?: string
}

/**
 * 无地址用户新增地址时，接口根据IP推荐的地址
 */
interface AddressLocateDetailVO {
  /** 省份一级地址ID */
  provinceId?: string
  /** 城市id */
  cityId?: string
  /** 县区域id */
  countyId?: string
  /** 乡镇级地址id */
  townId?: string
  /** 省名称 */
  provinceName: string
  /** 市名称 */
  cityName: string
  /** 区名称 */
  countyName: string
  /** 乡镇级地址名称 */
  townName?: string
}

export const mapping = {
  /** 是否打开区号弹层 */
  isOpenedArea: false,
  addressEditOpt: undefined,
  redTipsVal: undefined,
  requireCompleteDetailShow: false,
  /** 是否显示完善门牌号提示 */
  isShowDetailAddr: false,
  /** 地址信息 */
  editFillInfo: {
    areaCode: '86',
  },
  /** 是否显示请补充县/区信息 */
  showAddrRedTips: {
    isAddrComplete: false,
    showAddrErrTip: false,
  },
  /** 获取页面校验规则信息 */
  addAddressPage: {
    /**
     * 是否展示顶部定位引导黄条
     * 默认展示
     */
    isShowTopTip: true,
    /**
     * 是否展示标签楼层
     * 默认展示
     */
    showAddressTag: true,
    /**
     * 是否展示智能识别
     * 默认展示
     */
    supportIntelliGenText: true,
    /**
     * 是否展示默认地址开关楼层
     * 默认展示
     */
    showDefaultSet: true,
    /**
     * 默认组件标题
     */
    defaultAddressTitle: '设置默认地址',
    /**
     * 默认组件文案
     */

    defaultAddressMsg: '提醒：下单会优先使用该地址',
    /**
     * 顶部定位条提示文案等相关配置
     */
    customMap: {
      /**
       * 背景颜色
       */
      bgColor: '#FFF9F3',
      /**
       * 按钮边框颜色
       */
      buttonBorderColor: '#FF8000',
      /**
       * tip标题
       */
      titleString: '定位服务未开启',
      /**
       * tip 副文案
       */
      detailString: '请在[设置]中允许京东[使用APP期间]访问位置信息，并开启[精确位置]开关',
      /**
       * 按钮文案
       */
      buttonString: '去开启',
      changeB2CToast: '已为您切换地址样式',
      /**
       * icon链接
       */
      iconUrl: 'https://img12.360buyimg.com/img/jfs/t1/230632/7/11936/399/659e364dFff5669ac/01655c10ddc37ee5.png',
      /**
       * 文字颜色
       */
      textColor: '#FF8000',
    },
    /**
     * 粘贴板智能识别地址配置信息
     */
    addressIntelligentReminder: {
      /**
       * "地址粘贴板"
       */
      outTextMessage: '地址粘贴板',
      /**
       * 聚焦时展示文案："试试粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息",
       */
      focusMessage: '试试粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息',
      /**
       * 默认文案："试试粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息",
       */
      defaultMessage: '试试粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息',
    },
    addressDarkGrain: {
      area: '省市区县、乡镇等',
      addressDetail: '街道、楼牌号等',
      searchAddress: '支持地址联想，可以输入街道/小区楼栋/乡村名称哦~',
      houseNumber: '门牌号',
      phone: '',
      name: '请填写收货人姓名',
      mobile: '请填写收货人手机号',
      postCode: '',
      email: '',
    },
    allAddressDetailCheckMap: {
      foreignAddressCheck: '{"rule":"^[a-zA-Z\\\\d\\\\(\\\\)\\\\.\\\\-\\\\s\\\\&;#/,_]*$","message":"抱歉，只支持输入英文详细地址"}',
      addressCheck: '{"rule":"","message":""}',
      emailCheck:
        '{"rule":"^[a-zA-Z\\\\d\\\\$\\\\(\\\\)\\\\*\\\\+\\\\.\\\\[\\\\]\\\\?\\\\^\\\\{\\\\}\\\\-\\\\s\\\\@%&=;~\'”<>`!#:/,_]*$","message":"邮箱格式有误，请重新填写"}',
      nameCheck: '{"rule":"","message":""}',
      mobileCheck: '{"rule":"[()\\\\-0-9]+","message":"手机号格式有误，请重新填写"}',
      postCodeCheck:
        '{"rule":"^[a-zA-Z\\\\d\\\\$\\\\(\\\\)\\\\*\\\\+\\\\.\\\\[\\\\]\\\\?\\\\^\\\\{\\\\}\\\\-\\\\s\\\\@%&=;~\'”<>`!#:/,_]*$","message":"邮编格式有误，请重新填写"}',
      foreignNameCheck: '{"rule":"^[a-zA-Z\\\\d\\\\(\\\\)\\\\.\\\\-\\\\s\\\\&;#/,_]*$","message":"抱歉，只支持输入英文姓名"}',
    },
    foreignAddressDarkGrain: {
      area: '省市区县、乡镇等',
      addressDetail: '请用英文填写准确的城市、街道、门牌号',
      searchAddress: '支持地址联想，可以输入街道/小区楼栋/乡村名称哦~',
      houseNumber: '请用英文填写门牌号',
      phone: '选填',
      name: '请填写真实的英文全名',
      mobile: '填写收货地电话号码',
      postCode: '请填写准确的邮编',
      email: '必填',
    },
    gangAoAddressDarkGrain: {
      area: '省市區縣、鄉鎮等',
      addressDetail: '請填寫準確的城市、街道、門牌號',
      searchAddress: '支持地址联想，可以输入街道/小区楼栋/乡村名称哦~',
      houseNumber: '請填寫門牌號',
      phone: '建議填寫備用電話，以便及時聯絡您',
      name: '請填寫真實收件人姓名',
      mobile: '填寫收貨地電話號碼',
      postCode: '必填',
      email: '必填',
    },
    taiWanAddressDarkGrain: {
      area: '省市區縣、鄉鎮等',
      addressDetail: '請填寫準確的城市、街道、門牌號',
      searchAddress: '支持地址聯想，可以輸入街道/小區樓棟/鄉村名稱哦~',
      phone: '建議填寫備用電話，以便及時聯絡您',
      houseNumber: '請填寫門牌號',
      name: '請填寫真實收件人姓名',
      mobile: '填寫收貨地電話號碼',
      postCode: '必填',
      email: '必填',
    },
    addressCheck: {
      addressDetail: {
        limit: 50,
        message: '详细地址输入不正确，请重新输入',
        rule: '',
        textLimit: 50,
      },
      houseNumber: {
        limit: 50,
        message: '门牌号输入不正确，请重新输入',
        rule: '',
        textLimit: 50,
      },
      email: {
        limit: 50,
        message: '邮箱输入不正确，请重新输入',
        rule: '^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$',
        textLimit: 50,
      },
      mobile: {
        limit: 11,
        message: '手机号格式不正确，请重新输入',
        rule: '^1[0-9]{10}$',
        textLimit: 17,
      },
      name: {
        limit: 25,
        message: '姓名输入不正确，请重新输入',
        rule: '^[A-Za-z0-9· ]{1,25}$',
        textLimit: 50,
      },
      phone: {
        limit: 16,
        message: '备用电话格式不正确，请重新输入',
        rule: '',
        textLimit: 16,
      },
      postCode: {
        limit: 20,
        message: '邮编输入不正确，请重新输入',
        rule: '',
        textLimit: 20,
      },
    },
    foreignAddressCheck: {
      addressDetail: {
        limit: 200,
        message: '详细地址输入不正确，请重新输入',
        rule: '',
        textLimit: 200,
      },
      houseNumber: {
        limit: 200,
        message: '门牌号输入不正确，请重新输入',
        rule: '',
        textLimit: 200,
      },
      email: {
        limit: 50,
        message: '邮箱输入不正确，请重新输入',
        rule: '^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$',
        textLimit: 50,
      },
      mobile: {
        limit: 17,
        message: '手机号格式输入不正确，请重新输入',
        rule: '^\\d+$',
        textLimit: 17,
      },
      name: {
        limit: 50,
        message: '姓名输入不正确，请重新输入',
        rule: '^[a-zA-Z·_./ (),-]{1,50}$',
        textLimit: 50,
      },
      phone: {
        limit: 16,
        message: '备用电话格式不正确，请重新输入',
        rule: '^(\\d{1,4}\\-?)?(\\d{5,8})(-?\\d{1,4})?$',
        textLimit: 16,
      },
      postCode: {
        limit: 20,
        message: '邮编输入不正确，请重新输入',
        rule: '^[0-9a-zA-Z·]{1,20}$',
        textLimit: 20,
      },
    },
    taiWanAddressCheck: {
      addressDetail: {
        limit: 100,
        message: '详细地址输入不正确，请重新输入',
        rule: '',
        textLimit: 100,
      },
      houseNumber: {
        limit: 100,
        message: '门牌号输入不正确，请重新输入',
        rule: '',
        textLimit: 100,
      },
      email: {
        limit: 50,
        message: '邮箱输入不正确，请重新输入',
        rule: '^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$',
        textLimit: 50,
      },
      mobile: {
        limit: 10,
        message: '手机号格式输入不正确，请重新输入',
        rule: '^[0-9]{9,10}$',
        textLimit: 17,
      },
      name: {
        limit: 25,
        message: '姓名输入不正确，请重新输入',
        rule: '^[A-Za-z0-9· ]{1,25}$',
        textLimit: 50,
      },
      phone: {
        limit: 16,
        message: '备用电话格式不正确，请重新输入',
        rule: '^(\\d{1,4}\\-?)?(\\d{5,8})(-?\\d{1,4})?$',
        textLimit: 16,
      },
      postCode: {
        limit: 20,
        message: '邮编输入不正确，请重新输入',
        rule: '^[0-9a-zA-Z·]{1,20}$',
        textLimit: 20,
      },
    },
    gangAoAddressCheck: {
      addressDetail: {
        limit: 100,
        message: '详细地址输入不正确，请重新输入',
        rule: '',
        textLimit: 50,
      },
      houseNumber: {
        limit: 100,
        message: '门牌号输入不正确，请重新输入',
        rule: '',
        textLimit: 50,
      },
      email: {
        limit: 50,
        message: '邮箱输入不正确，请重新输入',
        rule: '^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$',
        textLimit: 50,
      },
      mobile: {
        limit: 8,
        message: '手机号格式输入不正确，请重新输入',
        rule: '^[0-9]{8}$',
        textLimit: 17,
      },
      name: {
        limit: 25,
        message: '姓名输入不正确，请重新输入',
        rule: '^[A-Za-z0-9· ]{1,25}$',
        textLimit: 50,
      },
      phone: {
        limit: 16,
        message: '备用电话格式不正确，请重新输入',
        rule: '^(\\d{1,4}\\-?)?(\\d{5,8})(-?\\d{1,4})?$',
        textLimit: 16,
      },
      postCode: {
        limit: 20,
        message: '邮编输入不正确，请重新输入',
        rule: '^[0-9a-zA-Z·]{1,20}$',
        textLimit: 20,
      },
    },
  },
  /** 是否不可修改数量 */
  isMobileChange: undefined,
  /** 是否显示请补充县/区信息 */
  showAddrSuggestion: false,
  /** 是否打开地址弹层 */
  isOpenAddrLayer: false,
  /** 是否隐藏邮编 */
  hidePostCode: undefined,
  /** 是否展示历史收货人列表 */
  showUserSuggestion: undefined,
  /** 加密后的实时经纬度 */
  locationData: undefined,
  /** 小时达页面是否开启定位授权 */
  locationAuthorize: undefined,
  /** 小时达页面是否开启定位授权 */
  addAddressReturn: undefined,
  /** 推荐定位信息** */
  recommendedAddressVO: undefined,
  /** 是否由联想地址点击触发的textarea变更 */
  isSuggestionClick: undefined,
  /** android软键盘弹出和收起时页面高度viewHeight、软键盘高度keyboardHeight */
  androidKeyboard: undefined,
  /** 选址类型 EditTabType类型 map地图选址 cascade地区选址 * */
  selectAddressType: SelectAddressType.MAP,
  /** router通信标识，传入表示h5调用 */
  pageListPathStr: '',
  /** 获取默认地址 */
  findDefaultAddress: undefined,
  /** 选择地址收货地址成功回调 */
  locationAddressInfo: undefined,
  /** 新建/编辑数据第一次更新 */
  editDataFirstUpdate: undefined,
  /** 新版新建编辑页 用户选择的详细地址推荐联想词对象 */
  userSelectedSuggestionInfo: undefined,
  /** 地图选址命中区县校验弹窗 */
  mapAddressPop: undefined,
  /** 列表、新建/编辑隐藏末级地址 */
  hidefourthAddress: undefined,
  /** 嵌套地址开关 */
  nestedAddress: undefined,
  /** 推荐poi精细化开关 */
  recommondPoiAddress: undefined,
  /** 从地图回来移除1-4级级联接口 */
  removeCascade: undefined,
  /** 文本识别多条poi地址 */
  multiplePoiAddress: [],
  /** 英文版模式下 国内地址支持海外手机号开关 */
  mainlandSupportOverSeaMobile: undefined,
}

export default mapping
