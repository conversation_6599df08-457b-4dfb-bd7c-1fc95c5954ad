# Toast 组件

一个轻量级的Toast提示组件，支持自定义内容、图标和自动消失时间。

## 功能特性

- 🎨 支持 4 种预设图标类型：success、error、warning、info
- 🔧 支持自定义图标组件
- ⏰ 可配置自动消失时间，默认 2 秒
- 📱 响应式设计，移动端适配
- 🚀 简单易用的 API
- 🛡️ CSS Modules 避免样式冲突

## 基础用法

```typescript
import toast from '@/common/toast'

// 基础用法
toast.error('保存失败，请稍后重试')

// 自定义消失时间（3秒）
toast.success('保存成功', { duration: 3000 })

// 自定义回调
toast.warning('网络异常', { 
  duration: 5000,
  onClose: () => console.log('Toast已关闭')
})

// 自定义图标
toast.show({
  content: '自定义提示',
  icon: <CustomIcon />,
  duration: 2000
})
```

## API

### toast.show(options)

显示自定义Toast

**参数**
- `options.content: string` - 提示内容（必填）
- `options.icon?: 'success' | 'error' | 'warning' | 'info' | React.ReactNode` - 图标类型或自定义图标
- `options.duration?: number` - 自动消失时间，单位毫秒，默认2000
- `options.onClose?: () => void` - 消失后的回调
- `options.container?: HTMLElement` - 挂载的容器，默认document.body

### 快捷方法

- `toast.success(content, options?)` - 成功提示
- `toast.error(content, options?)` - 错误提示  
- `toast.warning(content, options?)` - 警告提示
- `toast.info(content, options?)` - 信息提示

## 样式定制

Toast组件使用 **CSS Modules**，避免全局样式冲突。如需自定义样式，可以通过以下方式：

### 方式一：覆盖CSS变量
```scss
// 在全局样式中定义变量
:root {
  --toast-bg-color: rgba(0, 0, 0, 0.9);
  --toast-text-color: #fff;
  --toast-border-radius: 12px;
}
```

### 方式二：使用 :global 覆盖
```scss
// 在你的组件样式中
:global(.toast-custom) {
  .toastContainer {
    background: rgba(255, 0, 0, 0.8);
    border-radius: 12px;
  }
}
```

### 方式三：传入自定义容器
```typescript
// 使用自定义容器并添加特定类名
const customContainer = document.querySelector('.my-toast-container')
toast.error('错误信息', { container: customContainer })
```

## 主要样式类名

使用CSS Modules后的类名结构：
- `.toastWrapper` - 最外层包装器
- `.toastOverlay` - 遮罩层
- `.toastContainer` - Toast容器
- `.toastContent` - 内容区域
- `.toastIcon` - 图标
- `.toastText` - 文字

## 在项目中的使用场景

根据图片中的示例，这个Toast组件特别适用于：

1. **保存失败提示**：`toast.error('保存失败，请稍后重试')`
2. **网络错误提示**：`toast.error('网络异常，请检查网络连接')`
3. **操作成功反馈**：`toast.success('配送信息已保存')`
4. **表单验证提示**：`toast.warning('请填写完整信息')` 