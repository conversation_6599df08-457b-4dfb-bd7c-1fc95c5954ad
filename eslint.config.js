import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended'
import simplePluginSort from 'eslint-plugin-simple-import-sort'

export default tseslint.config(
  { ignores: ['dist', 'node_modules', '*.mjs'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'simple-import-sort': simplePluginSort,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      // 'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-namespace': 'off',
      // '@typescript-eslint/no-explicit-any': 'off',
      // '@typescript-eslint/consistent-type-definitions': 'off',
      // 'prefer-const': 'off',
      '@typescript-eslint/no-unused-expressions': 'off',
      'prettier/prettier': ['error', { endOfLine: 'auto' }],
      // 'prettier/prettier': ['error', { endOfLine: 'auto' }],
      // 'simple-import-sort/imports': [
      //   'error',
      //   {
      //     groups: [
      //       // react放在首行
      //       ['^react', '^@?\\w'],
      //       // 内部导入
      //       ['^(@|components)(/.*|$)'],
      //       // 父级导入. 把 `..` 放在最后.
      //       ['^\\.\\.(?!/?$)', '^\\.\\./?$'],
      //       // 同级导入. 把同一个文件夹.放在最后
      //       ['^\\./(?=.*/)(?!/?$)', '^\\.(?!/?$)', '^\\./?$'],
      //       // 样式导入.
      //       ['^.+\\.?(css)$'],
      //       // 带有副作用导入，比如import 'a.css'这种.
      //       ['^\\u0000'],
      //     ],
      //   },
      // ],
    },
  },
  eslintPluginPrettierRecommended,
)
