@use "../../../assets/styles/lib.scss";

.consignee {
  width: auto;
  height: 232px;
  border-radius: 8px;
  background-color: lib.$colorWhite;
  position: relative;
  &-header {
    .divider {
      border: 0.5px solid lib.$color15;
    }
  }
  
  &.expand {
    height: 468px;
    .consignee-items {
      height: 338px;
    }
  }
  &-items {
    flex-wrap: wrap;
    overflow-y: hidden;
    height: 118px;
  }
  &-item {
    width: 315px;
    height: 68px;
    border: 1px solid #0000000F;
    border-radius: 8px;
    margin-right: 16px;
    margin-bottom: 16px;
    padding: 16px 16px 16px 12px;
    position: relative;
    &-addressDetail {
      width: 285.3px;
    }
    &:nth-child(2n) {
      margin-right: 0;
      @media (min-width: 1660px) {
        margin-right: 16px;
      }
      @media (max-width: 1439px) and (min-width: 1240px) {
        margin-right: 16px;
      }
      @media (max-width: 1659px) and (min-width: 1440px) {
        margin-right: 0;
      }
      @media (max-width: 1239px) { 
        margin-right: 0;
      }
    }
    &:nth-child(3n) {
      @media (min-width: 1660px) {
        margin-right: 0;
      }
      @media (max-width: 1439px) and (min-width: 1240px) {
        margin-right: 0;
      }
      @media (max-width: 1659px) and (min-width: 1440px) {
        margin-right: 16px;
      }
      @media (max-width: 1239px) { 
        margin-right: 16px;
      }
    }
  }
  &-expand {
    position: absolute;
    bottom: 0;
  }
}
/* 窗口宽度在 1440px 到 1660px 之间 */
@media (max-width: 1659px) and (min-width: 1440px) {
  .consignee-item {
    width: 360px;
  }
}
/* 窗口宽度小于 1240px */
@media (max-width: 1239px) {
  .consignee-item {
    width: 360px;
  }
}