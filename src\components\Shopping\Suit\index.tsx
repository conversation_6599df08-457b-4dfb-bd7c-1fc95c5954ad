/**
 * 虚拟组套
 */
import scope from './index.module.scss'
import { ProductionType } from '@app/typings/master_api_response'

interface IProps {
  production: ProductionType
}

const Suit: React.FC<IProps> = ({ production }) => {
  // 字段缺失不展示
  if (!production.name || !production.num || !production.unitPrice) {
    console.error('虚拟组套信息缺失')

    return null
  }

  return (
    <div className={scope.suit}>
      <i className={scope.tag}>套装</i>
      <div className="flex-center flex-1">
        <div className={`text-ellipsis ${scope.name}`} title={production.name}>
          {production.name}
        </div>
        <div className={scope.quantity}>共{production.num}套</div>
      </div>
      <div className={scope.price}>
        <div className={`${scope['suit-price']} flex-end`}>
          <div className={scope.value}>&yen;{production.unitPrice}</div>
          <div className={scope.unit}>套装价</div>
        </div>
        {/* <div className={scope['jd-price']}>&yen;9500</div> */}
      </div>
    </div>
  )
}

export default Suit
