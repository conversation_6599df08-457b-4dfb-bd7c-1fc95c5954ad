/*
 * @Author: ext.xuchao26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息地址卡片里操作按钮
 */
import { use<PERSON><PERSON><PERSON>, MouseEventHandler, FC, memo } from 'react'
import { Checkbox, Toast2, Modal } from '@app/common/legao'
import { reportClick } from '@app/utils/event_tracking'
import { readCookie } from '../utils'
import apis from '../api'
import styles from '../index.module.scss'

type propsType = {
  onSelectAddress: (addr: any, idx: number) => void
  onShow: (a: any) => void
  addr: any
  idx: number
  addressInfoList: Array<any>
}
const EditBtns: FC<propsType> = memo(({ onSelectAddress, onShow, addr, idx, addressInfoList }) => {
  const onEdit: MouseEventHandler<HTMLDivElement> = useCallback(
    (e) => {
      e.stopPropagation()
      onShow({ ...addr, type: 'edit', idx })
    },
    [onShow, addr, idx],
  )
  const defaultAddressChange = useCallback(() => {
    const defaultAddress = !addr.defaultAddress
    apis
      .setAddressDefault({ addressId: addr.addressId, defaultAddress })
      .then(() => {
        // 点击列表的设为默认/取消默认按钮时，更新本地列表中默认状态
        onSelectAddress(addr, idx)

        if (defaultAddress) {
          Toast2.create({
            type: 'success',
            text: '已设为默认',
          })
        } else {
          Toast2.create({
            type: 'success',
            text: '已取消默认地址设置',
          })
        }
      })
      .catch(() => {
        Toast2.create({
          type: 'fail',
          text: '网络异常，请稍后再试',
        })
      })
      .finally(() => {
        reportClick('address', { skutype: '', clickPos: '2', addr: readCookie('ipLoc-djd') })
        reportClick('addaddress', {
          skutype: '',
          clickPos: defaultAddress ? '0' : '1',
          addr: readCookie('ipLoc-djd'),
          testgroup: '',
        })
      })
  }, [onSelectAddress, addr, idx])

  const handleRemove: MouseEventHandler<HTMLDivElement> = useCallback(
    (e) => {
      e.stopPropagation()
      Modal.create({
        title: '是否删除该地址?',
        confirmText: '确认',
        cancelText: '取消',
        onConfirm: () => {
          apis
            .deleteAddress({
              addressId: addr.addressId,
            })
            .then(async () => {
              const next = addressInfoList?.[1]
              const current = addressInfoList?.[0]
              // 由于删除地址（删除的刚好的是选中的），导致触发选中id变更
              await onSelectAddress(
                { ...addr, addressId: addr.selected ? `${next?.addressId || ''}` : `${current?.addressId || ''}` },
                addr.selected ? 1 : 0,
              )
              Toast2.create({ type: 'success', text: '删除成功' })
            })
            .catch(() => {
              Toast2.create({ type: 'fail', text: '网络异常，请稍后再试' })
            })
        },
        onCancel: () => {},
      })
    },
    [onSelectAddress, addressInfoList, addr],
  )
  return (
    <div className={`${styles['edit-btns']} font-14 cursor-pointer is-flex justify-between w-full`} onClick={(e) => e.stopPropagation()}>
      <Checkbox className={styles['checkbox']} checked={!!addr.defaultAddress} onChange={defaultAddressChange}>
        设为默认地址
      </Checkbox>
      <div className="is-flex">
        <div className={`${styles['edit-btn']} mr-12`} onClick={onEdit}>
          编辑
        </div>
        <div className={`${styles['edit-btn']} mr-12`} onClick={handleRemove}>
          删除
        </div>
      </div>
    </div>
  )
})

export default EditBtns
