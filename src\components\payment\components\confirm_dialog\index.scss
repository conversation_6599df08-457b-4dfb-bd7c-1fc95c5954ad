.confirm-dialog {
  box-sizing: border-box;
  display: inline-block;
  min-width: 340px;
  max-width: 496px;
  padding: 24px;
  background-color: #fff;
  border-radius: 12px;
  text-align: left;
  transform: translateY(-100px);
}

.confirm-dialog-title {
  display: flex;
  align-items: center;
  color: #1a1a1a;
  font-size: 18px;
  line-height: 1;
  font-weight: 600;

  i {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url("//img13.360buyimg.com/ling/jfs/t1/280203/14/16455/1306/67f3799fF560685be/ce58b4c487168393.png");
    background-size: 24px 24px;
    background-repeat: no-repeat;
    margin-right: 12px;
  }
}

.confirm-dialog-description {
  padding-left: 36px;
  margin-top: 10px;
  font-size: 14px;
  line-height: 20px;
}

.confirm-dialog-content {}

.confirm-dialog-footer {
  display: flex;
  justify-content: flex-end;  
  padding-top: 24px;

  button + button {
    margin-left: 8px;
  }
}

