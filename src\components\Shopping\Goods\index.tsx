/**
 * 商品信息
 */
import { Fragment, useState } from 'react'
import { AffiliatedListType, ServiceListType, ServiceType } from '@app/typings/master_api_response'
import getCDNImgUrl from '@app/utils/images'
import Tooltip from '@app/common/Tooltip'
import { reportClick, reportExpose } from '@app/utils/event_tracking'
import { DEFAULT_IMG } from './constant'
import { GeneralPrice, SuitPrice } from './components'
import scope from './index.module.scss'
import useIntersectionObserverOnce from '@app/hooks/useIntersectionObserverOnce'
import { IProps, FuJianSkuBpType } from './type'

const Goods: React.FC<IProps> = ({ sku, productionType, balanceShopInfos, bundleId }) => {
  // 监听商品露出上报曝光埋点
  const observerRef = useIntersectionObserverOnce(() => {
    const services: FuJianSkuBpType[] = []
    const gifts: FuJianSkuBpType[] = []

    sku?.serviceList?.forEach((s) => {
      services.push({ type: '0', clerk: s.name, sku: '' })
    })

    sku?.affiliatedList?.forEach((g) => {
      gifts.push({ type: '1', clerk: g.name, sku: `${g.id}` })
    })

    reportExpose('orddetailExpo', { skuid: `${sku.id}`, fujian_sku: [...services, ...gifts] })
  })

  const [panel, setPanel] = useState({ service: true, affiliated: true }) // 赠品 ｜ 服务 楼层折叠展开控制

  // 点击sku上报
  const clickBp = () => {
    reportClick('orddetail', { clickPos: '其他', packetid: bundleId })
  }

  // 赠品查看
  const viewAffiliated = (id: number) => {
    window.open(`https://item.jd.com/${id}.html`)

    clickBp()
  }

  // 跳转商品详情
  const skipItem = () => {
    window.open(`https://item.jd.com/${sku.id}.html`)

    clickBp()
  }

  // 渲染服务
  const renderServiceList = (list: ServiceListType = []) => {
    if (list.length === 0) return null
    const renderList = list.filter((l) => l.name && l.buyNum)

    const isCustom = list.some((item) => item.type === 19) // 是否含有定制服务
    let serviceTitle = '服务'

    if (isCustom) {
      serviceTitle = '定制'
    }

    // 服务名称前小标题
    const getServiceTitleTag = (s: ServiceType) => {
      // 定制服务
      if (s.type === 19) {
        return s.titleTag
      }

      // 默认不展示
      return ''
    }

    // 展开
    const expand = renderList.map((item, index) => (
      <div className={scope.strip} key={index}>
        <div className={scope.name}>{!!index || serviceTitle}</div>
        <div className={scope.list}>
          <div className={`text-ellipsis ${scope.item} ${scope.expand}`}>
            {getServiceTitleTag(item)}
            {item.name}
          </div>
        </div>
        <div className={`flex ${scope.price}`}>
          {item.jdPrice && <span className={scope.value}>&yen;{item.jdPrice}</span>} <span className={scope.num}>&times;{item.buyNum}</span>
        </div>
      </div>
    ))

    // 折叠提示
    const tips = (
      <Fragment>
        {renderList.map((item, index) => (
          <Fragment key={index}>
            <span className={scope.item}>
              {getServiceTitleTag(item)}
              {item.name}
            </span>
            {index < sku!.serviceList!.length - 1 && <span>、</span>}
          </Fragment>
        ))}
      </Fragment>
    )

    // 折叠
    const fold = (
      <div className={scope.strip}>
        <div className={scope.name}>{serviceTitle}</div>
        <Tooltip content={tips} placement="top" padding="12px" distance={20} arrow width={400}>
          <div className={`text-ellipsis ${scope.fold}`}>
            {renderList.map((item, index) => (
              <Fragment key={index}>
                <span className={scope.item}>
                  {getServiceTitleTag(item)}
                  {item.name}
                </span>
                {index < sku!.serviceList!.length - 1 && <span className={scope.line}></span>}
              </Fragment>
            ))}
          </div>
        </Tooltip>
      </div>
    )

    // 只有一个服务展示展开的
    if (list.length === 1) {
      return (
        <>
          {expand}
          <div className={scope.btn}>
            <div className={`${scope.disabled} ${scope.expand}`}></div>
          </div>
        </>
      )
    }

    return (
      <>
        {!panel.service && expand}
        {panel.service && fold}

        {/* 操作按钮 */}
        {list.length >= 2 && (
          <div className={scope.btn} onClick={() => setPanel({ ...panel, service: !panel.service })}>
            {panel.service && <div className={scope.expand}></div>}
            {!panel.service && <div className={scope.fold}></div>}
          </div>
        )}
      </>
    )
  }

  // 渲染赠品
  const renderAffiliated = (list: AffiliatedListType = []) => {
    if (list.length === 0) return null

    const renderList = list.filter((l) => l.name && l.buyNum)

    // 展开
    const expand = renderList.map((item, index) => (
      <div className={scope.strip} key={index}>
        <div className={scope.name}>{index === 0 ? '赠品' : ''}</div>
        <div className={scope.list}>
          <div className={`flex-center ${scope.item} ${scope.expand}`} onClick={() => viewAffiliated(item.id)}>
            <div className="text-ellipsis">{item.name}</div>
            <div>&nbsp;&times;{item.buyNum}</div>
            <div>
              <span className={scope.arrow}></span>
            </div>
          </div>
        </div>
      </div>
    ))

    // 折叠划过展示全部
    const tips = (
      <Fragment>
        {renderList.map((item, index) => (
          <Fragment key={index}>
            <span className={scope.item} onClick={() => viewAffiliated(item.id)}>
              {item.name}
            </span>
            {index < sku!.affiliatedList!.length - 1 && <span>、</span>}
          </Fragment>
        ))}
      </Fragment>
    )

    // 折叠
    const fold = (
      <div className={scope.strip}>
        <div className={scope.name}>赠品</div>
        <Tooltip content={tips} placement="top" padding="12px" distance={20} arrow width={400}>
          <div className={`text-ellipsis ${scope.fold}`}>
            {renderList.map((item, index) => (
              <Fragment key={index}>
                <span className={scope.item} onClick={() => viewAffiliated(item.id)}>
                  {item.name} &times;{item.buyNum}
                </span>
                <span className={scope.arrow}></span>
                {sku.affiliatedList && index < sku.affiliatedList.length - 1 && <span className={scope.line}></span>}
              </Fragment>
            ))}
          </div>
        </Tooltip>
      </div>
    )

    // 只有一个赠品展示展开的
    if (list.length === 1) {
      return (
        <>
          {expand}
          <div className={scope.btn}>
            <div className={`${scope.disabled} ${scope.expand}`}></div>
          </div>
        </>
      )
    }

    // 多个赠品
    return (
      <>
        {panel.affiliated && fold}
        {!panel.affiliated && expand}

        {/* 操作按钮 */}
        <div className={scope.btn} onClick={() => setPanel({ ...panel, affiliated: !panel.affiliated })}>
          {panel.affiliated && <div className={scope.expand}></div>}
          {!panel.affiliated && <div className={scope.fold}></div>}
        </div>
      </>
    )
  }

  // 渲染主图
  const renderGoodsImg = (url?: string) => {
    let imgUrl = DEFAULT_IMG

    if (url) {
      imgUrl = getCDNImgUrl(url) // 拼接图片地址
    }

    return imgUrl
  }

  // 渲染右侧价格
  const renderRight = () => {
    // 虚拟组套
    if (productionType === 23) {
      return <SuitPrice sku={sku} />
    }

    // 通用
    return <GeneralPrice sku={sku} />
  }

  // 渲染门店
  const renderLoc = () => {
    return (
      <div className={scope.loc}>
        <div className={scope.strip}>
          <div className={scope.name}>门店</div>
          <div className={scope.list}>
            <div className={`text-ellipsis ${scope.item} ${scope.expand}`}>
              {balanceShopInfos?.[sku?.locShopId || '']?.name || '门店：您选择的门店已失效，请重新选择'}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 渲染规格
  const renderSku = () => {
    // 特定套装无需展示sku
    if ([16].includes(productionType!)) {
      return null
    }

    return (
      (sku.color || sku.size) && (
        <div className={scope.sku}>
          {sku.color && <div className={scope.item}>{sku.color}</div>}
          {sku.size && <div className={scope.item}>{sku.size}</div>}
          {/* 号卡 */}
          {sku.selectCard && <div className={scope.item}>| &nbsp;{sku.selectCard}</div>}
        </div>
      )
    )
  }

  // 渲染服务标签
  const renderServiceTags = () => {
    // 特定套装无需展示服务标签
    if ([16].includes(productionType!)) {
      return null
    }

    const getColor = (v: string) => {
      // 促销标
      if (['storeReductionTag'].includes(v)) {
        return ` ${scope.red}`
      }

      // 默认
      return ''
    }

    return (
      <div className={`${scope.tag}${sku.skuIconList && !sku.skuIconList.length ? ` ${scope.opacity0}` : ''}`}>
        {sku?.skuIconList?.map((item, index) => (
          <div className={`${scope.item}${getColor(item.id)}`} key={index}>
            {item.desc}
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={`${scope.goods} flex`} ref={observerRef()}>
      <div className={scope.left}>
        <div className={scope['goods-img']}>
          {/* 商品主图 */}
          <img src={renderGoodsImg(sku.imgUrl)} className={scope.img} onClick={skipItem} />
          {/* 库存状态 */}
          {sku.stockStateText && <div className={`${scope['stock-text']} flex-center2`}>{sku.stockStateText}</div>}
        </div>
      </div>
      <div className={scope.center}>
        <div className={scope.block}>
          <div className={`${scope.title} flex-center`} onClick={skipItem}>
            {/* 商品标签 */}
            {sku?.skuTitleIconList?.map((i, x) => <img src={i.url} className={scope.icon} key={x} />)}
            {/* 商品名称 */}
            <div className={`text-ellipsis ${scope.text}`} title={sku.name}>
              {sku.titleTag || ''}
              {sku.name}
            </div>
          </div>

          {/* 规格 */}
          {renderSku()}

          {/* 服务标签 */}
          {renderServiceTags()}
        </div>

        {/* 服务 */}
        {sku.serviceList && !!sku.serviceList.length && <div className={scope.service}>{renderServiceList(sku.serviceList)}</div>}
        {/* 赠品 */}
        {sku.affiliatedList && !!sku.affiliatedList.length && <div className={scope.gift}>{renderAffiliated(sku.affiliatedList)}</div>}
        {/* 门店 */}
        {sku.loc && sku.locShopId && balanceShopInfos && renderLoc()}
      </div>

      {/* 处理右侧价格区域 */}
      {renderRight()}
    </div>
  )
}

export default Goods
