@use './_reset.scss';

// 定义全局媒体查询
$normal-media:  "(min-width: 1541px)";
$medium-media: "(min-width: 1281px) and (max-width: 1540px)";
$small-media: "(max-width: 1280px)";

html {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  margin: 0;
  padding: 0;
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Cantarell',
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  background: url('https://img10.360buyimg.com/img/jfs/t1/230778/1/31015/277720/67623023F6ce9d532/f959b604104fe4d4.png')
    no-repeat #f5f6fa;
  background-size: 100% 100%;
  // -webkit-font-smoothing: antialiased;
  // -moz-osx-font-smoothing: grayscale;
  // -webkit-touch-callout: none;
  // -webkit-user-drag: none;
  // user-select: none;
  // width: 100%;
}

@font-face {
  font-family: JDZhengHeiVHeavy2;
  src: url('https://storage.360buyimg.com/libres/pc_fonts/JDZhengHeiV2.0-Heavy.otf') format('truetype');
}

@font-face {
  font-family: JDZhengHeiVRegular2-1;
  src: url('https://storage.360buyimg.com/libres/pc_fonts/JDZhengHeiV2.1-Regular.otf') format('truetype');
}
