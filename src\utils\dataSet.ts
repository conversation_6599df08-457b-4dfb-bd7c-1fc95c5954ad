/**
 * @description
 * 埋点公共参数
 * @param {string} cv - 埋点版本 0-旧版本 1-新版本
 */
export class CommonPoint {
  pointData: ESObject

  constructor() {
    this.pointData = {
      cv: '1',
    }
  }

  setData(key: string, value: any) {
    this.pointData[key] = value
  }

  getData(key: string) {
    return this.pointData[key]
  }
}

/**
 * @description
 * 埋点上报数据存储类 - 数据增删查
 */
export class ExposeBuriedPoints {
  static DomKey = 'data-point-id'
  // 数据存储
  storage: ExposeBuriedPointsStorage
  // observer实例
  observer: IntersectionObserver

  constructor(observerFn: IntersectionObserverCallback, observerOption: IntersectionObserverInit) {
    // 页面数据初始化
    this.storage = new Map()
    // 通过外部参数初始化observer
    this.observer = new IntersectionObserver(observerFn, observerOption)
  }

  // 判断数据有没有
  has(key: ExposeKey) {
    return this.storage.has(key)
  }

  // 获取存储的数据
  get(key: ExposeKey) {
    let res: IExposeDomInfo | undefined = this.storage.get(key)
    if (!res) {
      if (key && typeof key === 'object' && key.nodeType === Node.ELEMENT_NODE) {
        res = this.storage.get(key.getAttribute(ExposeBuriedPoints.DomKey) || '')
      }
    }

    return res
  }

  /**
   * @description 添加数据
   * @param exposeDomInfo dom元素数据信息
   */
  add(exposeDomInfo: IExposeDomInfo) {
    this.storage.set(exposeDomInfo.key, exposeDomInfo)
    exposeDomInfo.dom && this.observer.observe(exposeDomInfo.dom)
  }

  delete(key: ExposeKey) {
    if (this.has(key)) {
      const d = this.get(key)
      d?.dom && this.observer.unobserve(d.dom)
      this.storage.delete(key)
    }
  }

  unobserve(dom: Element) {
    // 移除dom的监听
    this.observer.unobserve(dom)
  }

  // 销毁监听
  destory() {
    // console.log('destory!!', this.storage)
    this.observer.disconnect()
    this.storage.clear()
  }
}
