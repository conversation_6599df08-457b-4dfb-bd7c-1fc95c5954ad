import styles from './index.module.scss'

import { UiSelector } from './types'

export default <T,>({ isOpen, items, onClick, canOpen, noBorder }: UiSelector.SelectItemState<T> & { onClick?: () => void }) => {
  // const maxStyle = { maxWidth: 'auth', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }
  const maxStyle = {}
  return (
    <div
      style={noBorder ? { border: 'none' } : {}}
      className={`${styles.selector_item_wrap} ${isOpen ? styles.selected : ''}`}
      onClick={() => onClick?.()}
    >
      {items.map((item, index) => {
        if (!item.text) {
          return null
        }
        return (
          <div
            key={index}
            className={`${styles.item} ${item.type === 'gray' ? styles.ellipsis : ''}`}
            style={item.type === 'gray' ? { color: 'gray', ...maxStyle } : maxStyle}
          >
            {item.text.split(/(\[周[一二三四五六日]\])/).map((part, i) =>
              /^\[周[一二三四五六日]\]$/.test(part) ? (
                <span key={i} style={{ color: 'red' }}>
                  {part}
                </span>
              ) : (
                part
              ),
            )}
          </div>
        )
      })}
      {canOpen && (
        <div
          className={styles.item}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'absolute',
            right: '0px',
            width: '32px',
            backgroundColor: '#f7f8fc',
            top: '0',
            height: '100%',
          }}
        >
          <img
            style={!isOpen ? { transform: 'rotate(180deg)' } : {}}
            alt={''}
            src={'https://img30.360buyimg.com/img/jfs/t1/292968/29/17450/333/685cb5f8Ff731bed0/2c595de011227753.png'}
          />
        </div>
      )}
    </div>
  )
}
