import './index.scss'
import React from 'react'
import Button from '@app/common/button'

export type ConfirmDialogProps = {
  title?: string
  description?: string
  content?: React.ReactNode
  children?: React.ReactNode
  okText?: string | null
  onOk?: ((_close?: Function) => void) | null
  autoOkClose?: boolean
  cancelText?: string
  onCancel?: () => void
  _close?: () => void
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = (props) => {
  const {
    title,
    description,
    content,
    children,
    okText = '确认',
    onOk,
    autoOkClose = true,
    cancelText,
    onCancel,
    _close,
  } = props

  return (
    <div className="confirm-dialog">
      <div className="confirm-dialog-title">
        {title && <i />}
        {title}
      </div>
      <div 
        className="confirm-dialog-description" 
        dangerouslySetInnerHTML={{ __html: description && typeof description === 'string' ? description : '' }} 
      />
      <div className="confirm-dialog-content">{children ? children : content}</div>
      <div className="confirm-dialog-footer">
        {cancelText && (
          <Button
            onClick={() => {
              onCancel?.()
              _close?.()
            }}
          >
            {cancelText}
          </Button>
        )}
        {okText && (
          <Button
            variant="primary"
            onClick={() => {
              onOk?.(_close)
              if (autoOkClose) _close?.()
            }}
          >
            {okText}
          </Button>
        )}
      </div>
    </div>
  )
}

export default ConfirmDialog
