/**
 * @description: 请求color网关时携带的参数，参数呈现在URL的search部分；
 * 使用者不必关心该参数，调用请求方法时即可自动携带该参数。
 */
export type DefaultColorApiParams = {
  appid: 'pctrade-core'
  client: 'pc'
  clientVersion: '1.0.0'
  uuid: string
  loginType: '3'
  t: ReturnType<typeof Date.now>
  // 接口加固参数
  'x-api-eid-token'?: string
  h5st?: string
}

/**
 * @description: color网关请求携带的额外参数，使用者需要根据实际情况来设置。
 */
export type ExtraColorApiParams = {
  functionId: string
  body?: ReturnType<typeof JSON.stringify>
  source?: string
  forcebot?: '1'
  cthr?: '1'
}

/**
 * @description: 请求color网关时携带的参数，除`body`外的大部分参数呈现在URL的search部分中；
 * `body`参数会根据请求的方法不同呈现的位置不同；
 * GET方法时呈现在URL的search中，POST方法时呈现在请求报文的body中；
 */
export type ColorApiParams = DefaultColorApiParams & ExtraColorApiParams
