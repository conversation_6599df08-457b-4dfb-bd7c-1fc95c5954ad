/** 商城公共组件配置*/
;(function () {
  window.commonAppId = 'pctrade-core'
  window.pageConfig = {
    shortcutMobileData: [
      {
        title: '\u624b\u673a\u4eac\u4e1c',
        desc: '\u65b0\u4eba\u4e13\u4eab\u5927\u793c\u5305',
        img: 'jfs\/t1\/84498\/39\/9486\/28110\/5d72189bEee8e7110\/773e5a267ab3786c.png',
        url: 'https:\/\/app.jd.com\/',
        devices: [
          { type: 'iphone', src: 'https:\/\/itunes.apple.com\/cn\/app\/id414245413' },
          { type: 'android', src: 'https:\/\/storage.jd.com\/jdmobile\/JDMALL-PC2.apk' },
          { type: 'ipad', src: 'https:\/\/itunes.apple.com\/cn\/app\/jing-dong-hd\/id434374726' },
        ],
      },
      {
        title: '\u5173\u6ce8\u4eac\u4e1c\u5fae\u4fe1',
        desc: '\u5fae\u4fe1\u626b\u4e00\u626b\u5173\u6ce8\u4eac\u4e1c\u670d\u52a1\u53f7\u8ba2\u9605\u66f4\u591a\u4fc3\u9500\u4f18\u60e0\u798f\u5229',
        img: 'jfs\/t1\/74865\/18\/9334\/11346\/5d7218c7Ecccb8ff0\/34095b63503fc108.jpg',
        url: '',
        devices: [],
      },
      {
        title: '\u4eac\u4e1c\u91d1\u878d\u5ba2\u6237\u7aef',
        desc: '\u65b0\u4eba\u4e13\u4eab\u5927\u793c\u5305',
        img: 'jfs\/t1\/77329\/31\/9360\/15408\/5d7218f7Efb5248cb\/ac1de226dafd0455.jpg',
        url: 'https:\/\/m.jr.jd.com\/integrate\/download\/html\/pc.html',
        devices: [
          {
            type: 'iphone',
            src: 'https:\/\/itunes.apple.com\/cn\/app\/jing-dong-jin-rong-hui-li\/id895682747?mt=8',
          },
          { type: 'android', src: 'http:\/\/************\/downapp\/jrapp_jr188.apk' },
        ],
      },
      {
        title: '\u4eac\u4e1c\u5065\u5eb7\u5ba2\u6237\u7aef',
        desc: '',
        img: 'jfs\/t1\/171539\/38\/9328\/28300\/60404decE2ee00c60\/297e613c999daada.png',
        url: 'https:\/\/www.jd.com',
        devices: [
          { type: 'iphone', src: 'https:\/\/hlc.m.jd.com\/download\/?downloadSource=jdh_JDcom' },
          { type: 'android', src: 'https:\/\/hlc.m.jd.com\/download\/?downloadSource=jdh_JDcom' },
        ],
      },
      {
        title: '\u5173\u6ce8\u4eac\u4e1c\u5c0f\u7a0b\u5e8f',
        desc: '\u65b0\u4eba0.1\u5143\u8d2d',
        img: 'jfs\/t1\/154335\/38\/20291\/19657\/60404de0Ece970bda\/917415e2e3b10628.jpg',
        url: '',
        devices: [],
      },
    ],
    cartIcons: [
      { name: '首页', poiSuffix: '050', icon: 'Home' },
      // { name: '购物车', poiSuffix: '051', icon: 'Cart' },
      { name: '我的', poiSuffix: '052', icon: 'Me' },
      { name: '客服', poiSuffix: '05', icon: 'ConsumerService' },
      { name: '反馈', poiSuffix: '06', icon: 'Feedback' },
    ],
    product: {
      FEEDBACK: 'https://wj-dongjian.jd.com/q/61961d8186206f00b9b1bae0',
    },
    headShiLaoHua: true, // 是否显示“网站无障碍”导航
    hideCart: true, // 隐藏购物车图标
    headServiceType: 'default',
  }
})()

/** 参数签名初始化 */
;(function () {
  try {
    if (window.ParamsSign) {
      window.PSign = new ParamsSign({
        appId: '7cffe',
        debug: false,
        preRequest: false,
        onSign: function (data) {
          if (data && data.code && data.code != 200) {
            console.log(JSON.stringify(data))
          }
        },
        onRequestTokenRemotely: function (data) {
          if (data && data.code && data.code != 0) {
            console.log(JSON.stringify(data))
          }
        },
        onRequestToken: function (data) {
          if (data && data.code && data.code != 0) {
            console.log(JSON.stringify(data))
          }
        },
      })
    } else {
      // js_security_v3_0.1.6.js脚本加载失败
    }
  } catch (err) {
    // window.PSign 初始化失败
  }
})()

/** 烛龙初始化 */
;(function supervisoryControl() {
  try {
    !(function () {
      'use strict'
      !(function (e, r, n, t) {
        var o = {
          pre_m: [],
          run: function (e) {
            for (var r = arguments.length, n = new Array(r > 1 ? r - 1 : 0), t = 1; t < r; t++) n[t - 1] = arguments[t]
            o.pre_m.push({ method: e, args: n })
          },
        }
        e[n] = o
        const a = function (e, r) {
          void 0 === o[e] && (o[e] = []), o[e].push(r)
        }
        'addEventListener' in e &&
          'removeEventListener' in e &&
          ((o.errorHandler = function (r) {
            const n = (r = r || e.event).target || r.srcElement
            n instanceof Element || n instanceof HTMLElement ? a('s_err', r) : a('err', r)
          }),
          (o.rejectionHandler = function (e) {
            a('reject', e)
          }),
          e.addEventListener('error', o.errorHandler, !0),
          e.addEventListener('unhandledrejection', o.rejectionHandler, !0)),
          'PerformanceLongTaskTiming' in e &&
            ((o.entries = []),
            (o.observer = new PerformanceObserver(function (e) {
              o.entries = o.entries.concat(e.getEntries())
            })),
            o.observer.observe({ entryTypes: ['longtask'] }))
        const s = e.document.createElement('script')
        ;(s.src = r),
          (s.crossOrigin = 'anonymous'),
          s.setAttribute('globalName'.toLowerCase(), n),
          s.setAttribute('preCollect'.toLowerCase(), t),
          e.document.getElementsByTagName('head')[0].appendChild(s)
      })(window, 'https://storage.360buyimg.com/dev-static/dra/probe-web/1.1.0/browser.js', 'dra', '__dra_PreCollectedData__')
    })()
  } catch (error) {
    console.log(error)
  }
})()
;(function () {
  try {
    window.dra.run('init', { aid: '2332981ebb90219808f2dbb7bb316918' })
    window.dra.run('start')
  } catch (error) {
    console.log(error)
  }
})()
;(function () {
  function insertEquipInfo() {
    try {
      getJsToken(function (jdres) {
        // $("#eid").val(jdres.jsToken);
        // $("#fp").val(jdres.fp);
        $.cookie && $.cookie('jd_eid', jdres.jsToken, { expires: 365 })
      }, 1000)
    } catch (e) {}
  }
  insertEquipInfo()
})()
