import styles from './index.module.scss'

const Tips = ({ contentList, maxWidth }: { contentList: (string | React.ReactElement)[]; maxWidth?: string }) => {
  return (
    <div className={styles.tips_wrap} style={maxWidth ? { maxWidth: maxWidth } : {}}>
      <div className={styles.tip_type_1}>
        <div className={styles.content}>
          <div className={styles.icon}>
            <img src={'https://img13.360buyimg.com/img/jfs/t1/275542/16/13359/328/67eb8c26F990cbf8c/a9afc2d9ccba6405.png'} />
          </div>
          <div className={styles.text}>
            {contentList.map((content, i) => {
              return (
                <div key={i} style={i !== 0 ? { marginTop: '0px' } : {}} className={styles.text_item}>
                  {content}
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

const Tips2 = ({ title, contexts }: { title: string; contexts: string[] }) => {
  return (
    <div className={styles.tips_wrap}>
      <div className={styles.tip_type_2}>
        <p className={styles.title}>{title}</p>
        {contexts.map((context, i) => {
          return (
            <div key={i} className={styles.content}>
              {context.trim()}
            </div>
          )
        })}
      </div>
    </div>
  )
}

const PromptText = ({ text }: { text: string }) => {
  return <div className={styles.prompt_wrap}>{text}</div>
}

export { Tips, Tips2, PromptText }
