/** 
 * @file: OTPInput.tsx
 * @description: OTPInput 短密码输入组件
 */
import React, { useState, useRef } from 'react'
import { raf } from '@app/helpers'
import './index.scss'

export interface OTPRef {
  focus: VoidFunction
  blur: VoidFunction
  reset: VoidFunction
  readonly value: string[]
  nativeElement: HTMLDivElement
}

interface OTPInputProps {
  length?: number
  onInput?: (value: string[]) => void
  onComplete?: (value: string) => void
  type?: React.HTMLInputTypeAttribute
  disabled?: boolean
  autofocus?: boolean
}

const OTPInput = React.forwardRef<OTPRef, OTPInputProps>((props, ref) => {
  const { length = 6, onInput, onComplete, type, disabled, autofocus } = props
  const [valueCells, setValueCells] = useState<string[]>(Array(length).fill(''))
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])

  const containerRef = React.useRef<HTMLDivElement>(null)

  React.useImperativeHandle(ref, () => ({
    focus: () => {
      inputRefs.current[0]?.focus()
    },
    blur: () => {
      for (let i = 0; i < length; i += 1) {
        inputRefs.current[i]?.blur()
      }
    },
    reset: () => {
      for (let i = 0; i < length; i += 1) {
        const input = inputRefs.current[i]
        if (input) input.value = ''
      }
    },
    get value() {
      return valueCells
    },
    nativeElement: containerRef.current!,
  }))
  const handleChange = (index: number, value: string) => {
    if (!/^\d$/.test(value) && value !== '') return // 只允许输入数字

    const nextValueCells = [...valueCells]
    nextValueCells[index] = value
    setValueCells(nextValueCells)

    // 自动跳转到下一个输入框
    if (value && index < length - 1) {
      inputRefs.current[index + 1]?.focus()
    }

    if (onInput) {
      onInput(nextValueCells)
    }

    // 如果所有输入框都已填满，触发 onComplete
    if (
      onComplete &&
      nextValueCells.length === length &&
      nextValueCells.every((digit) => digit !== '') &&
      nextValueCells.some((digit, index) => valueCells[index] !== digit)
    ) {
      onComplete(nextValueCells.join(''))
    }
  }

  const syncSelection = (index: number) => {
    raf(() => {
      const inputEle = inputRefs.current[index]
      if (document.activeElement === inputEle && inputEle) {
        inputEle.select()
      }
    })
  }

  const handleKeyDown = (index: number, event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus()
      syncSelection(index - 1)
    } else if (event.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus()
      syncSelection(index + 1)
    }
  }

  const handleKeyUp = (index: number, event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Backspace' && index > 0 && valueCells[index] === '') {
      inputRefs.current[index - 1]?.focus()
      syncSelection(index - 1)
    }
  }

  const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {
    event.preventDefault()
    const pasteData = event.clipboardData.getData('text').slice(0, length)
    if (!/^\d+$/.test(pasteData)) return

    const nextValueCells = pasteData.split('')
    setValueCells([...nextValueCells, ...Array(length - nextValueCells.length).fill('')])

    // 跳转到最后一个填充的输入框
    inputRefs.current[Math.min(nextValueCells.length, length - 1)]?.focus()

    // 触发回调
    if (nextValueCells.length === length) {
      onComplete && onComplete(nextValueCells.join(''))
    }
  }

  const inputProps: React.InputHTMLAttributes<HTMLInputElement> = {
    type,
    disabled,
  }

  return (
    <div className="otp" onPaste={handlePaste} ref={containerRef}>
      {Array.from({ length }).map((_, index) => {
        const value = valueCells[index]
        return (
          <input
            className="otp-input"
            key={index}
            type="text"
            maxLength={1}
            {...inputProps}
            value={value}
            onInput={(e) => {
              const target = e.target as HTMLInputElement
              handleChange(index, target.value)
            }}
            onFocus={() => syncSelection(index)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            onKeyUp={(e) => handleKeyUp(index, e)}
            onMouseDown={() => syncSelection(index)}
            onMouseUp={() => syncSelection(index)}
            ref={(el) => (inputRefs.current[index] = el)}
            autoFocus={autofocus && index === 0}
          />
        )
      })}
    </div>
  )
})

OTPInput.displayName = 'OTPInput'

export default OTPInput
