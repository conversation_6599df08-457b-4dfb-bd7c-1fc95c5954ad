import React, { useState, useEffect, useImperativeHandle } from 'react'
import Checkbox from '@app/common/checkbox'
import { popup_agreement_dialog } from './components/agreement_dialog'

const PaymentAgreementContent: React.FC<{
  children?: React.ReactNode
}> = (props) => {
  const { children } = props
  return <>{children}</>
}

type PaymentAgreementItemProps = {
  label?: string
  checked?: boolean
  separator?: string
  agreementName?: string | string[] | React.ReactElement | React.ReactElement[]
  children?: React.ReactNode
  _onStateChange?: (value: boolean) => void
}

const PaymentAgreementItem: React.FC<PaymentAgreementItemProps> = (props) => {
  const { label = '已阅读并同意', separator = '和', agreementName, children, _onStateChange, checked } = props
  const names = (Array.isArray(agreementName) ? agreementName : [agreementName]).filter(Boolean) as string[]
  const contents = (Array.isArray(children) ? children : [children]).filter((el) => {
    if (el && el.type === PaymentAgreementContent) return true
    return false
  })
  const [_checked, setChecked] = useState(checked ?? false)

  useEffect(() => {
    if (typeof _onStateChange === 'function') {
      _onStateChange(_checked)
    }
  }, [_checked])

  return (
    <div className="payment-agreement-item">
      <Checkbox label={label} checked={_checked} onChange={(e) => setChecked(e.target.checked)} />
      <span className="payment-agreement-item__desc">
        {names.map((name, index) => (
          <React.Fragment key={index}>
            {index > 0 && separator}
            {React.isValidElement(name) ? (
              name
            ) : (
              <a
                onClick={
                  contents[index]
                    ? () =>
                        popup_agreement_dialog({
                          title: name,
                          content: contents[index],
                          onOk: () => {
                            setChecked(true)
                          },
                        })
                    : undefined
                }
              >
                {name}
              </a>
            )}
          </React.Fragment>
        ))}
      </span>
    </div>
  )
}

type PaymentAgreementProps = {
  children?: React.ReactNode
}

export type PaymentAgreementRef = {
  readonly values: Record<string, any>[]
}

interface PaymentAgreementComponent
  extends React.ForwardRefExoticComponent<React.PropsWithoutRef<PaymentAgreementProps> & React.RefAttributes<PaymentAgreementRef>> {
  Item: typeof PaymentAgreementItem
  Content: typeof PaymentAgreementContent
}
/** 用户协议组件 */
const PaymentAgreement = React.forwardRef<PaymentAgreementRef, PaymentAgreementProps>((props, ref) => {
  const { children } = props
  const agreements = (Array.isArray(children) ? children : [children]).filter((el) => {
    if (el && el.type === PaymentAgreementItem) return true
    return false
  }) as React.ReactElement<PaymentAgreementItemProps>[]
  const valuesRef = React.useRef<Record<string, any>[]>([])

  useImperativeHandle(ref, () => {
    return {
      get values() {
        return valuesRef.current
      },
    }
  }, [])

  return (
    <div className="payment-summary-agreement">
      {agreements.map((node, index) =>
        React.isValidElement(node)
          ? React.cloneElement(node, {
              key: index,
              _onStateChange: (value: boolean) => {
                valuesRef.current[index] = {
                  ...node.props,
                  checked: value,
                }
              },
            })
          : node,
      )}
    </div>
  )
}) as PaymentAgreementComponent

/** 用户协议勾选组件及文案 */
PaymentAgreement.Item = PaymentAgreementItem
/** 用户协议内容组件 */
PaymentAgreement.Content = PaymentAgreementContent
PaymentAgreement.displayName = 'PaymentAgreement'

export default PaymentAgreement
