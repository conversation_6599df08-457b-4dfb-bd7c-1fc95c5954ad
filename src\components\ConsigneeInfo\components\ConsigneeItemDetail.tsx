/*
 * @Author: ext.xuch<PERSON>26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息地址卡片
 */
import { FC } from 'react'
import { EllipsisTooltip } from '../components'
import { desensitizePhone, addrTags } from '../utils'
import classNames from 'classnames'
import styles from '../index.module.scss'

type Props = {
  addr: any
}

const ConsigneeItemDetail: FC<Props> = ({ addr }) => {
  const addrTag = (tag: string) => addrTags.find((m: any) => m.value === tag)?.label

  return (
    <div className={`${styles['consignee-item-addressDetail']} font-14 leading-16 ml-12`}>
      <div className="mb-4 h-18 is-flex">
        {addr.defaultAddress && <span className={`${styles['tag']} ${styles['is-default']}`}>默认</span>}
        {addrTag(addr.retTag) && <span className={styles['tag']}>{addrTag(addr.retTag)}</span>}
        {addr.userDefinedTag && <span className={styles['tag']}>{addr.userDefinedTag}</span>}
        <EllipsisTooltip content={addr.provinceName + addr.cityName + addr.countyName + addr.townName}>
          <span className="h-16 is-ellipsis">
            {addr.provinceName}&nbsp;{addr.cityName}&nbsp;{addr.countyName}&nbsp;{addr.townName}
          </span>
        </EllipsisTooltip>
      </div>
      <EllipsisTooltip content={addr.addressDetail}>
        <div className="font-16 h-20 leading-20 font-semibold mb-13 is-ellipsis ftx12">{addr.addressDetail}</div>
      </EllipsisTooltip>
      <div className="h-16 is-flex">
        <EllipsisTooltip content={addr.name}>
          <span className={`${styles['consignee-item-addressDetail-name']} mr-4 is-ellipsis`}>{addr.name}</span>
        </EllipsisTooltip>
        <span>{desensitizePhone(addr.mobile)}</span>
      </div>
      <div className={classNames(`${styles['consignee-divider']}`, 'my-11')}></div>
    </div>
  )
}

export default ConsigneeItemDetail
