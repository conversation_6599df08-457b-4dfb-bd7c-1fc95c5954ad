import classnames from 'classnames'

export default function useClassAndStyle(props) {
  const classNames = (...args) => {
    return classnames(args)
  }

  const className = (...args) => {
    const { className } = props
    return classNames(...args, className)
  }

  const style = (args) => {
    const { style } = props
    return { ...args, ...style }
  }

  return { classNames, className, style }
}
