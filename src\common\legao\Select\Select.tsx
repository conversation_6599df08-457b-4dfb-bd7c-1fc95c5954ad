import { createRef } from 'react'
import ReactDOM from 'react-dom'
import ClickOutside from './withClickOutside'
import { debounce } from '../libs/utils/debounce'
import { createPopper } from '../libs/utils/popper'
import { reset } from '../libs/utils/style'
import { Component, PropTypes, Transition, View } from '../libs'
import { addResizeListener, removeResizeListener } from '../libs/utils/resize-event'
import { Scrollbar } from '../Scrollbar'
import Tag from '../Tag'
import Input from '../Input'
import SelectContext from '@app/common/legao/Context'

reset(`
  .el-select-dropdown {
    position: absolute !important;
  }
`)

type State = {
  options: Array<object>
  isSelect: boolean
  inputLength: number
  inputWidth: number
  inputHovering: boolean
  filteredOptionsCount: number
  optionsCount: number
  hoverIndex: number
  bottomOverflowBeforeHidden: number
  cachedPlaceHolder: string
  currentPlaceholder: string
  selectedLabel: string
  value: any
  visible: boolean
  query: string
  selected: any
  voidRemoteQuery: boolean
  valueChangeBySelected: boolean
  selectedInit: boolean
  dropdownUl?: HTMLElement
}

const sizeMap: { [size: string]: number } = {
  large: 42,
  small: 30,
  mini: 22,
}

class Select extends Component {
  state: State
  debouncedOnInputChange: Function
  static contextType = SelectContext

  constructor(props: object) {
    super(props)

    this.state = {
      options: [],
      isSelect: true,
      inputLength: 20,
      inputWidth: 0,
      inputHovering: false,
      filteredOptionsCount: 0,
      optionsCount: 0,
      hoverIndex: -1,
      bottomOverflowBeforeHidden: 0,
      cachedPlaceHolder: props.placeholder || '请选择',
      currentPlaceholder: props.placeholder || '请选择',
      selectedLabel: '',
      selectedInit: false,
      visible: false,
      selected: undefined,
      value: props.value,
      valueChangeBySelected: false,
      voidRemoteQuery: false,
      query: '',
    }

    if (props.multiple) {
      this.state.selectedInit = true
      this.state.selected = []
    }

    if (props.remote) {
      this.state.voidRemoteQuery = true
    }

    this.debouncedOnInputChange = debounce(() => {
      this.onInputChange()
    }, this.debounce())

    this.resetInputWidth = this._resetInputWidth.bind(this)
    this.tagsRef = createRef()
    this.popperRef = createRef()
    this.referenceRef = createRef()
    this.rootRef = createRef()
    this.inputRef = createRef()
    this.popperJS = null
    this.optionsRef = this.state.options.map(() => createRef())
  }

  componentDidMount() {
    this.handleValueChange()
    addResizeListener(this.rootRef.current, this.resetInputWidth)
    this.setState({ inputWidth: this.referenceRef.current.getBoundingClientRect().width })
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.props.placeholder !== prevProps.placeholder) {
      this.setState({
        currentPlaceholder: this.props.placeholder,
        cachedPlaceHolder: this.props.placeholder,
      })
    }

    if (this.props.value !== prevProps.value) {
      this.setState(
        {
          value: this.props.value,
        },
        () => {
          this.handleValueChange()
        },
      )
    }

    if (prevState.value != this.state.value) {
      this.onValueChange(this.state.value)
    }

    if (prevState.visible != this.state.visible) {
      if (this.props.onVisibleChange) {
        this.props.onVisibleChange(this.state.visible)
      }

      this.onVisibleChange(this.state.visible)
    }

    if (prevState.query != this.state.query) {
      this.onQueryChange(this.state.query)
    }

    if (Array.isArray(prevState.selected)) {
      if (prevState.selected.length != this.state.selected.length) {
        this.onSelectedChange(this.state.selected)
      }
    }
  }

  componentWillUnmount() {
    removeResizeListener(this.rootRef.current, this.resetInputWidth)
  }

  debounce(): number {
    return this.props.remote ? 300 : 0
  }

  handleClickOutside() {
    if (this.state.visible) {
      this.setState({ visible: false })
    }
  }

  handleValueChange() {
    const { multiple, remote, filterable } = this.props
    const { value, options } = this.state

    if (multiple && Array.isArray(value)) {
      this.setState(
        {
          selected: options.reduce((prev, curr) => {
            return value.indexOf(curr.props.value) > -1 ? prev.concat(curr) : prev
          }, []),
        },
        () => {
          this.onSelectedChange(this.state.selected, false)
        },
      )
    } else {
      const selected = options.filter((option) => {
        return option.props.value === value
      })[0]

      if (!remote && selected) {
        this.setState({ selectedLabel: selected.props.label || selected.props.value })
      } else {
        remote && this.setState({ selectedLabel: value })
      }
    }
  }

  onBlur() {
    new Promise((r) => setTimeout(r, 200)).then(() => {
      const { remote, filterable, multiple } = this.props
      const { selected, cachedPlaceHolder } = this.state
      if (!multiple) {
        if (selected && selected.props) {
          if (selected.props.value) {
            !remote && this.setState({ selectedLabel: selected.currentLabel() })
          }
        }
        if (filterable) {
          // selectedLabel = ''
          this.setState({ currentPlaceholder: cachedPlaceHolder })
        }
      }
    })
  }

  onVisibleChange(visible: boolean) {
    const { multiple, remote, filterable, reserveKeyword = false } = this.props
    let { query, dropdownUl, selected, selectedLabel, bottomOverflowBeforeHidden, cachedPlaceHolder } = this.state

    if (!visible) {
      // this.referenceRef.current.blur()

      if (this.rootRef.current.querySelector('.el-input__icon')) {
        const elements = this.rootRef.current.querySelector('.el-input__icon')

        for (let i = 0; i < elements.length; i++) {
          elements[i].classList.remove('is-reverse')
        }
      }

      if (this.inputRef.current) {
        this.inputRef.current.blur()
      }

      this.resetHoverIndex()

      if (!multiple) {
        if (dropdownUl && selected) {
          const element: any = ReactDOM.findDOMNode(selected)
          bottomOverflowBeforeHidden = element.getBoundingClientRect().bottom - this.popperRef.current.getBoundingClientRect().bottom
        }

        if (selected && selected.props) {
          if (selected.props.value) {
            // !remote && (selectedLabel = selected.currentLabel())
          }
        }
        if (filterable) {
          // selectedLabel = ''
          // this.setState({ currentPlaceholder: cachedPlaceHolder })
        }

        this.setState({ bottomOverflowBeforeHidden, selectedLabel })
      }
    } else {
      const icon = this.rootRef.current.querySelector('.el-input__icon')

      if (icon && !icon.classList.contains('el-icon-circle-close')) {
        const elements = this.rootRef.current.querySelector('.el-input__icon')

        for (let i = 0; i < elements.length; i++) {
          elements[i].classList.add('is-reverse')
        }
      }

      if (this.popperJS) {
        this.popperJS.update()
      }

      if (filterable) {
        if (!reserveKeyword && selectedLabel) {
          this.setState({ currentPlaceholder: selectedLabel, selectedLabel: '' }, () => (query = selectedLabel))
        } else {
          query = selectedLabel
        }

        if (multiple) {
          this.inputRef.current.focus()
        } else {
          const input = this.referenceRef.current
          const length = input.value.length
          if (input) {
            Promise.resolve().then(() => {
              input.focus()
              input.setSelectionRange(length, length)
            })
          }
        }
      }

      if (!dropdownUl) {
        const dropdownChildNodes = this.popperRef.current.childNodes
        dropdownUl = [].filter.call(dropdownChildNodes, (item) => item.tagName === 'UL')[0]
      }

      if (!multiple && dropdownUl) {
        if (bottomOverflowBeforeHidden > 0) {
          dropdownUl.scrollTop += bottomOverflowBeforeHidden
        }
      }

      this.setState({ query: query || '', dropdownUl })
    }
  }

  onValueChange(val: mixed) {
    const { multiple, remote, filterable } = this.props

    let { options, valueChangeBySelected, selectedInit, selected, selectedLabel, currentPlaceholder, cachedPlaceHolder, query } = this.state

    if (valueChangeBySelected) {
      return this.setState({
        valueChangeBySelected: false,
      })
    }

    if (multiple && Array.isArray(val)) {
      this.resetInputHeight()

      this.setState({ selectedInit: true, selected: [], currentPlaceholder: cachedPlaceHolder })

      val.forEach((item) => {
        const option = options.filter((option) => option.props.value === item)[0]
        if (option) {
          this.addOptionToValue(option)
        }
      })
    }

    if (!multiple) {
      const option = options.filter((option) => option.props.value === val)[0]

      if (option) {
        this.addOptionToValue(option)
        this.setState({ selectedInit, currentPlaceholder })
      } else {
        if (!remote) {
          selectedLabel = ''
          selected = {}
        } else {
          selectedLabel = val
          filterable && (query = val)
        }
        this.setState({ selectedInit, selected, currentPlaceholder, selectedLabel, query }, () => {
          this.resetHoverIndex()
        })
      }
    }
  }

  onSelectedChange(val: any, bubble: boolean = true) {
    const { form } = this.context
    const { multiple, filterable, onChange } = this.props
    let { query, hoverIndex, inputLength, selectedInit, currentPlaceholder, cachedPlaceHolder, valueChangeBySelected } = this.state

    if (multiple) {
      if (val.length > 0) {
        currentPlaceholder = ''
      } else {
        currentPlaceholder = cachedPlaceHolder
      }

      this.setState({ currentPlaceholder }, () => {
        this.resetInputHeight()
      })

      valueChangeBySelected = true

      if (bubble) {
        onChange &&
          onChange(
            val.map((item) => item.props.value),
            val,
          )
        form && form.onFieldChange()
      }

      if (filterable) {
        query = ''
        hoverIndex = -1
        inputLength = 20

        this.inputRef.current.focus()
      }

      this.setState({ valueChangeBySelected, query, hoverIndex, inputLength }, () => {
        if (this.inputRef.current) {
          this.inputRef.current.value = ''
        }
      })
    } else {
      if (selectedInit) {
        // return this.setState({
        //   selectedInit: false,
        // })
      }

      if (bubble) {
        onChange && onChange(val.props.value, val)
        form && form.onFieldChange()
      }
    }
  }

  onQueryChange(query: string) {
    const { form } = this.context
    const { multiple, filterable, remote, remoteMethod, filterMethod, onChange } = this.props
    let { voidRemoteQuery, hoverIndex, options, optionsCount, visible } = this.state

    if (this.popperJS) {
      this.popperJS.update()
    }

    if (multiple && filterable) {
      this.resetInputHeight()
    }

    if (remote && typeof remoteMethod === 'function') {
      hoverIndex = -1
      voidRemoteQuery = query === ''

      remoteMethod(query)

      options.forEach((option) => {
        option.resetIndex()
      })
      remote && onChange && onChange?.(query)
      remote && form && form.onFieldChange()
    } else if (typeof filterMethod === 'function') {
      options.forEach((option) => {
        const matches = filterMethod(query, option.props)
        option.setState({ visible: matches })
      })
      Promise.resolve().then(() => {
        this.setState({
          filteredOptionsCount: options.filter((opt) => opt.state.visible).length,
        })
      })
    } else {
      this.setState(
        {
          filteredOptionsCount: optionsCount,
        },
        () => {
          options.forEach((option) => {
            option.queryChange(query)
          })
        },
      )
    }

    this.setState({ hoverIndex, voidRemoteQuery })
    !visible && this.setState({ visible: true })
  }

  onEnter(): void {
    if (!this.popperRef.current) return
    this.popperJS = createPopper(this.referenceRef.current, this.popperRef.current, {
      modifiers: [
        {
          name: 'computeStyle',
          options: { gpuAcceleration: false },
        },
        {
          name: 'offset',
          options: {
            offset: [0, 4], // 调整弹出框与触发元素的间距（x轴偏移，y轴偏移）
          },
        },
      ],
    })
  }

  onAfterLeave(): void {
    this.popperJS.destroy()
  }

  iconClass(): string {
    return this.showCloseIcon()
      ? 'circle-close'
      : this.props.remote && this.props.filterable
        ? ''
        : `caret-top ${this.state.visible ? 'is-reverse' : ''}`
  }

  showCloseIcon(): boolean {
    const criteria =
      this.props.clearable && this.state.inputHovering && !this.props.multiple && this.state.options.indexOf(this.state.selected) > -1

    if (!this.rootRef.current) return false

    const icon = this.rootRef.current.querySelector('.el-input__icon')

    if (icon) {
      if (criteria) {
        icon.addEventListener('click', this.deleteSelected.bind(this))
        icon.classList.add('is-show-close')
      } else {
        icon.removeEventListener('click', this.deleteSelected.bind(this))
        icon.classList.remove('is-show-close')
      }
    }

    return criteria
  }

  emptyText() {
    const { loading, filterable } = this.props
    const { voidRemoteQuery, options, filteredOptionsCount } = this.state

    if (loading) {
      return '加载中'
    } else {
      if (voidRemoteQuery) {
        this.state.voidRemoteQuery = false

        return false
      }

      if (filterable && filteredOptionsCount === 0) {
        return '无匹配数据'
      }

      if (options.length === 0) {
        return '无数据'
      }
    }

    return null
  }

  handleClose() {
    this.setState({ visible: false })
  }

  toggleLastOptionHitState(hit?: boolean): any {
    const { selected } = this.state

    if (!Array.isArray(selected)) return

    const option = selected[selected.length - 1]

    if (!option) return

    if (hit === true || hit === false) {
      return (option.hitState = hit)
    }

    option.hitState = !option.hitState

    return option.hitState
  }

  deletePrevTag(e: object) {
    if (e.target.value.length <= 0 && !this.toggleLastOptionHitState()) {
      const { selected } = this.state

      selected.pop()

      this.setState({ selected })
    }
  }

  addOptionToValue(option: any, init?: boolean) {
    const { multiple, remote } = this.props
    let { selected, selectedLabel, hoverIndex, value, selectedInit } = this.state

    if (multiple) {
      if (selected.indexOf(option) === -1 && (remote ? value.indexOf(option.props.value) === -1 : true)) {
        this.selectedInit = !!init

        selected.push(option)

        this.resetHoverIndex()
      }
    } else {
      this.selectedInit = !!init

      selected = option
      selectedLabel = selectedInit ? selectedLabel : option.currentLabel()
      hoverIndex = option.index

      this.setState({ selected, selectedLabel, hoverIndex })
    }
  }

  managePlaceholder() {
    let { currentPlaceholder, cachedPlaceHolder } = this.state

    if (currentPlaceholder !== '') {
      currentPlaceholder = this.inputRef.current.value ? '' : cachedPlaceHolder
    }

    this.setState({ currentPlaceholder })
  }

  resetInputState(e: object) {
    if (e.keyCode !== 8) {
      this.toggleLastOptionHitState(false)
    }

    this.setState({
      inputLength: this.inputRef.current.value.length * 15 + 20,
    })
  }

  _resetInputWidth() {
    this.setState({
      inputWidth: this.referenceRef.current?.getBoundingClientRect().width,
    })
  }

  resetInputHeight() {
    const inputChildNodes = this.referenceRef.current.childNodes
    const input = [].filter.call(inputChildNodes, (item) => item.tagName === 'INPUT')[0] || { style: {} }

    input.style.height = Math.max(this.tagsRef.current.clientHeight + 6, sizeMap[this.props.size] || 36) + 'px'

    if (this.popperJS) {
      this.popperJS.update()
    }
  }

  resetHoverIndex() {
    const { multiple } = this.props
    let { hoverIndex, options, selected } = this.state

    setTimeout(() => {
      if (!multiple) {
        hoverIndex = options.indexOf(selected)
      } else {
        if (selected.length > 0) {
          hoverIndex = Math.min.apply(
            null,
            selected.map((item) => options.indexOf(item)),
          )
        } else {
          hoverIndex = -1
        }
      }

      this.setState({ hoverIndex })
    }, 300)
  }

  toggleMenu() {
    const { filterable, disabled, remote } = this.props
    const { query, visible, selectedLabel } = this.state

    if (remote && filterable && !selectedLabel) {
      return
    }

    if (!disabled) {
      this.setState({
        visible: !visible,
      })
      setTimeout(() => {
        if (this.popperJS) {
          this.popperJS.update()
        }
      }, 100)
    }
  }

  navigateOptions(direction: string) {
    let { visible, hoverIndex, options } = this.state

    if (!visible) {
      return this.setState({
        visible: true,
      })
    }

    let skip

    if (options.length != options.filter((item) => item.props.disabled === true).length) {
      if (direction === 'next') {
        hoverIndex++

        if (hoverIndex === options.length) {
          hoverIndex = 0
        }

        if (
          options[hoverIndex].props.disabled === true ||
          options[hoverIndex].props.groupDisabled === true ||
          !options[hoverIndex].state.visible
        ) {
          skip = 'next'
        }
      }

      if (direction === 'prev') {
        hoverIndex--

        if (hoverIndex < 0) {
          hoverIndex = options.length - 1
        }

        if (
          options[hoverIndex].props.disabled === true ||
          options[hoverIndex].props.groupDisabled === true ||
          !options[hoverIndex].state.visible
        ) {
          skip = 'prev'
        }
      }
    }

    this.setState({ hoverIndex, options }, () => {
      if (skip) {
        this.navigateOptions(skip)
      }

      this.resetScrollTop()
    })
  }

  resetScrollTop() {
    const element: any = this.optionsRef[this.state.hoverIndex]
    const bottomOverflowDistance = element.getBoundingClientRect().bottom - this.popperRef.current.getBoundingClientRect().bottom
    const topOverflowDistance = element.getBoundingClientRect().top - this.popperRef.current.getBoundingClientRect().top

    if (this.state.dropdownUl) {
      if (bottomOverflowDistance > 0) {
        this.state.dropdownUl.scrollTop += bottomOverflowDistance
      }
      if (topOverflowDistance < 0) {
        this.state.dropdownUl.scrollTop += topOverflowDistance
      }
    }
  }

  selectOption() {
    const { hoverIndex, options } = this.state

    if (options[hoverIndex]) {
      this.onOptionClick(options[hoverIndex])
    }
  }

  deleteSelected(e: object) {
    e.stopPropagation()

    if (this.state.selectedLabel) {
      this.setState({
        selected: {},
        selectedLabel: '',
        visible: false,
      })

      this.context.form && this.context.form.onFieldChange()

      if (this.props.onChange) {
        this.props.onChange('')
      }

      if (this.props.onClear) {
        this.props.onClear()
      }
    }
  }

  deleteTag(tag: any) {
    const index = this.state.selected.indexOf(tag)

    if (index > -1 && !this.props.disabled) {
      const selected = this.state.selected.slice(0)

      selected.splice(index, 1)

      this.setState({ selected }, () => {
        if (this.props.onRemoveTag) {
          this.props.onRemoveTag(tag.props.value)
        }
      })
    }
  }

  handleIconClick(event) {
    if (this.iconClass().indexOf('circle-close') > -1) {
      this.deleteSelected(event)
    } else {
      this.toggleMenu()
    }
  }

  onInputChange() {
    if (this.props.filterable && this.state.selectedLabel !== this.state.value) {
      this.setState({
        query: this.state.selectedLabel,
      })
    }
  }

  onOptionCreate(option: any) {
    this.setState((prevState) => ({
      options: [...prevState.options, option],
      optionsCount: prevState.optionsCount + 1,
      filteredOptionsCount: prevState.filteredOptionsCount + 1,
      selectedInit: true,
    }))
    // this.forceUpdate()
    this.handleValueChange()
  }

  onOptionDestroy(option: any) {
    this.state.optionsCount--
    this.state.filteredOptionsCount--

    const index = this.state.options.indexOf(option)

    if (index > -1) {
      this.state.options.splice(index, 1)
    }

    this.state.options.forEach((el) => {
      if (el != option) {
        el.resetIndex()
      }
    })

    this.forceUpdate()
    this.handleValueChange()
  }

  onOptionClick(option: any) {
    const { multiple, remote } = this.props
    let { visible, selected, selectedLabel, query } = this.state

    if (!multiple) {
      selected = option
      selectedLabel = option.currentLabel()
      visible = false
    } else {
      let optionIndex = -1

      selected = selected.slice(0)

      selected.forEach((item, index) => {
        if (item === option || item.props.value === option.props.value) {
          optionIndex = index
        }
      })

      if (optionIndex > -1) {
        selected.splice(optionIndex, 1)
      } else {
        selected.push(option)
      }
    }

    this.setState({ selected, selectedLabel, query: remote ? selectedLabel : query }, () => {
      if (!multiple) {
        this.onSelectedChange(this.state.selected)
      }

      this.setState({ visible })
    })
  }

  onMouseDown(event) {
    !this.props.remote && event.preventDefault()
    // event.preventDefault()
    this.props?.onMouseDown?.(event)
    if (this.inputRef.current) {
      this.inputRef.current.focus()
    }

    this.toggleMenu()
  }

  onMouseEnter() {
    this.setState({
      inputHovering: true,
    })
  }

  onMouseLeave() {
    this.setState({
      inputHovering: false,
    })
  }

  render() {
    const { multiple, size, disabled, filterable, loading, type = 'text', autosize, autoComplete = 'off', title } = this.props
    const { selected, inputWidth, inputLength, query, selectedLabel, visible, options, filteredOptionsCount, currentPlaceholder } =
      this.state

    return (
      <SelectContext.Provider value={{ component: this }}>
        <div ref={this.rootRef} style={this.style()} className={this.className('el-select')}>
          {multiple && (
            <div
              ref={this.tagsRef}
              className="el-select__tags"
              onClick={this.toggleMenu.bind(this)}
              style={{
                maxWidth: inputWidth - 32,
              }}
            >
              {selected.map((el) => {
                return (
                  <Tag
                    type="primary"
                    key={el.props.value}
                    hit={el.hitState}
                    closable={!disabled}
                    closeTransition={true}
                    onClose={this.deleteTag.bind(this, el)}
                  >
                    <span className="el-select__tags-text">{el.currentLabel()}</span>
                  </Tag>
                )
              })}
              {filterable && (
                <input
                  ref={this.inputRef}
                  type="text"
                  className={this.classNames('el-select__input', size && `is-${size}`)}
                  style={{ width: inputLength, maxWidth: inputWidth - 42 }}
                  disabled={disabled}
                  defaultValue={query}
                  onKeyUp={this.managePlaceholder.bind(this)}
                  onKeyDown={(e) => {
                    this.resetInputState(e)

                    switch (e.keyCode) {
                      case 27:
                        this.setState({ visible: false })
                        e.preventDefault()
                        break
                      case 8:
                        this.deletePrevTag(e)
                        break
                      case 13:
                        this.selectOption()
                        e.preventDefault()
                        break
                      case 38:
                        this.navigateOptions('prev')
                        e.preventDefault()
                        break
                      case 40:
                        this.navigateOptions('next')
                        e.preventDefault()
                        break
                      default:
                        break
                    }
                  }}
                  onChange={(e) => {
                    clearTimeout(this.timeout)

                    this.timeout = setTimeout(() => {
                      this.setState({
                        query: this.state.value,
                      })
                    }, this.debounce())

                    this.state.value = e.target.value
                  }}
                />
              )}
            </div>
          )}
          <Input
            ref={this.referenceRef}
            value={selectedLabel}
            type={type}
            autosize={autosize}
            autoComplete={autoComplete}
            title={title}
            placeholder={currentPlaceholder}
            name="name"
            size={size}
            disabled={disabled}
            readOnly={!filterable || multiple}
            icon={this.iconClass() || undefined}
            onChange={(value) => this.setState({ selectedLabel: value }, () => this.debouncedOnInputChange.call(this))}
            onBlur={this.onBlur.bind(this)}
            onIconClick={this.handleIconClick.bind(this)}
            onMouseDown={this.onMouseDown.bind(this)}
            onMouseEnter={this.onMouseEnter.bind(this)}
            onMouseLeave={this.onMouseLeave.bind(this)}
            onKeyUp={this.debouncedOnInputChange.bind(this)}
            onKeyDown={(e) => {
              switch (e.keyCode) {
                case 9:
                case 27:
                  this.setState({ visible: false })
                  e.preventDefault()
                  break
                case 13:
                  this.selectOption()
                  e.preventDefault()
                  break
                case 38:
                  this.navigateOptions('prev')
                  e.preventDefault()
                  break
                case 40:
                  this.navigateOptions('next')
                  e.preventDefault()
                  break
                default:
                  break
              }
            }}
          />
          <Transition name="el-zoom-in-top" onEnter={this.onEnter.bind(this)} onAfterLeave={this.onAfterLeave.bind(this)}>
            <View show={visible && this.emptyText() !== false}>
              <div
                ref={this.popperRef}
                className={this.classNames('el-select-dropdown', { 'is-multiple': multiple })}
                style={{ minWidth: inputWidth }}
              >
                <View show={options.length > 0 && filteredOptionsCount > 0 && !loading}>
                  <Scrollbar viewComponent="ul" wrapClass="el-select-dropdown__wrap" viewClass="el-select-dropdown__list">
                    {this.props.children}
                  </Scrollbar>
                </View>
                {this.emptyText() && <p className="el-select-dropdown__empty">{this.emptyText()}</p>}
              </div>
            </View>
          </Transition>
        </div>
      </SelectContext.Provider>
    )
  }
}

Select.propTypes = {
  value: PropTypes.any,
  size: PropTypes.string,
  disabled: PropTypes.bool,
  clearable: PropTypes.bool,
  filterable: PropTypes.bool,
  loading: PropTypes.bool,
  remote: PropTypes.bool,
  remoteMethod: PropTypes.func,
  filterMethod: PropTypes.func,
  multiple: PropTypes.bool,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  onVisibleChange: PropTypes.func,
  onRemoveTag: PropTypes.func,
  onClear: PropTypes.func,
}

export default ClickOutside(Select)
