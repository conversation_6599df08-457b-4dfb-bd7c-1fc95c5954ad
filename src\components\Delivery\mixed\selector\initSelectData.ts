/**
 * @file: initSelectData.ts
 * @description: 初始化选择数据，用于处理选择数据
 */
import { BundleType } from '@app/typings/master_api_response'
import { Selector } from './types'
import { DeliveryHWJyDataIniter } from '../initDeliveryData'
import { DeliveryEnum } from '../types'

/**
 * 配送楼层选择器数据初始化
 * @param bundle 依赖的包裹信息(用于获取配送方式、时效、自提点等信息)
 * @returns 配送楼层选择器数据
 */
export const selectDataProcess = (bundle: BundleType): Selector.State => {
  const { deliveryInfoVO, comShipmentTypeVO } = bundle

  const initData: Selector.State = {
    deliveryItem: {
      isOpen: false,
      canOpen: false,
      items: [{ text: '配送', type: 'none', data: {} }],
    },
    tipText: '',
    otherItem: {
      isOpen: false,
      canOpen: false,
      items: [],
    },
    installItem: undefined,
    inputItem: {
      isOpen: false,
      canOpen: true,
      items: [
        { text: '留言', type: 'none', data: {} },
        { text: '建议先与商家沟通确认', type: 'gray', data: {} },
      ],
    },
    transportCtrl: false,
  }

  // 当shipmentType=74，展示 locShipmentMsg
  if (comShipmentTypeVO?.shipmentType === 74) {
    initData.deliveryItem?.items.push({
      text: bundle.comShipmentTypeVO?.shipmentTypeName || '',
      type: 'none',
      data: {},
    })
    initData.deliveryItem?.items.push({
      text: bundle.comShipmentTypeVO?.locShipmentMsg || '',
      type: 'none',
      data: {},
    })
    return initData
  }

  if (comShipmentTypeVO?.shipmentType === DeliveryEnum.ShipmentTypeEnum.JZD && comShipmentTypeVO?.honorFreight) {
    // initData.tipText = '京尊达服务费：￥' + comShipmentTypeVO.honorFreight
  }

  const getText = (promise: any) => {
    let text = ''
    if (promise.promiseMsg) {
      text = promise.promiseMsg
    } else if (promise.promiseDate || promise.promiseTimeRange) {
      const week = promise.weekString ? `[${promise.weekString}]` : ''
      text = (promise.promiseDate || '') + week + (promise.promiseTimeRange || '')
    } else if (promise.timeOffset === -2) {
      text = '暂缓安装'
    }
    return text
  }

  if (bundle.overseasShipmentTypeVO) {
    const text = getText(bundle.overseasShipmentTypeVO)
    initData.deliveryItem?.items.push({
      text: bundle.overseasShipmentTypeVO.shipmentTypeName || '',
      type: 'none',
      data: {},
    })

    if (bundle.overseasShipmentTypeVO.overseasPickInfo?.selected && bundle.overseasShipmentTypeVO.overseasPickInfo?.pickName) {
      //   initData.otherItem?.items.push({
      //     text: '自提点',
      //     type: 'none',
      //     data: {},
      //   })
      const name = bundle.overseasShipmentTypeVO.overseasPickInfo?.pickName || ''
      initData.deliveryItem?.items.push({
        text: '上门自提' + `[${name}]`,
        type: 'none',
        data: {},
      })
    }
    initData.deliveryItem?.items.push({
      text: text,
      type: 'none',
      data: {},
    })
  }

  if (bundle.overseasShipmentTypeVO) {
    initData.deliveryItem && (initData.deliveryItem.canOpen = true) && (initData.transportCtrl = true)
    if (bundle.overseasShipmentTypeVO.freight) {
      initData.tipText = DeliveryHWJyDataIniter.getTipByFreight(bundle.overseasShipmentTypeVO.freight)
    }
  }

  if (deliveryInfoVO?.promiseList) {
    if (deliveryInfoVO.promiseList[0]) {
      const promise = deliveryInfoVO.promiseList[0]
      const text = getText(promise)
      initData.deliveryItem && (initData.deliveryItem.canOpen = deliveryInfoVO.secondDelivery)

      let pickName = ''
      if (promise.venderSelfDeliveryStoreInfo) {
        pickName = promise.venderSelfDeliveryStoreInfo.name || ''
      }
      if (promise.pickId && promise.pickName) {
        pickName = promise.pickName
      }
      const shipmentTypeName = bundle.comShipmentTypeVO?.shipmentTypeName || promise.promiseTypeName || ''
      initData.deliveryItem?.items.push({
        text: shipmentTypeName + (pickName ? `[${pickName}]` : ''),
        type: 'none',
        data: {},
      })
      initData.deliveryItem?.items.push({
        text: text,
        type: 'none',
        data: {},
      })
    }

    //表示有安装楼层
    if (deliveryInfoVO.promiseList[1]) {
      const promise = deliveryInfoVO.promiseList[1]
      const text = getText(promise)
      initData.installItem = {
        isOpen: false,
        canOpen: true,
        items: [{ text: '安装', type: 'none', data: {} }],
      }
      initData.installItem?.items.push({
        text: promise.promiseTypeName || '',
        type: 'none',
        data: {},
      })
      initData.installItem?.items.push({
        text: text,
        type: 'none',
        data: {},
      })
    } else {
      initData.installItem = undefined
    }
  }

  if (initData.deliveryItem?.items.length === 1) {
    initData.deliveryItem = undefined
  }

  if (bundle.regularBuy && bundle.regularBuyPlan) {
    const promise = deliveryInfoVO.promiseList[0]
    const shipmentTypeName = bundle.comShipmentTypeVO?.shipmentTypeName || promise.promiseTypeName || ''
    initData.dingQiGouItem = {
      isOpen: false,
      canOpen: false,
      items: [], //{ text: '定期购', type: 'none', data: {} }
    }
    initData.dingQiGouItem.items.push({
      text: shipmentTypeName,
      type: 'none',
      data: {},
    })
    initData.dingQiGouItem.items.push({
      text:
        '首次配送: ' +
        (promise.promiseMsg ? promise.promiseMsg : (promise.promiseDate || '') + (promise.weekString ? `[${promise.weekString}]` : '')),
      type: 'none',
      data: {},
    })
    initData.dingQiGouItem.items.push({
      text: '配送计划: ' + bundle.regularBuyPlan,
      type: 'none',
      data: {},
    })
    initData.deliveryItem = undefined
    if (bundle.regularBuyCalendarList && bundle.regularBuyCalendarList?.length > 0) {
      initData.dingQiGouItem.canOpen = true
    }
  }

  if (initData.otherItem) {
    initData.otherItem.items = initData.otherItem.items.filter((i) => i.text.replace(' ', '').length > 0)
  }

  // console.log(initData, bundle, 'initData')

  return initData
}
