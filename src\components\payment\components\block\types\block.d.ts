// import Order, { Promotion, BalancePresale, BalancePurchaseCasual } from "~/types/order";
import type { BalancePresale } from '@app/typings/master_api_response'
import type { BlockSkuVO, BalancePurchaseVO, SubmitOrderPromptVO } from '@app/typings/submitOrder_api_response'

type BlockSku = BlockSkuVO

export interface BlockProps {
  errorCode?: string
  errorReason?: string
  /** 722 无货商品列表  */
  noStockSkuList?: Array<BlockSku>
  /** 7203 促销限pin商品集合 */
  scaleSkuInfoList?: Array<BlockSku>
  /** 7204 不支持销售卡单商品集合 */
  noAvailableSkuList?: Array<BlockSku>
  /** 7205 价格升高卡单商品集合 */
  priceChangeList?: Array<BlockSku>
  /** 7206 商品限购 */
  purchaseList?: Array<BalancePurchaseVO>
  /** 7207 不支持的服务家商品集合 */
  noSupportHomeServiceSkuList?: Array<BlockSku>
  /** 7208 统一供销商无货 */
  commonStockSkuInfoList?: Array<BlockSku>
  /** 主商品列表 */
  mainSkuIdList?: Array<number>
  /** 预售定金信息 */
  balancePresale?: BalancePresale
  /** 随手搭 */
  balancePurchaseCasual?: any
  /** 727 药品疫情未登记 */
  medicineSkuList?: Array<BlockSku> //
  /** 727 药品疫情未登记 登记地址 */
  jumpUrl?: string
  /** 卡单浮层 */
  submitOrderPromptVO?: SubmitOrderPromptVO
  /** 商品集合 */
  balanceSkus: Order['balanceSkus']
  /** 卡单形式：1-进结算，2-提单 */
  blockType?: string
  /** 弹窗形式：1-进结算，2-提单 */
  promptType?: string
  /** 卡单浮层 */
  popupPromptVO?: SubmitOrderPromptVO
  /** 国补卡单toast */
  subsidyToast?: string
  /** 四级区域id */
  locationId?: string
  roomId?: string // 直播
  supperRoomPromo?: string // 直播
  pt?: string // 直播
  /** 使用国补卡单toast */
  usedGovSubsidyToast?: string
  onClose: () => void
  goBack: () => void
  onResume: () => void
  onRefresh: () => Promise<any>
  onOpenPanel?: () => void
}
