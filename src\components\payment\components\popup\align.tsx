import React from 'react'

type XAlign = 'left' | 'right' | 'center'
type YAlign = 'top' | 'bottom' | 'center'

export type AlignProps = {
  position?: `${XAlign}-${YAlign}`
  style?: React.CSSProperties
  onClick?: () => void
  children?: React.ReactNode
}
const Align: React.FC<AlignProps> = function (props) {
  const { position, style, onClick, children } = props

  const wrapperStyle = {
    position: 'fixed',
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
    margin: 'auto',
    textAlign: 'center',
    ...style,
  } as React.CSSProperties

  const columnStyle = {
    display: 'inline-block',
    width: 0,
    height: '100%',
    verticalAlign: 'middle',
  }

  const contentStyle = {
    width: '100%',
    display: 'inline-block',
    verticalAlign: 'middle',
  }

  if (typeof position === 'string' && position.length > 0) {
    const arr = position.split('-')
    wrapperStyle.textAlign = {
      left: 'left',
      right: 'right',
      center: 'center',
    }[arr[0]] as XAlign
    contentStyle.verticalAlign = {
      top: 'top',
      bottom: 'bottom',
      center: 'middle',
    }[arr[1]] as YAlign
  }

  const handleClick = (_e: React.MouseEvent<HTMLDivElement>) => {
    if (typeof onClick === 'function') {
      onClick()
    }
  }

  return (
    <div style={wrapperStyle} onClick={handleClick}>
      <div style={columnStyle} />
      <div style={contentStyle}>{children}</div>
    </div>
  )
}

export default Align
