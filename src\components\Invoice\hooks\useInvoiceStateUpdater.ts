import { useSetAtom } from 'jotai'
import {
  activeInvoiceTabAtom,
  selectedInvoiceStatusAtom,
  needEleVatInvoiceAtom,
  typeAtom,
  popInvoiceAtom,
  selfInvoiceAtom,
  selfSkuListAtom,
  popSkuListAtom,
  invoiceEditVoAtom,
  isBpinAtom,
  invoiceCodeDescAtom,
} from '@app/components/Invoice/atom/invoiceAtom'
import { InvoiceInfoEditVO, InvoiceResult, VenderType } from '@app/typings/invoice.d'
import { useCallback } from 'react'

/**
 * 发票状态更新hook
 * 用于处理从API获取的发票数据，并更新到Jotai状态中
 */
export const useInvoiceStateUpdater = () => {
  // 判断单个场景 - 自营、pop
  const setTypeAtom = useSetAtom(typeAtom)
  const setSelfInvoiceAtom = useSetAtom(selfInvoiceAtom)
  const setPopInvoiceAtom = useSetAtom(popInvoiceAtom)
  const setSelfSkuList = useSetAtom(selfSkuListAtom)
  const setPopSkuList = useSetAtom(popSkuListAtom)
  // 在 Hook 顶层获取所有需要的 setter 函数
  const setSelectedInvoiceStatus = useSetAtom(selectedInvoiceStatusAtom)
  const setNeedEleVatInvoice = useSetAtom(needEleVatInvoiceAtom)
  const setActiveInvoiceTab = useSetAtom(activeInvoiceTabAtom)
  const setInvoiceEditVo = useSetAtom(invoiceEditVoAtom)
  const setIsBpin = useSetAtom(isBpinAtom)
  const setInvoiceCodeDesc = useSetAtom(invoiceCodeDescAtom)
  /**
   * 根据API返回的发票数据更新状态
   * @param data API返回的发票数据
   * @returns 处理是否成功
   */
  const updateInvoiceState = useCallback(
    (data: InvoiceResult<InvoiceInfoEditVO>): boolean => {
      if (!data || !data.body) {
        console.error('返回的发票编辑数据为空')
        return false
      }

      const invoiceEditVO = data.body.invoiceEditVO
      if (!invoiceEditVO) {
        console.error('返回的发票编辑数据为空')
        return false
      }

      try {
        // 设置是否需要电子增值税发票
        setNeedEleVatInvoice(invoiceEditVO.needEleVatInvoice)
        // 更新发票订单类型
        setSelectedInvoiceStatus(invoiceEditVO.selectedInvoiceStatus)
        // 设置纳税人识别号文案
        setInvoiceCodeDesc(invoiceEditVO.invoiceCodeDesc)
        // 保存查询发票数据
        setInvoiceEditVo(invoiceEditVO)
        setIsBpin(invoiceEditVO.bPin)
        // 判断是否为混合场景
        if (invoiceEditVO.selectedInvoiceStatus === VenderType.MIX) {
          // 混合场景: 自营 + POP
          const selfVenderInvoice = invoiceEditVO.venderInvoiceVOList?.find((item) => item.type === VenderType.SELF)
          const popVenderInvoice = invoiceEditVO.venderInvoiceVOList?.find((item) => item.type === VenderType.POP)

          // 设置自营商品发票抬头
          if (selfVenderInvoice && selfVenderInvoice.invoiceTypes) {
            setSelfInvoiceAtom(selfVenderInvoice)
            setSelfSkuList(selfVenderInvoice?.skuInfoList || [])
          }

          // 设置POP商品发票抬头
          if (popVenderInvoice && popVenderInvoice.invoiceTypes) {
            setPopInvoiceAtom(popVenderInvoice)
            setPopSkuList(popVenderInvoice?.skuInfoList || [])
          }

          // 设置初始活动标签页
          setActiveInvoiceTab(VenderType.SELF) // 默认展示自营
        } else {
          // 单一场景处理
          const invoiceVenderVO = invoiceEditVO.venderInvoiceVOList?.[0]
          if (invoiceVenderVO) {
            if (invoiceVenderVO.type === VenderType.SELF) {
              setSelfInvoiceAtom(invoiceVenderVO)
              setSelfSkuList(invoiceVenderVO?.skuInfoList || [])
              setActiveInvoiceTab(VenderType.SELF) // 默认展示自营
            } else {
              setPopInvoiceAtom(invoiceVenderVO)
              setPopSkuList(invoiceVenderVO?.skuInfoList || [])
              setActiveInvoiceTab(VenderType.POP) // 默认展示POP
            }
            setTypeAtom(invoiceVenderVO.type || VenderType.SELF)
          }
        }

        return true
      } catch (error) {
        console.error('更新发票状态失败:', error)
        return false
      }
    },
    [
      setNeedEleVatInvoice,
      setSelectedInvoiceStatus,
      setActiveInvoiceTab,
      setTypeAtom,
      setSelfInvoiceAtom,
      setPopInvoiceAtom,
      setSelfSkuList,
      setPopSkuList,
      setInvoiceEditVo,
      setIsBpin,
      setInvoiceCodeDesc,
    ],
  )

  return { updateInvoiceState }
}
