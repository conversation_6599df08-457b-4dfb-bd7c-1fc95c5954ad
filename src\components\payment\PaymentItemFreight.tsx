import React from 'react'
import getImgUrl from '@app/utils/images'
import type { BalanceVenderFreight } from '@app/typings/master_api_response'

type Props = Partial<{
  data: Array<BalanceVenderFreight>
}>

const PaymentItemDetailFreight: React.FC<Props> = ({ data }) => {
  return (
    <div className="payment-summary-item__detail">
      {data?.map((item, index) => {
        const freightDetails = item.freightDetailVoList?.filter((item) => item.freight > 0 || item.freight < 0 || item.weight)
        return (
          <React.Fragment key={index}>
            <div key={index} className="payment-summary-item__detail-desc">
              {freightDetails?.map((detail, index) => {
                return (
                  <React.Fragment key={index}>
                    {index === 0 && <span>{item.venderName}</span>}
                    {detail.weight ? (
                      <span>{detail.weight}</span>
                    ) : (
                      <span>
                        {detail.freightName || '基础运费'}:{' '}
                        {detail.freight > 0
                          ? `¥${detail.freight.toFixed(2)}`
                          : detail.freight < 0
                            ? `-¥${Math.abs(detail.freight).toFixed(2)}`
                            : '¥0.00'}
                      </span>
                    )}
                    {(detail.freshOverWeightMsg || detail.otherOverWeightMsg) && (
                      <span>
                        {detail.freshOverWeightMsg}
                        {detail.otherOverWeightMsg}
                      </span>
                    )}
                  </React.Fragment>
                )
              })}
            </div>
            <div className="payment-summary-item__detail-items">
              {item.shipmentSkuVoList?.map((sku, index) => {
                const imgUrl = sku?.imgUrl
                return (
                  imgUrl && (
                    <div key={index}>
                      <img src={getImgUrl(imgUrl)} alt={sku?.name} title={sku?.name} />
                    </div>
                  )
                )
              })}
            </div>
          </React.Fragment>
        )
      })}
    </div>
  )
}
PaymentItemDetailFreight.displayName = 'PaymentItemDetailFreight'
export default PaymentItemDetailFreight
