/**
 * @file: skeleton.tsx
 * @description: 付款详情模块骨架屏
 */
import './index.scss'
import React from 'react'
import Button from '@app/common/button'
import Checkbox from '@app/common/checkbox'
const Skeleton = () => {
  return (
    <div className="payment">
      <div className="payment__bg">
        <img src="//img11.360buyimg.com/img/jfs/t1/258688/15/22260/1632/67b6fe12Fa92db83b/0f82902127858fb3.png" />
      </div>
      <div className="payment-summary">
        <div className="payment-summary-title">
          <em
            className="jd-skeleton"
            style={{ display: 'block', position: 'relative', width: '110px', height: '20px', borderRadius: '4px' }}
          />
        </div>
        <div className="payment-summary-inner">
          {[
            {
              l: { display: 'block', position: 'relative', width: '64px', height: '20px', borderRadius: '4px' },
              r: { display: 'block', position: 'relative', width: '100px', height: '20px', borderRadius: '4px' },
            },
            {
              l: { display: 'block', position: 'relative', width: '40px', height: '20px', borderRadius: '4px' },
              r: { display: 'block', position: 'relative', width: '60px', height: '20px', borderRadius: '4px' },
            },
            {
              l: { display: 'block', position: 'relative', width: '40px', height: '20px', borderRadius: '4px' },
              r: { display: 'block', position: 'relative', width: '60px', height: '20px', borderRadius: '4px' },
            },
          ].map((_, index) => (
            <div className="payment-summary-item" key={index}>
              <div className="payment-summary-item__title">
                <span className="jd-skeleton" style={_.l as React.CSSProperties} />
              </div>
              <div className="jd-skeleton" style={_.r as React.CSSProperties} />
            </div>
          ))}
          <div className="payment-summary-agreement">
            {[
              {
                display: 'block',
                position: 'relative',
                width: '180px',
                height: '20px',
                borderRadius: '4px',
              },
              {
                display: 'block',
                position: 'relative',
                width: '40px',
                height: '20px',
                borderRadius: '4px',
              },
              {
                display: 'block',
                position: 'relative',
                width: '80px',
                height: '20px',
                borderRadius: '4px',
              },
            ].map((_, index) => (
              <div className="payment-agreement-item" key={index}>
                <Checkbox checked={false} style={{ marginRight: '8px' }} />
                <span className="jd-skeleton" style={_ as React.CSSProperties} />
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="payment-action">
        <div className="payment-action__inner">
          <div className="payment-action-total">
            <div className="payment-action-total__title">
              <em
                className="jd-skeleton"
                style={{ display: 'block', position: 'relative', width: '40px', height: '20px', borderRadius: '4px' }}
              />
            </div>
            <div
              className="jd-skeleton payment-action-total__price"
              style={{ display: 'block', position: 'relative', width: '100px', height: '20px', borderRadius: '4px' }}
            />
          </div>
          <div className="payment-action-submit">
            <Button variant="primary" size="large">
              提交订单
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
Skeleton.displayName = 'Skeleton'
export default Skeleton
