/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-25 16:45:47
 * @LastEditTime: 2025-07-02 17:36:53
 * @LastEditors: ext.wangchao120
 * @Description: 虚拟资产骨架屏
 * @FilePath: /pc_settlement/src/components/VirtualAsset/Skeleton.tsx
 */
import styles from './index.module.scss'
import classNames from 'classnames'
const Skeleton = () => {
  return (
    <div className={styles.virtualAsset}>
      <div className={classNames(styles.skeletonTop, styles.flex)}>
        <div className={classNames(styles.skeletonTopLeft, 'jd-skeleton')}></div>
        <div className={classNames(styles.skeletonTopRight, 'jd-skeleton')}></div>
      </div>
      <div className={classNames(styles.skeletonCneter, styles.flex)}>
        <div className="jd-skeleton"></div>
        <div className="jd-skeleton"></div>
        <div className="jd-skeleton"></div>
        <div className="jd-skeleton"></div>
      </div>
      <div className={classNames(styles.skeletonBottom, styles.flex)}>
        <div className="jd-skeleton"></div>
        <div className="jd-skeleton"></div>
        <div className="jd-skeleton"></div>
      </div>
    </div>
  )
}

export default Skeleton
