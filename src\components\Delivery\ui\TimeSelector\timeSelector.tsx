import { useEffect, useState } from 'react'
import styles from './index.module.scss'
import { UiTimeSelector } from './types'

export default <T, U>({ list, onChange, onClick }: UiTimeSelector.Props<T, U>) => {
  const [timeList, setTimeList] = useState<UiTimeSelector.TimeItem<T, U>[]>([])

  const chooseBody = (parent: UiTimeSelector.TimeItem<any, any>, son?: UiTimeSelector.TimeItemSon<any>) => {
    parent.items.forEach((item) => {
      item.isSelected = false
    })
    timeList.forEach((item) => {
      item.isSelected = false
    })
    parent.isSelected = true
    if (son) {
      son.isSelected = true
    } else if (parent.items.length > 0 && !parent.items.find((item) => item.isSelected)) {
      parent.items[0].isSelected = true
    }
    setTimeList([...timeList])
    onChange(parent, son)
  }

  useEffect(() => {
    console.log(list, 'list2222')
    setTimeList(list)
    const firstItem = list.find((item) => item.isSelected)
    if (firstItem) {
      const firstSonItem = firstItem.items.find((item) => item.isSelected)
      onChange(firstItem, firstSonItem)
    }
  }, [list])

  return (
    <>
      <div className={styles.time_selector}>
        <div className={styles.left}>
          {timeList.map((item, index) => {
            return (
              <div
                onClick={() => {
                  onClick && onClick(item.text)
                  chooseBody(
                    item,
                    item.items.find((v) => v.isSelected),
                  )
                }}
                key={index}
                className={`${styles.item} ${item.isSelected ? styles.item_selected : ''}`}
              >
                {item.text}
              </div>
            )
          })}
        </div>
        <div className={styles.line}></div>
        <div className={styles.right}>
          {timeList.map((item, index) => {
            if (item.isSelected) {
              return item.items.map((item2, index2) => {
                return (
                  <div
                    onClick={() => {
                      if (item2.isDisabled) return
                      onClick && onClick(item2.textList[0]?.text, true, item2.targetValue)
                      chooseBody(item, item2)
                    }}
                    key={`${index}_${index2}`}
                    className={`${styles.item} ${item2.isSelected && !item2.isDisabled ? styles.item_selected : ''}`}
                  >
                    {item2.textList.map((item3, index3) => {
                      return (
                        <div
                          key={`${index}_${index2}_${index3}`}
                          className={item2.isSelected && !item2.isDisabled ? '' : `${styles.text ?? ''} ${styles[item3.style] ?? ''}`}
                        >
                          {item3.text}
                        </div>
                      )
                    })}
                  </div>
                )
              })
            }
          })}
        </div>
      </div>
    </>
  )
}
