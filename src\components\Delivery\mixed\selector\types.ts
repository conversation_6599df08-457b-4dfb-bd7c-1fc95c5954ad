import { BundleType } from '@app/typings/master_api_response'
import { UiSelector } from '../../ui/Selector/types'

export namespace Selector {
  export type InitState = {
    delivery?: boolean
    install?: boolean
    input?: boolean
    bundle: BundleType
    venderId: number
    jdcombineStoreId?: number
    storeId?: number
  }
  export type Props = {
    initState: InitState
  }

  export type Action =
    | {
        type: 'cancel'
      }
    | {
        type: 'click'
        payload: {
          selectType: SelectoType
          show: boolean
        }
      }
    | {
        type: 'init'
        state: State
      }

  export type State = {
    deliveryItem?: SelectorItem.State<string>
    installItem?: SelectorItem.State<string>
    inputItem?: SelectorItem.State<string>
    dingQiGouItem?: SelectorItem.State<string>
    transportCtrl: boolean
    tipText?: string
    otherItem?: SelectorItem.State<string>
  }

  export type SelectoType = 'delivery' | 'install' | 'input' | 'dingQiGou'
}

export namespace SelectorItem {
  export type State<T> = UiSelector.SelectItemState<T>
  export type Props<T> = {
    state: State<T>
    onClick?: () => void
    children: React.ReactNode
  }
}
