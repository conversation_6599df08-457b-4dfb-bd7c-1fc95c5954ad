.goods {
  border: 0.5px solid rgba(0, 0, 0, 0.06);
  padding: 8px;
  border-radius: 8px;
  background: #f7f8fc;

  .arrow-wrap {
    display: inline-block;
    width: 10px;
    height: 16px;
  }

  .arrow {
    display: inline-block;
    border-top: 1px solid;
    border-right: 1px solid;
    width: 6px;
    height: 6px;
    transform: rotate(45deg);
    position: relative;
    top: -2px;
  }

  .opacity0 {
    opacity: 0;
  }

  .left {
    margin-right: 12px;
  }

  .center {
    flex: 1;
  }

  .price-quantity {
    display: flex;
    align-items: center;
    margin-top: 18px;

    .received-price {
      font-family: JDZhengHeiVRegular2-1;
      display: flex;
      align-items: center;
      color: #ff0f23;
      font-size: 16px;
      font-weight: 700;
      line-height: 16px;
      letter-spacing: 0px;
    }

    .value {
      display: flex;
      align-items: center;
      color: #ff0f23;
      font-size: 16px;
      font-weight: 700;
      line-height: 16px;
      letter-spacing: 0px;
    }

    .text {
      display: flex;
      align-items: center;
      color: #ff0f23;
      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0px;
    }

    .jd-price {
      display: flex;
      align-items: center;
      color: #888b94;
      font-size: 14px;
      font-family: JDZhengHeiVRegular2-1;
      text-decoration: line-through;
      margin-left: 4px;
    }

    .quantity {
      display: flex;
      align-items: center;
      color: #888b94;
      font-size: 14px;
    }

    // 套装
    &.suit {
      justify-content: center;

      .quantity {
        margin-top: 0;
      }
    }
  }

  .tag {
    padding: 2px 5px;
    border-radius: 2px;
    border: 0.33px solid #ffa199;
    color: #fa2c19;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
  }

  .goods-img {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    background: #f7f8fc;
    overflow: hidden;
    cursor: pointer;
    position: relative;

    .img {
      width: 100%;
      height: 100%;
      display: block;
    }

    .stock-text {
      width: 88px;
      height: 24px;
      background: rgba(0, 0, 0, 0.7);
      color: rgba(255, 255, 255, 0.96);
      font-size: 14px;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }

  .title {
    cursor: pointer;
    margin-top: 4px;

    .label {
      border-radius: 2px;
      background: #00bb80;
      margin-right: 4px;
      width: 50px;
      height: 16px;
      color: #fff;
      font-size: 12px;
      font-weight: 500;
      height: 20px;
    }

    .text {
      color: #1a1a1a;
      font-size: 14px;
      max-width: 700px;
    }

    .icon {
      height: 16px;
      margin-right: 4px;
    }
  }

  .sku {
    display: flex;
    margin-top: 12px;

    .item {
      color: #505259;
      font-size: 14px;
      margin-right: 8px;
    }
  }
}

/* 窗口宽度在 1440px 到 1660px 之间 */
@media (max-width: 1659px) and (min-width: 1440px) {
  .goods {
    .title {
      .text {
        max-width: 500px;
      }
    }

    .right {
      width: 80px;
    }
  }
}

/* 窗口宽度小于 1240px */
@media (max-width: 1239px) {
  .goods {
    .title {
      .text {
        max-width: 500px;
      }
    }

    .right {
      width: 80px;
    }
  }
}
