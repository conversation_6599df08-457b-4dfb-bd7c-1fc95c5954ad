import React, { useRef, useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import calcTextareaHeight from './calcTextareaHeight.ts'
// import classNames from 'classnames'
import useClassAndStyle from '@app/hooks/useClassAndStyle'

// 定义 props 类型
type InputProps = {
  type?: string
  autosize?: boolean | { minRows: number; maxRows: number }
  rows?: number
  trim?: boolean
  autoComplete?: string
  resize?: string
  style?: React.CSSProperties
  className?: string
  onChange?: (value: string) => void
  onFocus?: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  onBlur?: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  onIconClick?: (e: React.MouseEvent<HTMLElement>) => void
  value?: string
  prepend?: React.ReactNode
  append?: React.ReactNode
  icon?: string | React.ReactNode
  validating?: boolean
  onMouseEnter?: (e: React.MouseEvent<HTMLDivElement>) => void
  onMouseLeave?: (e: React.MouseEvent<HTMLDivElement>) => void
  [key: string]: any
}

const Input: React.FC<InputProps> = forwardRef((props, ref) => {
  const {
    type = 'text',
    autosize = false,
    rows = 1,
    trim = false,
    autoComplete = 'off',
    resize,
    className: propClassName,
    onChange,
    onFocus,
    onBlur,
    onIconClick,
    value: propValue,
    prepend,
    append,
    icon,
    validating,
    onMouseEnter,
    onMouseLeave,
    innerRef,
    ...otherProps
  } = props

  const inputRef = useRef<HTMLInputElement | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement | null>(null)
  const [textareaStyle, setTextareaStyle] = useState<{ resize: string; height?: string }>({ resize: resize || '' })
  const { classNames, className, style } = useClassAndStyle(props)

  useEffect(() => {
    resizeTextarea()
  }, [propValue])
  // useImperativeHandle(ref, () => ({
  //   focus: () => {
  //     ;(inputRef.current || textareaRef.current).focus()
  //   },
  //   blur: () => {
  //     ;(inputRef.current || textareaRef.current).blur()
  //   },
  //   getBoundingClientRect: () => (inputRef.current || textareaRef.current).getBoundingClientRect(),
  //   inativeElement: inputRef.current || textareaRef.current,
  // }))

  useImperativeHandle(ref, () => (inputRef.current || textareaRef.current) as HTMLDivElement)

  const fixControlledValue = (value: string | undefined | null): string => {
    if (typeof value === 'undefined' || value === null) {
      return ''
    }
    return value
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (onChange) {
      onChange(e.target.value)
    }
    resizeTextarea()
  }

  const handleFocus = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (onFocus) onFocus(e)
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (trim) handleTrim()
    if (onBlur) onBlur(e)
  }

  const handleTrim = () => {
    if (inputRef.current) {
      inputRef.current.value = inputRef.current.value.trim()
      if (onChange) {
        onChange(inputRef.current.value.trim())
      }
    }
  }

  const handleIconClick = (e: React.MouseEvent<HTMLElement>) => {
    if (onIconClick) {
      onIconClick(e)
    }
  }

  const resizeTextarea = () => {
    if (!autosize || type !== 'textarea') {
      return
    }

    const minRows = typeof autosize === 'object' ? autosize.minRows : 0
    const maxRows = typeof autosize === 'object' ? autosize.maxRows : Infinity
    const textareaCalcStyle = calcTextareaHeight(textareaRef.current, minRows, maxRows)

    setTextareaStyle({ ...textareaStyle, ...textareaCalcStyle })
  }

  const classname = classNames(
    type === 'textarea' ? 'el-textarea' : 'el-input',
    type === 'textarea' && props.size && `el-input--${props.size}`,
    {
      'is-disabled': props.disabled,
      'el-input-group': prepend || append,
      'el-input-group--append': !!append,
      'el-input-group--prepend': !!prepend,
    },
  )

  let value = propValue
  if ('value' in props) {
    value = fixControlledValue(propValue)
  }

  if (type === 'textarea') {
    return (
      <div style={style()} className={className(classname)}>
        <textarea
          {...otherProps}
          ref={(e) => {
            textareaRef.current = e
            if (innerRef) innerRef.current = e
          }}
          className="el-textarea__inner"
          style={textareaStyle}
          rows={rows}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          value={value}
        />
      </div>
    )
  } else {
    return (
      <div style={style()} className={className(classname)} onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave}>
        {prepend && <div className="el-input-group__prepend">{prepend}</div>}
        {typeof icon === 'string' ? (
          <i className={`el-input__icon el-icon-${icon}`} onClick={handleIconClick}>
            {prepend}
          </i>
        ) : (
          icon
        )}
        <input
          {...otherProps}
          ref={(e) => {
            inputRef.current = e
            if (innerRef) innerRef.current = e
          }}
          type={type}
          className="el-input__inner"
          autoComplete={autoComplete}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          value={value}
        />
        {validating && <i className="el-input__icon el-icon-loading"></i>}
        {append && <div className="el-input-group__append">{append}</div>}
      </div>
    )
  }
})

const ForwardInput = forwardRef((props, ref) => {
  return <Input {...props} innerRef={ref} />
})

export default ForwardInput
