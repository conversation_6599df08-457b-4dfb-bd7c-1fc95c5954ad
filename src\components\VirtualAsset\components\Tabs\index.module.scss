.tabs {
  position: relative;
  .rightTip {
    position: absolute;
    right: 0;
    top: 0;
    line-height: 38px;
  }
}
.tabs-button {
  .button {
    margin-right: 8px;
  }
}
.tabs-common {
  >.tabsTitle {
    display: flex;
    flex-direction: row;
    background-color: #fff;
    height: 48px;
    line-height: 48px;
    position: relative;
    &::after {
      content: "";
      display: block;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 1px;
      background-color: rgba(194, 196, 204, 0.6);
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      transform-origin: 0 0;
    }
  
    .tabTitleItem {
      margin: 0 16px;
      color: var(--custom-color-black);
      font-size: 16px;
      cursor: pointer;
      position: relative;
      &:hover {
        color: var(--custom-color-red);
      }
      &.dot::after{
        content: "";
        display: block;
        position: absolute;
        top: 12px;
        right: -4px;
        width: 7px;
        height: 7px;
        border: 0.5px solid #fff;
        box-sizing: border-box;
        border-radius: 50%;
        background-color: var(--custom-color-red);
     
      }
    }
    .active {
      color: var(--custom-color-red);
    }
    .tabTitleItem.active {
      border-bottom: 2px solid var(--custom-color-red);
    }
    .disabled {
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;
    }
  }
}

.tabsContent {
  position: relative;
  padding-top: 20px;
  // min-height: 150px;
  // max-height: 360px;
  // overflow-y: auto;
}

.scroll {
  overflow-y: auto;
  &::-webkit-scrollbar{ 
    display: none !important; 
  }
}
/* 单个标签页内容 */
.tabPane {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  // padding: 20px;
  display: none;

  // transition: opacity 0.1s ease;
}

.tabPane.active {
  position: relative;
  display: block;

}