.button-new-tag {
  height: 36px;
  line-height: 34px;
  padding: 0 14px;
  font-size: 14px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  cursor: pointer;
  background: #F7F8FC;
  border: 0.5px solid rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  color: #1A1A1A;
  margin: 0;
  outline: 0;
  text-align: center;
  box-sizing: border-box;
  .edit-icon {
    background: url(https://img10.360buyimg.com/imagetools/jfs/t1/271207/26/27893/525/680a2c48F6ca417d7/45eaf38ee38916c7.png) center / cover no-repeat;
  }
  &:hover, &.active { 
    .edit-icon {
      background: url(https://img12.360buyimg.com/imagetools/jfs/t1/278857/16/26417/509/680a2c48Fb9c880f1/198883f5603edd0f.png) center / cover no-repeat;
    }
  }
}

.el-button+ .button-new-tag, .el-button+ .input-new-tag {
  margin-left: 10px;
  margin-bottom: 10px;
}

.el-input.input-new-tag {
  margin-bottom: 10px;
  vertical-align: bottom;
  width: 228px;
  .el-input__inner {
    border-right-width: 0;
    &:focus+ .el-input-group__append{
      border-color: #FF8595;
    }
  }
  &:hover {
    .el-input__inner,
    .el-input-group__append{
      border-color: #FF8595;
    }
  }
  .el-input-group__append {
    width: 52px;
    line-height: 34px;
    text-align: center;
    padding: 0;
    cursor: pointer;
    font-weight: 600;
    color: #FF0F23;
    font-family: PingFang SC;
    position: relative;
    i {
      position: absolute;
      width: 1px;
      height: 12px;
      background: #FFEBEF;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
    span {
      width: 100%;
      display: inline-block;
      height: 100%;
    }
  }
  
}

.button-new-tag, .input-new-tag {
  &:hover, &.active {
    border-color: #FF8595;
    background-color: #FFEBF1;
    color: #FF0F23;
  }
}

.w-168 {
  width: 168px;
}
.w-244 {
  width: 244px;
}
.consignee-item-form {
  width: 516px;
}
.addressDetail .el-select-dropdown__item {
  height: 56px;
}