import React from 'react'
import Alert from '@app/common/alert'
import useMasterData from '@app/hooks/useMasterData'

const TopTips: React.FC = () => {
  const tipsVOList = useMasterData()?.body?.tipsVOList
  const tipsList = (Array.isArray(tipsVOList) ? tipsVOList : [])
    .filter((el) => {
      return el?.type == 4 && el?.tip && typeof el?.tip === 'string'
    })
    .map(({ tip, url }, index) => {
      let _tip: React.ReactNode = tip
      if (url && typeof url === 'string') {
        _tip = (
          <a href={url} target="_blank">
            {tip}
          </a>
        )
      }
      return (
        <React.Fragment key={index}>
          {index > 0 && <br />}
          {_tip}
        </React.Fragment>
      )
    })

  if (tipsList.length === 0) {
    return null
  }

  return <Alert>{tipsList}</Alert>
}

TopTips.displayName = 'TopTips'

export default TopTips
