export function safeStringify(obj: unknown, defaultReturnString: string | null = null) {
  try {
    // 处理循环引用
    const seen = new WeakSet()
    return JSON.stringify(obj, (_, value) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) return '[Circular]'
        seen.add(value)
      }
      return value
    })
  } catch (e) {
    console.error('Error during JSON.stringify:', e)
    return defaultReturnString || '{}'
  }
}
