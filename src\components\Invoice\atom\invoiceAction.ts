import { atom } from 'jotai'
import {
  activeInvoiceTabAtom,
  selectedInvoiceStatusAtom,
  selectedInvoiceTypeAtom,
  currentNormalInvoiceContentsAtom,
  currentInvoiceTitlesAtom,
  typeAtom,
  selfInvoiceAtom,
  popInvoiceAtom,
  currentInvoiceTypesAtom,
  currentInvoicePutTypesAtom,
  isFormModifiedAtom,
} from './invoiceAtom'
import { InvoiceType, InvoiceTypeVO, UsualInvoiceItem, VenderType } from '@app/typings/invoice.d'
import { FORBIDDEN_CHARS, replaceCharsWithAt } from '../services/invoice.validation.service'
// 更新表单字段
export const updateFieldAtom = atom(null, (get, set, update: { field: string; value: any }) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const type = get(typeAtom)
  const { field, value: rawValue } = update
  // 过滤非法字符
  const value = replaceCharsWithAt(rawValue, FORBIDDEN_CHARS.STANDARD)

  // 电子发票字段处理
  if (field.startsWith('electroInvoice.')) {
    const key = field.replace('electroInvoice.', '')
    // 根据业务场景选择对应的发票类型
    if (selectedInvoiceStatus === VenderType.MIX) {
      // 混合场景: 自营 + POP
      if (activeInvoiceTab === VenderType.SELF) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          electroInvoice: {
            ...get(selfInvoiceAtom).electroInvoice,
            [key]: value,
          },
        })
      } else {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          electroInvoice: {
            ...get(popInvoiceAtom).electroInvoice,
            [key]: value,
          },
        })
      }
    } else {
      // 非混合场景: 纯自营或纯POP
      if (type === VenderType.SELF) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          electroInvoice: {
            ...get(selfInvoiceAtom).electroInvoice,
            [key]: value,
          },
        })
      } else {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          electroInvoice: {
            ...get(popInvoiceAtom).electroInvoice,
            [key]: value,
          },
        })
      }
    }
  }

  // 普通发票字段处理
  if (field.startsWith('normalInvoice.')) {
    const key = field.replace('normalInvoice.', '')
    // 根据业务场景选择对应的发票类型
    if (selectedInvoiceStatus === VenderType.MIX) {
      // 混合场景: 自营 + POP
      if (activeInvoiceTab === VenderType.SELF) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          normalInvoice: {
            ...get(selfInvoiceAtom).normalInvoice,
            [key]: value,
          },
        })
      } else {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          normalInvoice: {
            ...get(popInvoiceAtom).normalInvoice,
            [key]: value,
          },
        })
      }
    } else {
      // 非混合场景: 纯自营或纯POP
      if (type === VenderType.SELF) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          normalInvoice: {
            ...get(selfInvoiceAtom).normalInvoice,
            [key]: value,
          },
        })
      } else {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          normalInvoice: {
            ...get(popInvoiceAtom).normalInvoice,
            [key]: value,
          },
        })
      }
    }
    return
  }

  // 增值税发票字段处理
  if (field.startsWith('vat.')) {
    const key = field.replace('vat.', '')

    // 处理多层嵌套字段，如vat.invoiceConsigneeEditVO.phone
    if (key.includes('.')) {
      const [parentKey, childKey] = key.split('.')

      // 根据业务场景选择对应的发票类型
      if (selectedInvoiceStatus === VenderType.MIX) {
        // 混合场景: 自营 + POP
        if (activeInvoiceTab === VenderType.SELF) {
          set(selfInvoiceAtom, {
            ...get(selfInvoiceAtom),
            vat: {
              ...get(selfInvoiceAtom).vat,
              [parentKey]: {
                ...((get(selfInvoiceAtom).vat as any)?.[parentKey] || {}),
                [childKey]: value,
              },
            },
          })
        } else {
          set(popInvoiceAtom, {
            ...get(popInvoiceAtom),
            vat: {
              ...get(popInvoiceAtom).vat,
              [parentKey]: {
                ...((get(popInvoiceAtom).vat as any)?.[parentKey] || {}),
                [childKey]: value,
              },
            },
          })
        }
      } else {
        // 非混合场景: 纯自营或纯POP
        if (type === VenderType.SELF) {
          set(selfInvoiceAtom, {
            ...get(selfInvoiceAtom),
            vat: {
              ...get(selfInvoiceAtom).vat,
              [parentKey]: {
                ...((get(selfInvoiceAtom).vat as any)?.[parentKey] || {}),
                [childKey]: value,
              },
            },
          })
        } else {
          set(popInvoiceAtom, {
            ...get(popInvoiceAtom),
            vat: {
              ...get(popInvoiceAtom).vat,
              [parentKey]: {
                ...((get(popInvoiceAtom).vat as any)?.[parentKey] || {}),
                [childKey]: value,
              },
            },
          })
        }
      }
    } else {
      // 处理单层字段，如vat.companyName
      // 根据业务场景选择对应的发票类型
      if (selectedInvoiceStatus === VenderType.MIX) {
        // 混合场景: 自营 + POP
        if (activeInvoiceTab === VenderType.SELF) {
          set(selfInvoiceAtom, {
            ...get(selfInvoiceAtom),
            vat: {
              ...get(selfInvoiceAtom).vat,
              [key]: value,
            },
          })
        } else {
          set(popInvoiceAtom, {
            ...get(popInvoiceAtom),
            vat: {
              ...get(popInvoiceAtom).vat,
              [key]: value,
            },
          })
        }
      } else {
        // 非混合场景: 纯自营或纯POP
        if (type === VenderType.SELF) {
          set(selfInvoiceAtom, {
            ...get(selfInvoiceAtom),
            vat: {
              ...get(selfInvoiceAtom).vat,
              [key]: value,
            },
          })
        } else {
          set(popInvoiceAtom, {
            ...get(popInvoiceAtom),
            vat: {
              ...get(popInvoiceAtom).vat,
              [key]: value,
            },
          })
        }
      }
    }
    return
  }
  // 表单修改
  set(isFormModifiedAtom, true)
})

// 发票类型切换处理
export const handleInvoiceTypeChangeAtom = atom(null, (get, set, invoiceType: number) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const type = get(typeAtom)

  // 根据业务场景更新发票抬头
  if (selectedInvoiceStatus === VenderType.MIX) {
    // 混合场景: 自营 + POP
    if (activeInvoiceTab === VenderType.SELF) {
      // 更新自营发票类型
      set(selfInvoiceAtom, {
        ...get(selfInvoiceAtom),
        invoiceTypes: get(selfInvoiceAtom)?.invoiceTypes?.map((item) => ({
          ...item,
          selected: item.value === invoiceType,
        })),
      })
    } else if (activeInvoiceTab === VenderType.POP) {
      // 更新POP发票类型
      set(popInvoiceAtom, {
        ...get(popInvoiceAtom),
        invoiceTypes: get(popInvoiceAtom)?.invoiceTypes?.map((item) => ({
          ...item,
          selected: item.value === invoiceType,
        })),
      })
    }
  } else {
    // 获取当前发票抬头选项
    const invoiceTypes = type === VenderType.SELF ? get(selfInvoiceAtom)?.invoiceTypes : get(popInvoiceAtom)?.invoiceTypes
    // 更新发票抬头选项
    const updatedInvoiceTypes = invoiceTypes?.map((item) => ({
      ...item,
      selected: item.value === invoiceType,
    }))
    // 设置更新后的发票抬头选项
    set(type === VenderType.SELF ? selfInvoiceAtom : popInvoiceAtom, {
      ...get(type === VenderType.SELF ? selfInvoiceAtom : popInvoiceAtom),
      invoiceTypes: updatedInvoiceTypes,
    })
  }
})

// 发票开票方式切换处理
export const handleInvoicePutTypeChangeAtom = atom(null, (get, set, way: number) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const type = get(typeAtom)
  const currentInvoicePutTypes = get(currentInvoicePutTypesAtom)
  const currentInvoiceType = get(currentInvoiceTypesAtom)?.find((item) => item.selected)?.value
  const updatedPutTypes = currentInvoicePutTypes?.map((item) => ({
    ...item,
    selected: item.value === way,
  }))
  // console.log('updatedPutTypes', updatedPutTypes)
  if (selectedInvoiceStatus === VenderType.MIX) {
    // 混合场景: 自营 + POP
    if (activeInvoiceTab === VenderType.SELF) {
      if (currentInvoiceType === InvoiceType.ELECTRONIC) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          electroInvoice: {
            ...get(selfInvoiceAtom).electroInvoice,
            invoicePutTypes: updatedPutTypes,
          },
        })
      } else if (currentInvoiceType === InvoiceType.VAT) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          vat: {
            ...get(selfInvoiceAtom).vat,
            invoicePutTypes: updatedPutTypes,
          },
        })
      }
    } else if (activeInvoiceTab === VenderType.POP) {
      if (currentInvoiceType === InvoiceType.ELECTRONIC) {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          electroInvoice: {
            ...get(popInvoiceAtom).electroInvoice,
            invoicePutTypes: updatedPutTypes,
          },
        })
      } else if (currentInvoiceType === InvoiceType.VAT) {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          vat: {
            ...get(popInvoiceAtom).vat,
            invoicePutTypes: updatedPutTypes,
          },
        })
      }
    }
  } else {
    // 非混合场景: 纯自营或纯POP
    if (type === VenderType.SELF) {
      if (currentInvoiceType === InvoiceType.ELECTRONIC) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          electroInvoice: {
            ...get(selfInvoiceAtom).electroInvoice,
            invoicePutTypes: updatedPutTypes,
          },
        })
      } else if (currentInvoiceType === InvoiceType.VAT) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          vat: {
            ...get(selfInvoiceAtom).vat,
            invoicePutTypes: updatedPutTypes,
          },
        })
      }
    } else if (type === VenderType.POP) {
      if (currentInvoiceType === InvoiceType.ELECTRONIC) {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          electroInvoice: {
            ...get(popInvoiceAtom).electroInvoice,
            invoicePutTypes: updatedPutTypes,
          },
        })
      } else if (currentInvoiceType === InvoiceType.VAT) {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          vat: {
            ...get(popInvoiceAtom).vat,
            invoicePutTypes: updatedPutTypes,
          },
        })
      }
    }
  }
})

// 发票抬头切换处理
export const handleInvoiceTitleChangeAtom = atom(null, (get, set, title: number) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const type = get(typeAtom)
  const invoiceTitles = get(currentInvoiceTitlesAtom)
  const currentInvoiceType = get(currentInvoiceTypesAtom)?.find((item) => item.selected)?.value
  const updatedTitles = invoiceTitles?.map((item) => ({
    ...item,
    selected: item.value === title,
  }))
  // 根据业务场景更新发票抬头
  if (selectedInvoiceStatus === VenderType.MIX) {
    // 混合场景: 自营 + POP
    if (activeInvoiceTab === VenderType.SELF) {
      if (currentInvoiceType === InvoiceType.NORMAL) {
        // 更新自营发票抬头
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          normalInvoice: {
            ...get(selfInvoiceAtom).normalInvoice,
            invoiceTitles: updatedTitles,
          },
        })
      } else if (currentInvoiceType === InvoiceType.ELECTRONIC) {
        set(selfInvoiceAtom, {
          ...get(selfInvoiceAtom),
          electroInvoice: {
            ...get(selfInvoiceAtom).electroInvoice,
            invoiceTitles: updatedTitles,
          },
        })
      }
    } else if (activeInvoiceTab === VenderType.POP) {
      // 更新POP发票抬头
      if (currentInvoiceType === InvoiceType.NORMAL) {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          normalInvoice: {
            ...get(popInvoiceAtom).normalInvoice,
            invoiceTitles: updatedTitles,
          },
        })
      } else if (currentInvoiceType === InvoiceType.ELECTRONIC) {
        set(popInvoiceAtom, {
          ...get(popInvoiceAtom),
          electroInvoice: {
            ...get(popInvoiceAtom).electroInvoice,
            invoiceTitles: updatedTitles,
          },
        })
      }
    }
  } else {
    // 获取当前发票抬头选项
    const currentInvoiceType = get(currentInvoiceTypesAtom)?.find((item) => item.selected)?.value
    if (currentInvoiceType === InvoiceType.NORMAL) {
      set(type === VenderType.SELF ? selfInvoiceAtom : popInvoiceAtom, {
        ...get(type === VenderType.SELF ? selfInvoiceAtom : popInvoiceAtom),
        normalInvoice: {
          ...get(type === VenderType.SELF ? selfInvoiceAtom : popInvoiceAtom).normalInvoice,
          invoiceTitles: updatedTitles,
        },
      })
    } else if (currentInvoiceType === InvoiceType.ELECTRONIC) {
      set(type === VenderType.SELF ? selfInvoiceAtom : popInvoiceAtom, {
        ...get(type === VenderType.SELF ? selfInvoiceAtom : popInvoiceAtom),
        electroInvoice: {
          ...get(type === VenderType.SELF ? selfInvoiceAtom : popInvoiceAtom).electroInvoice,
          invoiceTitles: updatedTitles,
        },
      })
    }
  }
})

// 发票内容切换处理
export const handleInvoiceContentChangeAtom = atom(null, (get, set, payload: { contentValue: number; contentType?: 'normal' | 'book' }) => {
  const { contentValue, contentType = 'normal' } = payload
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const selectedInvoiceType = get(selectedInvoiceTypeAtom)

  // 确定是处理普通发票内容还是图书发票内容
  if (contentType === 'normal') {
    // 处理普通发票内容
    const normalInvoiceContents = get(currentNormalInvoiceContentsAtom)
    if (!normalInvoiceContents) {
      console.warn('发票内容为空，无法更新')
      return
    }
    const updatedNormalContents = normalInvoiceContents.map((item) => ({
      ...item,
      selected: item.value === contentValue,
    }))

    // 根据发票类型和业务场景进行更新
    if (selectedInvoiceStatus === VenderType.MIX) {
      // 混合场景：自营 + POP
      if (selectedInvoiceType === InvoiceType.ELECTRONIC) {
        // 电子普通发票
        const selfInvoice = get(selfInvoiceAtom) || {}
        set(selfInvoiceAtom, {
          ...selfInvoice,
          electroInvoice: {
            ...selfInvoice.electroInvoice,
            normalInvoiceContents: updatedNormalContents as InvoiceTypeVO[],
          },
        })
      } else if (selectedInvoiceType === InvoiceType.NORMAL) {
        // 纸质普通发票
        const popInvoice = get(popInvoiceAtom) || {}
        set(popInvoiceAtom, {
          ...popInvoice,
          normalInvoice: {
            ...popInvoice.normalInvoice,
            normalInvoiceContents: updatedNormalContents,
          },
        })
      } else if (selectedInvoiceType === InvoiceType.VAT || selectedInvoiceType === InvoiceType.E_VAT) {
        // 专用发票或电子专用发票
        const selfInvoice = get(selfInvoiceAtom) || {}
        set(selfInvoiceAtom, {
          ...selfInvoice,
          vat: {
            ...selfInvoice.vat,
            normalInvoiceContents: updatedNormalContents as InvoiceTypeVO[],
          },
        })
      }
    } else {
      // 非混合场景：纯自营或纯POP
      if (selectedInvoiceType === InvoiceType.ELECTRONIC) {
        // 电子普通发票
        const selfInvoice = get(selfInvoiceAtom) || {}
        set(selfInvoiceAtom, {
          ...selfInvoice,
          electroInvoice: {
            ...selfInvoice.electroInvoice,
            normalInvoiceContents: updatedNormalContents as InvoiceTypeVO[],
          },
        })
      } else if (selectedInvoiceType === InvoiceType.NORMAL) {
        // 纸质普通发票
        const popInvoice = get(popInvoiceAtom) || {}
        set(popInvoiceAtom, {
          ...popInvoice,
          normalInvoice: {
            ...popInvoice.normalInvoice,
            normalInvoiceContents: updatedNormalContents,
          },
        })
      } else if (selectedInvoiceType === InvoiceType.VAT || selectedInvoiceType === InvoiceType.E_VAT) {
        // 专用发票或电子专用发票
        const selfInvoice = get(selfInvoiceAtom) || {}
        set(selfInvoiceAtom, {
          ...selfInvoice,
          vat: {
            ...selfInvoice.vat,
            normalInvoiceContents: updatedNormalContents as InvoiceTypeVO[],
          },
        })
      }
    }
  } else {
    // 处理图书发票内容
    const bookInvoiceContents = get(selfInvoiceAtom)?.electroInvoice?.bookInvoiceContents
    // 如果bookInvoiceContents为undefined或null，则直接返回
    if (!bookInvoiceContents) {
      console.warn('图书发票内容为空，无法更新')
      return
    }

    const updatedBookContents = bookInvoiceContents.map((item) => ({
      ...item,
      selected: item.value === contentValue,
    }))

    // 根据发票类型和业务场景进行更新
    if (selectedInvoiceStatus === VenderType.MIX) {
      // 混合场景：自营 + POP
      if (selectedInvoiceType === InvoiceType.ELECTRONIC) {
        // 电子普通发票
        const selfInvoice = get(selfInvoiceAtom) || {}
        set(selfInvoiceAtom, {
          ...selfInvoice,
          electroInvoice: {
            ...selfInvoice.electroInvoice,
            bookInvoiceContents: updatedBookContents,
          },
        })
      } else if (selectedInvoiceType === InvoiceType.NORMAL) {
        // 纸质普通发票
        const popInvoice = get(popInvoiceAtom) || {}
        set(popInvoiceAtom, {
          ...popInvoice,
          normalInvoice: {
            ...popInvoice.normalInvoice,
            bookInvoiceContents: updatedBookContents,
          },
        })
      } else if (selectedInvoiceType === InvoiceType.VAT || selectedInvoiceType === InvoiceType.E_VAT) {
        // 专用发票或电子专用发票
        const selfInvoice = get(selfInvoiceAtom) || {}
        set(selfInvoiceAtom, {
          ...selfInvoice,
          vat: {
            ...selfInvoice.vat,
            bookInvoiceContents: updatedBookContents,
          },
        })
      }
    } else {
      // 非混合场景：纯自营或纯POP
      if (selectedInvoiceType === InvoiceType.ELECTRONIC) {
        // 电子普通发票
        const selfInvoice = get(selfInvoiceAtom) || {}
        set(selfInvoiceAtom, {
          ...selfInvoice,
          electroInvoice: {
            ...selfInvoice.electroInvoice,
            bookInvoiceContents: updatedBookContents,
          },
        })
      } else if (selectedInvoiceType === InvoiceType.NORMAL) {
        // 纸质普通发票
        const popInvoice = get(popInvoiceAtom) || {}
        set(popInvoiceAtom, {
          ...popInvoice,
          normalInvoice: {
            ...popInvoice.normalInvoice,
            bookInvoiceContents: updatedBookContents,
          },
        })
      } else if (selectedInvoiceType === InvoiceType.VAT || selectedInvoiceType === InvoiceType.E_VAT) {
        // 专用发票或电子专用发票
        const selfInvoice = get(selfInvoiceAtom) || {}
        set(selfInvoiceAtom, {
          ...selfInvoice,
          vat: {
            ...selfInvoice.vat,
            bookInvoiceContents: updatedBookContents,
          },
        })
      }
    }
  }
})

// 更新常用发票列表
export const updateUsualInvoiceListAtom = atom(null, (get, set, payload: { invoiceList: UsualInvoiceItem[] }) => {
  const { invoiceList } = payload
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const type = get(typeAtom)
  if (selectedInvoiceStatus === VenderType.MIX) {
    if (activeInvoiceTab === VenderType.SELF) {
      set(selfInvoiceAtom, {
        ...get(selfInvoiceAtom),
        usualInvoiceList: invoiceList,
      })
    } else if (activeInvoiceTab === VenderType.POP) {
      set(popInvoiceAtom, {
        ...get(popInvoiceAtom),
        usualInvoiceList: invoiceList,
      })
    }
  } else {
    if (type === VenderType.SELF) {
      set(selfInvoiceAtom, {
        ...get(selfInvoiceAtom),
        usualInvoiceList: invoiceList,
      })
    } else if (type === VenderType.POP) {
      set(popInvoiceAtom, {
        ...get(popInvoiceAtom),
        usualInvoiceList: invoiceList,
      })
    }
  }
})
// 更新个人常用发票列表
export const updatePersonalUsualInvoiceListAtom = atom(null, (get, set, payload: { invoiceList: UsualInvoiceItem[] }) => {
  const { invoiceList } = payload
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const type = get(typeAtom)
  if (selectedInvoiceStatus === VenderType.MIX) {
    if (activeInvoiceTab === VenderType.SELF) {
      set(selfInvoiceAtom, {
        ...get(selfInvoiceAtom),
        personalUsualInvoiceList: invoiceList,
      })
    } else if (activeInvoiceTab === VenderType.POP) {
      set(popInvoiceAtom, {
        ...get(popInvoiceAtom),
        personalUsualInvoiceList: invoiceList,
      })
    }
  } else {
    if (type === VenderType.SELF) {
      set(selfInvoiceAtom, {
        ...get(selfInvoiceAtom),
        personalUsualInvoiceList: invoiceList,
      })
    } else if (type === VenderType.POP) {
      set(popInvoiceAtom, {
        ...get(popInvoiceAtom),
        personalUsualInvoiceList: invoiceList,
      })
    }
  }
})
