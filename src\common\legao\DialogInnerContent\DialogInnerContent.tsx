import { memo } from 'react'
/* eslint-disable react/display-name */
interface IDialogContentProps {
  type?: string
  IconType?: string
  submitBtnText?: string
  cancelBtnText?: string
  innerText: JSX.Element
  dialogSubmit?: () => void
  dialogCancel?: () => void
  style?: object
  height: number
}

export default memo((props: IDialogContentProps) => {
  const height = props?.height - 130 > 0 ? props?.height - 130 : 'auto'
  return (
    <div className="ui-dialog-content" style={{ height, overflowY: 'auto' }}>
      <div className="common-tips-dialog" style={props.style}>
        {props.type && (
          <div className="common-tips-icon-cont">
            <i className={`common-tips-icon ${props.type === 'succ' ? 'succ' : props.type === 'confirm' ? 'confirm' : 'error'}`} />
          </div>
        )}
        {props.innerText}
        {!!(props.submitBtnText || props.cancelBtnText) && (
          <p className="common-tips-opts">
            {props.cancelBtnText && (
              <a href="#none" className={`comon-tips-btn no ${props.submitBtnText ? 'mr10' : ''}`} onClick={props.dialogCancel}>
                {props.cancelBtnText}
              </a>
            )}
            {props.submitBtnText && (
              <a href="#none" className="comon-tips-btn yes" onClick={props.dialogSubmit}>
                {props.submitBtnText}
              </a>
            )}
          </p>
        )}
      </div>
    </div>
  )
})
