/**
 * 包裹列表
 */
import scope from './index.module.scss'
import { ErrorBoundary } from 'react-error-boundary'
import { useMasterData } from '@app/context/masterContext'
import Delivery from '@app/components/Delivery'

import Nav from './Nav'
import Shop from './Shop'
import Goods from './Goods'
import Suit from './Suit'
import Promotion from './Promotion'

const Shopping: React.FC = () => {
  const { balanceVendorBundleList, balanceShopInfos, newRemarkVOMap, pageLinkConfig, balanceSkuPriceDesc } = useMasterData()?.body || {}

  // 包裹不存在或空列表不展示订单信息楼层
  if (!balanceVendorBundleList || !balanceVendorBundleList?.length) {
    return null
  }

  return (
    <div className={scope.shopping}>
      <Nav pageLinkConfig={pageLinkConfig} balanceSkuPriceDesc={balanceSkuPriceDesc} />
      <div className={scope.list}>
        {balanceVendorBundleList?.map((vendor, vendorIndex) => (
          <div className="item" key={vendorIndex}>
            <div className={scope['bundle-list']}>
              {vendor?.bundleList?.map((bundle, bundleIndex) => (
                <div className={scope['bundle-item']} key={bundleIndex}>
                  <Shop name={vendor.venderName} isSelf={vendor.isSelfVendor}>
                    <Delivery
                      initState={{
                        jdcombineStoreId: vendor.jdcombineStoreId,
                        storeId: vendor.storeId,
                        venderId: vendor.venderId,
                        isSelfVendor: vendor.isSelfVendor,
                        bundle: bundle,
                        newRemarkVOMap: newRemarkVOMap,
                      }}
                      hasDelivery={true}
                    />
                  </Shop>

                  <div className={scope['production-list']}>
                    {bundle?.productionList?.map((production, productionIndex) => (
                      <div key={productionIndex} className="production-item">
                        {/* 套装促销 */}
                        {production.promotionDesc && <Promotion production={production} />}
                        {/* 虚拟套装 */}
                        {production.type === 23 && <Suit production={production} />}
                        <div className="sku-list">
                          {production?.balanceSkuList?.map((sku, skuIndex) => (
                            <div className="sku-item" key={skuIndex}>
                              {/* 商品信息 */}
                              <Goods
                                bundleId={bundle.bundleId}
                                sku={sku}
                                productionType={production.type}
                                balanceShopInfos={balanceShopInfos}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  <Delivery
                    initState={{
                      jdcombineStoreId: vendor.jdcombineStoreId,
                      storeId: vendor.storeId,
                      venderId: vendor.venderId,
                      isSelfVendor: vendor.isSelfVendor,
                      bundle: bundle,
                      newRemarkVOMap: newRemarkVOMap,
                    }}
                    hasDelivery={false}
                  />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

const CatchError = () => {
  const fallbackRender = () => {
    console.warn('商品白屏')
    // 插入白屏DOM
    return <div id="shopping-white-screen"></div>
  }

  return (
    <ErrorBoundary fallbackRender={fallbackRender}>
      <Shopping />
    </ErrorBoundary>
  )
}

export default CatchError
