import { FC, useState } from 'react'
import { useAtomValue } from 'jotai'
import './InvoiceProductList.scss'
import { selectedInvoiceStatusAtom, popSkuListAtom, selfSkuListAtom } from '@app/components/Invoice/atom/invoiceAtom'

/**
 * 发票商品列表展示组件
 * 后续如有需求需要展示不开发票的样式可以解开注释
 */
interface InvoiceProductListProps {
  activeTab: number
}

const InvoiceProductList: FC<InvoiceProductListProps> = ({ activeTab }) => {
  const [isSelfShowAll, setIsSelfShowAll] = useState(false)
  const [isPopShowAll, setIsPopShowAll] = useState(false)
  const selfProducts = useAtomValue(selfSkuListAtom)
  const popProducts = useAtomValue(popSkuListAtom)
  const selectedInvoiceStatus = useAtomValue(selectedInvoiceStatusAtom)
  // 不支持专票的商品IDs
  // const unsupportEleVatSkuIds = useAtomValue(currentUnsupportEleVatSkuIds)
  if (selfProducts.length === 0 && popProducts.length === 0 && selectedInvoiceStatus !== 2) return null
  return (
    <div className="products-container">
      <div className="products-header">共{activeTab === 0 ? selfProducts.length : popProducts.length}件商品开票</div>
      <div className="products-list">
        {activeTab === 0
          ? selfProducts.slice(0, isSelfShowAll ? selfProducts.length : 10).map((product, index) => (
              <div key={`self-${product.skuId}-${index}`} className="product-item">
                <div className="product-container">
                  <img
                    className="product-image"
                    src={product.skuImgUrl}
                    alt={product.skuName}
                    // onClick={() => {
                    //   window.open(`https://item.jd.com/${product.skuId}.html`, '_blank')
                    // }}
                  />
                </div>
                {/* <div className="product-status">
                  <span className="statusText">不可开票</span>
                  <img
                    className="helpIcon"
                    src="//img20.360buyimg.com/ling/jfs/t1/292849/17/10970/562/68415240F85d13905/daf282b799f825ff.png"
                  />
                </div> */}
                {/* <div className="product-name">{product.name}</div> */}
              </div>
            ))
          : popProducts.slice(0, isPopShowAll ? popProducts.length : 10).map((product, index) => (
              <div key={`pop-${product.skuId}-${index}`} className="product-item">
                <div className="product-container">
                  <img
                    className="product-image"
                    src={product.skuImgUrl}
                    alt={product.skuName}
                    // onClick={() => {
                    //   window.open(`https://item.jd.com/${product.skuId}.html`, '_blank')
                    // }}
                  />
                </div>
                {/* <div className="product-name">{product.name}</div> */}
              </div>
            ))}
      </div>
      {activeTab === 0
        ? selfProducts.length > 10 && (
            <div className="products-footer" onClick={() => setIsSelfShowAll(!isSelfShowAll)}>
              {/* 当商品数量大于10时，显示收起和展示全部商品 */}
              {isSelfShowAll ? (
                <>
                  收起
                  <i className="arrow-up"></i>
                </>
              ) : (
                <>
                  展示全部商品
                  <i className="arrow-down"></i>
                </>
              )}
            </div>
          )
        : popProducts.length > 10 && (
            <div className="products-footer" onClick={() => setIsPopShowAll(!isPopShowAll)}>
              {/* 当商品数量大于10时，显示收起和展示全部商品 */}
              {isPopShowAll ? (
                <>
                  收起
                  <i className="arrow-up"></i>
                </>
              ) : (
                <>
                  展示全部商品
                  <i className="arrow-down"></i>
                </>
              )}
            </div>
          )}
    </div>
  )
}

export default InvoiceProductList
