/**
 * 附近地址列表
 */
declare namespace AddressStandard {
  export interface NearbyTypes {
    /**
     * 导航标题
     */
    navigationTitle?: string
    /**
     * 附近地址列表
     */
    nearbyAddressList?: Array<NearbyListTypes>
    /**
     * 调转原生开关（true跳，false不跳）
     */
    jumpAppSwitch?: boolean
    /**
     * 降级控制逻辑
     */
    abResult?: IABResult[]
  }
  export interface NearbyListTypes {
    /**
     * 省ID
     */
    provinceId: number
    /**
     * 省名称
     */
    provinceName: string
    /**
     * 区ID
     */
    cityId: number
    /**
     * 区名称
     */
    cityName: string
    /** 县区域id */
    countyId: number
    /** 县区域名称 */
    countyName: string
    /** 乡镇级地址id */
    townId: number
    /** 乡镇级地址名称 */
    townName: string
    /**
     * 加密后的经纬度
     */
    latitudeString?: string
    /**
     * 加密后的经纬度
     */
    longitudeString?: string
    /**
     * 短地址
     */
    shortAddress: string
    /**
     * 详细地址
     */
    detailAddress: string
    /**
     * 获取定位下发的字段是addressDetail
     */
    addressDetail?: string
  }
}
