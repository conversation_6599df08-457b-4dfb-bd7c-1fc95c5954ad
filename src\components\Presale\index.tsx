import { ErrorBoundary } from 'react-error-boundary'
import Presale from './presale'
import Skeleton from './skeleton'
import { useAtomValue } from 'jotai'
import { loadingAtom } from '@app/atoms/loadingAtom'
import { getMasterApiParams } from '@app/services/parameters'
const PRESALE_BIZ = !!getMasterApiParams(true)?.balanceCommonOrderForm?.preSalePaymentTypeInOptional
import reportException from './report_exception'

const Index: React.FC = () => {
  const loading = useAtomValue(loadingAtom)
  if (PRESALE_BIZ) {
    return (
      <ErrorBoundary 
        FallbackComponent={Skeleton} 
        onError={(error, info) => reportException({ error, info, sence: 'ErrorBoundary' })}
      >
        {loading ? <Skeleton /> : <Presale />}
      </ErrorBoundary>
    )
  }
  return null
}

Index.displayName = 'PresaleModule'

export default Index
