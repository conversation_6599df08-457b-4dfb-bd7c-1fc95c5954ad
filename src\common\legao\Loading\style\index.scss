.jd-loading {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  min-height: 100px;
  height: 100% ;
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  background: #fff;
  &.empty {
    background: transparent;
  }
  .jd-spin-dot {
    position: absolute;
    display: inline-block;
    font-size: 20px;
    width: 1em;
    height: 1em;
    top: 50%;
    left: 50%;
    margin: -10px;
    &.jd-spin-dot-spin {
      transform: rotate(0);
      animation: antRotate 1.2s infinite linear;
    }
    .jd-spin-dot-item {
      position: absolute;
      display: block;
      width: 9px;
      height: 9px;
      background-color: #ff0f23;
      border-radius: 100%;
      transform: scale(0.75);
      transform-origin: 50% 50%;
      opacity: 0.3;
      animation: antSpinMove 1s infinite linear alternate;
      &:nth-child(1) {
        top: 0;
        left: 0;
      }
      &:nth-child(2) {
        top: 0;
        right: 0;
        animation-delay: 0.4s;
      }
      &:nth-child(3) {
        right: 0;
        bottom: 0;
        animation-delay: 0.8s;
      }
      &:nth-child(4) {
        bottom: 0;
        left: 0;
        animation-delay: 1.2s;
      }
    }
  }
  .cs-loading,
  .cs-error-joy {
    margin: 0 auto;
  }
  .cs-loading {
    width: 40px;
    height: 40px;
    /* background: url(./i/loading.gif) 0px 0px no-repeat; */
  }
  .cs-error-joy {
    width: 80px;
    height: 80px;
    /* background: url(./i/cs-joy.png) 0px 0px no-repeat; */
  }
  &-mask {
    height: 100%;
    top: 0;
    width: 100%;
    left: 0;
    z-index: 9;
    position: absolute;
    transition: opacity .3s;

    &:after {
      position: absolute;
      inset: 0;
      z-index: 10;
      /* display: none \; */
      width: 100%;
      height: 100%;
      background: #fff;
      opacity: 0;
      transition: all .3s;
      content: "";
      pointer-events: none;
    }
  }
  &-blur {
    clear: both;
    opacity: .9;
    user-select: none;
    pointer-events: none;
    &:after {
      opacity: .8;
      pointer-events: auto;
    }
  }
}


@keyframes antSpinMove {
  to {
    opacity: 1;
  }
}

@keyframes antRotate {
  to {
    transform: rotate(360deg);
  }
}
