/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-03 14:54:13
 * @LastEditors: ext.wangchao120
 * @Description: 领货码-卡片
 * @FilePath: /pc_settlement/src/components/VirtualAsset/DeliveryCode/Card/index.tsx
 */
import { ReactNode, FC, useEffect } from 'react'
import Tooltip from '@app/common/Tooltip'
import styles from './index.module.scss'
import classNames from 'classnames'
import { SELECTED_ICON } from '../../constants'

interface PropsType {
  active?: boolean
  item: any
  disabled?: boolean
  onClick?: (type: string, item: any) => void
  loadingDom?: (id: string) => void
  tabKey?: string
  activeKey?: string
}

const Card: FC<PropsType> = ({ item, disabled, onClick, loadingDom, tabKey, activeKey }) => {
  // 提示内容
  const dropDownCont = (
    <>
      <div className={styles.tips}>{disabled ? item?.disableDesc : item?.ableDesc}</div>
    </>
  ) as ReactNode

  // 面值
  const limit = (
    <div className={styles.limit}>
      <span>面值</span>
      <i>&yen;</i>
      {item?.amount}
    </div>
  ) as ReactNode

  const curUsedMoney = (
    <div className={styles.curUsedMoney}>
      <i>本次使用</i>
      <span>&yen;{item?.curUsedMoney}</span>
    </div>
  ) as ReactNode

  useEffect(() => {
    if (tabKey === 'deliveryCode' && activeKey !== 'add') {
      loadingDom && loadingDom(item?.id)
    }
  }, [tabKey, activeKey])

  return (
    <div
      className={classNames(styles.cardWarp, `${disabled ? 'un' : ''}redeemcode`)}
      data-point-id={item?.id}
      data-point-clerk={item?.cardBrandName}
    >
      <div className={classNames(styles.cardItem, disabled && styles.disabled)} onClick={() => onClick?.('accesskey', item)}>
        <div className={styles.title}>
          <div className={styles.flex}>
            <div className={styles.icon}></div>
            <span>京东领货码</span>
          </div>
          {/* 已勾选 */}
          {item?.selected && <img src={SELECTED_ICON} />}
        </div>
        <div className={styles.balance}>
          <span>余</span>
          <div className={styles.price}>
            <i>&yen;</i>
            {item?.leaveMoney}
          </div>
          {limit}
        </div>

        <div className={styles.date}>有效期至{item?.timeEnd}</div>

        <Tooltip width={254} padding={'16px'} content={dropDownCont} type="popover" className={styles.tooltip}>
          <div className={styles.selfOperated}>
            大部分自营商品可用
            <i></i>
          </div>
        </Tooltip>
      </div>

      {/* 当选择且有抵扣金额时显示 */}
      {item?.selected && item?.curUsedMoney && curUsedMoney}
    </div>
  )
}

export default Card
