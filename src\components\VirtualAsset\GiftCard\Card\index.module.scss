.cardWarp {
  display: flex;
  flex-direction: column;
}
.cardItem {
  width: 255px;
  height: 140px;
  position: relative;
  box-sizing: border-box;
  border: 0.5px solid linear-gradient(180deg, rgba(255, 15, 35, 0.1) 0%, rgba(255, 15, 35, 0) 100%);
  border-radius: 8px;
  background: linear-gradient(180deg, rgba(255, 235, 237, 1) 0%, rgba(255, 242, 243, 1) 100%),
    linear-gradient(0deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1));
  padding-top: 12px;
  padding-left: 12px;
  cursor: pointer;

  &::after {
    content: '';
    width: 100px;
    height: 60px;
    position: absolute;
    display: block;
    background: url('https://img13.360buyimg.com/img/jfs/t1/277967/7/14294/3645/67ebbdf9F467342f9/def55c517bf7496c.png') no-repeat;
    background-size: 100% 100%;
    right: 0;
    bottom: 8px;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #E53029;
    font-size: 14px;
    padding-right: 12px;
    overflow: hidden;
    .icon {
      width: 24px;
      height: 24px;
      border-radius: 12px;
      background: #E53029;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px;

      &::after {
        content: '';
        display: inline-block;
        width: 15px;
        height: 10px;
        background: url('https://img10.360buyimg.com/img/jfs/t1/282661/25/12601/495/67eb9e9aF32bc8889/3d3efd03dffb2556.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .flex {
      display: flex;
      align-items: center;
      span {
        white-space: nowrap;
      }
    }
    img {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 16px;
      height: 16px;
    }
  }
  .limit {
    font-size: 12px;
    font-weight: 400;
    font-family: JDZhengHeiVHeavy2;
    white-space: nowrap;

    span {
      font-size: 12px;
      font-weight: normal;
      margin-right: 4px;
      font-family: PingFang SC;
    }
  }
  .balance {
    display: flex;
    margin-top: 16px;
    align-items: flex-end;
    padding-left: 4px;
    color: #E53029;

    >span {
      font-size: 14px;
      margin-right: 4px;
      font-weight: 400;
    }

    .price {
      font-size: 20px;
      font-family: JDZhengHeiVHeavy2;
      font-weight: 400;
      margin-right: 8px;

      i,
      span {
        font-size: 16px;
      }
    }
  }

  .date {
    color: #FF8595;
    font-size: 12px;
    font-weight: 400;
    margin-top: 8px;
    padding-left: 4px;

  }
  .selfOperated {
    width: 255px;
    height: 28px;
    line-height: 28px;
    padding: 0 16px;
    background: #FFE5E8;
    position: absolute;
    z-index: 1;
    left: 0;
    bottom: 0;
    box-sizing: border-box;
    color: #FF8595;
    font-size: 12px;
    font-weight: 400;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;

    i {
      background: url('https://img11.360buyimg.com/img/jfs/t1/275723/10/14149/428/67ebdf3dF20e1aa2b/db334a26e81b3857.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 8px;
      height: 8px;
      display: inline-block;
      margin-left: 4px;
    }

    &:hover {
      i {
        transform: rotate(180deg);
      }
    }
  }

  &.disabled {
    cursor: not-allowed;
    border-color: linear-gradient(180deg, rgba(225, 226, 229, 1) 0%, rgba(225, 226, 229, 0) 100%);
    background: #F7F8FC;

    &::after {
      background: url('https://img11.360buyimg.com/img/jfs/t1/279112/11/14010/2867/67ebe382F9cd8205b/1f66c1f041a8e953.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .title {
      color: #888B94;

      .icon {
        background: #C2C4CC;
      }
    }

    .balance,
    .date,
    .selfOperated {
      color: #888B94;
    }

    .selfOperated {
      background: #F0F1F5;

      i {
        background: url('https://img11.360buyimg.com/img/jfs/t1/283960/13/12459/472/67ebdd85F79776590/10074a8ae5ebe40e.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }

    }
  }

}
.tips {
  color: #888B94;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
}

.tooltip {
  position: initial;
}
.curUsedMoney {
  // position: absolute;
  // bottom: -20px;
  // left: 0;
  height: 20px;
  padding-top: 8px;
  color: #888B94;
  font-size: 12px;
  font-weight: 400;
  i {
    margin-right: 2px;
    font-weight: 400;
  }
  // span {
  //   color: var(--custom-color-red);
  // }
}