import React from 'react'
import { Component, PropTypes } from '../libs'
import SelectContext from '@app/common/legao/Context'
export default class OptionGroup extends Component {
  static contextType = SelectContext

  render() {
    const visibleChildren = this.context.component.state.options.filter((m) => !!m.state.visible && m.props.sortCode === this.props.label)
    return (
      <ul style={this.style()} className={this.className('el-select-group__wrap')}>
        {!!visibleChildren.length && <li className="el-select-group__title">{this.props.label}</li>}
        <li>
          <ul className="el-select-group">{this.props.children}</ul>
        </li>
      </ul>
    )
  }
}

OptionGroup.propTypes = {
  label: PropTypes.string,
}
