import AgreementDialog from './AgreementDialog'
import { popup } from '../popup'
import type { ModalProps } from '../popup'
import type { AgreementDialogProps } from './AgreementDialog'

const defaultOptions: ModalProps['options'] = {
  mask: true,
  position: 'center-center',
  wrapperStyle: { zIndex: 100 },
}

export const popup_agreement_dialog = (props?: AgreementDialogProps, options?: ModalProps['options']) =>
  popup(<AgreementDialog {...props} />, { ...defaultOptions, ...options })
