import { FC, useCallback } from 'react'
import { useAtomValue } from 'jotai'
import { currentInvoiceTypesAtom, selectedInvoiceTypeAtom, currentInvoiceSupportAtom } from '../atom/invoiceAtom'
import { InvoiceTypeVO, InvoiceType } from '@app/typings/invoice.d'
import { Form } from '@app/common/legao'
import Tooltip from '@app/common/Tooltip'

/**
 * 发票类型选择器组件
 */
interface InvoiceTypeSelectorProps {
  onChange: (type: number) => void
}

const InvoiceTypeSelector: FC<InvoiceTypeSelectorProps> = ({ onChange }) => {
  // 获取当前的发票抬头
  const invoiceTypes = useAtomValue(currentInvoiceTypesAtom)
  // 获取当前选中的发票类型值
  const selectedTypeValue = useAtomValue(selectedInvoiceTypeAtom)

  // 获取支持状态
  const { supportVatMessage, supportElectroMessage, supportEleVatMessage } = useAtomValue(currentInvoiceSupportAtom)

  /**
   * 处理发票类型变化
   */
  const handleTypeChange = useCallback(
    (type: number) => {
      if (type === selectedTypeValue) return
      onChange(type)
    },
    [onChange, selectedTypeValue],
  )

  // 获取禁用原因
  const getDisabledReason = (type: InvoiceTypeVO) => {
    if (!type.disabled) return ''

    switch (type.value) {
      case InvoiceType.VAT:
        return supportVatMessage || '部分商品不支持开具专用发票'
      case InvoiceType.ELECTRONIC:
        return supportElectroMessage || '当前商品不支持开具电子普通发票'
      case InvoiceType.E_VAT:
        return supportEleVatMessage || '当前商品不支持开具电子增值税发票'
      default:
        return ''
    }
  }

  /**
   * 过滤出应该显示的发票类型
   * 这里是后端要求的，需要根据对应的 value 来展示固定的内容，这里是维持老版线上的发票类型。
   * */
  const visibleTypes = invoiceTypes?.map((item) => {
    if (item.value === InvoiceType.NORMAL) {
      return {
        ...item,
        content: '纸质普票',
      }
    }
    if (item.value === InvoiceType.ELECTRONIC) {
      return {
        ...item,
        content: '电子普通发票',
      }
    }
    if (item.value === InvoiceType.VAT) {
      return {
        ...item,
        content: '专用发票',
      }
    }
    if (item.value === InvoiceType.E_VAT) {
      return {
        ...item,
        content: '电子增值税发票',
      }
    }
    return item
  })
  visibleTypes?.sort((a, b) => (b.content?.length || 0) - (a.content?.length || 0))
  const selectedType = visibleTypes?.find((item) => item.value === selectedTypeValue)
  return (
    <Form.Item label="发票类型" required>
      <div className="radio-group" style={{ flexGrow: 1 }}>
        {visibleTypes?.map((type) => {
          const isDisabled = !!type.disabled
          const disabledReason = getDisabledReason(type as InvoiceTypeVO)
          const isSelected = type.value === selectedTypeValue

          // 如果禁用，使用Tooltip包裹
          if (isDisabled) {
            return (
              <Tooltip key={type.value} content={disabledReason} placement="top" arrow={true} trigger="hover" type="tooltip">
                <div className={`radio-button ${isSelected ? 'active' : ''} disabled`}>{type.content}</div>
              </Tooltip>
            )
          }

          // 未禁用的正常渲染
          return (
            <div key={type.value} className={`radio-button ${isSelected ? 'active' : ''}`} onClick={() => handleTypeChange(type.value)}>
              {type.content}
            </div>
          )
        })}
      </div>
      {selectedType && selectedType?.descLabel && selectedType?.descLabel !== '' && (
        <div className="invoice-note">
          {/* <Tooltip
            content={<div dangerouslySetInnerHTML={{ __html: selectedType?.descLabel.trim() }}></div>}
            placement="top"
            arrow={true}
            trigger="hover"
            type="tooltip"
          >
            <img src={'//img13.360buyimg.com/ling/jfs/t1/319798/31/6255/735/683ee395Fbd94e235/be602901616ec835.png'} alt="noticeArrow" />
          </Tooltip> */}
          <span
            className="invoice-note-text"
            dangerouslySetInnerHTML={{
              __html: selectedType?.descLabel,
            }}
          ></span>
        </div>
      )}
    </Form.Item>
  )
}

export default InvoiceTypeSelector
