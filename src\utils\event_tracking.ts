/**
 * @file: event_tracking.ts
 * @description: 埋点上报
 */
import monitoring, { monitorName, monitorCode } from '@app/utils/monitoring'
import { CommonPoint, ExposeBuriedPoints } from './dataSet'

const _DEV_ = import.meta.env.MODE === 'development'

// 公共埋点参数
export const commonPoints = new CommonPoint()

// 合并埋点参数
export const getParams = (params: ESObject): ESObject => {
  return { ...commonPoints.pointData, ...params }
}

function reportException(values: Record<string, any>) {
  monitoring({
    name: monitorName.Settlement,
    code: monitorCode.Other,
    msg: {
      whistleblower: 'event_tracking',
      ...values,
    },
  })
}

/** 业务标识 */
const BIZ_TYPE = 'ord'

// dom观察器配置
const observerOptions: IntersectionObserverInit = {
  threshold: 0.5,
}
/**
 * reportClick   点击上报
 * @param funcId 功能标识
 * @param params 上报参数
 */
function reportClick(funcId: string, params: Record<string, any>) {
  try {
    params = getParams(params)
    if (_DEV_) {
      console.log('点击埋点上报参数>>>', funcId, params)
    }
    window.log(BIZ_TYPE, funcId, JSON.stringify(params))
  } catch (error) {
    reportException({ reportClickException: error, funcId, params })
  }
}

/**
 * reportExpose  曝光上报
 * @param funcId 功能标识
 * @param params 上报参数
 */
function reportExpose(funcId: string, params: Record<string, any>) {
  try {
    params = getParams(params)
    if (_DEV_) {
      console.log('曝光埋点上报参数>>>', funcId, params)
    }
    window.expLogJSON(BIZ_TYPE, funcId, params)
  } catch (error) {
    reportException({ reportExposeException: error, funcId, params })
  }
}

/**
 * 批量曝光一批 dom 元素
 * @param funcId 功能标识
 * @param params 参数
 * @param domInfoList dom元素及相关参数信息的列表
 * @param key 上报数据对应的字段名
 * @param cb 本次上报的回调函数
 * */
const batchExposeDom = (
  funcId: string,
  params: ESObject,
  domInfoList: IExposeDomInfo[],
  key: string,
  cb?: (exposeList: ESObject[]) => void,
): ExposeBuriedPoints | undefined => {
  try {
    if (!domInfoList || domInfoList.length === 0) return
    // dom映射信息
    const domInfoMap = new Map<Element, IExposeDomInfo>()
    // 观察器
    const observer = new ExposeBuriedPoints((entries) => {
      // 需要曝光参数列表
      const exposeList: any = []
      // 额外参数列表
      const extParamList: any = []
      entries.forEach((entry) => {
        // 元素曝光了
        if (entry.isIntersecting && entry.intersectionRatio >= 0.5) {
          const dom = entry.target
          const domInfo = domInfoMap.get(dom)
          if (domInfo) {
            exposeList.push(domInfo.params)
            // 额外参数
            domInfo?.extParams && extParamList.push(domInfo.extParams)
            observer.delete(domInfo.key)
          }
        }
      })
      if (exposeList.length) {
        cb && cb(exposeList)
        if (extParamList.length) {
          // 领货码额外参数
          params.clerk = extParamList
        }
        // 曝光上报
        reportExpose(funcId, { ...params, [key]: exposeList })
      }
    }, observerOptions)
    // 监听dom
    for (const item of domInfoList) {
      domInfoMap.set(item.dom as Element, item)
      observer.add(item)
    }
    return observer
  } catch (err) {
    console.log('src/utils/event_tracking.ts 103', err)
  }
}

export { reportClick, reportExpose, batchExposeDom }
