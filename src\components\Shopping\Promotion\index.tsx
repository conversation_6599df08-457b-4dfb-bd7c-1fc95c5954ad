/**
 * 商品促销
 */
import scope from './index.module.scss'
import { ProductionType } from '@app/typings/master_api_response'

interface IPorps {
  production: ProductionType
}

const Promotion: React.FC<IPorps> = ({ production }) => {
  return (
    <div className={scope.promotion}>
      {production.promotionIcon && <i className={scope.tag}>{production.promotionIcon}</i>}
      <div className="flex flex-1">
        <div className={`text-ellipsis ${scope.name}`}>{production.promotionDesc}</div>
      </div>
    </div>
  )
}

export default Promotion
