/**
 * @file: dingQiGou.tsx
 * @description: 定期购组件，用于展示定期购等逻辑
 */
import { useEffect, useState } from 'react'
import <PERSON>tton from '../../ui/botton.tsx'
import TimeSelector from '../../ui/TimeSelector/timeSelector.tsx'
import style from './index.module.scss'
import { RegularBuyCalendarItem, RegularBuyContentVO } from '@app/typings/master_api_response.js'
import { UiTimeSelector } from '../../ui/TimeSelector/types.ts'
import { api_balance_getRegularBuyPromise_pc } from '@app/services/api.ts'
import { SaveData, saveDeliveryData } from '../saveDelivery.ts'
import { useDelivery } from '../../context.tsx'
import { PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp.js'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring.ts'
import { useMasterData, useUpdateMasterData } from '@app/context/masterContext.tsx'
import toast from '@app/common/toast/index.tsx'

// import  from "../../types.ts";

export type Props = {
  regularBuyCalendarList: RegularBuyCalendarItem[]
  regularBuyContentVO: RegularBuyContentVO
  regularBuyPlan: string
  bundleUUID: string
  onCancel: () => void
}
export default ({ regularBuyCalendarList, onCancel, regularBuyContentVO, regularBuyPlan, bundleUUID }: Props) => {
  const [list, setList] = useState<UiTimeSelector.TimeItem<RegularBuyCalendarItem, any>[]>([])
  const [value, setValue] = useState<RegularBuyCalendarItem | null>(null)
  const deliveryState = useDelivery()
  const { balanceAddress } = useMasterData()?.body || {}
  const updateMasterData = useUpdateMasterData()
  useEffect(() => {
    const _list: UiTimeSelector.TimeItem<RegularBuyCalendarItem, any>[] = []
    regularBuyCalendarList.forEach((item) => {
      _list.push({
        isSelected: item.selected,
        text: item.fullDate,
        targetValue: item,
        items: [
          {
            isSelected: false,
            isDisabled: true,
            textList: [{ style: 'gray', text: '配送详情' }],
            targetValue: '',
          },
        ],
      })
    })
    setList(_list)
  }, [regularBuyCalendarList])

  const onChange = (parent: UiTimeSelector.TimeItem<RegularBuyCalendarItem, any>) => {
    console.log(parent, 'dingqigou onchange')
    if (parent.items.length === 1) {
      const buyInfo = parent.targetValue
      api_balance_getRegularBuyPromise_pc({
        ...regularBuyContentVO,
        firstDeliveryDate: buyInfo.fullDate,
        bundleUUID,
      })
        .then((res) => {
          if (res?.body && Array.isArray(res.body) && res.body.length > 0) {
            res.body.forEach((item) => {
              parent.items[0].textList[0].text = '配送详情'
              parent.items.push({
                isSelected: false,
                isDisabled: true,
                textList: [
                  // { style: 'gray', text: "" },
                  { style: 'none', text: item.periodNumber },
                  { style: 'none', text: item.buyNum },
                  { style: 'none', text: item.sendDate },
                ],
                targetValue: item,
              })
            })
            setList([...list])
          }
        })
        .finally(() => {
          if (parent.items.length === 1 && parent.items[0].textList[0].text === '配送详情') {
            toast.error('服务繁忙，请稍后重试～')
            parent.items[0].textList[0].text = '暂无配送详情'
            setList([...list])
          }
        })
    }
    setValue(parent.targetValue)
  }

  const save = () => {
    // console.log(selectValue, deliveryDataIniter.getDeliveryInfo(), deliveryDataIniter.initState, "selectValue");
    // console.log(selectValue, "selectValue");
    if (!value) {
      onCancel()
      return
    }
    const saveData: SaveData<any> = {
      baseData: deliveryState.state.wrapInitState,
      addrId: balanceAddress?.id.toString() || '',
      currentValue: value,
      saveType: 'dqg',
      promiseItem: (deliveryState.state.wrapInitState.bundle.deliveryInfoVO?.promiseList[0] || {}) as unknown as PromiseListItem,
      shipmentTypeItem: deliveryState.state.wrapInitState.bundle.comShipmentTypeVO,
      deliveryBaseData: {
        ...deliveryState.state.wrapInitState.bundle.deliveryInfoVO,
        venderUuidMap: deliveryState.state.wrapInitState.bundle.venderUuidMap,
        regularBuy: deliveryState.state.wrapInitState.bundle.regularBuy || undefined,
        promiseList: undefined,
      },
    }
    const ret = saveDeliveryData(saveData)
    if (!ret) {
      onCancel()
      return
    } else {
      ret
        .then(() => {
          updateMasterData()
          onCancel()
        })
        .catch((e) => {
          toast.error('保存失败，请稍后再试')
          onCancel()
          monitoring({
            name: monitorName.Settlement,
            code: monitorCode.Delivery,
            msg: {
              type: '配送模块',
              error: e,
              data: saveData,
              info: '保存 定期购失败',
            },
          })
        })
    }
  }

  return (
    <div className={`${style.ding_qi_gou}`}>
      <div className={`${style.title}`}>定期配送计划：{regularBuyPlan}</div>
      <div className={`${style.title2}`}>请选择首次配送日期</div>
      {<TimeSelector onChange={(p) => onChange(p)} list={list} />}
      <div style={{ marginTop: '10px' }}></div>
      {<Botton onSure={() => save()} onCancel={() => onCancel()} />}
    </div>
  )
}
