/**
 * 文件处理文档 https://storage.jd.com/doc/jd-image.pdf
 */
import Cookies from 'js-cookie'

export function checkWebp(): boolean {
  return !!Cookies.get('webp')
}

export function checkAVIF(): boolean {
  return !!Cookies.get('avif')
}

export function checkIsRetain(): boolean {
  if (
    window.devicePixelRatio > 1 ||
    (window.matchMedia &&
      window.matchMedia(
        '(-webkit-min-device-pixel-ratio: 1.5), (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (min-device-pixel-ratio: 1.5), (min-resolution: 144dpi), (min-resolution: 1.5dppx)',
      ).matches)
  ) {
    return true
  }
  return false
}

export interface ImgOptions {
  clip?: string

  resize?: [string, string] | string[]

  quality?: number

  webp?: boolean

  replacem?: boolean | number
}

/**
 * [0] 宽
 * [1] 高
 */
export type Size = [number, number]

export interface Options {
  retina: boolean
  clip: boolean
}

/**
 * 居中裁切图片
 * @param {String} url
 * @param {String} ratio
 * @returns {String}
 */
const clipImage = (url: string, ratio: string) => {
  let qualityParam = ''
  const _url = url.replace(/!cc_\d+x\d+/, '').replace(/(!q\d{0,2}.(jpg|jpeg|png|bmp|webp))/, (m, g1) => {
    qualityParam = g1
    return ''
  })
  return `${_url}!cc_${ratio}${qualityParam}`
}

/**
 * 设置图片质量
 * @param {String} url
 * @param {String} quality
 * @returns {String}
 */
const setImageQuality = (url: string, quality: number) => {
  const q = String((Math.max(+quality, 0) % 100) + 100).substr(1)
  let clip = ''
  const baseUrl = url.replace(/!q[^!]+/, '').replace(/!c[cr][^!]+/, (m) => {
    clip = m
    return ''
  })
  return `${baseUrl}!q${q}${clip}`
}

/**
 * 根据key，hash出图片域名
 * @param {String} key
 * @returns {String}
 */
const pool = [10, 11, 12, 13, 14, 20, 30]
const replaceImageDomain = (url: string) => {
  const key = url && url.split('/').reverse()[0]
  const hash = parseInt(key, 36) % pool.length

  return url.replace(/(\/\/)(m|img\d{1,2})(\.360buyimg\.com)/, '$1' + 'img' + pool[hash] + '$3')
}

/**
 * 设置图片尺寸
 * @param {String} url
 * @param {String} retina
 * @param {String} normal
 * @returns {String}
 */
const resizeImage = (url: string, retina: string, normal: string) =>
  url.replace(/^https?:/, '').replace(/(360buyimg\.com\/[^/]+)\/([^/]+)/, function (m, g1, g2) {
    const businessName = g2.replace(/s\d+x\d+_([\s\S]+)/, (ma: any, gr1: any) => gr1)
    const size = checkIsRetain() ? retina : normal || retina
    return `${g1}/s${size}_${businessName}`
  })

/**
 * 替换为webp链接
 * @param {String} url
 * @returns {String}
 */
const getWebpImage = (url: string) => `${url.replace(/\.webp/g, '')}.webp`

const getAVIFImage = (url: string) => `${url.replace(/\.webp|.avif/g, '')}.avif`
/**
 * 大而全的图片处理函数。
 * @param {String} _url
 * @param {Object} opts
 * @param {Boolean|String} opts.clip
 * @param {Boolean|String[]} opts.resize
 * @param {Boolean|Number} opts.quality
 * @param {Boolean|Number} opts.replacem
 * @param {Boolean} opts.webp
 * @returns {String}
 */
export default function (url: string, options: ImgOptions = {}): string {
  if (!url) return ''

  if (url.indexOf('data:image') === 0 && url.indexOf('base64') !== -1) {
    return url
  }

  // 如果传入图片为 webp 需要获取初始地址
  url = url.replace(/\.webp$/, '')

  // 去除链接中空白字符
  url = url.replace(/\s+/g, '')

  // Gif 图不做后续处理
  if (/\.gif/i.test(url)) return url

  // 如果不包含 m.360buyimg.com 或 imgxx.360buyimg.com 则给url加上前缀
  if (!/^(https?:)?\/\//.test(url) && !/(m|img\d{1,2})\.360buyimg\.com/.test(url)) {
    url = '//img10.360buyimg.com/img/' + url
  }

  // 非JFS 或CDN 图片不处理
  if (!/(m|img\d{1,2})\.360buyimg\.com/.test(url) || !/\.(jpg|jpeg|png|webp)/.test(url)) {
    return url
  }

  const { clip, resize, quality, webp = true } = options

  // 根据key，hash出图片域名
  url = replaceImageDomain(url)

  if (resize && Array.isArray(resize)) {
    const [retina, normal = retina] = resize
    // 设置图片尺寸
    url = resizeImage(url, retina, normal)
  }

  // 居中裁切图片
  if (clip) {
    url = clipImage(url, clip)
  }
  // 设置图片质量
  if (quality) {
    url = setImageQuality(url, quality)
  }
  // 先判断是否支持avif
  if (checkAVIF()) {
    url = getAVIFImage(url)
  } else {
    // 不支持avif的，尝试替换为webp链接
    if (checkWebp() && webp) {
      url = getWebpImage(url)
    }
  }
  return url
}

/**
 * 仅传递一个 尺寸 获取正确的数据
 * @param size 传递一个正常尺寸
 * @param retina 是否兼容retian
 * @param clip 是否对素材到正常尺寸
 */
export function getOptions(
  size: Size,
  options: Options = {
    retina: true,
    clip: true,
  },
): Pick<ImgOptions, 'resize' | 'clip'> {
  const { retina, clip } = options

  const [width, height] = size
  const resize: string[] = []
  if (retina) {
    resize.push([width * 2, height * 2].join('x'))
  }

  const normalSize = [width, height].join('x')
  resize.push(normalSize)

  const result = {
    resize,
    clip: clip ? normalSize : '',
  }

  return result
}
