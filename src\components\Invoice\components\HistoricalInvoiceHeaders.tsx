import { FC, useState, useEffect, useRef } from 'react'
import { useAtomValue, useSetAtom } from 'jotai'
import {
  currentPersonalUsualInvoiceListAtom,
  selectedInvoiceTitleAtom,
  currentUsualInvoiceListAtom,
  activeInvoiceTabAtom,
} from '@app/components/Invoice/atom/invoiceAtom'
import { UsualInvoiceItem, InvoiceTitle } from '@app/typings/invoice.d'
import './HistoricalInvoiceHeaders.scss'
import { Form } from '@app/common/legao'
import CompanySelect from './CompanySelect'
import useAddInvoiceToUsual from '@app/components/Invoice/hooks/useAddInvoiceToUsual'
import useDeleteInvoiceFromUsual from '@app/components/Invoice/hooks/useDeleteInvoiceFromUsual'
import useEditInvoiceToUsual from '@app/components/Invoice/hooks/useEditInvoiceToUsual'
import showToast from '@app/components/payment/components/toast'
import { updatePersonalUsualInvoiceList<PERSON>tom, updateUsualInvoiceListAtom } from '@app/components/Invoice/atom/invoiceAction'
import { useInvoiceSave } from '../hooks/userInvoiceSave'
import Modal from '@app/common/legao/Modal'

/**
 * 历史发票抬头组件
 * 用于展示和管理个人/单位历史发票抬头
 */
interface HistoricalInvoiceHeadersProps {
  selectedId: number | null
  onSelectHeader: (item: UsualInvoiceItem) => void
  invoiceCode?: string
  type: InvoiceTitle
}

const HistoricalInvoiceHeaders: FC<HistoricalInvoiceHeadersProps> = ({ selectedId, onSelectHeader, invoiceCode }) => {
  const [editingItem, setEditingItem] = useState<UsualInvoiceItem | null>(null)
  const [newItem, setNewItem] = useState<UsualInvoiceItem | null>()
  const [hoveredItemId, setHoveredItemId] = useState<number | null>(null)
  const [inputFocused, setInputFocused] = useState(false)

  // 获取并设置发票列表
  const personalUsualInvoice = useAtomValue(currentPersonalUsualInvoiceListAtom)
  const setPersonalUsualInvoice = useSetAtom(updatePersonalUsualInvoiceListAtom)
  const usualInvoice = useAtomValue(currentUsualInvoiceListAtom)
  const setUsualInvoice = useSetAtom(updateUsualInvoiceListAtom)
  const selectInvoiceTitle = useAtomValue(selectedInvoiceTitleAtom)
  const { handleSave: saveInvoice } = useInvoiceSave()
  const activeInvoiceTab = useAtomValue(activeInvoiceTabAtom)
  // 根据发票类型使用对应的数据源
  const invoiceList = selectInvoiceTitle === InvoiceTitle.PERSONAL ? personalUsualInvoice : usualInvoice

  // 添加一个ref来跟踪选中状态的变化
  const isProcessingRef = useRef(false)

  /**
   * 添加发票抬头
   */
  const addInvoiceToUsual = useAddInvoiceToUsual({
    selectInvoiceTitle,
    updateUsualInvoice: (newInvoiceItem) => {
      // 更新本地状态
      if (selectInvoiceTitle === InvoiceTitle.PERSONAL) {
        setPersonalUsualInvoice({
          invoiceList: [{ ...newInvoiceItem, selected: true }, ...personalUsualInvoice.map((item) => ({ ...item, selected: false }))],
        })
      } else {
        setUsualInvoice({
          invoiceList: [{ ...newInvoiceItem, selected: true }, ...usualInvoice.map((item) => ({ ...item, selected: false }))],
        })
      }
      // 选中新添加的项
      onSelectHeader(newInvoiceItem)
    },
  })

  /**
   * 删除发票抬头
   */
  const deleteInvoiceFromUsual = useDeleteInvoiceFromUsual({
    updateInvoiceList: (itemId) => {
      // 从本地状态中移除该发票
      if (selectInvoiceTitle === InvoiceTitle.PERSONAL) {
        // 更新个人常用发票
        setPersonalUsualInvoice({ invoiceList: personalUsualInvoice.filter((invoice) => invoice.id !== itemId) })
      } else if (selectInvoiceTitle === InvoiceTitle.COMPANY) {
        // 更新单位常用发票
        setUsualInvoice({ invoiceList: usualInvoice.filter((invoice) => invoice.id !== itemId) })
      }
    },
  })

  /**
   * 编辑发票抬头
   */
  const editInvoiceTitle = useEditInvoiceToUsual({
    updateInvoiceItem: (updatedItem) => {
      // 更新本地状态
      if (selectInvoiceTitle === InvoiceTitle.PERSONAL) {
        // 更新个人常用发票
        setPersonalUsualInvoice({ invoiceList: personalUsualInvoice.map((item) => (item.id === updatedItem.id ? updatedItem : item)) })
      } else {
        // 更新单位常用发票
        setUsualInvoice({ invoiceList: usualInvoice.map((item) => (item.id === updatedItem.id ? updatedItem : item)) })
      }
      // 如果是编辑项，继续选中这个项
      onSelectHeader(updatedItem)
    },
  })

  /**
   * 在组件加载时，如果有selected为true的发票，自动选中它
   */
  useEffect(() => {
    // 防止无限循环：只在不处理中且没有已选择项时才自动选择
    if (isProcessingRef.current || selectedId !== null) {
      return
    }

    const selectedInvoice = invoiceList.find((item) => item.selected)
    if (selectedInvoice) {
      isProcessingRef.current = true
      onSelectHeader(selectedInvoice as UsualInvoiceItem)
      // 延迟重置处理状态，避免同一渲染周期中的多次处理
      setTimeout(() => {
        isProcessingRef.current = false
      }, 100)
    }
  }, [invoiceList, selectedId, onSelectHeader])

  /**
   * 处理选择发票抬头
   */
  const handleSelectHeader = (item: UsualInvoiceItem) => {
    // 如果正在处理中，直接返回
    if (isProcessingRef.current) {
      return
    }
    isProcessingRef.current = true

    // 更新本地状态以反映选择
    if (selectInvoiceTitle === InvoiceTitle.PERSONAL) {
      setPersonalUsualInvoice({
        invoiceList: personalUsualInvoice.map((prevItem) => ({
          ...prevItem,
          selected: prevItem.id === item.id,
        })),
      })
    } else {
      setUsualInvoice({
        invoiceList: usualInvoice.map((prevItem) => ({
          ...prevItem,
          selected: prevItem.id === item.id,
        })),
      })
    }
    // 调用父组件的回调
    onSelectHeader(item as UsualInvoiceItem)

    // 延迟重置处理状态
    setTimeout(() => {
      isProcessingRef.current = false
    }, 100)
  }

  /**
   * 获取发票抬头提示文字
   */
  const getPlaceholderText = () => {
    return selectInvoiceTitle === InvoiceTitle.PERSONAL ? '请填写个人发票抬头' : '请填写单位发票抬头'
  }

  /**
   * 删除历史抬头
   */
  const handleDeleteHistory = async (item: UsualInvoiceItem, event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation() // 阻止冒泡，防止触发选择事件
    Modal.create({
      title: '您确定要删除该发票信息吗？',
      confirmText: '确认',
      cancelText: '取消',
      onConfirm: async () => {
        // 点击确定按钮后执行的操作
        const result = await deleteInvoiceFromUsual(item)
        console.log('result', result)
        if (!result.success) {
          console.error('删除常用发票失败:', result.message)
        }
      },
      onCancel: () => {},
    })
  }

  /**
   * 编辑历史抬头
   */
  const handleEditHistory = (item: UsualInvoiceItem, event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation() // 阻止冒泡，防止触发选择事件
    if (item) {
      setEditingItem(item)
      setNewItem(item)
    }
  }

  /**
   * 处理编辑完成的保存操作
   */
  const handleSaveEdit = async () => {
    if (!editingItem || !newItem?.content?.trim()) {
      resetEditingState()
      return
    }

    // 使用编辑发票抬头hook
    const result = await editInvoiceTitle(editingItem, newItem.content || '', newItem.invoiceCode)
    if (result.success) {
      resetEditingState()
    } else {
      console.error('编辑发票抬头失败:', result.message)
    }
  }

  /**
   * 保存新增常用发票
   */
  const handleSaveNewHeader = async () => {
    if (!newItem?.content?.trim()) {
      resetEditingState()
      return
    }

    // 检查是否有重复的发票抬头
    const invoiceHeaders = selectInvoiceTitle === InvoiceTitle.PERSONAL ? personalUsualInvoice : usualInvoice
    const hasDuplicate = invoiceHeaders?.some((item) => item.content?.trim().toLowerCase() === newItem.content?.trim().toLowerCase())

    if (hasDuplicate) {
      // 如果有重复的抬头，显示Toast提示
      showToast({ title: '已存在相同的历史抬头' }, { timeout: 800, wrapperStyle: { zIndex: 9999 }, position: 'center-center' })
      return
    }

    // 使用添加常用发票hook
    const result = await addInvoiceToUsual(newItem, selectInvoiceTitle === 5 ? invoiceCode : undefined)
    if (result.success) {
      resetEditingState()
      saveInvoice(activeInvoiceTab)
    } else {
      console.error('保存发票抬头失败:', result.message)
    }
  }

  /**
   * 处理保存操作（区分编辑和新增）
   */
  const handleSaveHeader = (event?: React.MouseEvent<HTMLElement>) => {
    if (event) {
      event.stopPropagation()
    }
    if (editingItem) {
      // 编辑模式 - 只更新本地状态
      handleSaveEdit()
    } else {
      // 新增模式 - 调用API
      handleSaveNewHeader()
    }
  }

  /**
   * 取消操作并重置状态
   */
  const resetEditingState = () => {
    setEditingItem(null)
    setNewItem(null)
    setInputFocused(false)
  }

  /**
   * 处理输入框获取焦点
   */
  const handleAddNewInputFocus = () => {
    setInputFocused(true)
  }

  /**
   * 处理输入框失去焦点
   */
  const handleAddNewInputBlur = () => {
    // 如果输入框为空，则隐藏确定按钮
    if (!newItem?.content?.trim()) {
      setInputFocused(false)
    }
  }

  return (
    <Form.Item label="新增历史抬头" className="historical-headers">
      <div className="input-wrapper">
        {/* 新增发票抬头输入框 - 显示在列表顶部 */}
        {invoiceList.length < 10 &&
          (selectInvoiceTitle === InvoiceTitle.PERSONAL
            ? !editingItem && (
                // 个人抬头
                <div className={`company--new ${inputFocused ? 'focused' : ''}`} style={{ padding: '8px 12px' }}>
                  <input
                    type="text"
                    placeholder={getPlaceholderText()}
                    value={newItem?.content || ''}
                    className="company--new-input"
                    onFocus={handleAddNewInputFocus}
                    onBlur={handleAddNewInputBlur}
                    onChange={(e) => setNewItem({ ...newItem, content: e.target.value } as UsualInvoiceItem)}
                  />
                  {inputFocused && (
                    <>
                      <div className="confirm__line"></div>
                      <span className="confirm__text" onClick={(e) => handleSaveHeader(e)}>
                        确定
                      </span>
                    </>
                  )}
                </div>
              )
            : !editingItem && (
                // 单位抬头
                <div className={`company--new ${inputFocused ? 'focused' : ''}`}>
                  <CompanySelect
                    value={newItem?.content || ''}
                    onChange={(company) => {
                      onSelectHeader({ content: company.name, invoiceCode: company.creditCode } as UsualInvoiceItem)
                      setNewItem({ content: company.name, invoiceCode: company.creditCode } as UsualInvoiceItem)
                    }}
                    placeholder={getPlaceholderText()}
                    onVisibleChange={handleAddNewInputFocus}
                  />
                  {inputFocused && (
                    <>
                      <div className="confirm__line"></div>
                      <span className="confirm__text" onClick={(e) => handleSaveHeader(e)}>
                        确定
                      </span>
                    </>
                  )}
                </div>
              ))}

        <div className="history-card-container">
          {/* 历史发票抬头列表 */}
          {invoiceList &&
            invoiceList.map((item) => (
              <div
                key={item.id}
                className={`history-card ${selectedId === item.id || item.selected ? 'selected' : ''} ${
                  editingItem && editingItem.id === item.id ? 'company--edit' : hoveredItemId === item.id ? 'hovered' : ''
                }`}
                onMouseEnter={() => setHoveredItemId(item.id || -1)}
                onMouseLeave={() => setHoveredItemId(null)}
                onClick={() => (editingItem ? null : handleSelectHeader(item))}
              >
                {editingItem && editingItem.id === item.id ? (
                  <>
                    <div style={{ width: '100%' }}>
                      <input
                        type="text"
                        placeholder={getPlaceholderText()}
                        value={newItem?.content || ''}
                        onChange={(e) => setNewItem({ ...newItem, content: e.target.value } as UsualInvoiceItem)}
                        className="edit-input"
                        onClick={(e) => e.stopPropagation()}
                        autoFocus
                      />
                    </div>
                    <div className="confirm__line"></div>
                    <span className="confirm__text" onClick={(e) => handleSaveHeader(e)}>
                      确定
                    </span>
                  </>
                ) : (
                  <>
                    <div className="header-info">
                      <span className="company__name">{item.content}</span>
                    </div>
                    <div className="action" onClick={(e) => e.stopPropagation()}>
                      <div className="icon__wrap" onClick={(e) => handleEditHistory(item, e)}>
                        <div className="icon edit"></div>
                      </div>
                      <div className="icon__wrap" onClick={(e) => handleDeleteHistory(item, e)}>
                        <div className="icon delete"></div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
        </div>
      </div>
    </Form.Item>
  )
}

export default HistoricalInvoiceHeaders
