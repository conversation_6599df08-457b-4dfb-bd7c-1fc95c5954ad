.historical-headers {
  .history-card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    max-height: 220px;
    overflow-y: auto;
  }

  .history-card {
    height: 36px;
    border-radius: 4px;
    background-color: #f7f8fc;
    border: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    position: relative;
    flex-grow: 1;
    transition: all 0.2s ease;
    box-sizing: border-box;

    &.selected {
      background-color: #fff4f4;
      border: 0.5px solid #ff8595;
    }

    &.hovered {
      border: 0.5px solid #ff8595;

      .edit {
        background: url('//img13.360buyimg.com/ling/jfs/t1/300858/14/12183/411/683ee395F8733ae3d/32f628fdf42e31f2.png') no-repeat center
          center;
        background-size: 100% 100%;
      }
    }

    &.company--edit {
      background-color: #ffffff;
      border: 1px solid #ff8595;
      box-sizing: border-box;
      padding-right: 12px;
    }

    &.hidden {
      display: none;
    }
  }

  .company--new {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #e8e8e8;
    padding-right: 12px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    caret-color: #ff0f23;
    appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    box-sizing: border-box;
    color: #1a1a1a;
    height: 36px;
    font-size: inherit;
    line-height: 1;
    outline: 0;
    margin-bottom: 10px;

    &:hover {
      border-color: #ff8595;
    }

    &.focused {
      border-color: #f23030;
      outline: none;
      background: #fff;
    }

    input {
      border: none;
      outline: none;
      background: transparent;
      box-sizing: border-box;
      font-size: inherit;
    }
  }

  .company--new-input {
    border: none;
    width: 85%;
  }

  .company--new-input:focus .company--new {
    border: 1px solid #5a0000;
  }

  .company__name {
    line-height: 1;
    color: rgb(26, 26, 26);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 12px;
  }

  .edit-input {
    flex: 1;
    border: none;
    background: transparent;
    width: 100%;
    padding: 8px 12px;
    height: 36px;
    box-sizing: border-box;
    font-size: 14px;
    color: rgb(26, 26, 26);

    &::placeholder {
      color: rgb(194, 196, 204);
    }

    &:focus {
      outline: none;
    }
  }

  .action {
    display: flex;
    width: 40px;
    height: 20px;
    justify-content: space-around;
  }

  .icon__wrap {
    display: flex;
    gap: 10px;
    box-sizing: border-box;
    height: 20px;
    align-items: center;
    cursor: pointer;
  }

  .icon {
    overflow: hidden;
    width: 12px;
    height: 12px;
  }

  .edit {
    background: url('//img13.360buyimg.com/ling/jfs/t1/320151/8/5923/422/683ee24eFa5275308/cca6c7b4d292dcef.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .delete {
    background: url('//img30.360buyimg.com/ling/jfs/t1/312790/26/6442/272/683ee24eF4a093338/26a155b0db7059da.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .add-new-button {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    color: #ff8595;
    padding: 4px 0;
  }

  .confirm {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    position: relative;
    box-sizing: border-box;
    width: 48px;
    height: 32px;
    cursor: pointer;

    &__text {
      z-index: 10;
      font-size: 14px;
      font-weight: 600;
      line-height: 1;
      color: rgb(255, 15, 35);
      text-align: center;
      cursor: pointer;
      margin-left: 5px;
      width: 40px;
    }

    &__line {
      width: 14px;
      transform: rotate(90deg);
      height: 1px;
      background-color: rgba(255, 15, 35, 0.2);
    }
  }

  .company--empty {
    background-color: rgb(255, 255, 255);
    border: 1px solid #ff8595;
  }

  .placeholder {
    z-index: 10;
    font-size: 14px;
    line-height: 1;
    color: rgb(194, 196, 204);
  }

  .cursor {
    position: absolute;
    z-index: 11;
    top: 8px;
    left: 12px;
    width: 1px;
    height: 16px;
    background-color: rgb(255, 15, 35);
  }
}
