import { SOURCE } from '@app/services/const'
import type { MasterApiResponse } from '@app/typings/master_api_response'

/**
 * getBalanceSkus 扁平化数据结构
 * @param masterData
 * @returns
 */
export function getBalanceSkus(masterData: NonNullable<MasterApiResponse>['body']) {
  const balanceSkus: Record<string, any>[] = []
  masterData?.balanceVendorBundleList?.forEach((vender) => {
    vender.bundleList?.forEach((bundle) => {
      bundle.productionList?.forEach((production) => {
        production.balanceSkuList?.forEach((sku) => {
          const current = balanceSkus.find((item) => item.id === sku.id)
          if (current) {
            current.buyNum = (current.buyNum || 1) + (sku.buyNum || 1)
            return
          }
          balanceSkus.push({
            ...sku,
            vender: {
              ...vender,
              bundleList: undefined,
            },
            bundle: {
              ...bundle,
              productionList: undefined,
            },
            production: {
              ...production,
              balanceSkuList: undefined,
            },
          })
        })
      })
    })
  })
  return balanceSkus
}

/**
 * buildTrackParams
 * 构建埋点参数
 */
export function buildTrackParams(masterData: NonNullable<MasterApiResponse>['body'], patchParams?: Record<string, any>) {
  const presale = masterData?.balancePresaleVO
  const balanceSkus = getBalanceSkus(masterData)
  const paymentVOList = masterData?.paymentVOList
  const skusPaymentMap = new Map<string[], string>()

  if (Array.isArray(paymentVOList)) {
    paymentVOList.forEach((el) => {
      const skuIds = el.supSkuIds
      if (Array.isArray(skuIds) && skuIds.length) {
        skusPaymentMap.set(skuIds, el.paymentId)
      }
    })
  }

  const balanceSkuMdVO = masterData?.balanceSkuMdVO
  const skuMdVO: Record<string, NonNullable<typeof balanceSkuMdVO>[0]> = {}
  if (Array.isArray(balanceSkuMdVO) && balanceSkuMdVO.length) {
    balanceSkuMdVO.forEach((skuInfo) => {
      const { skuId, skuNum, skuType } = skuInfo || {}
      if (skuId != null && skuNum != null && skuType != null) {
        skuMdVO[skuId] = {
          skuId,
          skuNum,
          skuType,
        }
      }
    })
  }

  const skuinfo = balanceSkus.map((sku) => {
    const tmp = []
    for (const [skuIds, paymentId] of Array.from(skusPaymentMap)) {
      if (skuIds.includes(`${sku.id}`)) tmp.push(+paymentId)
    }

    return {
      skuid: sku.id != null ? `${sku.id}` : '',
      skunum: sku.buyNum != null ? `${sku.buyNum}` : '',
      skutype: skuMdVO[sku.id]?.skuType ?? '',
      jspayway: transformPayType(tmp),
      promlabel: sku.promotionDesc ?? '',
      coupon_info: '',
      chargePrice: sku.jdPrice,
    }
  })

  return {
    source: SOURCE ? { item: '2', cart: '1' }[SOURCE] : '',
    businesstype: presale ? 2 : 1,
    skuinfo,
    ...patchParams,
  }
}

// 0-在线支付、1-货到付款、2-对公转账、3-在线支付+货到付款
function transformPayType(originPayType: number[]) {
  const origin2currMap = new Map([
    [[1], '1'],
    [[4], '0'],
    [[5], '2'],
    [[1, 4], '3'],
  ])
  for (const [origin, curr] of Array.from(origin2currMap)) {
    if (origin.length == originPayType.length && origin.every((el) => originPayType.includes(el))) {
      return curr
    }
  }
  return ''
}
