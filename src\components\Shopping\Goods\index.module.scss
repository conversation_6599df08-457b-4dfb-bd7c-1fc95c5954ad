.goods {
  margin-bottom: 16px;

  .arrow {
    display: inline;
    width: 10px;
    height: 14px;
    margin-left: 2px;

    &::after {
      content: ' ';
      display: inline-block;
      border-top: 1px solid;
      border-right: 1px solid;
      border-color: #888b94;
      width: 6px;
      height: 6px;
      transform: rotate(45deg);
      position: relative;
      top: -1.5px;
    }
  }

  .btn {
    position: absolute;
    left: -28px;
    top: 0;
    cursor: pointer;

    .fold {
      width: 16px;
      height: 16px;
      background: url(https://img11.360buyimg.com/imagetools/jfs/t1/296486/20/17296/274/685e4427F61a86aa9/5c11ba5812d99859.png) left top /
        100% 100% no-repeat;
    }

    .expand {
      width: 16px;
      height: 16px;
      background: url(https://img11.360buyimg.com/imagetools/jfs/t1/286195/13/13773/272/685e43e3F922dece8/ab39880e302134ed.png) left top /
        100% 100% no-repeat;
    }

    .disabled {
      cursor: not-allowed;
      opacity: 0.7;
    }
  }

  .opacity0 {
    opacity: 0;
  }

  .left {
    margin-right: 12px;
  }

  .center {
    flex: 1;

    .block {
      height: 88px;
    }
  }

  .right {
    width: 228px;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .price {
      margin-top: 6px;
    }

    .received-price {
      font-family: JDZhengHeiVRegular2-1;
      display: flex;
      align-items: center;
      color: #ff0f23;
      font-size: 16px;
      font-weight: 700;
      line-height: 16px;
      letter-spacing: 0px;
    }

    .value {
      display: flex;
      align-items: center;
      color: #ff0f23;
      font-size: 16px;
      font-weight: 700;
      line-height: 16px;
      letter-spacing: 0px;
    }

    .text {
      display: flex;
      align-items: center;
      color: #ff0f23;
      font-size: 14px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0px;
      margin-left: 4px;
    }

    .jd-price {
      display: flex;
      align-items: center;
      color: #888b94;
      font-size: 14px;
      font-family: JDZhengHeiVRegular2-1;
      font-weight: 400;
      line-height: 14px;
      letter-spacing: 0px;
      margin-top: 8px;
    }

    .quantity {
      display: flex;
      align-items: center;
      color: #888b94;
      font-size: 14px;
      margin-top: 8px;
    }

    // 套装
    &.suit {
      justify-content: center;

      .quantity {
        margin-top: 0;
      }
    }

    // 到手价样式
    .jd-final-price {
      color: #ff0f23;
    }
    .jd-final-price-title {
      color: #ff0f23;
    }

    // plus到手价样式
    .jd-final-plus-price {
      color: #ff0f23;
    }
    .jd-final-plus-price-title {
      color: #994d00;
    }

    // 企业到手价样式
    .jd-final-enterprise-price {
      color: #ff0f23;
    }
    .jd-final-enterprise-price-title {
      color: #006aff;
    }
  }

  .goods-img {
    width: 88px;
    height: 88px;
    border-radius: 8px;
    background: #f7f8fc;
    overflow: hidden;
    cursor: pointer;
    position: relative;

    .img {
      width: 100%;
      height: 100%;
      display: block;
    }

    .stock-text {
      width: 88px;
      height: 24px;
      background: rgba(0, 0, 0, 0.7);
      color: rgba(255, 255, 255, 0.96);
      font-size: 14px;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }

  .title {
    cursor: pointer;
    padding-top: 6px;
    max-width: 700px;

    .label {
      border-radius: 2px;
      background: #00bb80;
      margin-right: 4px;
      width: 50px;
      height: 16px;
      color: #fff;
      font-size: 12px;
      font-weight: 500;
      height: 20px;
    }

    .text {
      color: #1a1a1a;
      font-size: 14px;
      font-weight: 600;
    }

    .icon {
      height: 16px;
      margin-right: 4px;
    }
  }

  .sku {
    display: flex;
    margin-top: 16px;

    .item {
      color: #505259;
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .tag {
    display: flex;
    margin-top: 16px;
    height: 14px;

    .item {
      color: #b5691a;
      font-size: 14px;
      margin-right: 8px;

      &.red {
        color: #ff0f23;
      }
    }
  }

  .service {
    display: flex;
    flex-direction: column;
    position: relative;
    margin-top: 12px;

    .strip {
      display: flex;
      align-items: center;
      margin-bottom: 6px;

      .fold {
        max-width: 660px;
      }

      .expand {
        max-width: 560px;
      }
    }

    & > div:nth-last-child(2) {
      margin-bottom: 0;
    }

    & > div:nth-last-child(1) {
      margin-bottom: 0;
    }

    .list {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .item {
      color: #888b94;
      font-size: 14px;
    }

    .name {
      color: #1a1a1a;
      font-size: 14px;
      font-weight: 600;
      margin-right: 8px;
      width: 28px;
    }

    .line {
      height: 10px;
      width: 1px;
      background: #888b94;
      margin: 0 8px;
      display: inline-block;
    }

    .price {
      color: #ff0f23;
      font-size: 14px;
      font-family: JDZhengHeiVRegular2-1;
      margin-left: 8px;
      width: 100px;

      .value {
        margin-right: 5px;
        flex: 1;
        text-align: right;
      }

      .num {
        color: #888b94;
      }
    }
  }

  .gift {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    position: relative;

    .strip {
      display: flex;
      margin-bottom: 6px;
      align-items: center;

      .expand > div:nth-child(1) {
        max-width: 660px;
      }

      .fold {
        max-width: 660px;
      }
    }

    & > div:nth-last-child(2) {
      margin-bottom: 0;
    }

    & > div:nth-last-child(1) {
      margin-bottom: 0;
    }

    .list {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .item {
      color: #888b94;
      font-size: 14px;
      cursor: pointer;
    }

    .name {
      color: #1a1a1a;
      font-size: 14px;
      font-weight: 600;
      margin-right: 8px;
      width: 28px;
    }

    .line {
      height: 10px;
      width: 1px;
      background: #888b94;
      margin: 0 8px;
      display: inline-block;
    }
  }

  .loc {
    margin-top: 14px;
    display: flex;
    flex-direction: column;
    position: relative;

    .btn {
      position: absolute;
      left: -28px;
      top: 0;

      .fold {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background: #ffebef;
        cursor: pointer;

        .line {
          width: 10px;
          height: 1px;
          border-radius: 1px;
          background: #ff0f23;
          position: absolute;
        }
      }

      .expand {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background: #f7f8fc;
        cursor: pointer;

        .line1 {
          width: 1px;
          height: 10px;
          border-radius: 1px;
          background: #505259;
          position: absolute;
        }

        .line2 {
          width: 10px;
          height: 1px;
          border-radius: 1px;
          background: #505259;
          position: absolute;
        }
      }
    }

    .strip {
      display: flex;
      margin-bottom: 6px;
      align-items: center;

      .expand {
        max-width: 660px;
      }

      .fold {
        max-width: 660px;
      }
    }

    & > div:nth-last-child(2) {
      margin-bottom: 0;
    }

    & > div:nth-last-child(1) {
      margin-bottom: 0;
    }

    .list {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .item {
      color: #888b94;
      font-size: 14px;
    }

    .name {
      color: #1a1a1a;
      font-size: 14px;
      font-weight: 600;
      margin-right: 8px;
      width: 28px;
    }

    .line {
      height: 10px;
      width: 1px;
      background: #888b94;
      margin: 0 8px;
      display: inline-block;
    }
  }

  .custom-service {
    display: flex;
    flex-direction: column;
    position: relative;
    margin-top: 14px;

    .strip {
      display: flex;
      align-items: center;
      margin-bottom: 6px;

      .fold {
        max-width: 660px;
      }

      .expand {
        max-width: 560px;
      }
    }

    & > div:nth-last-child(2) {
      margin-bottom: 0;
    }

    & > div:nth-last-child(1) {
      margin-bottom: 0;
    }

    .btn {
      position: absolute;
      left: -28px;
      top: 0;

      .fold {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background: #ffebef;
        cursor: pointer;

        .line {
          width: 10px;
          height: 1px;
          border-radius: 1px;
          background: #ff0f23;
          position: absolute;
        }
      }

      .expand {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background: #f7f8fc;
        cursor: pointer;

        .line1 {
          width: 1px;
          height: 10px;
          border-radius: 1px;
          background: #505259;
          position: absolute;
        }

        .line2 {
          width: 10px;
          height: 1px;
          border-radius: 1px;
          background: #505259;
          position: absolute;
        }
      }
    }

    .list {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .item {
      color: #888b94;
      font-size: 14px;
    }

    .name {
      color: #1a1a1a;
      font-size: 14px;
      font-weight: 600;
      margin-right: 8px;
      width: 28px;
    }

    .line {
      height: 10px;
      width: 1px;
      background: #888b94;
      margin: 0 8px;
      display: inline-block;
    }

    .price {
      color: #ff0f23;
      font-size: 14px;
      font-family: JDZhengHeiVRegular2-1;
      margin-left: 8px;
      width: 100px;

      .value {
        margin-right: 5px;
        flex: 1;
        text-align: right;
      }

      .num {
        color: #888b94;
      }
    }
  }

  .tip {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background: #f7f8fc;
    cursor: pointer;
    margin-right: 8px;
    padding: 9px 12px;

    color: #1a1a1a;
    font-size: 14px;

    .list {
      display: flex;
      align-items: center;
      margin-right: 8px;
    }

    .icon {
      width: 12px;
      height: 12px;
    }
  }

  .get-gov-tip {
    color: #0aad48;
    font-size: 14px;
    margin-top: 8px;
    cursor: pointer;

    .arrow {
      &::after {
        border-color: #0aad48;
      }
    }
  }
}

/* 窗口宽度在 1440px 到 1660px 之间 */
@media (max-width: 1659px) and (min-width: 1440px) {
  .goods {
    .service {
      .strip {
        .fold {
          width: 400px;
        }

        .expand-list {
          width: 400px;
        }
      }
    }

    .gift {
      .strip {
        .fold {
          max-width: 400px;
        }

        .expand > div:nth-child(1) {
          max-width: 400px;
        }
      }
    }

    .custom-service {
      .strip {
        .fold {
          max-width: 400px;
        }

        .expand {
          max-width: 400px;
        }
      }
    }

    .title {
      max-width: 450px;
    }

    .right {
      width: 200px;
    }
  }
}

/* 窗口宽度小于 1240px */
@media (max-width: 1239px) {
  .goods {
    .service {
      .strip {
        .fold {
          width: 400px;
        }

        .expand-list {
          width: 400px;
        }
      }
    }

    .gift {
      .strip {
        .fold {
          max-width: 400px;
        }

        .expand > div:nth-child(1) {
          max-width: 400px;
        }
      }
    }

    .custom-service {
      .strip {
        .fold {
          max-width: 400px;
        }

        .expand {
          max-width: 400px;
        }
      }
    }

    .title {
      max-width: 450px;
    }

    .right {
      width: 200px;
    }
  }
}
