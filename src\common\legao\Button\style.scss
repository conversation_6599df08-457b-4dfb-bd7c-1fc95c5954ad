.el-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #F7F8FC;
  border: 0.5px solid rgba(0, 0, 0, 0.06);
  color: #1A1A1A;
  margin: 0;
  padding: 10px 14px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.el-button+.el-button {
  margin-left: 10px
}

// .el-button:focus,

.el-button:hover, .el-button.is-active {
  border-color: #FF8595;
  background-color: #FFEBF1;
  color: #FF0F23;
  outline: 0;
}

.el-button::-moz-focus-inner {
  border: 0
}

.el-button [class*=el-icon-]+span {
  margin-left: 5px
}

.el-button.is-loading {
  position: relative;
  pointer-events: none
}

.el-button.is-loading:before {
  pointer-events: none;
  content: '';
  position: absolute;
  left: -1px;
  top: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: inherit;
  background-color: rgba(255,255,255,.35)
}

.el-button.is-disabled,.el-button.is-disabled:focus,.el-button.is-disabled:hover {
  color: #bfcbd9;
  cursor: not-allowed;
  background-image: none;
  background-color: #eef1f6;
  border-color: #d1dbe5
}

.el-button.is-disabled.el-button--text {
  background-color: transparent
}

.el-button.is-disabled.is-plain,.el-button.is-disabled.is-plain:focus,.el-button.is-disabled.is-plain:hover {
  background-color: #fff;
  border-color: #d1dbe5;
  color: #bfcbd9
}

.el-button.is-plain:focus,.el-button.is-plain:hover {
  background: #fff;
  border-color: #20a0ff;
  color: #20a0ff
}

.el-button.is-plain:active {
  background: #fff;
  border-color: #1d90e6;
  color: #1d90e6;
  outline: 0
}

.el-button--primary {
  color: #fff;
  background-color: #20a0ff;
  border-color: #20a0ff
}

.el-button--primary:focus,.el-button--primary:hover {
  background: #4db3ff;
  border-color: #4db3ff;
  color: #fff
}

.el-button--primary.is-active,.el-button--primary:active {
  background: #1d90e6;
  border-color: #1d90e6;
  color: #fff
}

.el-button--primary:active {
  outline: 0
}

.el-button--primary.is-plain {
  background: #fff;
  border: 1px solid #bfcbd9;
  color: #1f2d3d
}

.el-button--primary.is-plain:focus,.el-button--primary.is-plain:hover {
  background: #fff;
  border-color: #20a0ff;
  color: #20a0ff
}

.el-button--primary.is-plain:active {
  background: #fff;
  border-color: #1d90e6;
  color: #1d90e6;
  outline: 0
}

.el-button--success {
  color: #fff;
  background-color: #13ce66;
  border-color: #13ce66
}

.el-button--success:focus,.el-button--success:hover {
  background: #42d885;
  border-color: #42d885;
  color: #fff
}

.el-button--success.is-active,.el-button--success:active {
  background: #11b95c;
  border-color: #11b95c;
  color: #fff
}

.el-button--success:active {
  outline: 0
}

.el-button--success.is-plain {
  background: #fff;
  border: 1px solid #bfcbd9;
  color: #1f2d3d
}

.el-button--success.is-plain:focus,.el-button--success.is-plain:hover {
  background: #fff;
  border-color: #13ce66;
  color: #13ce66
}

.el-button--success.is-plain:active {
  background: #fff;
  border-color: #11b95c;
  color: #11b95c;
  outline: 0
}

.el-button--warning {
  color: #fff;
  background-color: #f7ba2a;
  border-color: #f7ba2a
}

.el-button--warning:focus,.el-button--warning:hover {
  background: #f9c855;
  border-color: #f9c855;
  color: #fff
}

.el-button--warning.is-active,.el-button--warning:active {
  background: #dea726;
  border-color: #dea726;
  color: #fff
}

.el-button--warning:active {
  outline: 0
}

.el-button--warning.is-plain {
  background: #fff;
  border: 1px solid #bfcbd9;
  color: #1f2d3d
}

.el-button--warning.is-plain:focus,.el-button--warning.is-plain:hover {
  background: #fff;
  border-color: #f7ba2a;
  color: #f7ba2a
}

.el-button--warning.is-plain:active {
  background: #fff;
  border-color: #dea726;
  color: #dea726;
  outline: 0
}

.el-button--danger {
  color: #fff;
  background-color: #ff4949;
  border-color: #ff4949
}

.el-button--danger:focus,.el-button--danger:hover {
  background: #ff6d6d;
  border-color: #ff6d6d;
  color: #fff
}

.el-button--danger.is-active,.el-button--danger:active {
  background: #e64242;
  border-color: #e64242;
  color: #fff
}

.el-button--danger:active {
  outline: 0
}

.el-button--danger.is-plain {
  background: #fff;
  border: 1px solid #bfcbd9;
  color: #1f2d3d
}

.el-button--danger.is-plain:focus,.el-button--danger.is-plain:hover {
  background: #fff;
  border-color: #ff4949;
  color: #ff4949
}

.el-button--danger.is-plain:active {
  background: #fff;
  border-color: #e64242;
  color: #e64242;
  outline: 0
}

.el-button--info {
  color: #fff;
  background-color: #50bfff;
  border-color: #50bfff
}

.el-button--info:focus,.el-button--info:hover {
  background: #73ccff;
  border-color: #73ccff;
  color: #fff
}

.el-button--info.is-active,.el-button--info:active {
  background: #48ace6;
  border-color: #48ace6;
  color: #fff
}

.el-button--info:active {
  outline: 0
}

.el-button--info.is-plain {
  background: #fff;
  border: 1px solid #bfcbd9;
  color: #1f2d3d
}

.el-button--info.is-plain:focus,.el-button--info.is-plain:hover {
  background: #fff;
  border-color: #50bfff;
  color: #50bfff
}

.el-button--info.is-plain:active {
  background: #fff;
  border-color: #48ace6;
  color: #48ace6;
  outline: 0
}

.el-button--large {
  padding: 11px 19px;
  font-size: 16px;
  border-radius: 4px
}

.el-button--small {
  padding: 7px 9px;
  font-size: 12px;
  border-radius: 4px
}

.el-button--mini {
  padding: 4px;
  font-size: 12px;
  border-radius: 4px
}

.el-button--text {
  border: none;
  color: #20a0ff;
  background: 0 0;
  padding-left: 0;
  padding-right: 0
}

.el-button--text:focus,.el-button--text:hover {
  color: #4db3ff
}

.el-button--text:active {
  color: #1d90e6
}

.el-button-group {
  display: inline-block;
  vertical-align: middle
}

.el-button-group .el-button--primary:first-child {
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--primary:last-child {
  border-left-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--primary:not(:first-child):not(:last-child) {
  border-left-color: rgba(255,255,255,.5);
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--success:first-child {
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--success:last-child {
  border-left-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--success:not(:first-child):not(:last-child) {
  border-left-color: rgba(255,255,255,.5);
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--warning:first-child {
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--warning:last-child {
  border-left-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--warning:not(:first-child):not(:last-child) {
  border-left-color: rgba(255,255,255,.5);
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--danger:first-child {
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--danger:last-child {
  border-left-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--danger:not(:first-child):not(:last-child) {
  border-left-color: rgba(255,255,255,.5);
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--info:first-child {
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--info:last-child {
  border-left-color: rgba(255,255,255,.5)
}

.el-button-group .el-button--info:not(:first-child):not(:last-child) {
  border-left-color: rgba(255,255,255,.5);
  border-right-color: rgba(255,255,255,.5)
}

.el-button-group .el-button {
  float: left;
  position: relative
}

.el-button-group .el-button+.el-button {
  margin-left: 0
}

.el-button-group .el-button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.el-button-group .el-button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.el-button-group .el-button:not(:first-child):not(:last-child) {
  border-radius: 0
}

.el-button-group .el-button:not(:last-child) {
  margin-right: -1px
}

.el-button-group .el-button.is-active,.el-button-group .el-button:active,.el-button-group .el-button:focus,.el-button-group .el-button:hover {
  z-index: 1
}