import type { FC, ButtonHTMLAttributes } from 'react'
import './index.scss'

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'default' | 'dashed' | 'text' | 'link'
  size?: 'large' | 'middle' | 'small'
}

const Button: FC<ButtonProps> = ({
  variant = 'default',
  size = 'middle',
  disabled = false,
  children,
  className,
  ...restProps
}) => {
  let _className = `btn btn-${variant} btn-${size} ${disabled ? 'btn-disabled' : ''}`

  if (className) {
    _className += ` ${className}`
  }
  
  return (
    <button type="button" className={_className} {...restProps}>
      {children}
    </button>
  )
}

export default Button
