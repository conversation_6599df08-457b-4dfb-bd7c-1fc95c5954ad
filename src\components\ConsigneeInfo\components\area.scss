.ui-area-wrap.ui-area-hover.jd-settle {
  z-index: 1001;
  &.ui-area-oversea-mode .ui-area-content-wrap .ui-area-c-c {
    .ui-area-c-c-list .ui-area-content-list li {
      margin-right: 6px !important;
      width: 141.33px;
    }
    .ui-area-c-c-idx { 
      width: 468px;
      .ui-area-c-c-idx-c a {
        margin-right: 8px;
      }
    }
  }
  .ui-area-content-wrap {
    width: 516px;
    padding: 8px 15px 0;
    .ui-area-tab a {
      min-width: 121px;
      em {
        max-width: 79px;
        text-overflow: ellipsis;
        display: inline-block;
      }
      i {
        margin: 8px 8px;
      }
    }
    .ui-area-content {
      .ui-switchable-panel:not(:first-child) .ui-area-content-list {
        li:nth-child(5n) {
          margin-right: 12px;
        }
        li:nth-child(3n) {
          margin-right: 0;
        }
      } 
      .ui-switchable-panel:first-child .ui-area-content-list li {
        width: 106px;
        &:nth-child(3n) {
          margin-right: 12px;
        }
        &:nth-child(4n) {
          margin-right: 0;
        }
      }
      .ui-area-content-list li {
        width: 145.3px;
      }
    }
  }
}
.ui-area-wrap {
  &.settle {
    width: 100%;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    padding: 0 12px;
    height: 36px !important;
    &:hover {
      border-color: #FF8595;
    }
  }
  box-sizing: border-box;
  display: flex;
  align-items: center;
  .ui-area-text-wrap {
    font-size: 14px;
    line-height: 16px;
    height: 34px;
    display: flex;
    justify-content: space-between;
    width: 100%;
    [data-id='0-0-0-0'] {
      color: #c2c4cc;
    }
    b {
      width: 8px;
      height: 8px;
      top: auto;
    }
  }
  .ui-area-content-tab-wrap {
    top: 34px;
  }
}