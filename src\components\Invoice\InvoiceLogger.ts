import { reportClick, reportExpose } from '@app/utils/event_tracking'

// 发票-楼层 埋点参数
export type InvoicePointParams = {
  // 实验标识：棱镜，试金石适用；"ZXhwMXxDdmVyc2lvbl8x"包含实验标识和实验组
  touchstone_expids?: string[]
  // 0-bpin，1-cpin
  pin: string
  // 0-自营已开，1-自营未开，2-pop已开，3-pop未开
  is_invoice: string
  // 发票楼层文案
  text: string
  skuinfo: {
    // 商品skuid
    skuid: string
    // 商品状态：0-纯自营、1-纯pop、2-自营pop
    skuStatus: string
  }[]
  // 实验组：test-off、test-show、base
  testgroup?: string
}
// 发票-弹窗 埋点参数
export type InvoiceLayerPointParams = {
  // 实验标识：棱镜，试金石适用；"ZXhwMXxDdmVyc2lvbl8x"包含实验标识和实验组
  touchstone_expids?: string[]
  // 0-取消，1-保存，2-关闭，3-点击修改发票类型
  clickPos: string
  // 多个信息用"_"拼接上报
  invoice_detail: string
}
export const InvoiceLogger = {
  /**
   * 发票事件埋点
   * 埋点参数：
   * @param event 事件名称 invoice invoiceLayer
   * @param params 埋点参数
   */
  invoiceEvent(event: string, params: InvoicePointParams | InvoiceLayerPointParams) {
    reportClick(event, params)
  },

  /**
   * 发票曝光埋点
   * 埋点参数：
   * @param event 事件名称 invoiceExpo invoiceLayerExpo
   * @param params 埋点参数
   */
  invoiceExpo(event: string, params: InvoicePointParams | InvoiceLayerPointParams) {
    reportExpose(event, params)
  },
}
