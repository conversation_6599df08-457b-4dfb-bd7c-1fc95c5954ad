/**
 * @file: exception.ts
 * @description: 网络层异常解析处理
 */
import type { AxiosResponse, AxiosError } from 'axios'
import { GlobalEventEmitter } from '@app/helpers'
import reportException from './report_exception'

/**
 * isCartEmpty 根据接口返回的message判断是否为空购物车
 * @param message
 * @returns
 */
export const isCartEmpty = (message: string) => {
  const CODE = '2-11-1012-200001'
  return message.includes(CODE)
}

/**
 * isLogin 根据接口返回的code判断是否登录
 * @param code
 * @returns
 */
export const isNoLogin = (code: string) => {
  const CODE = '3'
  return code == CODE
}

/**
 * handleBizException
 * @param code
 * @returns
 */
export const handleBizException = (response: AxiosResponse) => {
  const { config, data } = response
  reportException({ BizException: response })
  /** 用户未登录 */
  if (isNoLogin(data.code)) {
    GlobalEventEmitter.emit('NO_LOGIN')
    return
  }

  /** 购物车为空 */
  if (
    isCartEmpty(`${data.message ?? ''}`) &&
    ['balance_getCurrentOrder_pc', 'balance_getCurrentOrderWithNewRule_pc'].includes(config.params.functionId)
  ) {
    GlobalEventEmitter.emit('CART_EMPTY')
    return
  }

  if (data.message) {
    GlobalEventEmitter.emit('SHOW_TOAST', data.message)
    return
  }
}

/**
 * handleNetException
 * @param code
 * @returns
 */
export const handleNetException = (error: AxiosError) => {
  reportException({ NetException: error })
  if (error.response) {
    // 服务器返回了状态码，但不是 2xx
    console.error(`Server responded with status code ${error.response.status}:`, error.response.data)
  } else if (error.request) {
    // 请求已发出，但没有收到响应
    console.error('No response received:', error.request)
  } else {
    // 发生了一些其他错误
    console.error('Error occurred during request:', error.message)
  }
}
