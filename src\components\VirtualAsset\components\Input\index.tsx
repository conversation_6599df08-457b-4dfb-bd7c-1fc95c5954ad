import React, { forwardRef } from 'react'
import classnames from 'classnames'

import styles from './index.module.scss'
import type { StringOrNumber, SizeType } from '@app/typings/component'
interface InputProps {
  /**
   * 输入框类型
   */
  type?: 'text' | 'number'

  /**
   * 输入框值
   */
  value?: StringOrNumber

  /**
   * 输入框尺寸
   */
  size?: SizeType

  /**
   * 输入框占位符
   */
  placeholder?: string

  /**
   * 输入框输入框变化时的回调
   */
  onChange?: (value: StringOrNumber) => void

  /**
   * 输入框失去焦点时的回调
   */
  onBlur?: (value: StringOrNumber) => void

  /**
   * 粘贴事件
   */
  onPaste?: (value: StringOrNumber) => void

  /**
   * 输入框最大长度
   */
  maxLength?: number

  /**
   * 输入框引用
   */
  ref?: React.RefObject<HTMLInputElement>

  /**
   * 输入框自定义样式
   */
  className?: string

  /**
   * 是否禁用输入框
   */
  disabled?: boolean
}

/**
 * 通用输入框
 * */
const Input = (props: InputProps, ref: any) => {
  const {
    value = '',
    type = 'text',
    size = 'small',
    placeholder = '请输入',
    onChange,
    onBlur,
    onPaste,
    maxLength,
    className = '',
    disabled = false,
  } = props

  /**
   * 输入事件处理
   */
  const handleInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    const val = event.target.value
    if (type === 'number') {
      const newVal = Number(val)
      if (!isNaN(newVal) || val === '') {
        onChange && onChange(val)
      }
    } else {
      onChange && onChange(val)
    }
  }
  const handleBlur = () => {
    onBlur?.(value)
  }

  /**
   * 粘贴事件
   * @param event
   */
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    let newData
    if (window?.clipboardData && window.clipboardData.getData('text')) {
      // for IE
      newData = window.clipboardData.getData('text')
    } else if (event && event.clipboardData && event.clipboardData.getData('text')) {
      newData = event.clipboardData.getData('text')
    } else {
      return
    }
    onPaste?.(newData)
    event.preventDefault()
  }
  /**
   *  键盘松开事件
   * @param event
   */
  const handleKeyUp = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (String(value).length >= 4) {
      onChange?.(value)
    }
  }

  return (
    <div
      className={classnames(
        styles['common-input'],
        styles[`common-input--${size}`],
        disabled && styles[`input-disabled`],
        className as any,
        // prefixIcon ? styles['common-input--prefix'] : '',
      )}
    >
      <input
        ref={ref}
        type="text"
        value={value}
        disabled={disabled}
        maxLength={maxLength || 100}
        className={styles['common-input__inner']}
        placeholder={placeholder}
        onInput={handleInput}
        onBlur={handleBlur}
        onPaste={handlePaste}
        onKeyUp={handleKeyUp}
      />
      {/* {prefixIcon ? <span className={styles['common-input__prefix']}>{prefixIcon}</span> : ''} */}
    </div>
  )
}

export default forwardRef(Input)
