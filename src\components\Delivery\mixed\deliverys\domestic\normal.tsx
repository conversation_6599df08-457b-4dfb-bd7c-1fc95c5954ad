/**
 * @file: normal.tsx
 * @description: 标准配送组件，用于展示标准配送等逻辑
 */
import { PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp'
import TimeSelector from '../../../ui/TimeSelector/timeSelector'
import { promiseToComponentData } from '../../initDeliveryData'
import { useEffect, useRef, useState } from 'react'
import { useDelivery } from '../../../context'
import PreciseTime from '../../../ui/TimeSelector/PreciseTime'
import { DeliveryEnum } from '../../types'
import { useLatest } from '../../hooks'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'

export default function Normal({
  onSelectValue,
  promise,
  tipComponent,
}: {
  promise: PromiseListItem
  onSelectValue: (type: 'timeList' | null, value: any) => void
  tipComponent?: React.ReactNode
}) {
  const componentDataMaker = useRef(new promiseToComponentData())

  const [list, setList] = useState(componentDataMaker.current.getTimeList(promise))

  const [selectList, setSelectList] = useState<{ promiseTagName: string; promiseTagType: string; selected: boolean }[]>([])

  const { deliveryLogger } = useDelivery()

  const thisOnSelectValue = useLatest(onSelectValue)

  useEffect(() => {
    setList(componentDataMaker.current.getTimeList(promise))
    if (componentDataMaker.current.getTimeList(promise)?.length === 0) {
      thisOnSelectValue.current(null, null)
    }
  }, [promise, thisOnSelectValue])

  useEffect(() => {}, [onSelectValue, promise, tipComponent])

  return (
    <>
      {promise && promise.promiseType === DeliveryEnum.PromiseEnum.JZD && selectList.length > 0 && (
        <PreciseTime
          onClick={(s) => {
            deliveryLogger.shipmentPOPClick({ second_tab_name: s.promiseTagName })
            selectList.length > 1 &&
              monitoring({
                name: monitorName.Settlement,
                code: monitorCode.Delivery,
                msg: {
                  data: { promise },
                  info: '时间精确度数',
                  type: '配送模块-时间精确度数数据上报',
                },
              })
            // setSelectList([...selectList].map((item) => ({ ...item, selected: item.promiseTagType === s.promiseTagType })))
          }}
          selectList={selectList}
        />
      )}
      {tipComponent && <>{tipComponent}</>}
      <TimeSelector
        list={list}
        onChange={(p, s) => {
          onSelectValue('timeList', { p: p, s: s })
          setSelectList(p?.targetValue?.supportPromiseTagList || [])
        }}
        onClick={(item, is2, data) => {
          deliveryLogger.shipmentlayer('3', '', item)
          if (!is2) {
            deliveryLogger.shipmentPOPClick({ date: item })
          } else {
            console.log(data, 'data')
            deliveryLogger.shipmentPOPClick({ shipmentTitle: item, transportation_expenses: data?.freight || '' })
          }
        }}
      />
    </>
  )
}
