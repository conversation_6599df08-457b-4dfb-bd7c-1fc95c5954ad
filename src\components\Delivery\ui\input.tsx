import { useEffect, useRef, useState } from 'react'

const InputMessageBox = ({ reportValue, refKey, initValue }: { reportValue: (v: string) => void; refKey: number; initValue: string }) => {
  const max = 200
  const [inputValue, setInputValue] = useState('')
  const [charCount, setCharCount] = useState(0)

  const inputRef = useRef<any | null>(null)

  const handleInputChange = (e: { target: { value: string } }) => {
    const value = e.target.value
    if (value.length > max) {
      return
    }
    setInputValue(value)
    reportValue(value)
    setCharCount(value.length)
  }

  useEffect(() => {
    inputRef.current.focus()
    setInputValue(initValue)
    setCharCount(initValue.length)
  }, [refKey])

  useEffect(() => {
    setInputValue(initValue)
    setCharCount(initValue.length)
  }, [initValue])

  return (
    <div
      style={{
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      <textarea
        ref={inputRef}
        value={inputValue}
        placeholder="建议先与商家沟通确认"
        onChange={handleInputChange}
        style={{
          backgroundColor: 'rgba(0, 0, 0, 0.03)' /* 灰色背景 */,
          width: '100%',
          minWidth: '496px',
          lineHeight: '20px',
          minHeight: '200px',
          border: 'none',
          resize: 'vertical',
          outline: 'none',
          borderRadius: '8px',
          padding: '12px',
          color: '#1A1A1A',
          fontSize: '14px',
          fontWeight: 400,
        }}
      />
      <div
        style={{
          bottom: '12px',
          right: '12px',
          textAlign: 'right',
          lineHeight: '14px',
          height: '14px',
          width: '100px',
          fontSize: '12px',
          color: charCount >= max ? 'red' : '#9e9e9e',
          position: 'absolute',
        }}
      >
        {charCount}/200
      </div>
    </div>
  )
}

export default InputMessageBox
