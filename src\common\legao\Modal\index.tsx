import React, { useState, useRef, useCallback, useEffect } from 'react'
import { Button } from '@app/common/legao'
import { createRoot } from 'react-dom/client'
import classNames from 'classnames'

import styles from './index.module.scss'

export interface ModalProps {
  className?: string
  customStyle?: string | React.CSSProperties
  /**
   * 是否显示模态框
   * @defaultValue false
   */
  isOpened: boolean
  /**
   * 点击浮层的时候时候自动关闭
   * @defaultValue true
   */
  closeOnClickOverlay?: boolean
  /**
   * 触发关闭时的事件
   */
  onClose?: () => void
}
interface ModalFormItem {
  name?: string
  value: string
}
export interface CreateModalProps {
  title?: string
  content?: string | React.ReactNode
  confirmText?: string
  cancelText?: string
  closeOnClickOverlay?: boolean
  children?: React.ReactNode
  onConfirm?: () => boolean | Promise<void> | undefined | void
  onCancel?: () => void
  onClose?: () => void
  type?: 'waring' | 'success' | 'fail'
}
interface AlertModalprops extends CreateModalProps {
  contentArr?: ModalFormItem[]
}

interface CompoundedComponent {
  (props: React.PropsWithChildren<ModalProps>): React.ReactElement | null
  create: (props: CreateModalProps | AlertModalprops) => { destroy: () => void } | undefined
}

const Modal: CompoundedComponent = ({ children, className, closeOnClickOverlay = true, isOpened, onClose }) => {
  const callback = useRef<{ onClose?: typeof onClose }>({})
  const [open, setOpen] = useState<boolean>(false)

  useEffect(() => {
    if (open) {
      const current = callback.current
      return () => current.onClose?.()
    }
  }, [open])

  useEffect(() => {
    setTimeout(() => setOpen(isOpened), 0)
  }, [isOpened])

  const handleOverlayClick = useCallback(() => {
    if (closeOnClickOverlay) {
      callback.current.onClose = onClose
      setOpen(false)
    }
  }, [closeOnClickOverlay, onClose])

  const rootClass = classNames(
    styles.modal,
    {
      [styles.active]: open,
    },
    className,
  )

  return (
    <div onTouchMove={(e) => e.stopPropagation()} className={rootClass}>
      <div className={styles.overlay} onClick={handleOverlayClick} />
      <div className={styles.container}>{children}</div>
    </div>
  )
}

let block = false
Modal.create = (props) => {
  if (block) {
    return
  }
  block = true
  setTimeout(() => {
    block = false
  }, 300)

  const root = document.body
  if (!root) {
    throw new Error('create modal error: root element does not exists')
  }

  const node = document.createElement('div')
  root.appendChild(node)

  const reactRoot = createRoot(node)

  const destroy = () => {
    Promise.resolve().then(() => {
      reactRoot.unmount()
    })
    node.remove?.()
  }

  const close = () => {
    destroy()
    props.onClose?.()
  }

  /**
   * Sync render blocks React event.
   */
  Promise.resolve().then(() => {
    reactRoot.render(
      <ModalComponent {...props} onClose={close}>
        {props.children}
      </ModalComponent>,
    )
  })

  return {
    destroy,
  }
}

const ModalComponent: React.FC<CreateModalProps> = ({
  children,
  title,
  content,
  confirmText,
  cancelText,
  closeOnClickOverlay,
  onConfirm,
  onCancel,
  onClose,
  type = 'warning',
}) => {
  const [loading, setLoading] = useState<boolean>(false)
  const clicked = useRef<boolean>(false)

  const handleConfirm = useCallback(() => {
    if (clicked.current) {
      return
    }
    clicked.current = true

    const value = onConfirm?.()

    if (value === false) {
      clicked.current = true
      return
    }

    if (value !== true && value !== undefined && value?.then) {
      setLoading(true)
      value?.then(
        () => {
          onClose?.()
          clicked.current = false
        },
        (e) => {
          console.error(e)
          setLoading(false)
          clicked.current = false
        },
      )
      return
    }

    onClose?.()
    clicked.current = false
  }, [onConfirm, onClose])

  const handleCancel = useCallback(() => {
    onClose?.()
    onCancel?.()
  }, [onCancel, onClose])

  return (
    <Modal isOpened closeOnClickOverlay={closeOnClickOverlay} onClose={onClose}>
      {children || (
        <div className={styles.root}>
          <div className={styles.header}>
            {title && type && <i className={styles[type]} />}
            <div>{title}</div>
          </div>
          <div className={styles.body}>
            {typeof content === 'string' ? (
              <div className={styles.content}>{content}</div>
            ) : Array.isArray(content) ? (
              content.map((m, i) => (
                <div key={m.name + i} className={classNames(styles.content, styles.contentArr)}>
                  {m.name}&nbsp;{m.value}
                </div>
              ))
            ) : (
              content
            )}
          </div>
          <div className={styles.footer}>
            {cancelText && (
              <Button className={styles.cancel} onClick={handleCancel}>
                {cancelText}
              </Button>
            )}
            {confirmText && (
              <Button className={styles.confirm} onClick={handleConfirm} loading={loading} disabled={loading}>
                {confirmText}
              </Button>
            )}
          </div>
        </div>
      )}
    </Modal>
  )
}

export default Modal
