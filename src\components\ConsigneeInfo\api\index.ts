import wrapper from './wrapper'
import { AddPageRuleData } from '../atoms/address'

interface Rule {
  addressDetail: string
  searchAddress: string
  houseNumber: string
  area: string
  email: string
  mobile: string
  name: string
  phone: string
  postCode: string
}
interface Rules {
  addressDarkGrain: Rule
  foreignAddressDarkGrain: Rule
  /* 香港和澳门 */
  gangAoAddressDarkGrain: Rule
  /* 台湾校验规则 */
  taiWanAddressDarkGrain: Rule
}

export interface AddrSuggestType {
  areaName: string // 区级地址名称
  category: string
  cityName: string // 市级地址名称
  coord_type: string // 坐标系
  distance: string // 距离（客户端传经纬度，就下发这个字段）
  id: string // 地址id（非地址中台addressId，可能为空字符串）
  idx: string // 索引值（从0开始）
  isForeignOverSea: string // 是否是海外
  isGangAoTai: string // 是否是港澳台
  latitude: string // 经度
  longitude: string // 纬度
  provinceName: string // 省级地址名称
  shortAddress: string // 地址短路标门牌号
  shortTitle: string // 地址短名称
  title: string // 地址名称
}

const apis = {
  /** 添加地址 */
  addAddress: wrapper<
    {
      keyId?: string
    } & AddressStandard.EditFillInType,
    {
      resultFlag: boolean
      addressId?: number
      townId?: number
      message: string
    }
  >({
    functionId: 'pc_address_cmpnt_addAddress',
    umps: [1],
  }),
  /** 获取页面初始校验规则 */
  addAddressPage: wrapper<
    {
      action: '1' | '2'
      /**
       * 当前是否要用POI地址逻辑，注意新增/编辑地址页回传的标识不一样
       * 服务端透传下发，无逻辑，客户端传不传都行
       */
      usePoiAddress?: boolean
      keyId?: string // 加密需传
      editVersion?: number
      abTests?: string // AB测试入参 '实验名1,实验名2'
    },
    Rules & AddPageRuleData
  >({
    functionId: 'pc_address_cmpnt_addAddressPage',
    umps: [2],
  }),
  /** 地址联想点击上报腾讯-结算地图/结算搜索地址 */
  addressSearchClickReport: wrapper<
    {
      /**
       * addressSearch接口下发的requestId
       */
      requestId: string
      /**
       * addressSearch接口下发的地址列表中的id值
       */
      dataId: string
      /**
       * 索引值，addressSearch接口下发的索引位置(从0开始)
       */
      idx: string
    },
    {}
  >({
    functionId: 'address_cmpnt_addressSearchClickReport',
    umps: [3],
    hideLoading: true, // 隐藏小狗子
  }),

  /** 删除地址 */
  deleteAddress: wrapper<
    {
      addressId: number
    },
    {
      resultFlag: boolean
      message: string
    }
  >({
    functionId: 'pc_address_cmpnt_deleteAddress',
    umps: [4],
  }),
  /** 获取区号 */
  getAreaCodeList: wrapper<
    undefined,
    {
      commonAreaCodeList: Array<AddressStandard.Areas>
      areaCodeList: Array<AddressStandard.Areas>
      wordList: Array<string>
      wordCodeResult: Array<AddressStandard.WordCodeResultTypes>
    }
  >({
    functionId: 'pc_address_cmpnt_getAreaCodeList',
    umps: [5],
  }),
  /** 获取级联地址 */
  getCascadeAddress: wrapper<
    {
      action: 'GetProvinces' | 'GetCitys' | 'GetAreas' | 'GetTowns'
      provinceId?: number
      cityId?: number
      countyId?: number
      townId?: number
      /**
       * 暂不知道:传'0'时，不展示暂不选择
       */
      unknow?: string
      /** 新建的版本，用于服务端区分新老编辑页：2为新版 */
      editVersion?: number
    },
    AddressStandard.CascadeAddress
  >({
    functionId: 'pc_address_cmpnt_getCascadeAddress',
    umps: [6],
  }),
  /** 地址联想 */
  getIntelligentText: wrapper<
    {
      text: string
      /**
       * 是否使用poi
       */
      usePoiAddress: boolean
      /**
       * 0:人工粘贴（地址粘贴）1:智能粘贴如不传入服务端默认为0
       */
      scene?: string
    },
    {
      addressInfo: {
        complete: boolean
        provinceName: string
        cityName: string
        countyName: string
        addressDetail: string
        mobile: string
        provinceId: number
        cityId: number
        countyId: number
      }
      addressInfoVOList: Array<AddressStandard.Address> // 文本转poi数据
      addressTips: string // 单条poi识别文案
      resultFlag: boolean // 识别结果，true-识别成功
      resultCode: string // 识别识别的类型：-1，识别失败
      message: string // 识别结果message
    }
  >({
    functionId: 'pc_address_cmpnt_getIntelligentText',
    umps: [7],
  }),
  /** 获取微信地址id对应的京东3级地址 */
  getWeChatAddress: wrapper<
    {
      /** 省名称 */
      provinceName: string
      /** 市名称 */
      cityName: string
      /** 区名称 */
      countyName: string
      /** 详细地址(对应微信返回detailInfo字段) */
      addressDetail: string
    },
    {
      addressInfo?: AddressStandard.Address
      resultFlag: boolean
      resultCode: string
      message: string
    }
  >({
    functionId: 'address_getWeChatAddress',
    umps: [8],
  }),
  /** 常用地址列表,接口文档：https://joyspace.jd.com/pages/juIeAtsBEZ7kcnKHZfxu */
  queryAddress: wrapper<AddressStandard.QueryAddressInParams & { authorize?: boolean }, AddressStandard.AddressInfo>({
    functionId: 'pc_address_cmpnt_queryAddress',
    umps: [9],
  }),

  /** 根据地址id查询地址信息 */
  queryAddressById: wrapper<
    AddressStandard.QueryAddressInParams,
    {
      addressInfo?: AddressStandard.Address
      addressTipsVoList?: Array<{ text?: string }>
    }
  >({
    functionId: 'address_cmpnt_querySingleAddress',
    umps: [10],
  }),

  /** 搜索地址::https://joyspace.jd.com/pages/JolL1601E4mVMxqE3mqO  */
  searchAddress: wrapper<
    {
      /** 地址的文字描述,并且以|号分割（如果没有，1,2,3,都不传）(10.1.0 加密上报)） */
      region: string
      /** 搜索内容 */
      keyword: string
      /**
       * 一级地址：（如果没有，1,2,3都不传）
       */
      provinceId?: string | number
      /**
       * 二级地址：（如果没有，1,2,3都不传）
       */
      cityId?: string | number
      /**
       * 三级地址：（如果没有，1,2,3都不传）
       */
      countyId?: string | number
      /**
       * 纬度
       */
      latitude?: string
      /**
       * 经度
       */
      longitude?: string
      /**
       * 纬度-加密
       */
      latitudeString?: string | number
      /**
       * 经度-加密
       */
      longitudeString?: string | number
      /**
       * true：加密，false：不加密
       */
      supportNewParamEncode?: boolean
      keyId?: string // 有加密串的需传
      /**
       * 是否是地址弹窗:用来判断下发几条数据(全屏和弹层下发数量不一样)
       */
      layerFlag?: boolean
    },
    {
      addressSuggestionVOList: Array<AddrSuggestType>
      /**
       * 请求ID
       */
      requestId: string
      resultFlag: boolean
    }
  >({
    functionId: 'pc_address_cmpnt_addressSearch',
    umps: [11],
    hideLoading: true, // 隐藏小狗子
  }),
  /** 设置默认地址 */
  setAddressDefault: wrapper<
    {
      /** 地址id */
      addressId: number
      /** 是否默认 */
      defaultAddress: boolean
    },
    void
  >({
    functionId: 'pc_address_cmpnt_setAddressDefault',
    umps: [12],
  }),
  /** 编辑地址 */
  updateAddress: wrapper<
    // @ts-ignore
    AddressStandard.EditFillInType & {
      keyId?: string
    },
    {
      addressId?: number
      townId?: number
      resultFlag: boolean
      message: string
    }
  >({
    functionId: 'pc_address_cmpnt_updateAddress',
    umps: [13],
  }),
  /** 根据经纬度获取地址详情 */
  getLocation: wrapper<
    {
      longitude?: number
      latitude?: number
      /** 如果使用定位门店范围（storeId  storeType venderId ）必传 并返回 */
      storeId?: number
      /**
       * 门店来源 1. 达达 2. 青龙 3:伽利略 4:门店帮 6:医药（泰国传0） 8:前置仓门店 9:重货仓
       */
      storeType?: number
      venderId?: number
    },
    {
      /** 地址信息列表 */
      addressInfo: AddressStandard.Address
      coverageStatus?: string
      /** 返回标识 成功或失败 */
      resultFlag: boolean
      resultCode: string
      /** 返回错误信息 */
      message: string
    }
  >({
    functionId: 'address_getLocation',
    umps: [14],
  }),
  /** 获取历史收货人推荐列表 */
  getUserSuggestionList: wrapper<undefined, AddressStandard.UserSuggestionListTypes>({
    functionId: 'address_cmpnt_recommendAddressInfo',
    umps: [15],
  }),
  /** 获取附近地址推荐列表 */
  getNearbyList: wrapper<
    {
      /** 加密后的经纬度 */
      latitudeString?: string | number
      longitudeString?: string | number
      keyId?: string // 加密需传
      abTests?: string // AB测试入参 '实验名1,实验名2'
    },
    AddressStandard.NearbyTypes
  >({
    functionId: 'address_cmpnt_queryNearbyAddressesList',
    umps: [16],
  }),
  /** 根据经纬度获取当前地址信息 */
  getCurrentAddress: wrapper<
    {
      /** 加密后的经纬度 */
      latitudeString?: string | number
      longitudeString?: string | number
      keyId?: string // 加密需传
    },
    AddressStandard.NearbyListTypes
  >({
    functionId: 'address_cmpnt_queryCurrentAddress',
    umps: [17],
  }),
  /** 根据全地址查询位置id相关信息 */
  fullAddressQueryId: wrapper<
    {
      provinceName: string
      cityName: string
      countyName: string
      addressDetail: string
      shortAddress: string
      longitudeString: string
      latitudeString: string
    },
    AddressStandard.NearbyListTypes
  >({
    functionId: 'address_cmpnt_queryAddressIdInfo',
    umps: [18],
  }),
  /** 获取推荐定位 */
  getRecommendAddress: wrapper<
    {
      /** 加密后的经纬度。统一通过env控制加密 */
      latitudeString?: string | number
      longitudeString?: string | number
      keyId?: string // 加密需传
    },
    AddressStandard.RecommendedAddressVO
  >({
    functionId: 'address_cmpnt_getRecommendAddress',
    umps: [19],
  }),
  /** 获取是否支持地址选择 */
  supportAddressSelect: wrapper<AddressStandard.SupportAddressSelectReqVO, AddressStandard.SupportAddressSelectVO>({
    functionId: 'address_cmpnt_supportAddressSelect',
    umps: [20],
  }),
  /** 校验超配接口 */
  coverage: wrapper<
    {
      shopInfoList: Array<AddressStandard.IShopInfoVO>
      locationInfoList: Array<AddressStandard.IOldLocationInfoVo>
      bizCode: string
      sceneCode: string
    },
    AddressStandard.IOldLocationInfoVo
  >({
    functionId: 'address_cmpnt_coverage',
    umps: [21],
  }),
  /** 获取两个坐标距离 */
  getDistance: wrapper<AddressStandard.GetDistanceReqVO, AddressStandard.GetDistanceVO>({
    functionId: 'address_cmpnt_distance',
    umps: [22],
  }),
  /** 地图选址，根据地图返回的区域信息，查询几级地区隐藏 */
  queryAreaInfo: wrapper<
    {
      provinceId?: number
      cityId?: number
      countyId?: number
      townId?: number
    },
    AddressStandard.Address
  >({
    functionId: 'address_cmpnt_queryAreaInfo',
    umps: [23],
  }),
}

export default apis
