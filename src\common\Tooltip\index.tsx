import styles from './index.module.scss'
import React, { useState, useEffect, useRef, forwardRef, useMemo, useImperativeHandle, type RefObject } from 'react'
import classNames from 'classnames'
import ReactDOM from 'react-dom'
import Popover from './Popover'
import Tooltip from './Tooltip'
import { throttle } from '@app/utils/throttledebounce'
type placement =
  | 'top'
  | 'left'
  | 'right'
  | 'bottom'
  | 'topLeft'
  | 'topRight'
  | 'bottomLeft'
  | 'bottomRight'
  | 'leftTop'
  | 'leftBottom'
  | 'rightTop'
  | 'rightBottom'
interface PropsType {
  /**
   * 节点内容
   **/
  children: React.ReactNode
  /**
   * 触发行为，可选 hover/click
   *
   * @default 'hover'
   **/
  trigger?: 'hover' | 'click'
  /**
   * 是否禁用
   *
   * @default false
   **/
  disabled?: boolean
  /**
   * 触发元素自定义className
   *
   **/
  className?: string
  /**
   * 下拉/提示框的classname
   *
   */
  tooltipCls?: string

  /**
   * 提示框标题
   *
   **/
  // title?: React.ReactNode
  /**
   * 下拉/提示框内容
   **/
  content?: React.ReactNode
  /**
   * 鼠标移入后延时多少才显示 Tooltip，单位：毫秒
   *
   * @default 0
   **/
  // mouseEnterDelay?: number
  /**
   * 鼠标移出后延时多少才隐藏 Tooltip，单位：毫秒
   *
   * @default 0
   **/
  mouseLeaveDelay?: number
  /**
   * 是否有箭头
   *
   * @default true
   **/
  arrow?: boolean
  /**
   * 气泡框位置，可选 top left right bottom topLeft topRight bottomLeft bottomRight leftTop leftBottom rightTop rightBottom
   *
   * @default 'bottom'
   **/
  placement?: placement

  /**
   * 显示隐藏的回调
   */
  onChange?: (visible: boolean) => void

  /**
   * 触发元素的ref
   */
  innerRef?: RefObject<any>

  /**
   * 弹出框与触发元素的距离， 默认6px
   */
  distance?: number
  /**
   * 下拉/提示框容器宽度
   */
  width?: number

  /**
   * 下拉/提示框容器最小宽度
   */
  minWidth?: number

  /**
   * 下拉/提示框容器最大宽度
   */
  maxWidth?: number

  /**
   * 下拉/提示框容器高度，默认高度为332px
   */
  height?: number

  /**
   * 下拉/提示框容器最大高度
   */
  maxHeight?: number

  /**
   * 容器内边距，默认为16px
   */
  padding?: string

  /**
   * 边框颜色
   */
  borderColor?: string

  /**
   * 边框圆角
   */
  borderRadius?: string

  /**
   * 下拉/提示框类型，默认 tooltip
   * 可选值：tooltip | popover
   */
  type?: 'tooltip' | 'popover'

  /**
   * 下拉/提示框背景色
   */
  backgroundColor?: string
  renderElement?: React.ReactNode
}

export type TooltipActions = {
  hide: () => void
  show: () => void
}

export type TooltipRef = TooltipActions

const validateRef = (ref?: RefObject<any>): boolean => {
  return ref && ref.current
}

const CustomTooltip = forwardRef<TooltipRef, PropsType>((props, ref) => {
  const {
    children,
    trigger = 'hover',
    disabled,
    onChange,
    className,
    content,
    arrow,
    innerRef,
    distance = 6,
    width,
    minWidth,
    maxWidth,
    height,
    maxHeight,
    padding,
    borderColor,
    borderRadius,
    backgroundColor,
    mouseLeaveDelay = 200,
    type = 'tooltip',
    tooltipCls,
    renderElement,
  } = props
  const throttleTimer = useRef<NodeJS.Timeout | null>(null)
  const triggerRef = useRef<HTMLDivElement>(null as unknown as HTMLDivElement)
  const containerRef = useRef<HTMLDivElement>(null as unknown as HTMLDivElement)
  const [positionStyle, setPositionStyle] = useState<React.CSSProperties>({})
  const [placement, setPlacement] = useState<placement>(props.placement || 'bottom')
  // 控制显示隐藏
  const [visible, setVisible] = useState(false)
  // 窗口大小和滚动计数
  const [count, setCount] = useState<number>(0)

  /**
   * 隐藏
   */
  const hide = () => {
    setVisible(false)
  }
  /**
   * 显示
   */
  const show = () => {
    setVisible(true)
  }

  /**
   * 鼠标移入
   */
  const handleMouseEnterTrigger = () => {
    if (throttleTimer.current) {
      clearTimeout(throttleTimer.current)
    }
    trigger === 'hover' && show()
  }

  /**
   * 鼠标移出
   */
  const handleMouseLeaveTrigger = () => {
    throttleTimer.current = setTimeout(() => {
      trigger === 'hover' && hide()
    }, mouseLeaveDelay)
  }
  /**
   * 窗口大小改变时 | 滚动时更新位置
   */
  const update = throttle(100, () => {
    if (validateRef(innerRef) || validateRef(triggerRef)) {
      setCount((prevCount) => prevCount + 1)
    }
  })

  useEffect(() => {
    if (!visible) return
    let styleObj = {}
    let eleAttr
    let _placement: placement = props.placement || 'bottom'
    // 父组件传入的
    if (innerRef?.current) {
      eleAttr = innerRef.current.getBoundingClientRect()
    } else {
      eleAttr = triggerRef.current.getBoundingClientRect()
    }
    // 获取页面垂直滚动距离
    const scrollY = document.documentElement.scrollTop || document.body.scrollTop
    // 获取页面水平滚动距离
    const scrollX = document.documentElement.scrollLeft || document.body.scrollLeft
    const wrapAttr = containerRef.current.getBoundingClientRect()
    // 顶部位置
    const top = eleAttr.top + scrollY
    // 左侧位置
    const left = eleAttr.left + scrollX
    const offsetCenterLeft = left + eleAttr.width / 2 - wrapAttr.width / 2
    const offsetCenterTop = top + eleAttr.height / 2 - wrapAttr.height / 2
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight
    // 是否超出屏幕右侧
    const isOverflowRight = left + wrapAttr.width > windowWidth
    // 是否超出屏幕底部
    const isOverflowBottom = eleAttr.top + eleAttr.height + wrapAttr.height + distance > windowHeight
    // 是否超出屏幕顶部
    const isOverflowTop = eleAttr.top < wrapAttr.height
    // 超出屏幕右侧
    if (_placement?.toLowerCase()?.includes('bottom') && isOverflowRight) {
      _placement = 'bottomRight'
    }
    // 超出屏幕底部
    if (_placement?.toLowerCase()?.includes('bottom') && isOverflowBottom) {
      _placement = _placement.replace(/bottom/g, 'top') as placement
    }
    // 超出屏幕顶部
    if (_placement?.toLowerCase()?.includes('top') && isOverflowTop) {
      _placement = _placement.replace(/top/g, 'bottom') as placement
    }

    switch (_placement) {
      case 'topLeft':
        styleObj = {
          left: `${left}px`,
          top: `${top - wrapAttr.height - distance}px`,
        }
        break
      case 'top':
        styleObj = {
          left: `${offsetCenterLeft}px`,
          top: `${Math.round(top - wrapAttr.height - distance)}px`,
        }
        break
      case 'topRight':
        styleObj = {
          left: `${left + eleAttr.width - wrapAttr.width}px`,
          top: `${top - wrapAttr.height - distance}px`,
        }
        break
      case 'rightTop':
        styleObj = {
          left: `${left + eleAttr.width + distance}px`,
          top: `${top}px`,
        }
        break
      case 'right':
        styleObj = {
          left: `${left + eleAttr.width + distance}px`,
          top: `${offsetCenterTop}px`,
        }
        break
      case 'rightBottom':
        styleObj = {
          left: `${left + eleAttr.width + distance}px`,
          top: `${top + eleAttr.height - wrapAttr.height}px`,
        }
        break
      case 'bottomLeft':
        styleObj = {
          left: `${left}px`,
          top: `${top + eleAttr.height + distance}px`,
        }
        break
      case 'bottom': {
        styleObj = {
          left: `${offsetCenterLeft}px`,
          top: `${Math.round(top + eleAttr.height + distance)}px`,
        }
        break
      }
      case 'bottomRight':
        styleObj = {
          left: `${left + eleAttr.width - wrapAttr.width}px`,
          top: `${top + eleAttr.height + distance}px`,
        }
        break
      case 'leftTop':
        styleObj = {
          left: `${left - wrapAttr.width - distance}px`,
          top: `${top}px`,
        }
        break
      case 'left':
        styleObj = {
          left: `${left - wrapAttr.width - distance}px`,
          top: `${offsetCenterTop}px`,
        }
        break
      case 'leftBottom':
        styleObj = {
          left: `${left - wrapAttr.width - distance}px`,
          top: `${top + eleAttr.height - wrapAttr.height}px`,
        }
        break
    }
    setPlacement(_placement)
    setPositionStyle(styleObj)
  }, [visible, count])

  useEffect(() => {
    onChange?.(visible)
  }, [visible])
  useEffect(() => {
    // 当点击页面其他位置时隐藏
    const documentHandler = (event: Event) => {
      if (
        validateRef(containerRef) &&
        validateRef(triggerRef) &&
        !containerRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        hide()
      }
    }

    document.addEventListener('mousedown', documentHandler)
    return () => {
      document.removeEventListener('mousedown', documentHandler)
    }
  }, [containerRef, triggerRef])

  useEffect(() => {
    window.addEventListener('resize', update)
    window.addEventListener('scroll', update)
    return () => {
      window.removeEventListener('resize', update)
      window.removeEventListener('scroll', update)
    }
  }, [])

  /**
   * 下拉/提示框容器样式
   */
  const containerBodyStyle = useMemo(() => {
    const obj: any = {}

    if (width) {
      obj.width = `${width}px`
    }
    if (minWidth) {
      obj.minWidth = `${minWidth}px`
    }

    if (maxWidth) {
      obj.maxWidth = `${maxWidth}px`
      obj.width = `max-content`
    }

    if (padding) {
      obj.padding = padding
    } else {
      obj.padding = '16px'
    }

    if (height) {
      obj.height = `${height}px`
    } else {
      obj.maxHeight = '332px'
      obj.overflow = 'auto'
    }

    if (maxHeight) {
      obj.maxHeight = `${maxHeight}px`
      obj.overflow = 'auto'
    }

    if (borderColor) {
      obj.borderColor = borderColor
    } else {
      obj.borderColor = 'rgb(0, 0, 0, 0.06)'
    }
    if (borderRadius) {
      obj.borderRadius = borderRadius
    }
    if (backgroundColor) {
      obj.backgroundColor = backgroundColor
    } else {
      if (type === 'popover') {
        obj.backgroundColor = '#fff'
      } else if (type === 'tooltip') {
        obj.backgroundColor = 'rgba(0, 0, 0, 0.75)'
      }
    }

    return obj
  }, [width, padding, height, borderColor, maxHeight, minWidth, maxWidth, borderRadius, backgroundColor, type])

  useImperativeHandle(ref, () => ({
    hide,
    show,
    triggerRef,
  }))

  const renderChildrenSlot = () => {
    if (renderElement) {
      return (
        <span
          className={classNames(styles.custom, className)}
          ref={triggerRef}
          onMouseEnter={handleMouseEnterTrigger}
          onMouseLeave={handleMouseLeaveTrigger}
          onClick={() => {
            trigger === 'click' && !disabled && show()
          }}
        >
          {children}
        </span>
      )
    }
    return (
      <span className={classNames(styles.custom, className && className)}>
        {React.isValidElement(children) ? (
          React.cloneElement(children, {
            ref: triggerRef,
            onMouseEnter: handleMouseEnterTrigger,
            onMouseLeave: handleMouseLeaveTrigger,
            onClick: (e) => {
              // e.stopPropagation()
              trigger === 'click' && !disabled && show()
            },
          } as React.DOMAttributes<HTMLDivElement>)
        ) : (
          <div
            ref={triggerRef}
            onMouseEnter={handleMouseEnterTrigger}
            onMouseLeave={handleMouseLeaveTrigger}
            onClick={() => {
              trigger === 'click' && !disabled && show()
            }}
          >
            {children}
          </div>
        )}
      </span>
    )
  }

  return (
    <>
      {renderChildrenSlot()}
      {visible &&
        ReactDOM.createPortal(
          // 弹窗内容
          <div
            className={classNames(type === 'popover' ? styles.popoverCon : styles.tooltipCon, styles.customCon)}
            ref={containerRef}
            style={positionStyle}
            onMouseEnter={handleMouseEnterTrigger}
            onMouseLeave={handleMouseLeaveTrigger}
            onClick={(e) => {
              e.stopPropagation()
            }}
          >
            {arrow && (
              <div
                className={classNames(
                  styles.tooltipConArrow,
                  placement.includes('top') && styles.tooltipConArrowBottom,
                  placement.includes('bottom') && styles.tooltipConArrowTop,
                  // placement === 'left' && styles.tooltipConArrowRight,
                  // placement === 'right' && styles.tooltipConArrowLeft,
                )}
              />
            )}

            <div style={containerBodyStyle} className={classNames(styles.customConInner, tooltipCls)}>
              {type === 'popover' ? <Popover>{content}</Popover> : <Tooltip>{content}</Tooltip>}
            </div>
          </div>,
          document.body,
        )}
    </>
  )
})

export default CustomTooltip
