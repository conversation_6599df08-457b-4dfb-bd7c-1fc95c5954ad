import './index.scss'
import React, { useState, useMemo, useEffect, useRef } from 'react'
import { useAtom, useAtomValue } from 'jotai'
import phoneNumberAtom from './atoms/phoneNumberAtom'
import { presalePayTypeAtom } from '@app/atoms/presaleAtom'
import useMasterData from '@app/hooks/useMasterData'
import { TW_HK_OVERSEA_ID } from '@app/services/const'

const Presale: React.FC = () => {
  const inputRef = useRef<HTMLInputElement>(null)
  const [errorMsg, setErrorMsg] = useState<string>()
  const [isVisible, setIsVisible] = useState(false)
  const [phoneNumber, setPhoneNumber] = useAtom(phoneNumberAtom)
  const masterData = useMasterData()?.body
  const presale = masterData?.balancePresaleVO
  const presalePayType = useAtomValue(presalePayTypeAtom)
  // 是否预售定金支付
  const isDepositPay = ['3', '5'].includes(presale?.payStepType || '') || presalePayType === 2
  const balanceAddress = masterData?.balanceAddress
  const mobile = balanceAddress?.mobile
  const provinceId = balanceAddress?.provinceId
  const toggle = useMemo(
    () => () => {
      setIsVisible((s) => !s)
      setErrorMsg(undefined)
    },
    [],
  )

  useEffect(() => {
    if (mobile) {
      setPhoneNumber(mobile)
    }
  }, [mobile])

  const handleClick = useMemo(
    () => () => {
      let value = inputRef.current?.value
      if (!value) return toggle()
      value = value.replace(/\s/g, '')
      const isValid = TW_HK_OVERSEA_ID.includes(+provinceId!) ? /^\d{1,17}$/.test(value) : /^\d{11}$/.test(value)
      if (!isValid) return setErrorMsg('请输入正确的手机号码')
      setPhoneNumber(value)
      toggle()
    },
    [provinceId],
  )

  return (
    <div className="presale">
      <div className="presale-title">预售付款方式</div>
      {isDepositPay ? (
        <div className="presale-contact">
          尾款支付联系方式
          {!isVisible && (
            <span className="presale-contact-tel">
              {phoneNumber}
              <i onClick={toggle} />
            </span>
          )}
          {isVisible && (
            <label className="presale-contact-tel-input">
              <input type="text" name="tel" ref={inputRef} maxLength={TW_HK_OVERSEA_ID.includes(+provinceId!) ? 17 : 11} />
              <span onClick={handleClick}>确定</span>
              {errorMsg && <div className="error-msg">{errorMsg}</div>}
            </label>
          )}
        </div>
      ) : (
        <div className="presale-desc"> 全款支付（优先发货） </div>
      )}
    </div>
  )
}
Presale.displayName = 'Presale'
export default Presale
