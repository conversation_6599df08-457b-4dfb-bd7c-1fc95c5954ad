// .selector_wrap {
//   max-width: 100%;
//   display: inline-flex;
//   flex-wrap: wrap;
//   flex-direction: row;
//   align-items: center;
//   justify-content: start;
//   gap: 8px;
//   box-sizing: border-box;
//   border-radius: 4px;
//   //background: #F7F8FC;
//   //cursor: pointer;
//   .item {
//     position: relative;
//   }

//   .selected {
//     border: 0.5px solid #FF8595!important;
//   }
// }

.selector_item_wrap {
  cursor: pointer;

  height: 36px;
  padding: 9px 12px;
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  gap: 8px;
  box-sizing: border-box;
  // max-width: 315px;
  border-radius: 4px;
  background: #f7f8fc;
  border: 0.5px solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  max-width: 100%;
  position: relative;
  padding-right: 32px;


  .item {
    color: #1a1a1a;
    font-size: 14px;
    font-family: 'PingFang SC';
    font-weight: 500;
    letter-spacing: 0px;
    text-align: right;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    img {
      width: 8px;
      height: 8px;
    }
  }
  // .item:nth-child(2) {
  //   overflow: hidden;
  //   white-space: nowrap;
  //   text-overflow: ellipsis;
  // }
  .item:nth-child(3) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }
}
