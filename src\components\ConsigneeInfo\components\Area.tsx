/*
 * @Author: ext.xuchao26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息地址组件封装
 */
import { useEffect, useRef, forwardRef, useImperativeHandle, useContext } from 'react'
import './area.scss'
import AreaContext from '@app/common/legao/Context'

interface AreaProps {
  value: string
  scopeLevel: number
  writeCookie?: boolean
  placement?: 'bottom' | 'bottomRight' | 'bottomLeft' | undefined
  showWaitData?: string
  isUpScroll?: boolean
  reLevel?: boolean
  topClassName?: string
  onChange?: (area: any, local: any) => void
}

const Area = (
  { value, scopeLevel = 4, placement = 'bottomRight', writeCookie = false, topClassName = 'jd-consignee', ...props }: AreaProps,
  ref: any,
) => {
  // 使用ref记录area实例
  const areaInstanceRef = useRef<any>(null)
  // 使用ref记录初始化状态
  const initializedRef = useRef<boolean>(false)
  // form表单的context上下文
  const { form }: { form: any } = useContext(AreaContext) || {}

  const onChange = (area: any, local: any) => {
    props?.onChange?.(area, local)
    // 自动去除form表单的验证error信息
    form && form.onFieldChange()
  }

  useImperativeHandle(ref, () => {
    if (areaInstanceRef.current) {
      const { areaInstance } = areaInstanceRef.current || {}
      return areaInstance.current
    }
  })

  function renderAreaBeforeFilter(this: any, index: number) {
    if (props.showWaitData !== 'all') return
    let clstag = ''
    //判断直辖市
    if (this.isMunicipality(this.data.tempLocalId[0])) {
      if (index == 1) {
        clstag = 'pageclick|keycount|201704194|3'
      }
      if (index == 2) {
        clstag = 'pageclick|keycount|201704194|4'
      }
    } else {
      if (index == 2) {
        clstag = 'pageclick|keycount|201704194|3'
      }
      if (index == 3) {
        clstag = 'pageclick|keycount|201704194|4'
      }
    }
    // 等地址组件改为纯react后，再改
    this.options.showWaitTpl =
      '<li><a data-id="wait" ' +
      (clstag ? 'clstag="' + clstag + '"' : '') +
      ' href="javascript:void(0)" style="color:#f10215;background: none;">全部</a></li>'
  }

  useEffect(() => {
    // 如果已经初始化过，直接返回
    if (initializedRef.current) {
      return
    }

    try {
      if (window.$ && window.area) {
        // 如果存在旧实例，先销毁
        if (areaInstanceRef.current) {
          const { root } = areaInstanceRef.current || {}
          root?.unmount()
        }

        // 创建新实例
        areaInstanceRef.current = window.area(window.$(`#area-2025-${topClassName}`), {
          topClassName,
          placement,
          hasOversea: true,
          scopeLevel,
          showLoading: true,
          selectedClose: true,
          initArea: value,
          showAreaTextSeparator: '/',
          cookieOpts: { path: '/', domain: 'jd.com', expires: 30 },
          isNotEmpty: false,
          repLevel: false,
          isTop: true,
          writeCookie,
          renderAreaBeforeFilter: function (index: number) {
            renderAreaBeforeFilter?.call?.(this, index)
          },
          ...props,
          onChange,
        })
        const referEl = document.getElementById(topClassName + '-' + 'area-refer')
        referEl?.classList?.add('jd-settle')

        // 标记为已初始化
        initializedRef.current = true
      }
    } catch (error) {
      console.error(error)
    }
  }, [value])

  return (
    <div id={'area-2025' + '-' + topClassName} className="ui-area-wrap settle">
      <div className="ui-area-text-wrap">
        <div className="ui-area-text ftx14">请选择省/市/区</div>
        <b></b>
      </div>
    </div>
  )
}

export default forwardRef(Area)
