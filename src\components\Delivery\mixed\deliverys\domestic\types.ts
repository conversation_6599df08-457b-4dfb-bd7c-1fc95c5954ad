/**
 * @file: types.ts
 * @description: 国内配送组件类型定义
 */
import { ComShipmentType, PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp'
import { SaveType } from '../../saveDelivery'
import { List as HeaderList } from '../../../ui/header'
import { DeliveryDataIniter } from '../../initDeliveryData'

// 配送状态类型
export interface DeliveryState {
  headerList: HeaderList<ComShipmentType, PromiseListItem>
  promise: PromiseListItem | null
  shipmentInfo: ComShipmentType | null
  selectValue: { type: SaveType; value: any } | null
  isEmpty: boolean
}

// 选择值类型
export interface SelectValue {
  type: SaveType
  value: any
}

// 配送组件 Props 类型
export interface DeliveryComponentProps {
  deliveryDataIniter: DeliveryDataIniter // DeliveryDataIniter 类型
  defaultTsx: () => React.ReactElement
  onCancel: () => void
}

// 头部列表项类型
export type HeaderListItem = {
  text: string
  isSelected: boolean
  targetValue: ComShipmentType
  sonList: {
    text: string
    isSelected: boolean
    targetValue: PromiseListItem
  }[]
}