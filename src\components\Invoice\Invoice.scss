// Variables
$primary-color: #f23030;
$text-primary: #1a1a1a;
$text-secondary: #505259;
$text-muted: #888b94;
$text-light: #c2c4cc;
$border-color: #e5e6eb;
$background-light: #f7f8fc;
$background-hover: #fff7f7;
$font-family: 'PingFang SC';

.invoice-note {
  font-size: 12px;
  display: flex;
  line-height: 16px;
  margin-top: 4px;
  color: rgb(153, 153, 153);
  font-family: $font-family;
  font-weight: 400;
  letter-spacing: 0px;
  text-align: left;
  display: flex;

  img {
    width: 12px;
    height: 12px;
    margin-right: 4px;
    margin-top: 4px;
  }

  .invoice-note-text {
    font-size: 12px;
    color: rgb(153, 153, 153);
    margin-left: 4px;
    // 备注： 文字太多隐藏样式，后续有需要再打开
    // max-height: 50px;
    // overflow: hidden;
    // display: -webkit-box;
    // -webkit-line-clamp: 3;
    // -webkit-box-orient: vertical;
    // text-overflow: ellipsis;
  }
}

.subtitle {
  font-size: 14px;
  color: #666;
  margin-left: 10px;
  font-weight: normal;
}

/* 发票类型Tab样式 */
.invoice-tabs {
  display: flex;
  margin-bottom: 10px;
  justify-content: flex-start;
  align-items: center;
}

.invoice-tab {
  height: 36px;
  padding: 0 12px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  text-align: center;
  margin-right: 8px;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f7f7f7;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #ffccc7;
    background-color: $background-hover;
  }

  &.active {
    color: white;
    background-color: $primary-color;
    border-color: $primary-color;
    font-weight: bold;

    &::after {
      display: none;
    }
  }
}

.step-btn {
  margin-right: 8px;
}

/* 发票信息楼层样式 */
.invoice-info {
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding: 0 16px 24px;
  margin-bottom: 16px;
  border: 1px solid #ffffff;
  border-radius: 8px;
  background-color: #ffffff;
  font-family: $font-family;

  &-content {
    margin: 20px 0 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-title {
    color: $text-primary;
    font-family: $font-family;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0px;
    display: flex;
    align-items: center;
  }
  &-tips {
    color: rgb(136, 139, 148);
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 14px;
    letter-spacing: 0px;
    margin-left: 8px;
  }

  &-notice {
    width: 72px;
    height: 16px;
    font-size: 14px;
    color: $text-muted;
    line-height: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-family: $font-family;
    font-weight: 400;
  }

  &-details {
    height: 14px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.notice-arrow {
  width: 12px;
  height: 12px;
  margin-left: 4px;
}

.invoice-type {
  font-size: 14px;
  color: $text-secondary;
  line-height: 14px;
  font-family: $font-family;
  font-weight: 400;
  letter-spacing: 0px;
  text-align: right;
  display: flex;
  gap: 8px;
  align-items: center;
}

.invoice-edit {
  font-size: 14px;
  line-height: 14px;
  font-weight: 400;
  letter-spacing: 0px;
  cursor: pointer;
  color: #505259;
}

.invoice-disclaimer {
  margin-bottom: 20px;
}

.arrow-right {
  width: 8px;
  height: 8px;
  margin-left: 4px;
}

.invoice-info-notice {
  color: #888b94;
  font-weight: 400;
  font-size: 14px;
  letter-spacing: 0px;
}
