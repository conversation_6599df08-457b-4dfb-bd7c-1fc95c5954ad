// import Taro from '@tarojs/taro'

import apis from '../api'

export interface GetAddrInfoType {
  list: Array<AddressStandard.Areas> // 地址列表
  hotMainlandAreas: undefined | Array<AddressStandard.Areas> // 热门城市列表
  hotOverseaAreas: undefined | Array<AddressStandard.Areas> // 港澳台及海外热门城市
  topAreas: undefined | Array<AddressStandard.Areas> // 顶部tab区域（中国大陆|港澳台及海外）
  infos: Array<AddressStandard.Areas> // areaid对应的区域地址详细信息
  idList: Array<number> // // areaid对应的区域地址id
  nameList: Array<string> // areaid对应的地址名称
  areaCode: string // 仅海外地址有值
  nameCode: string // 仅海外地址有值
  nextLevel: number // 下一级地址的等级
  complete: boolean // 是否完整地址
  isErrorAddr: boolean // 是否错误的地址。0_0_0_0、地址长度超出4段、areid中包含错误的id等场景，都算错误地址
  done: boolean
}

interface CascadeParams {
  action?: 'GetProvinces' | 'GetCitys' | 'GetAreas' | 'GetTowns'
  provinceId?: number
  cityId?: number
  countyId?: number
  townId?: number
  /**
   * 暂不知道:传'0'时，不展示暂不选择
   */
  unknow?: string
  /** 实验参数 */
  abTests?: string
  /** 新建的版本，用于服务端区分新老编辑页：2为新版 */
  editVersion?: number
}

/** 扩展参数 */
interface Extra {
  /** 暂不知道:传'0'时，不展示暂不选择 */
  unknow?: string
  /** 实验参数 */
  abTests?: string
  /** 是否隐藏末级地区 */
  hidefourthAddress?: boolean
  /** 新建的版本，用于服务端区分新老编辑页：2为新版 */
  editVersion?: number
}

//
const ACTION_PARAMS: Array<'GetProvinces' | 'GetCitys' | 'GetAreas' | 'GetTowns'> = ['GetProvinces', 'GetCitys', 'GetAreas', 'GetTowns']
const addrCache: Record<string, any> = {}
/**
 * 获取省市县区地址列表
 * @param params
 */
const getCascadeList = (params: CascadeParams, level: number): Promise<AddressStandard.CascadeAddress> => {
  const { cityId, countyId, townId } = params

  // 获取级联地址的action参数
  const action = ACTION_PARAMS[level]
  // unknow未发生变化
  const unknowIsEqual = params?.unknow === addrCache[`${action}Unknow`]
  // 省信息
  if (addrCache.provinceListCache && action === 'GetProvinces' && unknowIsEqual) {
    return Promise.resolve(addrCache.provinceListCache)
  }

  // 市信息
  if (
    cityId &&
    unknowIsEqual &&
    action === 'GetCitys' &&
    addrCache?.cityListCache &&
    Object.prototype.hasOwnProperty.call(addrCache?.cityListCache, cityId)
  ) {
    return Promise.resolve(addrCache.cityListCache[cityId])
  }

  // 县信息
  if (
    countyId &&
    unknowIsEqual &&
    action === 'GetAreas' &&
    addrCache?.areasListCache &&
    Object.prototype.hasOwnProperty.call(addrCache?.areasListCache, countyId)
  ) {
    return Promise.resolve(addrCache.areasListCache[countyId])
  }

  // 街道信息缓存
  if (
    townId &&
    unknowIsEqual &&
    action === 'GetTowns' &&
    addrCache?.townListCache &&
    Object.prototype.hasOwnProperty.call(addrCache?.townListCache, townId)
  ) {
    return Promise.resolve(addrCache.townListCache[townId])
  }

  // 重置
  addrCache[`${action}Unknow`] = undefined
  action === 'GetCitys' && (addrCache.cityListCache = {})
  action === 'GetAreas' && (addrCache.areasListCache = {})
  action === 'GetTowns' && (addrCache.townListCache = {})

  return apis.getCascadeAddress({ ...params, action }).then((res) => {
    // 更新省缓存townId > 0 || (townId === 0 && countyId > 0)
    if (action === 'GetProvinces') {
      addrCache.provinceListCache = res
      // unknow有变化时重新调接口
      addrCache[`${action}Unknow`] = params?.unknow
    }
    // 更新市缓存
    if (cityId && action === 'GetCitys') {
      addrCache.cityListCache[cityId] = res
      // unknow有变化时重新调接口
      addrCache[`${action}Unknow`] = params?.unknow
    }
    // 更新县缓存
    if (countyId && action === 'GetAreas') {
      addrCache.areasListCache[countyId] = res
      // unknow有变化时重新调接口
      addrCache[`${action}Unknow`] = params?.unknow
    }
    // 更新街道缓存,townId 在直辖市中会为0
    if (typeof townId === 'number' && action === 'GetTowns') {
      addrCache.townListCache[townId] = res
      // unknow有变化时重新调接口
      addrCache[`${action}Unknow`] = params?.unknow
    }
    if (townId == 0) res = { ...res, resultFlag: false }

    return Promise.resolve(res)
  })
}
// 四级地址对应key
const ADDR_PARAMS = ['provinceId', 'cityId', 'countyId', 'townId']

/**
 *
 * @param level-第XX级地址
 * @param idList
 * @param extra - 扩展参数
 */
const loadLevel = (level: number, idList: Array<number> = [], extra?: Extra): Promise<AddressStandard.CascadeAddress> => {
  // level>2,即最后一级不调接口
  if (level > 3) {
    return Promise.reject()
  }

  const val: CascadeParams = {}

  // 处理接口入参，四级地址有值就传
  for (let i = 1; i <= level; i++) {
    val[ADDR_PARAMS[i]] = idList?.[i - 1]
  }
  level === 0 && (val[ADDR_PARAMS[level]] = idList?.[0])
  // 调用接口
  return getCascadeList({ ...val, unknow: extra?.unknow, abTests: extra?.abTests, editVersion: extra?.editVersion }, level)
}

/**
 * 根据areaid获取地址详细信息
 * areaid, 用下划线拼接，示例：'19_1607_4773_0'
 * extra, - 扩展参数
 */

let extraCache: Extra = {}
const getAddrInfoByAreaId = (areaid: string, extra?: Extra): Promise<GetAddrInfoType & AddressStandard.CascadeAddress> => {
  extraCache = { ...(extraCache || {}), ...(extra || {}) }
  const areaIdList = areaid ? areaid.split('_') : []

  const isErrorAddr = areaIdList?.length > 4
  const result = {
    list: [], // 地址列表
    infos: [], // areaid对应的区域地址详细信息
    idList: [0, 0, 0, 0], // // areaid对应的区域地址id
    nameList: ['', '', '', ''], // areaid对应的地址名称
    areaCode: '86', // 仅海外地址有值
    nameCode: '', // 仅海外地址有值
    nextLevel: 0, // 下一级地址的等级
    complete: !isErrorAddr, // 是否完整地址
    isErrorAddr, // 是否错误的地址。0_0_0_0、地址长度超出4段、areid中包含错误的id等场景，都算错误地址
    hotMainlandAreas: [], // 中国热门城市
    hotOverseaAreas: [], // 港澳台及海外热门城市
    topAreas: [], // 顶部tab区域（中国大陆|港澳台及海外）
    done: false,
  }
  const getAddrInfo = function (promise, curVal, curIndex, arr) {
    return promise.then((res) => {
      // 如果传入的四级地址不合法，不用向下执行
      if (res.done || !res.complete || res.isErrorAddr || (res.infos?.length && res.infos.slice(-1)[0]?.showNext === false)) {
        return Promise.resolve(res)
      }
      return loadLevel(curIndex, arr, extraCache)
        .then((data) => {
          const list = data?.wordCodeResult // 之前代码是获取的areas数据，因areas数据不需要下发，因此代码获取为wordCodeResult数据，并还原之前查找数据逻辑
          const otherAreas = data?.otherAreas
          const listAreas: Array<Partial<AddressStandard.Areas>> = []
          if (list && list.length) {
            // 当前元素是否存在于列表中
            // 还原老逻辑，将wordCodeResult处理到一个数组中，还原之前areas数据逻辑
            for (const key in list) {
              listAreas.push(...list[key].value)
            }
            const item = listAreas.find((it) => it.addrId === Number(curVal)) || otherAreas?.find((it) => it.addrId === Number(curVal))
            // 热门海外地址
            if (data?.hotOverseaAreas) {
              res.hotOverseaAreas = data?.hotOverseaAreas
            }
            // 热门大陆地址
            if (data?.hotMainlandAreas) {
              res.hotMainlandAreas = data?.hotMainlandAreas
            }
            // 顶部地址
            if (data?.topAreas) {
              res.topAreas = data?.topAreas
            }
            // 字母归堆数据
            if (data?.wordCodeResult) {
              res.wordCodeResult = data.wordCodeResult
            }
            // 字母排序列表
            if (data?.wordList) {
              res.wordList = data.wordList
            }
            // ab实验字段
            if (data?.abResult) {
              res.abResult = data?.abResult
            }

            res.list = list
            if (!data?.resultFlag) {
              res.done = true
              res.complete = false
            }

            if (item) {
              res.infos.push({ ...item })
              res.idList[curIndex] = item.addrId
              // 999999是暂不选择，不展示到所在地区
              res.nameList[curIndex] = item.addrId === 999999 ? '' : item.name
              if (item.areaCode) {
                res.areaCode = item.areaCode
              }
              if (item.nameCode) {
                res.nameCode = item.nameCode
              }
            } else {
              res.done = true
              res.complete = false
            }
            res.nextLevel = curIndex
          } else {
            res.done = true
          }

          return Promise.resolve(res)
        })
        .catch((err) => {
          console.error('ERROR:', err)
        })
    })
  }

  const [provinceId, cityId, countyId, townId] = areaIdList
  // 调用接口级数。areaIdList不包含undefined项。(shownext是false并且命中实验hidefourthAddress为true，不调用下级数据接口)
  const reduceArr = [provinceId, cityId, countyId, townId]
  return reduceArr.reduce(getAddrInfo, Promise.resolve(result))
}

/**
 * 获取地址授权
 */
const getWxAuthorize = (): Promise<any> => {
  //   if (!Taro.authorize) {
  //     return Promise.reject(Error('Taro.authorize API no found'))
  //   }
  //   return new Promise((resolve, reject) => {
  //     Taro.authorize({
  //       scope: 'scope.userLocation',
  //       success: resolve,
  //       fail: reject,
  //     })
  //   })
  return Promise.resolve()
}

const getWxLocation = (): Promise<any> => {
  //   if (!Taro.getLocation) {
  //     return Promise.reject(Error('Taro.getLocation API no found'))
  //   }
  //   return new Promise((resolve, reject) => {
  //     Taro.getLocation({
  //       type: 'gcj02',
  //       success(data) {
  //         if (data && data.latitude && data.longitude) {
  //           resolve(data)
  //         } else {
  //           reject(data)
  //         }
  //       },
  //       fail: reject,
  //     })
  //   })
  return Promise.resolve()
}

/**
 * 查询用户定位授权状态
 */
const getLbsAuthStatus = (): Promise<any> => {
  //   if (!Taro.getSetting) {
  //     return Promise.reject(Error('Taro.getSetting API no found'))
  //   }
  //   return new Promise((resolve, reject) => {
  //     Taro.getSetting({
  //       success(data) {
  //         if (data && data.authSetting) {
  //           const scope = data.authSetting['scope.userLocation']
  //           const result = typeof scope === 'undefined' ? 0 : scope === true ? 1 : 2
  //           resolve(result)
  //         }
  //         reject(data)
  //       },
  //       fail: reject,
  //     })
  //   })
  return Promise.resolve()
}
// 调用微信 Taro.authorize 获取地址授权
function getWxChooseLocation(params?: { latitude: number; longitude: number }): Promise<any> {
  //   if (!Taro.chooseLocation) {
  //     return Promise.reject(Error('Taro.chooseLocation API no found'))
  //   }
  //   return new Promise((resolve, reject) => {
  //     const { latitude = 0, longitude = 0 } = params || {}
  //     const data = {
  //       type: 'gcj02',
  //       success(res) {
  //         const isSuccess = res && res.latitude && res.longitude
  //         return isSuccess ? resolve(res) : reject(res)
  //       },
  //       fail: (res) => {
  //         return reject(res)
  //       },
  //     }
  //     latitude &&
  //       longitude &&
  //       Object.assign(data, {
  //         longitude: Number(longitude),
  //         latitude: Number(latitude),
  //       })
  //     Taro.chooseLocation(data)
  //   })
  return Promise.resolve()
}

/**
 * 获取用户当前位置的四级地址
 */
const getCurLbsAddr = (params: {
  venderid?: string
  shopid?: string
  shoptype?: string
  longitude: number
  latitude: number
  wxAddress: string
  wxAddrName: string
}): Promise<
  Partial<{
    areaId: string
    areaName: string
    latitude: number
    longitude: number
    coverageStatus: string
  }>
> => {
  return getWxAuthorize()
    .then(() => getWxLocation())
    .then((res) => getLocation({ ...(params || {}), ...(res || {}) }))
    .then((data) => {
      const result = {
        areaId: data.areaId, // 区域地址id
        areaName: data.areaName, // 区域地名称， 如 "广东省_深圳市_南山区_南头街道"
        latitude: data.latitude,
        longitude: data.longitude,
        coverageStatus: data.coverageStatus,
      }
      return Promise.resolve(result)
    })
}

/**
 * 获取用户通过地图手动定位的四级地址
 * 这里会拉起微信地图。
 */
const getCustomLbsAddr = (params?: { latitude: number; longitude: number }) => {
  return getWxAuthorize()
    .then(() => getWxChooseLocation(params))
    .then((res) => {
      const { longitude, latitude, address, name } = res || {}
      const data = {
        ...params,
        longitude: Number(longitude),
        latitude: Number(latitude),
        wxAddress: address,
        wxAddrName: name,
      }
      return getLocation(data)
    })
}
// 增加地址
const addAddress = (
  options: AddressStandard.EditFillInType,
): Promise<{
  addressId?: number
  townId?: number
  resultFlag: boolean
  message: string
  resultCode?: string // 异常码：noChooseAddress，四级地址未选择全
  addressCheckResultVO?: AddressStandard.AddressCheckResultVo // 根据详细地址地址识别的数据
  frameTips?: AddressStandard.IFrameTips // 保存未通过时弹窗提示的数据
}> => {
  return apis.addAddress(options)
}
// 修改地址
const modifyAddress = (
  options: AddressStandard.EditFillInType,
): Promise<{
  addressId?: number
  townId?: number
  resultFlag: boolean
  message: string
  resultCode?: string // 异常码：noChooseAddress，四级地址未选择全
  addressCheckResultVO?: AddressStandard.AddressCheckResultVo // 根据详细地址地址识别的数据
  frameTips?: AddressStandard.IFrameTips // 保存未通过时弹窗提示的数据
}> => {
  return apis.updateAddress(options)
}

export interface Params {
  venderid?: string
  shopid?: string
  shoptype?: string
  longitude: number
  latitude: number
  wxAddress: string
  wxAddrName: string
}

// 微信地址换成京东地址
const tryTranslateWxAddr2JdAddr = (wxAddr: string, jdAreaName: string): string => {
  const result = ''
  if (!wxAddr || !jdAreaName) return result

  const [proviceName = '', cityName = '', countyName = '', townName = ''] = jdAreaName.split('_')
  // 京东地址库，proviceName中没有「省」字。如果是直辖市，proviceName中也没有「市」字。因此这里尝试加上缺失的字来匹配
  const strs = [proviceName, '省', '市', cityName, countyName, townName]
  // addr.indexOf(str) == 0 注意这里，目标匹配子串必须在首位
  return strs.reduce((addr, str) => (addr.indexOf(str) === 0 ? addr.replace(str, '') : addr), wxAddr)
}
/** 获取四级地址 */
const getLocation = (params: Params | undefined) => {
  const { venderid, shopid, shoptype, longitude, latitude, wxAddress, wxAddrName } = params || {}

  if (!longitude && !latitude) {
    return Promise.reject(new Error('参数错误'))
  }

  const data = { longitude, latitude }

  if (venderid && shopid && shoptype) {
    Object.assign(data, { venderid, shopid, shoptype })
  }

  // 微信详细地址
  if (typeof wxAddress === 'string') {
    Object.assign(data, { address: wxAddress })
  }
  // 微信地址名称
  if (typeof wxAddrName === 'string') {
    Object.assign(data, { addrname: wxAddrName })
  }

  return apis
    .getLocation({
      longitude,
      latitude,
    })
    .then((body) => {
      const { addressInfo, coverageStatus = '' } = body
      const {
        provinceName = '',
        cityName = '',
        countyName = '',
        townName = '',
        provinceId = 0,
        cityId = 0,
        countyId = 0,
        townId = 0,
      } = addressInfo || {}
      const areaName = [provinceName, cityName, countyName, townName].join('_')
      let shortAddr = addressInfo?.detailAddress || ''

      // 异常处理，接口返回的地址与微信返回的地址不一致
      // 城中村等建筑密集处容易出现接口返回的地址与地图不一致
      if (wxAddress && wxAddrName && shortAddr.indexOf(wxAddrName) < 0) {
        // 从微信地址中截街道详细地址
        const townDetail = tryTranslateWxAddr2JdAddr(wxAddress, areaName)
        if (townDetail) {
          shortAddr = `${townDetail}${wxAddrName}`
        }
      }

      const result = {
        ...params,
        coverageStatus,
        areaId: [provinceId, cityId, countyId, townId].join('_'), // 区域地址 ID
        areaName, // 区域地址名称， 如 "广东_深圳市_南山区_南头街道"
        provinceName,
        cityName,
        countyName,
        townName,
        provinceId,
        cityId,
        countyId,
        townId,
        addrTitle: wxAddrName || '', // 地址名称，如 "卓越前海壹号T3写字楼"
        shortAddr, // 短地址，如 "桂湾五路卓越前海壹号T3写字楼"
        addressName: `${areaName.replace(/_/g, '')}${shortAddr}`, // 完整地址，如 "广东省深圳市南山区南头街道桂湾五路卓越前海壹号T3写字楼"
      }
      return result
    })
}

export {
  getAddrInfoByAreaId, // 是否完整地址
  loadLevel,
  getLbsAuthStatus, // 调用 Taro.getSetting 检查用户是否已同意地址授权
  getWxAuthorize, // 调用微信 Taro.authorize 获取地址授权
  getWxLocation, // 调用微信 Taro.getLocation 获取用户当前位置
  getWxChooseLocation, // 调用微信Taro.chooseLocation
  getCurLbsAddr, // 获取用户当前位置的四级地址
  getCustomLbsAddr, // 获取用户通过地图手动定位的四级地址
  getLocation, // 根据 「国标经纬度和门店信息」 查询京东四级地址信息以及是否支持配送
  addAddress, // 新增地址
  modifyAddress, // 编辑地址
}
