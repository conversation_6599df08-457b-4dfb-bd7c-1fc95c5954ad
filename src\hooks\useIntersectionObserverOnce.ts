import { useEffect, useRef, useCallback } from 'react'

type IntersectionObserverOptions = {
  root?: Element | Document | null
  rootMargin?: string
  threshold?: number | number[]
}
// 基础类型定义
type KeyType<T> = T extends undefined ? undefined : T

const useIntersectionObserverOnce = <
  T extends string | number | symbol | object | any | undefined = undefined,
  E extends Element = Element,
>(
  callback: (key?: KeyType<T>) => void,
  options?: IntersectionObserverOptions,
) => {
  const targetsMap = useRef(new Map<KeyType<T>, E | null>())
  const triggeredSet = useRef(new Set<KeyType<T>>())
  const observerRef = useRef<IntersectionObserver | null>(null)

  // 设置目标元素引用
  const setTargetRef = useCallback(
    (key?: KeyType<T>) => (node: E | null) => {
      // 如果已经触发过，则不再处理
      if (key !== undefined && triggeredSet.current.has(key)) return
      // 移除旧节点监听
      if (key !== undefined) {
        const oldNode = targetsMap.current.get(key)
        if (oldNode && observerRef.current) {
          observerRef.current.unobserve(oldNode)
        }
      } else if (targetsMap.current.size > 0) {
        // 无key模式只支持一个元素
        targetsMap.current.forEach((oldNode) => {
          if (oldNode && observerRef.current) {
            observerRef.current.unobserve(oldNode)
          }
        })
        targetsMap.current.clear()
      }

      // 设置新节点
      if (key !== undefined) {
        targetsMap.current.set(key, node)
      } else {
        // 无key模式使用一个特殊符号存储
        targetsMap.current.set(Symbol() as KeyType<T>, node)
      }

      // 对新节点添加监听（如果observer已初始化）
      if (node && observerRef.current) {
        observerRef.current.observe(node)
      }
    },
    [],
  )

  useEffect(() => {
    const map = targetsMap.current
    const triggered = triggeredSet.current
    if (map.size === 0 || observerRef.current) return

    const handleIntersect: IntersectionObserverCallback = (entries) => {
      entries.forEach((entry) => {
        // 查找元素对应的键
        let key: KeyType<T> | null = null
        map.forEach((el, k) => {
          if (el === entry.target) key = k
        })

        if (key === null || triggered.has(key)) return

        if (entry.isIntersecting) {
          triggered.add(key)
          callback(key)

          // 停止监听该元素
          if (observerRef.current) {
            observerRef.current.unobserve(entry.target)
          }
        }
      })
    }

    observerRef.current = new IntersectionObserver(handleIntersect, options)

    // 对所有已存在的节点添加监听
    map.forEach((target, key) => {
      if (target && !triggered.has(key)) {
        observerRef.current?.observe(target)
      }
    })

    return () => {
      observerRef.current?.disconnect()
      observerRef.current = null
    }
  }, [callback, options])

  return setTargetRef
}

export default useIntersectionObserverOnce
