import InvoiceTitleSelector from './InvoiceTitleSelector'
import InvoiceContentPanel from './InvoiceContentPanel'
import HistoricalInvoiceHeaders from './HistoricalInvoiceHeaders'
import { Input, Form } from '@app/common/legao'
import { NormalInvoiceEditVO, UsualInvoiceItem, InvoiceTitle } from '@app/typings/invoice.d'
import { useAtomValue, useSetAtom } from 'jotai'
import { invoiceCodeDescAtom, selectedInvoiceTitleAtom } from '@app/components/Invoice/atom/invoiceAtom'
import { handleInvoiceTitleChangeAtom } from '@app/components/Invoice/atom/invoiceAction'
import { useState, FC, useRef, useCallback } from 'react'
import { normalInvoiceFields } from '@app/components/Invoice/form/formFieldsConfig'
import CustomTooltip from '@app/common/Tooltip/index'

/**
 * 普票表单组件
 */
interface NormalInvoiceFormProps {
  // 表单字段
  normalInvoice: Partial<NormalInvoiceEditVO>
  // 表单事件处理 - 使用Jotai状态一致的字段名
  onPersonalNameChange: (val: string) => void
  onRegAddressChange: (val: string) => void
  onRegTelChange: (val: string) => void
  onRegBankChange: (val: string) => void
  onRegAccountChange: (val: string) => void
  onInvoiceCodeChange: (val: string) => void
  onCompanyNameChange: (val: string) => void
}

const NormalInvoiceForm: FC<NormalInvoiceFormProps> = ({
  normalInvoice,
  onPersonalNameChange,
  onRegAddressChange,
  onRegTelChange,
  onRegBankChange,
  onRegAccountChange,
  onInvoiceCodeChange,
  onCompanyNameChange,
}) => {
  // 电子普通发票-普通发票
  // 解构需要的属性
  const [isMoreInfoExpanded, setIsMoreInfoExpanded] = useState(false)
  const [selectedPersonalItemId, setSelectedPersonalItemId] = useState<number | null>(null)
  const [selectedCompanyItemId, setSelectedCompanyItemId] = useState<number | null>(null)
  // 使用派生状态获取当前选中的发票抬头值
  const selectInvoiceTitle = useAtomValue(selectedInvoiceTitleAtom)
  const handleInvoiceTitleChange = useSetAtom(handleInvoiceTitleChangeAtom)
  const invoiceCodeDesc = useAtomValue(invoiceCodeDescAtom)
  // 添加一个ref来跟踪上一次选择的公司
  const lastSelectedCompanyRef = useRef<string | null>(null)
  const isProcessingRef = useRef(false)

  /**
   * 使用useCallback函数，添加防重复处理逻辑
   */
  const handleSelectCompanyHistory = useCallback(
    (company: UsualInvoiceItem) => {
      // 如果正在处理或者公司名称与上次相同，则不处理
      if (isProcessingRef.current || (lastSelectedCompanyRef.current === company.content && company.id === selectedCompanyItemId)) {
        return
      }
      isProcessingRef.current = true
      lastSelectedCompanyRef.current = company.content || null
      if (company && company.content) {
        onCompanyNameChange(company.content)
        setSelectedCompanyItemId(company.id || null)
        if (company.invoiceCode) {
          onInvoiceCodeChange(company.invoiceCode)
        }
      }
      // 重置处理状态
      setTimeout(() => {
        isProcessingRef.current = false
      }, 100)
    },
    [onCompanyNameChange, onInvoiceCodeChange, selectedCompanyItemId],
  )

  /**
   * 选择个人历史抬头
   */
  const handleSelectPersonalHistory = (item: Partial<UsualInvoiceItem>) => {
    if (item && item.content) {
      onPersonalNameChange(item.content)
      setSelectedPersonalItemId(item.id || null)
    }
  }

  return (
    <>
      {/* 发票抬头选择 - 传递invoiceTitles和正确的onChange处理函数 */}
      <InvoiceTitleSelector onChange={(invoiceTitle) => handleInvoiceTitleChange(invoiceTitle)} />

      {/* 个人发票信息 */}
      {selectInvoiceTitle === InvoiceTitle.PERSONAL && (
        <>
          {/* 历史抬头列表 - 使用抽取的组件 */}
          <HistoricalInvoiceHeaders
            selectedId={selectedPersonalItemId}
            onSelectHeader={handleSelectPersonalHistory}
            type={InvoiceTitle.PERSONAL}
          />
        </>
      )}

      {/* 单位发票信息 */}
      {selectInvoiceTitle === InvoiceTitle.COMPANY && (
        <>
          {/* 单位历史抬头 */}
          <HistoricalInvoiceHeaders
            selectedId={selectedCompanyItemId}
            onSelectHeader={handleSelectCompanyHistory}
            invoiceCode={normalInvoice.invoiceCode}
            type={InvoiceTitle.COMPANY}
          />

          <Form.Item
            {...normalInvoiceFields['normalInvoice.invoiceCode']}
            label={
              <>
                纳税人识别号
                <CustomTooltip
                  content={
                    <div
                      style={{ maxWidth: '400px', padding: '8px 12px', lineHeight: '1.5', fontSize: '12px' }}
                      dangerouslySetInnerHTML={{ __html: invoiceCodeDesc }}
                    />
                  }
                  placement="top"
                  trigger="hover"
                >
                  <i className="icon-info" style={{ marginLeft: '4px', verticalAlign: 'bottom' }} />
                </CustomTooltip>
              </>
            }
          >
            <Input type="text" placeholder="请填写纳税人识别号" value={normalInvoice.invoiceCode || ''} onChange={onInvoiceCodeChange} />
          </Form.Item>

          {/* 更多信息切换按钮 */}
          <Form.Item label="更多选填项">
            <div
              className="more-info-field"
              onClick={() => setIsMoreInfoExpanded(!isMoreInfoExpanded)}
              style={{
                display: 'flex',
                alignItems: 'center',
                borderRadius: '4px',
                height: '36px',
                boxSizing: 'border-box',
                cursor: 'pointer',
              }}
            >
              <span style={{ color: '#888B94' }}>单位地址、电话、开户银行及账号</span>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <img
                  src={'//img14.360buyimg.com/ling/jfs/t1/306908/16/12689/359/685d890dFac920fe0/1d9a48ad2b1928d7.png'}
                  style={{
                    width: '10px',
                    height: '10px',
                    transform: isMoreInfoExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.3s',
                  }}
                  alt={isMoreInfoExpanded ? '收起' : '展开'}
                />
              </div>
            </div>
          </Form.Item>

          {/* 更多选填项区域 */}
          {isMoreInfoExpanded && (
            <>
              <Form.Item {...normalInvoiceFields['normalInvoice.regAddress']}>
                <Input type="text" placeholder="请填写单位注册地址" value={normalInvoice.regAddress || ''} onChange={onRegAddressChange} />
              </Form.Item>

              <Form.Item {...normalInvoiceFields['normalInvoice.regTel']}>
                <Input type="text" placeholder="请填写单位注册电话" value={normalInvoice.regTel || ''} onChange={onRegTelChange} />
              </Form.Item>

              <Form.Item {...normalInvoiceFields['normalInvoice.regBank']}>
                <Input type="text" placeholder="请填写单位开户银行" value={normalInvoice.regBank || ''} onChange={onRegBankChange} />
              </Form.Item>

              <Form.Item {...normalInvoiceFields['normalInvoice.regAccount']}>
                <Input type="text" placeholder="请填写单位银行账户" value={normalInvoice.regAccount || ''} onChange={onRegAccountChange} />
              </Form.Item>
            </>
          )}
        </>
      )}

      {/* 使用发票内容面板组件 */}
      <InvoiceContentPanel hasNotBookSku={true} />
    </>
  )
}

export default NormalInvoiceForm
