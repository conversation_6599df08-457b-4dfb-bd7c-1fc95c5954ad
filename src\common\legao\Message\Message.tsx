import * as React from 'react'
import classNames from 'classnames'
import { addPrefix } from '../libs/utils/prefix'
import showMsg from './showMsg'
import { info, success, warn, wrong } from './showInfo'
// import { ConfigConsumer, ConfigConsumerProps } from "../Config";

export const STATUS = ['success', 'warn', 'wrong', 'info']
export interface IMessageProps {
  /**
   * Message 类型
   *
   * @default 'info'
   **/
  type?: 'success' | 'warn' | 'wrong' | 'info'
  /**
   * 自定义组件类名
   *
   * @default ""
   **/

  className?: string
  /**
   * 关闭回调
   *
   * @default () => void;
   **/
  onClose?: () => void
  /**
   * 是否有显示关闭按钮
   *
   * @default false
   **/
  closable?: boolean
  /**
   * 关闭文案
   *
   * @default ''
   **/
  closeLabel?: React.ReactNode
  /**
   * 显示title
   *
   * @default
   **/
  title?: React.ReactNode
  /**
   * 显示描述内容
   *
   * @default
   **/
  description?: React.ReactNode
  /**
   * 是否显示Icon图标
   *
   * @default false
   **/
  showIcon?: boolean
  /**
   * 是否铺满显示
   *
   * @default false
   **/
  full?: boolean
  /**
   * 默认前缀
   *
   * @default 'lg'
   **/
  prefixCls?: string
  /**
   * 持续显示时间，单位ms
   *
   * @default 1500
   **/
  duration?: number
  /**
   * 显示位置
   *
   * @default 'top-center'
   **/
  position?: string
  /**
   * 是否自动隐藏
   *
   * @default false
   **/
  autohide?: boolean
  /**
   * 消失动画持续时间 单位ms
   *
   * @default 350
   **/
  animationTime?: number
  /**
   * 自定义样式
   *
   * @default
   **/
  style?: React.CSSProperties
}
interface IMessageState {
  display: 'show' | 'hide' | 'hiding'
}

interface MessageDefaultProps extends IMessageProps {
  type: 'info'
  prefixCls: string
  closeLabel: React.ReactNode
  duration: number
  position: string
  autohide: boolean
  animationTime: number
}

class Message extends React.PureComponent<IMessageProps, IMessageState> {
  private durationTimer: any
  static showMsg = showMsg
  static info = info
  static success = success
  static warn = warn
  static wrong = wrong
  static defaultProps = {
    type: 'info',
    closeLabel: '',
    className: '',
    duration: 1500,
    position: 'top-center',
    autohide: false,
    animationTime: 350,
  }
  constructor(props: IMessageProps) {
    super(props)
    this.state = {
      display: 'show',
    }
  }

  componentDidMount() {
    const { autohide, duration } = this.props
    if (autohide) {
      setTimeout(() => {
        this.handleClose()
      }, duration)
    }
  }

  UNSAFE_componentWillMount() {
    clearTimeout(this.durationTimer)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.autohide !== nextProps.autohide) {
      const { autohide, duration } = nextProps
      if (autohide) {
        setTimeout(() => {
          this.handleClose()
        }, duration)
      }
    }
  }

  handleClose = () => {
    const { onClose, animationTime } = this.props as MessageDefaultProps
    this.setState({ display: 'hiding' })

    this.durationTimer = setTimeout(
      () =>
        this.setState({ display: 'hide' }, () => {
          onClose && onClose()
        }),
      animationTime,
    )
  }

  renderCloseButton(closeLabel: React.ReactNode, prefixCls: string) {
    return (
      <div className={addPrefix(['message-close', 'icon-close'], prefixCls)} aria-label="Close" role="button" onClick={this.handleClose}>
        <span className="bc-only">{closeLabel}</span>
      </div>
    )
  }

  render() {
    const {
      prefixCls: customizePrefixCls,
      className,
      type,
      title,
      description,
      closeLabel,
      closable,
      full,
      showIcon,
      position,
      autohide,
      duration,
      animationTime,
      style,
      ...props
    } = this.props as MessageDefaultProps

    const prefixCls = 'lg'

    const { display } = this.state

    if (display === 'hide') {
      return null
    }

    const hasTitle = !!title
    const hasDesc = !!description

    const wrapCls = classNames(addPrefix('message-container', prefixCls), {
      [addPrefix(`message--${position}`, prefixCls)]: position,
    })

    const classes = classNames(
      className,
      addPrefix('message', prefixCls),
      addPrefix(`message-${type}-s1`, prefixCls),
      addPrefix(display, prefixCls),
      {
        [addPrefix('message-has-title', prefixCls)]: hasTitle,
        [addPrefix('message-has-icon', prefixCls)]: showIcon,
        [addPrefix('message-full', prefixCls)]: full,
        ['message--in']: display !== 'hiding',
        ['message--out']: display === 'hiding',
      },
    )

    return (
      <div {...props} style={style} className={wrapCls}>
        <div className={classes}>
          <div
            className={classNames(addPrefix('message-content', prefixCls), {
              [addPrefix('message-cont-close', prefixCls)]: closable,
            })}
          >
            {closable && this.renderCloseButton(closeLabel, prefixCls)}
            {showIcon && <i className={classNames(addPrefix('message-icon', prefixCls), addPrefix(`icon-${type}-s1`, prefixCls))} />}
            <div className={addPrefix('message-cont', prefixCls)}>
              {hasTitle && <h5 className={addPrefix('header', prefixCls)}>{title}</h5>}
              {hasDesc && <div className={addPrefix('body', prefixCls)}>{description}</div>}
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default Message
