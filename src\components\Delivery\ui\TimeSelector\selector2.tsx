import { useEffect } from 'react'
import styles from './index.module.scss'

export default ({ showStr, onClick }: { showStr: string; onClick?: (s: string) => void }) => {
  useEffect(() => {
    console.log(showStr, 'showStr')
  }, [showStr])
  return (
    <>
      <div className={styles.time_selector} style={{ height: 'auto', width: '100%' }}>
        <div className={styles.right} style={{ height: '80px', width: '100%' }}>
          <div
            style={{ width: 'auto', padding: '8px 8px' }}
            onClick={() => onClick && onClick(showStr)}
            className={`${styles.item} ${styles.item_selected}`}
          >
            <div style={{ lineHeight: '20px' }} className={`${styles.text} `}>
              {showStr}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
