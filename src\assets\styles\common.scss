@use "../../assets/styles/lib.scss";
/* 定位 */
$position: (relative, absolute, fixed, static);
@each $value in $position {
  .is-#{$value} {
    position: $value;
  }
}

/* 垂直居中 */
/* .is-vertically{
    @include v(2)
} */
/* 显示方式 */
$display: (
  "hide": none,
  "block": block,
  "inline-block": inline-block,
  "table": table,
  "inline-table": inline-table,
  "table-cell": table-cell,
  "flex": flex,
  "inline-flex": inline-flex,
);
@each $classname, $value in $display {
  .is-#{$classname} {
    display: $value;
  }
}
@for $value from 12 through 168 {
  .h-#{$value} {
    height: #{$value}px;
  }
  .w-#{$value} {
    width: #{$value}px;
  }
  .leading-#{$value} {
    line-height: #{$value}px;
  }
}
@for $value from 101 through 244 {
  .w-#{$value} {
    width: #{$value}px;
  }
}
.w-full {
  width: 100%;
}
@for $value from 0 through 50 {
  .-mt-#{$value} {
    margin-top: -#{$value}px;
  }
  .-mb-#{$value} {
    margin-bottom: -#{$value}px;
  }
  .-ml-#{$value} {
    margin-left: -#{$value}px;
  }
  .-mr-#{$value} {
    margin-right: -#{$value}px;
  }
}
@for $value from 0 through 100 {
  .m-#{$value} {
    margin: #{$value}px;
  }
  .my-#{$value} {
    margin: #{$value}px 0;
  }
  .mx-#{$value} {
    margin: 0 #{$value}px;
  }
  .mt-#{$value} {
    margin-top: #{$value}px;
  }
  .mb-#{$value} {
    margin-bottom: #{$value}px;
  }
  .ml-#{$value} {
    margin-left: #{$value}px;
  }
  .mr-#{$value} {
    margin-right: #{$value}px;
  }
  .p-#{$value} {
    padding: #{$value}px;
  }
  .py-#{$value} {
    padding: #{$value}px 0;
  }
  .px-#{$value} {
    padding: 0 #{$value}px;
  }
  .pt-#{$value} {
    padding-top: #{$value}px;
  }
  .pb-#{$value} {
    padding-bottom: #{$value}px;
  }
  .pl-#{$value} {
    padding-left: #{$value}px;
  }
  .pr-#{$value} {
    padding-right: #{$value}px;
  }
}
$items: (
  stretch: stretch,
  start: flex-start,
  end: flex-end,
  center: center,
  baseline: baseline,
);
@each $classname, $value in $items {
  .items-#{$classname} {
    align-items: $value;
  }
}
$justify: (
  normal: normal,
  start: flex-start,
  end: flex-end,
  center: center,
  between: space-between,
  around: space-around,
  evenly: space-evenly,
  stretch: stretch,
);
@each $classname, $value in $justify {
  .justify-#{$classname} {
    justify-content: $value;
  }
}
$weight: (
  thin: 100,
  extralight: 200,
  light: 300,
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
  extrabold: 800,
  "black": 900,
);
@each $classname, $value in $weight {
  .font-#{"" + $classname} {
    font-weight: $value;
  }
}
$size: (
  '11': 11px,
  "12": 12px,
  "14": 14px,
  "16": 16px,
  "20": 20px,
);
@each $classname, $value in $size {
  .font-#{$classname} {
    font-size: $value;
  }
}
$vertical: (top, middle, text-top);
@each $value in $vertical {
  .v-#{$value} {
    vertical-align: $value;
  }
}
$align: (left, right, center);
@each $value in $align {
  .a-#{$value} {
    text-align: $value;
  }
}
.is-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.is-overflow-hidden {
  overflow: hidden;
}
.shrink-0 {
  flex-shrink: 0;
}
.jd-checkbox,
.ant-checkbox {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  border: 1px solid #bfbfbf;
  background-color: #fff;
  border-radius: 50%;
  box-sizing: border-box;
  &::after {
    background-color: #fff;
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.15s cubic-bezier(0.71, -0.46, 0.88, 0.6);
  }
  &.is-checked,
  &.ant-checkbox-checked {
    border-color: #fa2c19;
    background: linear-gradient(90deg, #ff404f 0%, #fa2c19 100%);
    > input[type="checkbox"] {
      cursor: pointer;
    }
  }
  &.is-checked::after,
  &.ant-checkbox-checked::after {
    transform: translate(-50%, -50%) scale(1);
    width: 10px;
    height: 6.6px;
    background: url(https://img12.360buyimg.com/imagetools/jfs/t1/262811/17/16930/344/67a31d47F4eced4d0/66b189e4ff6534c8.png)
      center / cover no-repeat;
  }
  &.is-disabled,
  &.ant-checkbox-disabled {
    border-color: #bfbfbf;
    background: #bfbfbf;
    > input[type="checkbox"] {
      cursor: not-allowed;
    }
  }
  &.is-disabled::after,
  &.ant-checkbox-disabled::after {
    transform: translate(-50%, -50%) scale(1);
    border-radius: 1px;
    width: 8px;
    height: 2px;
  }
  > input[type="checkbox"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    outline: 0;
    z-index: 1;
    cursor: pointer;
  }
}

.jd-radio {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  border: 1px solid #bfbfbf;
  background-color: #fff;
  border-radius: 50%;
  box-sizing: border-box;
  &::after {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #fff;
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.15s cubic-bezier(0.71, -0.46, 0.88, 0.6);
  }
  &.is-checked {
    border-color: #ff0f23;
    background: linear-gradient(90deg, #ff475d 0%, #ff0f23 100%);
    > input[type="radio"] {
      cursor: pointer;
    }
  }
  &.is-checked::after {
    transform: translate(-50%, -50%) scale(1);
  }
  &.is-disabled {
    border-color: #bfbfbf;
    background: #bfbfbf;
    > input[type="radio"] {
      cursor: not-allowed;
    }
  }
  &.is-disabled::after {
    transform: translate(-50%, -50%) scale(1);
    border-radius: 1px;
    width: 8px;
    height: 2px;
  }
  > input[type="radio"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    outline: 0;
    z-index: 1;
    cursor: pointer;
  }
}

.ftx01 {
  color: lib.$color01;
}

.ftx02 {
  color: lib.$color02;
}

.ftx03,
.gray,
.grey {
  color: lib.$color03;
}

.ftx04 {
  color: lib.$color04;
}

.ftx05 {
  color: lib.$color05;
}

.ftx06 {
  color: lib.$color06;
}

.ftx07 {
  color: lib.$color07;
}

.ftx08 {
  color: lib.$color08;
}

.ftx09 {
  color: lib.$color09;
}

.ftx10 {
  color: lib.$color10;
}

.ftx11.ftx11 {
  color: lib.$color11;
}
.ftx12.ftx12 {
  color: lib.$color12;
}
.ftx13.ftx13 {
  color: lib.$color13;
}
.ftx14.ftx14 {
  color: lib.$color14;
}
.ftx15.ftx15 {
  color: lib.$color15;
}
.colorBlack {
  color: lib.$colorBlack;
}
.colorWhite {
  color: lib.$colorWhite;
}
.hover\:ftx01:hover {
  color: lib.$color01;
}
.bg-ftx15 {
  background-color: lib.$color15;;
}
.bg-white{
  background-color: #fff;
}
.cursor-pointer {
  cursor: pointer;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.round-4 {
  border-radius: 4px;
}
.jd-skeleton {
  position: relative;
  z-index: 0;
  overflow: hidden;
  background: transparent;
  &::after {
    position: absolute;
    inset: 0 -150%;
    background: linear-gradient(90deg, rgba(190, 190, 190, .2) 25%, rgba(129, 129, 129, .24) 37%, rgba(190, 190, 190, .2) 63%);
    animation: jd-skeleton-loading 2s ease infinite;
    content: "";
  }
}

@keyframes jd-skeleton-loading {
  0% {
      transform: translate(-37.5%)
  }

  to {
      transform: translate(37.5%)
  }
}