import React from 'react'
import ReactDOM from 'react-dom'
import classNames from 'classnames'
import confirm from './Confirm'
import '../Dialog/style.scss'

export interface IDialogProps {
  /** 头部标题, 空则无 */
  title?: string
  /** 头部关闭按钮是否显示 */
  closeButton?: boolean
  /** 是否存在mask层 */
  hasMask?: boolean
  /** 打开弹窗 */
  isOpen?: boolean
  /** mask层是否能关闭弹窗 */
  outsideClose?: boolean
  /** 弹窗中内容主体宽度 */
  width?: number
  /** 弹窗中内容主体高 */
  height?: number
  /** 弹窗中内容主体 */
  children?: JSX.Element
  /** 弹窗外层类名 */
  className: string
  /** 关闭弹窗时的回调 */
  onClose?: any
  /** 打开弹窗时的回调 */
  onReady?: any
  /** 标题图标类型 */
  iconType?: any
}

class Dialog extends React.Component<IDialogProps> {
  static confirm = confirm
  constructor(props: IDialogProps) {
    super(props)
  }

  private getScrollbarWidth() {
    const outer = document.createElement('div')
    outer.style.visibility = 'hidden'
    outer.style.overflow = 'scroll'
    outer.style.width = '100px'
    outer.style.height = '100px'
    document.body.appendChild(outer)
    const inner = document.createElement('div')
    inner.style.width = '100%'
    inner.style.height = '100%'
    outer.appendChild(inner)
    const scrollbarWidth = outer.offsetWidth - inner.offsetWidth
    outer?.parentNode?.removeChild(outer)
    return scrollbarWidth
  }

  private checkVerticalScrollbar() {
    // 获取文档的高度
    const documentHeight = Math.max(
      document.body.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.clientHeight,
      document.documentElement.scrollHeight,
      document.documentElement.offsetHeight,
    )

    // 获取视口的高度
    const viewportHeight = document.documentElement.clientHeight || window.innerHeight

    // 判断是否存在垂直滚动条
    const hasVerticalScrollbar = documentHeight > viewportHeight
    return hasVerticalScrollbar
  }

  // 插入样式函数
  private insertStyle(key, css) {
    const existing = document.querySelector(`style[rc-util-key="${key}"]`)
    if (existing) return

    const style = document.createElement('style')
    style.setAttribute('data-rc-order', 'append')
    style.setAttribute('rc-util-key', key)
    style.textContent = css
    document.head.appendChild(style)
  }

  // 删除样式函数
  private removeStyle(key) {
    const target = document.querySelector(`style[rc-util-key="${key}"]`)
    target?.parentNode?.removeChild(target)
  }

  public componentDidUpdate(prevProps: Readonly<{}>, prevState: Readonly<{}>, snapshot?: any) {
    if (this.props.isOpen && this.props.onReady) {
      this.props.onReady()
    }
    if (this.props.isOpen !== prevProps.props?.isOpen) {
      const px = this.checkVerticalScrollbar() ? this.getScrollbarWidth() : 0
      // 创建 style 元素
      const key = 'rc-util-locker-1749819662642_2'
      const css = `
          html body {
              overflow-y: hidden;
              width: calc(100% - ${px}px);
          }
      `
      if (this.props.isOpen) {
        this.insertStyle(key, css)
      } else {
        this.removeStyle(key)
      }
    }
  }

  public render() {
    const { title, closeButton, children, className, hasMask, isOpen, onClose, outsideClose, width, height, iconType } = this.props
    const Slot = { ...children, props: { ...(children?.props || {}), height } }
    return (
      isOpen &&
      ReactDOM.createPortal(
        <div className="dialog-wrapper">
          <div
            className={classNames('jd-dialog', {
              [className]: className,
            })}
            style={{
              width: width + 'px',
              height: height + 'px',
              // marginLeft: -(Number(width) + 20 + 8) / 2,
              // marginTop: -(Number(height) + 20 + (title ? 31 : 0)) / 2,
            }}
          >
            {title && (
              <div className="dialog-head">
                {title ? iconType ? <span className="dialog-title">{title}</span> : <span className="dialog-title">{title}</span> : ''}
                {closeButton && <button className="dialog-close" onClick={onClose} />}
              </div>
            )}
            {closeButton && !title && <button className="dialog-close dialog-close-notitle" onClick={onClose} />}
            <div className="dialog-body">{Slot}</div>
          </div>
          {hasMask && <div className="dialog-mask" onClick={outsideClose ? onClose : null} aria-hidden />}
        </div>,
        document.body,
      )
    )
  }
}

export default Dialog
