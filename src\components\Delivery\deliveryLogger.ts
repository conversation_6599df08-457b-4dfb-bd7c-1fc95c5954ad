/**
 * @file: deliveryLogger.ts
 * @description: 配送楼层日志记录器，用于记录配送楼层相关日志
 */
import { BundleType } from '@app/typings/master_api_response'
import { reportClick, reportExpose } from '@app/utils/event_tracking'

export default class DeliveryLogger {
  public bundle: BundleType
  constructor(bundle: BundleType) {
    this.bundle = bundle
  }
  // 配送弹窗露出
  public shipmentlayerExpo() {
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: // 埋点实验分组标识，文档说明，无实际代码
    // }
    reportExpose('shipmentlayerExpo', {})
  }

  //配送弹窗点击
  public shipmentlayer(clickPos: '0' | '1' | '2' | '3', deliveryType: string, promiseMsg: string) {
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   clickPos: '0-取消,1-确认,2-配送方式,3-',
    //   deliveryType: '配送方式(deliveryType)',
    //   promiseMsg: '配送时效(promiseMsg)',
    // }
    reportClick('shipmentlayer', { clickPos, deliveryType, promiseMsg })
  }

  //留言弹窗点击 留言(leaveMsg)
  public leavemsg(leaveMsg: string) {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   leaveMsg: '',
    // }
    reportClick('leavemsg', { leaveMsg })
  }

  //配送/弹窗曝光
  public shipmentPOPExpo(p: {
    toptabname: string[]
    second_tab_name: string[]
    date: string[]
    shipmentTitle: string[]
    transportation_expenses: string[]
    button_name: ('0' | '1')[]
  }) {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   toptabname: ['一级tab名称,曝光的tab都上报;中文'],
    //   second_tab_name: ['二级tab名称,tab前有文案时,文案也上报,如配送时间精准到2小时;中文'],
    //   date: ['弹窗露出的配送日期'],
    //   shipmentTitle: ['弹窗露出的配送波次'],
    //   transportation_expenses: ['弹窗露出的运费 如需加运费3元'],
    //   button_name: '0-取消,1-确认',
    // }
    reportExpose('shipmentPOPExpo', p)
  }

  //配送/弹窗点击
  public shipmentPOPClick(p: {
    toptabname?: string
    second_tab_name?: string
    date?: string
    shipmentTitle?: string
    transportation_expenses?: string
    button_name?: '0' | '1'
  }) {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   toptabname: '用户点击的一级tab',
    //   second_tab_name: '用户点击的二级tab',
    //   date: '用户点击的日期',
    //   shipmentTitle: '用户点击的波次',
    //   transportation_expenses: '用户点击波次的运费信息',
    //   button_name: '用户点击的按钮名称',
    // }
    const _exp = {
      toptabname: '-100',
      second_tab_name: '-100',
      date: '-100',
      shipmentTitle: '-100',
      transportation_expenses: '-100',
      button_name: '-100',
    }
    reportClick('shipmentPOPClick', { ..._exp, ...p })
  }

  //配送楼层曝光
  public shipmentEXPO(agingType: string) {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   agingType: '配送时间(agingType)字段类型 字符串 数据样例23',
    //   packetid: '包裹ID(packetid)',
    // }
    reportExpose('shipmentEXPO', { packetid: this.bundle.bundleId, agingType })
  }

  //配送楼层点击
  public shipmentClick(agingType: string) {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   agingType: '配送时间(agingType)字段类型 字符串 数据样例23',
    //   packetid: '包裹ID(packetid)',
    // }
    reportClick('shipmentClick', { packetid: this.bundle.bundleId, agingType })
  }
  //安装时间楼层曝光
  public InstalltimeEXPO() {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   packetid: '',
    // }
    reportExpose('InstalltimeEXPO', { packetid: this.bundle.bundleId })
  }

  //安装时间楼层点击
  public InstalltimeClick() {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   packetid: '',
    // }
    reportClick('InstalltimeClick', { packetid: this.bundle.bundleId })
  }

  //留言楼层曝光
  public messageEXPO() {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    // 说明：以下 _exp 变量仅为文档说明用途，描述埋点参数结构，无实际代码功能。
    // 示例：
    // const _exp = {
    //   touchstone_expids: ['棱镜,试金石适用;ZXhwMXxDdmVyc2lvbl8x包含实验标识和实验组'],
    //   packetid: '',
    // }
    reportExpose('messageEXPO', { packetid: this.bundle.bundleId })
  }

  // 留言楼层点击
  public messageClick() {
    reportClick('messageClick', { packetid: this.bundle.bundleId })
  }

  //订单信息楼层点击
  public orddetail(clickPos: '0' | '1' | '2') {
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    reportClick('orddetail', { packetid: this.bundle.bundleId, clickPos })
  }
}
