import React, { SyntheticEvent, createContext } from 'react'
import { Component, PropTypes } from '../libs'

// 创建上下文
export const CheckboxGroupContext = createContext<{ ElCheckboxGroup: any } | null>(null)

interface CheckboxProps {
  label?: string
  trueLabel?: string | number
  falseLabel?: string | number
  disabled?: boolean
  checked?: boolean
  indeterminate?: boolean
  focus?: boolean
  onChange?: (checked: boolean) => void
}

interface CheckboxState {
  checked: boolean
  focus: boolean
  label: string
}

export default class Checkbox extends Component<CheckboxProps, CheckboxState> {
  static elementType = 'Checkbox'
  // 使用新的上下文
  static contextType = CheckboxGroupContext

  constructor(props: CheckboxProps) {
    super(props)
    this.state = {
      checked: props.checked ?? false,
      focus: props.focus ?? false,
      label: this.getLabel(props),
    }
  }

  componentDidUpdate(prevProps: CheckboxProps) {
    if (
      prevProps.checked !== this.props.checked ||
      prevProps.focus !== this.props.focus ||
      prevProps.label !== this.props.label ||
      prevProps.trueLabel !== this.props.trueLabel ||
      prevProps.falseLabel !== this.props.falseLabel
    ) {
      this.setState({
        checked: this.props.checked ?? false,
        // focus: this.props.focus ?? false,
        label: this.getLabel(this.props),
      })
    }
  }

  onFocus(): void {
    this.setState({
      focus: true,
    })
  }

  onBlur(): void {
    this.setState({
      focus: false,
    })
  }

  onChange(e: SyntheticEvent<HTMLInputElement>): void {
    const { label } = this.state
    const { trueLabel, falseLabel } = this.props
    const checked = e.currentTarget.checked
    const group = this.context?.ElCheckboxGroup

    if (group) {
      const length = group.state.options.length + (checked ? 1 : -1)

      if (group.props.min !== undefined && length < group.props.min) {
        return
      }

      if (group.props.max !== undefined && length > group.props.max) {
        return
      }
    }

    let newLabel = label

    if (this.props.trueLabel || this.props.falseLabel) {
      newLabel = checked ? trueLabel : falseLabel
    }

    this.setState(
      {
        checked: checked,
        label: newLabel as string,
      },
      () => {
        if (this.props.onChange) {
          this.props.onChange(checked)
        }
      },
    )
  }

  getLabel(props: CheckboxProps): string {
    if (props.trueLabel || props.falseLabel) {
      return props.checked ? (props.trueLabel as string) : (props.falseLabel as string)
    } else {
      return props.label ?? ''
    }
  }

  render(): JSX.Element {
    return (
      <label style={this.style()} className={this.className('el-checkbox')}>
        <span
          className={this.classNames('el-checkbox__input', {
            'is-disabled': this.props.disabled,
            'is-checked': this.state.checked,
            'is-indeterminate': this.props.indeterminate,
            'is-focus': this.state.focus,
          })}
        >
          <input
            className="el-checkbox__original"
            type="checkbox"
            checked={this.state.checked}
            disabled={this.props.disabled}
            onFocus={this.onFocus.bind(this)}
            onBlur={this.onBlur.bind(this)}
            onChange={this.onChange.bind(this)}
          />
          <span className="el-checkbox__inner"></span>
        </span>
        <span className="el-checkbox__label">{this.props.children || this.state.label}</span>
      </label>
    )
  }
}

Checkbox.propTypes = {
  label: PropTypes.string,
  trueLabel: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  falseLabel: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  disabled: PropTypes.bool,
  checked: PropTypes.bool,
  indeterminate: PropTypes.bool,
  focus: PropTypes.bool,
  onChange: PropTypes.func,
}

Checkbox.defaultProps = {
  checked: false,
  focus: false,
}
