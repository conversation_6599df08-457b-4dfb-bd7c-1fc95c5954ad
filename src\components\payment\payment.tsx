/**
 * @file index.tsx
 * @description 付款详情模块
 */
import './index.scss'
import classNames from 'classnames'
import React, { useRef } from 'react'
import { useAtomValue } from 'jotai'
import Button from '@app/common/button'
import Tooltip from '@app/common/Tooltip'
import useRootClassName from './hooks/useRootClassName'
import useGlobalZero from './hooks/useGlobalZero'
import useSubmitOrder from './hooks/useSubmitOrder'
import PaymentItem, { type PaymentItemProps } from './PaymentItem'
import PaymentItemFreight from './PaymentItemFreight'
import PaymentItemDetail from './PaymentItemDetail'
import PaymentMethod from './PaymentMethod'
import PaymentAgreement from './PaymentAgreement'
import type { PaymentAgreementRef } from './PaymentAgreement'
import PaymentPassword from './PaymentPassword'
import type { PaymentPasswordRef } from './PaymentPassword'
import PaymentStateSubsidy from './PaymentStateSubsidy'
import useMasterData from '@app/hooks/useMasterData'
import { presalePayTypeAtom } from '@app/atoms/presaleAtom'
import { JZD_DESCRIPTION } from '@app/const'

const Payment: React.FC = () => {
  /** 窄屏时展开收起功能 */
  const [rootClassName, toggle] = useRootClassName()
  /** 协议组件ref */
  const agreementRef = useRef<PaymentAgreementRef>(null)
  /** 密码组件ref */
  const passwordRef = useRef<PaymentPasswordRef>(null)
  /** 提交订单逻辑 */
  const submitOrder = useSubmitOrder()
  /** 页面主数据 */
  const masterData = useMasterData()?.body
  /** 商品件数 */
  const qty = masterData?.sumBuyNum ?? 0
  /** 用户信息 */
  const user = masterData?.balanceUser
  /** 付款明细 */
  const total = masterData?.balanceTotal
  /** 国补提示 */
  const govSubsidTips = total?.govSubsidTips
  /** 未领取国补资格跳转链接 */
  const unHitGovSubsidySkuJumpUrl = total?.unHitGovSubsidySkuJumpUrl
  /** 规则文案 */
  const rules = masterData?.rules ?? {}
  /** 提示文案 */
  const tipsList = masterData?.tipsVOList
  /** 支付方式 */
  const paymentVOList = masterData?.paymentVOList
  /** 用户协议 */
  const licenseList = masterData?.licenseList
  /** 运费详情 */
  const freightVoList = masterData?.balanceVenderFreightVoList
  /** 是否企业用户 */
  const enterPriseUser = user?.userLevel == 90
  /** 已选支付方式 */
  const selectedPayment = paymentVOList?.find((el) => el.selected)?.paymentId
  /** 兑换价格 */
  const exchangePrice = masterData?.balanceExt?.exchangeInfo
  /** 预售数据 */
  const presale = masterData?.balancePresaleVO
  const presalePayType = useAtomValue(presalePayTypeAtom)
  /** 是否预售定金支付 */
  const isDepositPay = ['3', '5'].includes(presale?.payStepType || '') || presalePayType === 2
  const price = (isDepositPay && presale?.earnest) || total?.factPrice
  /** 费用明细 */
  const items = [
    total?.itemTotalPrice &&
      !isDepositPay && {
        name: '商品金额',
        value: `￥${total?.itemTotalPrice || '0.00'}`,
        bold: true,
      },
    isDepositPay && {
      name: '定金',
      value: `￥${presale?.earnest}`,
      bold: true,
      children: <PaymentItemDetail summary="如有可用券、红包、京豆等，可在尾款时使用" />,
      enableActionIcon: false,
      defaultShowDetail: true,
    },
    ...(Array.isArray(total?.priceInfoList) ? total.priceInfoList : []).map((el) => {
      const { type, value } = el
      let { info } = el
      let children
      if (type == 1 || type == 3) {
        // 国内运费、全球售运费
        if (freightVoList && freightVoList.length && value != '￥0.00') children = <PaymentItemFreight data={freightVoList} />
      } else if (type == 7) {
        // plus会员补贴
        info ??= Array.isArray(rules?.plusInterestRule) && rules.plusInterestRule.length ? rules.plusInterestRule.join('\n') : ''
      } else if (type == 5) {
        // 小时购包装费
        info ??=
          Array.isArray(rules?.packagingServiceUseRule) && rules.packagingServiceUseRule.length
            ? rules.packagingServiceUseRule.join('\n')
            : ''
      } else if (type == 6) {
        // 海外消费税
        info ??= Array.isArray(rules?.taxUseRule) && rules.taxUseRule.length ? rules.taxUseRule.join('\n') : ''
      } else if (type == 9) {
        // 礼金
        if (Array.isArray(el.contentList) && el.contentList.length) children = <PaymentItemDetail summary={el.contentList.join('\n')} />
      } else if (type == 10) {
        // 京尊达服务费
        info = JZD_DESCRIPTION
      }
      return { ...el, info, children }
    }),
  ].filter(Boolean) as PaymentItemProps[]

  /** 协议列表 */
  const agreementList = []
  if (isDepositPay && (presale?.refundDeposit == '0' || presale?.refundDeposit == null)) {
    agreementList.push({
      checked: false,
      text: '同意支付定金',
      link: <span className="agreement-presale-tips">预售商品，定金不退哦</span>,
      url: null,
    })
  }
  if (Array.isArray(licenseList) && licenseList.length) {
    agreementList.push(...licenseList)
  }

  /** 全球购0元单，业务别名：税费击穿 */
  const [isGlobalZero, handleGlobalZero] = useGlobalZero()

  /** 支付密码提示 */
  const passwdTips = (Array.isArray(tipsList) ? tipsList : []).find((el) => {
    return el?.type == 9 && el?.tip && typeof el?.tip === 'string'
  })?.tip

  return (
    <div className={classNames('payment', rootClassName)}>
      {/* 边框氛围背景图片 */}
      <div className="payment__bg">
        <img src="//img11.360buyimg.com/img/jfs/t1/258688/15/22260/1632/67b6fe12Fa92db83b/0f82902127858fb3.png" />
      </div>
      <div className="payment-summary">
        <div className="payment-summary-title">
          <em>付款详情</em>
          <span>
            共<i>{qty}</i>件商品
          </span>
        </div>
        <div className="payment-summary-inner">
          {/* 费用明细 */}
          {items.map((item, index) => (
            <PaymentItem key={'' + index + item.name} {...item} />
          ))}
          {/* 支付方式 */}
          {Array.isArray(paymentVOList) && paymentVOList.length ? <PaymentMethod paymentModeList={paymentVOList} /> : null}
          {/* 密码框 */}
          {user?.fundsPwd && user?.isNeed && (
            <PaymentPassword ref={passwordRef} mode={user?.isShortPwdActive ? 'short' : 'long'} info={passwdTips} />
          )}
          {/* 用户协议 */}
          {Array.isArray(agreementList) && agreementList.length > 0 && (
            <PaymentAgreement ref={agreementRef}>
              {agreementList.map((item, index) => {
                const { link, text, url, checked } = item ?? {}
                return (
                  <PaymentAgreement.Item key={index} label={text} agreementName={link} checked={checked}>
                    {url && (
                      <PaymentAgreement.Content>
                        <iframe name="agreement" loading="lazy" src={url} width="100%" height="98%" />
                      </PaymentAgreement.Content>
                    )}
                  </PaymentAgreement.Item>
                )
              })}
            </PaymentAgreement>
          )}
        </div>
      </div>
      <div className="payment-action">
        {/* 国补提示 */}
        {govSubsidTips && typeof govSubsidTips === 'string' && <PaymentStateSubsidy {...{ govSubsidTips, unHitGovSubsidySkuJumpUrl }} />}
        <div className="payment-action__inner">
          <div className="payment-action-total">
            <div className="payment-action-total__title">
              <em>实付款</em>
              {/* 悬浮模式时展开&收起明细 */}
              <span className="payment-action-toggle" onClick={toggle} />
            </div>
            <div className="payment-action-total__price">
              {price != null && <>&yen;{price}</>} {isGlobalZero && <i className="icon-info mr-4" onClick={handleGlobalZero} />}
            </div>
            {/* 兑换价格 */}
            {exchangePrice && <div className="payment-action-total__eprice">{exchangePrice}</div>}
          </div>
          <div className="payment-action-submit">
            {enterPriseUser && selectedPayment == '5' && (
              <Button variant="default" size="large" onClick={() => submitOrder({ agreementRef, passwordRef, enterPriseUser })}>
                <Tooltip
                  content="若您要下多个订单，可以先提交订单再去订单中心合并支付，效率更高哟~"
                  width={280}
                  placement="top"
                  padding="12px"
                  distance={20}
                  arrow
                >
                  提交不支付
                </Tooltip>
              </Button>
            )}
            {qty ? (
              <Button
                variant="primary"
                size="large"
                onClick={() => submitOrder({ agreementRef, passwordRef })}
                className={classNames({ 'orange-style': isDepositPay })}
              >
                {isDepositPay ? '支付定金' : '提交订单'}（{qty}）
              </Button>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  )
}
Payment.displayName = 'Payment'
export default Payment
