/**
 * @file: input.tsx
 * @description: 留言组件，用于展示留言等逻辑
 */
import { noteBundleIdMap } from '@app/atoms/masterAtom'
import Input from '@app/components/Delivery/ui/input.tsx'
import { useAtom } from 'jotai'
import { useDelivery } from '../context'
import <PERSON><PERSON> from '../ui/botton'
import { useState } from 'react'

export default ({ onClose }: { onClose: () => void }) => {
  const [remarkObj, setArray] = useAtom(noteBundleIdMap)
  const [v, setV] = useState('')

  const deliveryState = useDelivery()

  const initValue = remarkObj?.get(deliveryState.state.wrapInitState.bundle.bundleId)?.remark || ''

  return (
    <>
      <div style={{ height: '16px' }}></div>
      <Input
        refKey={1}
        initValue={initValue}
        reportValue={(v) => {
          setV(v)
        }}
      />
      <Botton
        onSure={() => {
          deliveryState.deliveryLogger.leavemsg(v)
          setArray((prev) => {
            const newMap = new Map(prev)
            newMap.set(deliveryState.state.wrapInitState.bundle.bundleId, {
              venderId: deliveryState.state.wrapInitState.venderId,
              jdcombineStoreId: deliveryState.state.wrapInitState.jdcombineStoreId,
              storeId: deliveryState.state.wrapInitState.storeId,
              remark: v,
            })
            return newMap
          })
          onClose()
        }}
        onCancel={() => onClose()}
      />
    </>
  )
}
