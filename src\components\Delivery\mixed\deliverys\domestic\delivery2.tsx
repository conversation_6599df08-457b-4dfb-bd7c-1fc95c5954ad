/**
 * @file: delivery2.tsx
 * @description: 国内配送组件，用于展示国内配送安装等
 */
import SelectBody from '../../selector/SelectBody'
import Botton from '../../../ui/botton'
import DeliveryFactory from './deliveryFactory'
import { DeliveryTips } from './components/DeliveryTips'
import { useDeliveryState } from './hooks/useDeliveryState'
import { useDeliverySave } from './hooks/useDeliverySave'
import { useDeliveryAnalytics } from './hooks/useDeliveryAnalytics'
import { DeliveryComponentProps } from './types'
import { useMemo } from 'react'

export default function Delivery2({ deliveryDataIniter, onCancel, defaultTsx }: DeliveryComponentProps) {
  // 使用自定义 Hook 管理状态
  const { headerList, promise, shipmentInfo, selectValue, changeHeader, handleSelectValue } = useDeliveryState(deliveryDataIniter)

  // 使用自定义 Hook 管理保存逻辑
  const { save } = useDeliverySave(deliveryDataIniter, onCancel)

  // 使用自定义 Hook 管理数据上报
  const { log } = useDeliveryAnalytics(deliveryDataIniter, headerList, promise, false)

  // 处理保存
  const handleSave = () => {
    save(selectValue, promise, shipmentInfo)
  }

  // 处理取消
  const handleCancel = () => {
    onCancel()
  }

  const getTips = useMemo(() => {
    return <DeliveryTips shipmentInfo={shipmentInfo} bundle={deliveryDataIniter.initState.bundle} />
  }, [shipmentInfo, deliveryDataIniter.initState.bundle])

  // 如果没有头部列表，显示默认内容
  if (!headerList?.length) {
    return defaultTsx()
  }

  return (
    <SelectBody headerList={headerList} isDelivery={true} onChange={changeHeader}>
      {promise && (
        <DeliveryFactory
          tipComponent={getTips}
          deliveryDataIniter={deliveryDataIniter}
          promise={promise}
          onSelectValue={handleSelectValue}
        />
      )}

      {/* 操作按钮 */}
      <Botton
        onSure={() => {
          handleSave()
          log.shipmentlayer('1', '', '')
          log.shipmentPOPClick({ button_name: '1' })
        }}
        onCancel={() => {
          handleCancel()
          log.shipmentlayer('0', '', '')
          log.shipmentPOPClick({ button_name: '0' })
        }}
      />
    </SelectBody>
  )
}
