export type BaseApiResponse<T = unknown> = Partial<{
  /** 错误码 */
  code: `${number}`
  /** 错误信息 */
  message: string
  /** 服务器时间戳 */
  timestamp: number
  /** 响应数据 */
  body: T
}>
// 滑动验证码
interface IVirtualAssetCaptchaParams {
  presaleStockSign: number
}

// 查询绑定礼品卡信息
interface IQueryGiftCardInfoParasm {
  giftCardPwd: string
  sessionId: string
  verifyCode: string
  eid: string
  doBindFlag: 0 | 1
  queryGiftCardType: number
  origin: string
}

// 绑定礼品卡
interface IBindGiftCardParams {
  pwdKey: string
  giftCardPwd: string
  eid: string
  doBindFlag: 0 | 1
  queryGiftCardType: number
  origin: string
}

/**
 * 1 所有可用卡分页 -1 所有不可用的卡分页 2 可用领货码 -2 不可用领货码
 */
export type cardListQueryType = 1 | -1 | 2 | -2

// 获取礼品卡列表
interface IGetGiftCardListParams {
  // 查询礼品卡列表页面数量
  cardListPageSize: number
  // 查询礼品卡列表页码
  cardListPageNo: number
  cardListQueryType: cardListQueryType
  // 是否支持预售现货买
  presaleStockSign?: number
}

// 使用/取消礼品卡
export interface IGiftCardSelectionParams {
  // 礼品卡key
  giftCardKey: string
  // 礼品卡id
  giftCardId: string
  // 是否选中
  selected: boolean
  // 1 所有可用卡分页 -1 所有不可用的卡分页 2 可用领货码 -2 不可用领货码
  cardListQueryType: cardListQueryType
  // 查询礼品卡列表页面数量
  cardListPageSize: number
  // 查询礼品卡列表页码
  cardListPageNo: number
}

// 礼品卡列表data
interface GiftCardItem {
  // 礼品卡id
  id: string
  // 礼品卡key
  key: string
  // 3京东卡
  type: number
  // 礼品卡名称
  cardBrandName: string
  // 是否被选中
  selected: boolean
  // 不可用说明
  disableDesc: string
  // 可用说明
  ableDesc: string
  // 面值
  amount: string
  // 余额
  leaveMoney: string
  // 当前折扣金额
  discountUsed: string
  // 开始使用时间
  timeBegin: string
  // 结束使用时间
  timeEnd: string
  // 福粒文案
  soonExpiredStr: string
  // 是否可用
  yn: number
}

// 礼品卡列表出参
interface GiftCardListResponse {
  // 礼品卡列表
  giftCardList: GiftCardItem[]
  // 可用数量
  availableTotal: number
  // 不可用数量
  unAvailableTotal: numbe
  // 最大可用数量
  canUseCardMaxNum: number
  // 已用礼品卡数量
  giftCardNum: number
  // 礼品卡抵扣金额
  giftCardDiscount: string
}

export type GiftCardListResponseBody = BaseApiResponse<GiftCardListResponse>

interface DiscountItem {
  /** 优惠券类型 */
  type: number
  /** 优惠券类型描述 */
  typeDesc: string
  /** 优惠券面值 */
  discount: string
  /** 优惠券限额 */
  quota: string
}
/**
 * 优惠劵
 */
export interface CouponItem {
  /** 优惠券id */
  id: string
  /** 优惠卷key */
  key: string
  /** 限店铺券店铺id，如果为非限店铺券，则venderId=0 */
  venderId: number
  /** 优惠券类型，京券=0 东券=1 运费券=2（couponType=2 and couponStyle=2 才是运费券） */
  couponType: number
  /** 优惠券名称 */
  couponTypeStr: string
  /** 优惠券样式-----couponType0:电子券;1: 实体券 (已废弃)2:免运费券 */
  couponStyle: number
  /** 优惠券面值/限额，如果是京券，该值=0，如果是东券，该值=使用该券的最低金额 */
  quota: string
  /** 优惠额度,优惠金额，面值   这里需要显示的金额 */
  discount: string
  /** 优惠券发放时间 */
  beginTime: string
  /** 优惠劵过期时间 */
  endTime: string
  /** 是否选中 */
  selected: boolean
  /** 优惠券限制信息 */
  limitDesc: string
  /** 该张券可使用的sku列表 */
  supportSkus: ShowSkuVO[]
  /** 东券叠加描述 */
  dongOverlapStr: string[]
  /** 折扣券的最大优惠金额 */
  highDiscount: string
  /** 折扣信息模板 */
  discountInfos: DiscountItem[]
  /** 差X元可用该券 */
  lessQuota: string
  /** 不可用原因描述码,券为可用券时,该字段为空 */
  descCode: string
  /** 不可用原因描述 */
  desc: string
  /** 消费劵标识 */
  couponIconClass: {
    /** 样式key */
    iconClass: string
    /** 文案key */
    desc: string
  }
  /** 是否氛围卷  true显示氛围卷背景 */
  coupon200: boolean
  /** 皮肤背景 */
  backgroundImg: string
  /** 是否可叠加 true只读，false可选择 */
  readOnly: boolean
}

/**
 * 可用sku列表
 */
export interface ShowSkuVO {
  /**
   * 商品id
   */
  skuId: number
  /**
   * 商品名称
   */
  skuName: string
  /**
   * 商品图片
   */
  imgUrl: string
}
