import './index.scss'
import React from 'react'
import getImgUrl from '@app/utils/images'
import type { ShipmentSku } from '@app/typings/master_api_response'

type Props = {
  /** 支付方式名称 */
  paymentName: string
  /** 支持此支付方式的sku */
  mainSupportSkus?: ShipmentSku[]
  /** 不支持此支付方式的sku */
  subSupportSkus?: ShipmentSku[]
}

/** 混合支付确认弹窗 */
const PayMethodDialog: React.FC<Props> = (props) => {
  const { paymentName, mainSupportSkus, subSupportSkus } = props
  return (
    <div className="mixed-payment-dialog">
      <div className="mixed-payment-dialog__title">
        以下商品支持<span>{paymentName}</span>
      </div>
      <div className="mixed-payment-dialog__content">
        {mainSupportSkus?.map((sku, index) => {
          const imgUrl = sku?.imgUrl
          return (
            imgUrl && (
              <div key={index}>
                <img src={getImgUrl(imgUrl)} alt={sku?.name} title={sku?.name} />
              </div>
            )
          )
        })}
      </div>
      <div className="mixed-payment-dialog__title">
        以下商品不支持<span>{paymentName}，将使用在线支付</span>
      </div>
      <div className="mixed-payment-dialog__content">
        {subSupportSkus?.map((sku, index) => {
          const imgUrl = sku?.imgUrl
          return (
            imgUrl && (
              <div key={index}>
                <img src={getImgUrl(imgUrl)} alt={sku?.name} title={sku?.name} />
              </div>
            )
          )
        })}
      </div>
    </div>
  )
}

export default PayMethodDialog
