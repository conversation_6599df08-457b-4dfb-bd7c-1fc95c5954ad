import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
// 上报异常
function postDraData(errcode: number, msg: string, fid: string, error: any, params: any, requestUrl: string) {
  // 新版烛龙上报
  try {
    const { code, msg } = params || {}
    monitoring({
      name: monitorName.Settlement,
      code: monitorCode.Address,
      msg: {
        functionId: code,
        error_type_txt: `收货人信息接口异常-${code}`,
        error_msg: JSON.stringify(msg),
      },
    })
  } catch (e) {
    console.error(e)
  }

  try {
    window.jmfe?.jsagentReport(
      window.jmfe.JSAGENT_EXCEPTION_TYPE.business, //固定值不变
      errcode || 712, //固定值: 异常码
      `收货人信息接口异常-${msg}-${fid}`, // 异常信息
      {
        fid, // 网关对应的functionid
        error: JSON.stringify(error),
        params, // body序列化后的字符；由于浏览器对url有长度限制，body约定限定在1000字符内是绝对满足上报条件的，超过部分前端自行截断。
        requestUrl,
      },
    )
  } catch (error) {
    console.log(error)
  }
}

const colorApi = { postDraData }

export default colorApi
