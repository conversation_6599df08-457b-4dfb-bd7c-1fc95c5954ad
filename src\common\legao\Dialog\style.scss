@use "@app/assets/styles/lib.scss";

.jd-dialog,
.single-button {
  position: relative;
  // left: 50%;
  // top: 50%;
  z-index: 999;
  border-radius: 8px;
  // overflow: hidden;
  background: #fff;
  margin: 0 auto 50px;
  margin-top: 100px;
}
// .jd-dialog button,
// .single-button button {
//   padding: 0;
//   cursor: pointer;
//   outline: 0;
//   border: 0;
// }
.dialog-wrapper {
  // position: relative;
  z-index: 1000;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
}
.dialog-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 998;
  background-color: #000000B2;
}
.dialog-head {
  position: relative;
  padding: 0 24px;
  height: 58px;
  line-height: 58px;
  color: #1a1a1a;
  font-family: PingFang SC;
  font-size: 18px;
  font-weight: 600;
}
.dialog-head-notitle {
  background: #fff;
}
.dialog-close {
  padding: 0;
  outline: 0;
  border: 0;
  cursor: pointer;
  display: block;
  position: absolute;
  z-index: 1000;
  top: 24px;
  right: 24px;
  width: 16px;
  height: 16px;
  background: url(https://img12.360buyimg.com/imagetools/jfs/t1/275638/17/11200/598/67e669f0F62ee6ede/bf00226f8543b19b.png) center / 100% 100% no-repeat;
}
.dialog-close-notitle {
  width: 16px;
  height: 16px;
  background: url(//misc.360buyimg.com/user/cart/css/i/commontips/common-tips-close.png)
    no-repeat;
}
.dialog-body {
  padding: 0 24px;
  background: #fff;
}
.dialog-foot {
  height: 50px;
  border-top: 1px solid #f5f5f5;
  overflow: hidden;
}
.dialog-foot button {
  display: inline-block;
  width: 50%;
  line-height: 50px;
  text-align: center;
  font-size: 14px;
}
.dialog-cancel {
  background-color: #fff;
}
.single-button .dialog-cancel {
  width: 100%;
}
.dialog-submit {
  background-color: #c71622;
  color: #fff;
}

.d-c-wrap {
  width: 610px;
  height: 407px;
}
.ui-dialog-content {
  padding: 38px 0 24px 0;
  .common-tips-dialog {
    // text-align: center;
    .common-tips-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 15px;
      display: inline-block;
      position: relative;
      vertical-align: middle;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
    .succ {
      background-image: url(//misc.360buyimg.com/user/cart/css/i/commontips/common-tips-succ.png);
    }
    .error {
      background-image: url(https://img10.360buyimg.com/img/jfs/t1/250379/34/38330/705/678ca770F8297bc6b/80cd468fdf1ddc51.png);
    }
    .confirm {
      background-image: url(//misc.360buyimg.com/user/cart/css/i/commontips/common-tips-confirm.png);
    }
    .common-tips-tit {
      text-align: left;
      color: #1a1a1a;
      font-family: PingFang SC;
      font-size: 18px;
      font-weight: 600;
      line-height: 18px;
      > .iconTip {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url("https://img10.360buyimg.com/img/jfs/t1/250379/34/38330/705/678ca770F8297bc6b/80cd468fdf1ddc51.png")
          no-repeat;
        background-size: 100% 100%;
        vertical-align: -6px;
        margin-right: 12px;
      }
    }
    .common-tips-cont {
      line-height: 14px;
      vertical-align: middle;
      text-align: left;
      padding-left: 36px;
      margin-top: 12px;
      font-weight: 400;
      color: #1a1a1a;
      font-family: PingFang SC;
      font-size: 14px;
    }
    .common-tips-opts {
      position: absolute;
      z-index: 10;
      bottom: 24px;
      width: calc(100% - 48px);
      text-align: right;
      padding-top: 16px;
      background-color: #ffffff;
      .comon-tips-btn {
        display: inline-block;
        width: 80px;
        // height: 30px;
        line-height: 30px;
        // border: 0.5px solid #c2c4cc;
        border-radius: 4px;
        color: #ffffff;
        font-family: PingFang SC;
        font-size: 14px;
        text-align: center;
        position: relative;
      }
      .yes {
        color: #fff;
        background: linear-gradient(90deg, #FF475D 0%, #FF0F23 100%);
        // height: 32px;
        line-height: 32px;
        border: none;
      }
      .no {
        color: #1a1a1a;
        background: #fff;
        margin-right: 8px;
        &:hover {
          background-color: #FFEBF1;
          color: #FF0F23;
          &::after {
            border-color: #FF8595;
          }
        }
      }
      .no::after {
        content: "";
        position: absolute;
        top: -1px;
        left: -1px;
        width: 100%;
        height: 100%;
        border: 0.5px solid #c2c4cc;
        border-radius: 4px;
        transform: scale(1);
        transform-origin: 0 0;
        pointer-events: none;
      }
    }
  }
}