/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-26 18:00:57
 * @LastEditTime: 2025-07-02 17:53:10
 * @LastEditors: ext.wangchao120
 * @Description: 右边的提示
 * @FilePath: /pc_settlement/src/components/VirtualAsset/components/RightTip/index.tsx
 */

import React from 'react'
import style from './index.module.scss'
import useMasterData from '@app/hooks/useMasterData'
import classNames from 'classnames'
interface Props {
  /**
   * couponDescLink 优惠券
   * redPacketLink 红包
   * jingDouDescLink 京豆
   */
  type: 'couponDescLink' | 'redPacketLink' | 'jingDouDescLink' | 'paymentPassword' | 'realNameAuthentication'
  className?: string
  onClick?: () => void
}
interface PageLinkConfig {
  couponDescLink?: string
  redPacketLink?: string
  jingDouDescLink?: string
  paymentPassword?: string
  realNameAuthentication?: string
}

const obj = {
  couponDescLink: '优惠券',
  redPacketLink: '红包',
  jingDouDescLink: '京豆',
  paymentPassword: '开启支付密码',
  realNameAuthentication: '完成实名认证',
}
const arr = ['paymentPassword', 'realNameAuthentication']
const RightTip: React.FC<Props> = ({ type, className, onClick }) => {
  const pageLinkConfig = (useMasterData()?.body?.pageLinkConfig ?? {}) as PageLinkConfig
  if (!obj[type]) return null

  const click = () => {
    onClick && onClick()
  }
  return (
    <span className={classNames(style.RightTip, className)}>
      {!arr.includes(type) ? (
        <>
          <i>了解</i>
          <a href={pageLinkConfig[type]} target="_blank" onClick={click}>
            什么是{obj[type]}
          </a>
        </>
      ) : (
        <a href={pageLinkConfig[type]} target="_blank" onClick={click}>
          {obj[type]}
        </a>
      )}
    </span>
  )
}

export default RightTip
