import { FC, ReactNode, useEffect, useState } from 'react'
import ReactDOM from 'react-dom'
import classNames from 'classnames'
import './InvoiceDialog.scss'
/**
 * 发票弹窗基础组件
 */
export interface IInvoiceDialogProps {
  /** 头部标题, 空则无 */
  title?: string
  /** 头部关闭按钮是否显示 */
  closeButton?: boolean
  /** 是否存在mask层 */
  hasMask?: boolean
  /** 打开弹窗 */
  isOpen?: boolean
  /** mask层是否能关闭弹窗 */
  outsideClose?: boolean
  /** 弹窗中内容主体宽度 */
  width?: number
  /** 弹窗中内容主体高 */
  height?: number
  /** 弹窗中内容主体 */
  children?: ReactNode
  /** 弹窗外层类名 */
  className?: string
  /** 关闭弹窗时的回调 */
  onClose?: () => void
  /** 打开弹窗时的回调 */
  onReady?: () => void
  /** 标题图标类型 */
  iconType?: 'success' | 'warning' | 'error' | 'info'
  /** 自定义头部内容 */
  headerContent?: ReactNode
  /** 头部标签页 */
  headerTabs?: ReactNode
  /** 底部内容 */
  footer?: ReactNode
}

const InvoiceDialog: FC<IInvoiceDialogProps> = ({
  title,
  closeButton = true,
  hasMask = true,
  isOpen = false,
  outsideClose = false,
  width,
  height,
  children,
  className,
  onClose,
  onReady,
  iconType,
  headerContent,
  headerTabs,
  footer,
}) => {
  const [viewportHeight, setViewportHeight] = useState(window.innerHeight)
  const [viewportWidth, setViewportWidth] = useState(window.innerWidth)

  useEffect(() => {
    if (isOpen && onReady) {
      onReady()
    }
  }, [isOpen, onReady])

  /**
   * 监听窗口大小变化
   */
  useEffect(() => {
    const handleResize = () => {
      setViewportHeight(window.innerHeight)
      setViewportWidth(window.innerWidth)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  useEffect(() => {
    const body = document.querySelector('.invoice-dialog-body') as HTMLElement
    const dialog = document.querySelector('.invoice-dialog') as HTMLElement
    const header = document.querySelector('.invoice-dialog-head') as HTMLElement
    const footer = document.querySelector('.modal-footer') as HTMLElement
    if (height && body && dialog && header && footer) {
      if (dialog.clientHeight + header.clientHeight + footer.clientHeight - 24 > height) {
        body.style.maxHeight = height - header.clientHeight - footer.clientHeight - 24 + 'px'
        body.style.overflowY = 'auto'
      }
    }
  }, [viewportHeight, height])

  /**
   * 处理mask点击事件
   */
  const handleMaskClick = () => {
    if (outsideClose && onClose) {
      onClose()
    }
  }

  /**
   * 渲染头部内容
   */
  const renderHeader = () => {
    if (headerContent) {
      return (
        <div className="invoice-dialog-head">
          {headerContent}
          {closeButton && <button className="invoice-dialog-close" onClick={onClose} />}
        </div>
      )
    }

    if (headerTabs) {
      return (
        <div className="invoice-dialog-head">
          <div className="modal-header-with-tabs">{headerTabs}</div>
          {closeButton && <button className="invoice-dialog-close" onClick={onClose} />}
        </div>
      )
    }

    if (title) {
      return (
        <div className="invoice-dialog-head">
          {iconType ? <span className="invoice-dialog-title">{title}</span> : <span className="invoice-dialog-title">{title}</span>}
          {closeButton && <button className="invoice-dialog-close" onClick={onClose} />}
        </div>
      )
    }

    return null
  }

  if (!isOpen) return null

  return ReactDOM.createPortal(
    <div className="invoice-dialog-wrapper">
      {hasMask && <div className="invoice-dialog-mask" onClick={handleMaskClick} />}
      <div
        className={classNames('invoice-dialog', {
          [className as string]: className,
        })}
        style={{
          width: width + 'px',
          height: height + 'px',
          marginTop: (viewportHeight - (height || 0)) / 2 + 'px',
          marginLeft: (viewportWidth - (width || 0)) / 2 + 'px',
        }}
      >
        {renderHeader()}
        <div className="invoice-dialog-body">{children}</div>
        {footer && (
          <div className="modal-footer">
            <div className="modal-footer-content">{footer}</div>
          </div>
        )}
      </div>
    </div>,
    document.body,
  )
}

export default InvoiceDialog
