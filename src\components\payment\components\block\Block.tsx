/**
 * @file: Block.tsx
 * @description: 提交订单成功后的卡单弹窗
 */
import React, { useCallback, useMemo } from 'react'
import Button from '@app/common/button'
import showToast from '../toast'
import * as apis from '@app/services/api'
import styles from './index.module.scss'
import type { BlockProps, BlockSku } from './types/block'
import getImgUrl from '@app/utils/images'

const headers = {
  '722': '抱歉，您本单购买的以下商品或赠品在所选择的地址下暂时无货',
  '7203': '抱歉，您本单购买的以下促销商品暂时限制购买',
  '7204': '抱歉，您本单购买的以下商品在所选择的地址下暂时不支持销售',
  '7205': '抱歉，您本单购买的以下商品价格发生变动，请重新下单',
  '7206': '抱歉，您本单购买的以下商品暂时限制购买',
  '7207': '抱歉，您本单购买的以下商品暂时不支持购买',
  '7208': '抱歉，您本单购买的以下商品或赠品在所选择的地址下暂时无货',
  '727': '疫情购药登记',
} as const

const Block: React.FC<BlockProps> = ({
  errorCode,
  errorReason,
  noStockSkuList = [],
  scaleSkuInfoList = [],
  noAvailableSkuList = [],
  priceChangeList = [],
  noSupportHomeServiceSkuList = [],
  commonStockSkuInfoList = [],
  medicineSkuList = [],
  purchaseList,
  mainSkuIdList,
  balanceSkus,
  balancePresale,
  balancePurchaseCasual,
  jumpUrl,
  submitOrderPromptVO,
  subsidyToast,
  onClose,
  onResume,
  onRefresh,
  onOpenPanel,
  goBack,
}) => {
  const stocks = useMemo(() => balanceSkus?.filter((item) => item.stockState === 1), [balanceSkus])
  const purchase = useMemo(
    () => [...(submitOrderPromptVO?.purchaseList || []), ...(purchaseList || [])],
    [purchaseList, submitOrderPromptVO?.purchaseList],
  )
  const allPurchaseSkuItems =
    purchase?.reduce<Array<BlockSku>>((accumulator, currentValue) => {
      return accumulator.concat(currentValue.purchaseSkuList || [])
    }, []) || []

  const items: Array<BlockSku> = useMemo(
    () =>
      submitOrderPromptVO?.skuList || [
        ...noStockSkuList,
        ...scaleSkuInfoList,
        ...noAvailableSkuList,
        ...priceChangeList,
        ...noSupportHomeServiceSkuList,
        ...commonStockSkuInfoList,
        ...medicineSkuList,
      ],
    [
      noStockSkuList,
      scaleSkuInfoList,
      noAvailableSkuList,
      priceChangeList,
      noSupportHomeServiceSkuList,
      commonStockSkuInfoList,
      medicineSkuList,
      submitOrderPromptVO?.skuList,
    ],
  )

  const handleRemove = useCallback(() => {
    const skuItems: Parameters<typeof apis.removeItems>[1] = []

    if (balancePresale) {
      items.forEach((item) => {
        balanceSkus?.forEach((sku) => {
          if (sku.id === item.id) {
            skuItems.push({
              skuId: sku.id,
              num: sku.buyNum,
              storeId: sku.vender.jDCombineStoreId,
              cartUuid: sku.cartUuid,
            })
          }
          sku.affiliatedList?.forEach((affi) => {
            if (affi.id === item.id) {
              skuItems.push({
                skuId: sku.id,
                num: affi.buyNum,
                storeId: sku.vender.jDCombineStoreId,
                cartUuid: sku.cartUuid,
                giftSkuId: affi.id,
              })
            }
          })
        })
      })
      if (submitOrderPromptVO?.purchaseList) {
        submitOrderPromptVO?.purchaseList.forEach((item) => {
          item.purchaseSkuList?.forEach((item) => {
            balanceSkus?.forEach((sku) => {
              sku.affiliatedList?.forEach((affi) => {
                if (affi.id === item.id) {
                  skuItems.push({
                    skuId: sku.id,
                    num: affi.buyNum,
                    storeId: sku.vender.jDCombineStoreId,
                    cartUuid: sku.cartUuid,
                    giftSkuId: affi.id,
                  })
                }
              })
            })
          })
        })
      }
    } else if (purchaseList || submitOrderPromptVO?.purchaseList) {
      ;(purchaseList || submitOrderPromptVO?.purchaseList)?.forEach((item) => {
        item.purchaseSkuList?.forEach((item) => {
          skuItems.push({
            ...item,
            skuId: item.id,
          })
        })
      })
    } else {
      items.forEach((item) => {
        skuItems.push({
          ...item,
          skuId: item.id,
          itemType: item.type,
          num: item.buyNum,
          skuUuid: item.uuid,
        })
      })
    }

    apis
      .removeItems({ skuItems })
      .then(() => {
        onClose()
        onRefresh().then(() => {
          showToast({ title: '移除成功' })
        })
        // events.trigger('order-change', { message: '移除成功' })
      })
      .catch(() => {
        showToast({ title: '移除商品失败，请重试' })
      })
  }, [balancePresale, purchaseList, submitOrderPromptVO, items, balanceSkus, onClose])

  const handleCancel = useCallback(() => {
    const scaleSkuIds = scaleSkuInfoList?.map((item) => item.id)
    if (!scaleSkuIds) {
      return
    }

    apis
      .cancelSkuScaleBuyPrice({ scaleSkuIds })
      .then(() => {
        onClose()
        onRefresh().then(() => {
          showToast({ title: '已取消促销优惠,请检查后重新提单' })
        })
        // events.trigger('order-change', { message: '已取消促销优惠,请检查后重新提单' })
      })
      .catch(() => {
        showToast({ title: '取消促销优惠失败，请重试' })
      })
  }, [scaleSkuInfoList, onClose])

  // 疫情登记
  const handleLink = useCallback(() => {
    const target = submitOrderPromptVO?.jumpUrl || jumpUrl
    if (!target) {
      return
    }
    window.location.href = target
  }, [jumpUrl, submitOrderPromptVO, onClose])

  const handleChangeCount = useCallback(() => {
    onClose()
    // const promises: Array<Promise<{ num?: number }>> = []
    // submitOrderPromptVO?.purchaseList?.forEach((item) => {
    //   item.purchaseSkuList?.forEach((item) => {
    //     item.canMaxBuyNum &&
    //       promises.push(
    //         apis.cartChange({
    //           cartParam: {
    //             roomId,
    //             supperRoomPromo,
    //             pt,
    //             skuItem: {
    //               skuId: item.id,
    //               num: item.canMaxBuyNum,
    //               cartUuid: item.cartUuid,
    //             },
    //           },
    //           multiCompositeMainItem: balanceSkus?.find((balance) => item.id === balance.id)?.multiCompositeMainItem,
    //         }),
    //       )
    //   })
    // })
    // if (promises.length === 0) {
    //   onClose()
    //   return
    // }
    // Promise.all(promises).then(() => {
    //   onClose()
    //   // events.trigger('order-change')
    // })
  }, [submitOrderPromptVO, onClose, balanceSkus])

  const handleRefresh = useCallback(() => {
    onClose()
    onRefresh()
  }, [onClose, onRefresh])

  const handleOpenPanel = useCallback(() => {
    onClose()
    onOpenPanel?.()
  }, [onClose, onOpenPanel])

  const handldleAfreshSubsidy = useCallback(() => {
    const purchaseUuids = purchase.reduce<string[]>((acc, item) => {
      const uuids = item.purchaseSkuList?.map((it) => it.uuid)?.filter(Boolean) || []
      return acc.concat(uuids)
    }, [])
    const uuidList = [...items].map((item) => item.uuid).filter(Boolean) || []

    onClose()
    /** @ts-ignore */
    onRefresh({
      balanceCommonOrderForm: {
        govSubsidyRemoveSkuUuidList: [...uuidList, ...purchaseUuids],
      },
    })
  }, [items, onClose, purchase])

  const handleConfirmSubsidy = useCallback(() => {
    onClose()
    subsidyToast && showToast({ title: subsidyToast })
  }, [subsidyToast, onClose])

  const methods: Record<string, { onClick: () => void; track?: string } | undefined> = {
    '1': {
      onClick: handleRemove,
    },
    '2': {
      onClick: handleCancel,
    },
    '4': {
      onClick: handleLink,
    },
    '5': {
      onClick: () => {
        onClose()
      },
    },
    '7': {
      onClick: () => {
        onClose()
      },
    },
    '8': {
      onClick: handleLink,
    },
    '9': {
      onClick: onResume,
    },
    '10': {
      onClick: handleChangeCount,
    },
    '11': {
      onClick: handleRefresh,
    },
    '12': {
      onClick: handleOpenPanel,
    },
    '13': {
      onClick: handleRemove,
    },
    '14': {
      onClick: handldleAfreshSubsidy,
    },
    '15': {
      onClick: handleConfirmSubsidy,
    },
    '16': {
      onClick: () => {
        onClose()
        // events.trigger('order-change')
      },
    },
    '17': {
      onClick: () => {
        onClose()
      },
    },
  }

  const confirmText = submitOrderPromptVO?.confirmText
    ? //  &&
      // (submitOrderPromptVO.promptType !== 1 ||
      //   (commonStockSkuInfoList.length === 0 &&
      //     !balancePurchaseCasual?.skuNum &&
      //     ((mainSkuIdList?.length || 0) > 1 || (stocks?.length || 0) > 0) &&
      //     mainSkuIdList?.find((id) => ![...items, ...allPurchaseSkuItems].find((item) => item.id === id))))
      submitOrderPromptVO.confirmText
    : undefined

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <i />
        {submitOrderPromptVO?.title || (errorCode && (errorCode === '7206' ? errorReason || headers[errorCode] : headers[errorCode]))}
      </div>
      {(submitOrderPromptVO?.context || errorCode === '727') && (
        <div className={styles.desc}>{submitOrderPromptVO?.context || errorReason}</div>
      )}
      {submitOrderPromptVO?.tips && <div className={styles.tips}>{submitOrderPromptVO?.tips}</div>}
      <div className={styles.body}>
        {submitOrderPromptVO?.content && <div className={styles.content}>{submitOrderPromptVO?.content}</div>}
        {items.map((item, index) => {
          // 如果是服务不支持，渲染该UI
          if (item.containsNoSupportService && item.noSupportServiceList?.length) {
            return item.noSupportServiceList.map((service, index) => {
              return (
                <div className={styles.service_row} key={`service_${index}`}>
                  <div className={styles.service_name}>{service.name}</div>
                  <div className={styles.service_num}>{service.priceAndNumText}</div>
                </div>
              )
            })
          }

          return (
            <div key={index} className={styles.row}>
              <div className={styles.pic}>
                <img className={styles.image} mode="aspectFit" src={`${getImgUrl(item.imgUrl)}`} />
              </div>
              <div className={styles.info}>
                <div className={styles.name}>{item.name}</div>
                {item.blockExtMsg && <div className={styles.block}>{item.blockExtMsg}</div>}
                <div className={styles.line}>
                  {item.jdPrice && item.pricePrevious && (
                    <>
                      <div className={styles.jdPrice}>当前价：¥{item.jdPrice}</div>
                      <div className={styles.price}>初始价：¥{item.pricePrevious}</div>
                    </>
                  )}
                  {!!item.buyNum && <div className={`${styles.count} `}>x{item.buyNum}</div>}
                </div>
              </div>
            </div>
          )
        })}
        {(purchaseList || submitOrderPromptVO?.purchaseList)?.map((purchase, index) => {
          const renderItem = (item: BlockSku) => {
            return (
              <>
                <div className={styles.row}>
                  <div className={styles.pic}>
                    <img className={styles.image} src={`${getImgUrl(item.imgUrl)}`} />
                  </div>
                  <div className={styles.info}>
                    <div className={styles.name}>{item.name}</div>
                    {(item.pastText || item.showText) && (
                      <div className={styles.block}>
                        {item.pastText}
                        {item.showText}
                      </div>
                    )}
                    <div className={styles.line}>
                      {item.jdPrice && item.pricePrevious && (
                        <>
                          <div className={styles.jdPrice}>当前价：¥{item.jdPrice}</div>
                          <div className={styles.price}>初始价：¥{item.pricePrevious}</div>
                        </>
                      )}
                      {item.buyNum && !item.pastText && <div className={styles.count}>x{item.buyNum}</div>}
                    </div>
                  </div>
                </div>
              </>
            )
          }

          return (
            <React.Fragment key={index}>
              {purchase.purchaseSkuList?.map((item, index) => {
                return (
                  <React.Fragment key={index}>
                    {item.limitReason && <div className={styles.alert}>{item.limitReason}</div>}
                    {renderItem(item)}
                  </React.Fragment>
                )
              })}
            </React.Fragment>
          )
        })}
      </div>
      {submitOrderPromptVO ? (
        <div className={styles.footer}>
          {submitOrderPromptVO.cancelText && (
            <Button
              variant={confirmText ? 'default' : 'primary'}
              onClick={() => {
                if (submitOrderPromptVO?.cancelType === 'back') {
                  goBack()
                } else if (/^\d+$/.test(submitOrderPromptVO?.cancelType)) {
                  methods[submitOrderPromptVO.cancelType]?.onClick()
                } else {
                  onClose()
                }
              }}
            >
              {submitOrderPromptVO.cancelText}
            </Button>
          )}
          {confirmText && (
            <Button
              variant="primary"
              onClick={() => {
                methods[submitOrderPromptVO.promptType]?.onClick()
              }}
            >
              {confirmText}
            </Button>
          )}
        </div>
      ) : (
        <div className={styles.footer}>
          {errorCode !== '727' && (
            <Button variant="default" onClick={() => goBack()}>
              返回上一页
            </Button>
          )}
          {errorCode === '727' && (
            <>
              <Button variant="default" onClick={() => onClose()}>
                取消
              </Button>
              <Button variant="primary" onClick={handleLink}>
                立即登记
              </Button>
            </>
          )}
          {(errorCode === '7208' || errorCode === '722' || errorCode === '7204') &&
            commonStockSkuInfoList.length === 0 &&
            !balancePurchaseCasual?.skuNum &&
            ((mainSkuIdList?.length || 0) > 1 || (stocks?.length || 0) > 0) &&
            mainSkuIdList?.find((id) => !items.find((item) => item.id === id)) && (
              <Button variant="primary" onClick={handleRemove}>
                移除无货商品
              </Button>
            )}
          {errorCode === '7203' && (
            <Button variant="primary" onClick={handleCancel}>
              取消促销优惠
            </Button>
          )}
        </div>
      )}
    </div>
  )
}

export default Block
