const getProto = Object.getPrototypeOf
const toString = Object.prototype.toString
const hasOwn = Object.prototype.hasOwnProperty
const fnToString = hasOwn.toString
const ObjectFunctionString = fnToString.call(Object)

function isPlainObject(obj: any) {
  let proto, Ctor

  if (!obj || toString.call(obj) !== '[object Object]') {
    return false
  }

  proto = getProto(obj)

  // Objects with no prototype (e.g., `Object.create( null )`) are plain
  if (!proto) {
    return true
  }

  // Objects with prototype are plain iff they were constructed by a global Object function
  Ctor = hasOwn.call(proto, 'constructor') && proto.constructor
  return typeof Ctor === 'function' && fnToString.call(Ctor) === ObjectFunctionString
}

export { getProto, toString, hasOwn, isPlainObject as default }
