/**
 * 链接携带参数
 * 列表
 * 新增/编辑页
 */
declare namespace AddressStandard {
  /**
   * 地址列表链接拼接参数
   */
  export interface ListUrlParamsType {
    /** 当前选中的地址id */
    id?: string
    /** 是否可选择 */
    selectable?: boolean
    /** 是否可多选 */
    multipleSelectable?: boolean
    /** 请求来源 */
    from?: string
    addrAgent?: string
    /** 回跳url */
    rurl?: string
    /** 标题头 */
    header?: 'true' | 'false' | boolean
    /** 提示信息 */
    alert?: string
    /** 地址添加/编辑页完成后回到地址列表页 */
    editNavigateBackDelta?: string
    /** **以下入参列表接口需要------------------------------**** */
    /**
     * 门店ID集合（结算场景使用）
     */
    shopIdsArr?: Array<string>
    /**
     * 门祥到家融合 （该字段true 时，必须venderId、storeId）
     */
    jdStoreSign?: boolean
    /**
     * 店铺id 京东自营传JD（门详场景使用）
     */
    venderId?: string
    /**
     * 门店id（门详场景使用）
     */
    storeId?: string
    /**
     * 门店类型（门详场景使用）:门店来源 1. 达达 2. 青龙 3:伽利略 4:门店帮 6:医药（泰国传0） 8:前置仓门店 9:重货仓
     */
    storeType?: number
    /**
     * 地址组件接入规范：https://cf.jd.com/pages/viewpage.action?pageId=1002739550
     * 必须入参否则提示无权限访问 地址组件授权
     */
    token?: string
    /**
     * 客户端传入地址集合
     */
    addressClients?: Array<AddressClientInfoVO>
    /**
     * 对于APP结算特殊场景字段，是否做初始化，"0":不初始化  ”1“初始化
     */
    isInitialize?: string
    /**
     * APP结算特殊场景字段
     */
    addressBalanceAppVoList?: Array<AddressBalanceAppVo>
    /**
     * 归堆排序字段 枚举值：edit,delete,seleted,readonly
     * 默认按照线上的是否可选排序
     */
    homingWord?: 'edit' | 'delete' | 'seleted' | 'readonly'
    /**
     * 当前是否要用POI地址逻辑
     */
    usePoiAddress?: boolean
    /**
     * 是否包含 到家 || 天选 || 前置仓 业务
     */
    hasO2OTag?: boolean
    /**
     * 是否返回默认地址
     */
    defaultAddress?: boolean
    /**
     * 返回按钮返回默认地址
     */
    returnDefault?: boolean
    /**
     * 侧滑返回默认地址
     */
    sideslipDefault?: boolean
    /**  定位分场景授权的场景id */
    sceneId?: string
    /**  关闭webview层级 */
    delta?: number | string
    /**
     * 是否在进入列表页时，直接提示地址条数已达上限
     */
    showLimitToast?: boolean
    /**
     * list样式，半弹层：panel，默认：全页面，
     */
    ui?: 'panel'
    /**
     * 打开方式 halfScreen/fullScreen 半屏/全屏
     */
    openMode?: string
    /**
     * 半屏切全屏
     */
    halfSwitchFull?: string
    /**
     * 埋点所需：1：结算，2：我京，3：其他
     */
    channelsource?: string | number
    /**
     * 未登录，提示用户去登录
     */
    showLogin?: boolean
    /**
     * 埋点上报所需。（多品多地址）
     * 1-从多地址入口进入、2-多地址结算页进入,3-多地址拖拽页进入
     */
    sourceToReport?: string
    /**
     * 跳转新编辑
     */
    edit2?: boolean
    /* * 当前打开的页面类型，对应mpass的配置链接
     */
    currentWebPageType?: string
    /**
     * 当前打开的页面Id，客户端自增
     */
    currentWebPageId?: string
    /**
     * 参数拼接
     */
    pageListPathStr?: string
    /** 配送地址点击新建和编辑 * */
    deliveryJumplistOrEdit?: boolean | 'true' | 'false'
    /**
     * 列表选择模式，如是否根据店铺id进行是否可选校验等
     * 'secondArrive' 首页秒送专区校验 PRD:https://joyspace.jd.com/pages/8wwfFXPA5Gni6SVqAOhW
     * 'overhangCheck' 小时达订单校验 修改门店范围+1km校验 PRD:https://joyspace.jd.com/pages/o52ygXgCQzDu4XfFupCz 后端接口文档：https://joyspace.jd.com/pages/Uwb186C88Ld6LaReZnPk
     */
    ruleType?: string
    /**
     * bizCode 服务端校验用的业务类型入参
     */
    bizCode?: string
    /**
     * sceneCode 服务端校验用的场景码入参
     */
    sceneCode?: string
    /**
     * 店铺相关信息，主要做地址是否可用校验用
     */
    shopInfos?: IShopInfoVO[]
    /**
     *  新建，编辑成功要跳转的页面
     */
    editFinishPage?: EditFinishPage
    /**
     * 未登录展示四级地址
     */
    showCascade?: boolean
    /**
     * 是否更新缓存地址
     */
    isUpdateCacheAddress?: boolean
    /**
     * 配送地址标题隐藏
     */
    head?: boolean
    /**
     * 超配地址分堆子标题文案
     */
    overhangSubtitle?: string
    /**
     * 配送半弹层新建/修改地址回到配送页面不校验超配逻辑，点击重新定位按钮不校验超配能力
     */
    isOverhangVerification?: boolean
    /**
     * 配送半弹层超配子标题强弱提示字段，默认弱提示灰色子，强提示红色字
     */
    overhangSubtitleStrongTips?: boolean
    /**
     * 点击切换地址的时候校验经纬度是否为空以及合理；不合理则弹窗提示。
     */
    verifyLatAndLong?: boolean
    /**
     * 使用原生网络库 1:使用 0：不使用
     */
    network?: string
    /**
     * 封闭场域标识
     */
    svcType?: string
  }

  /**
   * 新增/编辑，链接上允许拼接的参数
   */
  export interface EditUrlParamsType extends ListUrlParamsType {
    confirmText?: string // 底部按钮文案
    containGlobal?: boolean // todo：false 固定值 可移除；未在接入文档暴露
    globalBranchName?: string // todo："京东国际" 固定值 可移除；未在接入文档暴露
    isUAV?: boolean // todo：没用 可移除；未在接入文档暴露
    onResult?: (val1, val2) => void //  todo：小程序里用的 可移除；未在接入文档暴露
    edittype?: string // todo：false 固定值 可移除；未在接入文档暴露
    showSniffClip?: boolean //  todo： 结算传的固定值 可移除；未在接入文档暴露
    from?: string // 渠道。用于限流
    showAddressTag?: 'true' | 'false' | boolean // 是否展示地址标签
    showDefaultSet?: 'true' | 'false' | boolean // 是否展示默认地址楼层
    showDelete?: 'true' | 'false' | boolean // 是否展示删除按钮
    showOverseas?: 'true' | 'false' | boolean // 是否展示海外地址
    showParseAddress?: 'true' | 'false' | boolean // 是否展示智能识别组件
    isSupportVoice?: 'true' | 'false' | boolean // 是否支持语音输入
    type?: string // edit  or  add
    isLoc?: string // 是否本地。收货地址中的手机号将用于接收商品消费码
    areaId?: string // 下划线拼接的四级地址
    topTip?: TopTip // 顶部提示条（非定位引导条）配置信息，支持背景色，文字内容，文字颜色
    mobileTextColor?: string // 手机号颜色
    fromLocationAddress?: 'true' | 'false' | boolean // 是否是定位地址。默认false
    addressBussinessType?: number // 地址业务标识，埋点参数，0-o2o业务，1-其他业务，表示B2C。usePoiAddress为true表示o2o
    defaultAddressMsg?: string // 表单底部的默认地址组件，标题下方文案
    defaultAddressTitle?: string // 表单底部的默认地址组件，标题
    id?: string // 地址ID
    addressInfo?: AddressStandard.Address // 单条地址信息
    idCard?: string // 身份证号
    /**
     * 外部传入的详细地址
     */
    detailAddress?: string
    /** 是否展示定位信息  */
    showLocation?: boolean | string
    /**  接口下发的定位信息 */
    recommendedAddressVO?: AddressStandard.RecommendedAddressVO
    /**  定位分场景授权的场景id */
    sceneId?: string
    /** 列表下发的 * */
    baseId?: string
    /** 刷新地址列表 * */
    refresh?: string
    /** 缓存地址是否默认地址 * */
    isDefault?: 'true' | 'false' | boolean
    /** h5列表跳转编辑页 * */
    jumpEdit?: boolean
    /** 默认地址 * */
    defaultAddressInfo?: AddressStandard.Address
    /**
     * 定位经纬度信息，主要做地址是否可用校验用
     */
    oldLocationInfos?: IOldLocationInfoVo[]
    /**
     * 校验距离入参 单位（米）
     */
    distance?: string
    /**
     * 当前订单所使用地址id，用于校验地址可用性
     */
    currUsedAddressId?: string
    /**
     * 校验距离的方式'bicycling'等
     */
    distanceCheckMode?: string
    /**
     * 编辑的第几条地址
     */
    index?: number
  }

  interface TopTip {
    background?: string
    content?: string
    color?: string
  }

  interface EditFinishPage {
    target?: string // origin表示新建、编辑成功后，回到目标页面，默认回到列表页
  }
}
