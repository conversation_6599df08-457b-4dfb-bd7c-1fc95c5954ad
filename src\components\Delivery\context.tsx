/**
 * @file: context.tsx
 * @description: 配送楼层上下文，用于提供配送楼层上下文
 */
import React, { createContext, useContext, useEffect, useReducer } from 'react'
import { Context, DeliveryWrap } from './types'
import DeliveryLogger from './deliveryLogger'

export const DeliveryContext = createContext({} as Context.DeliveryContextValue)

// 2. 定义 reducer 函数
const deliveryReducer = (state: Context.DeliveryState, action: Context.Action): Context.DeliveryState => {
  switch (action.type) {
    case 'setHasOpen':
      return {
        ...state,
        hasOpen: true,
      }

    case 'setIsOpen':
      return {
        ...state,
        isOpen: !!action.isOpen,
      }
    case 'setWrapData':
      if (!action.wrapInitState) {
        return state
      }
      return {
        ...state,
        wrapInitState: action.wrapInitState,
      }
    default:
      return state
  }
}

// 3. 创建 Context 提供者组件
export const DeliveryProvider = ({ children, initState }: { children: React.ReactNode; initState: DeliveryWrap.InitState }) => {
  // 初始状态
  const initialState = {
    hasOpen: false,
    isOpen: false,
    wrapInitState: initState,
  }

  // 使用 useReducer 初始化状态和获取 dispatch 函数
  const [state, dispatch] = useReducer(deliveryReducer, initialState)

  useEffect(() => {
    // console.log(state.wrapInitState, "state.wrapInitState")
    if (state.wrapInitState) {
      dispatch({ type: 'setWrapData', wrapInitState: initState })
    }
  }, [initState, state.wrapInitState])

  // console.log(state.currentSelectValue?.delivery, "currentSelectValue")

  // 提供给消费者的上下文值
  const value = {
    state: state,
    deliveryLogger: new DeliveryLogger(state.wrapInitState.bundle),
    dispatch,
  }

  // 渲染 Context 提供者
  return (
    <>
      <DeliveryContext.Provider value={value}>{children}</DeliveryContext.Provider>
    </>
  )
}

// 4. 创建自定义钩子，方便组件使用
export const useDelivery = () => {
  return useContext(DeliveryContext)
}
