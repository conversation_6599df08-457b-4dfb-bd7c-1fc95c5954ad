/**
 * @file: deliveryHwjy.tsx
 * @description: 海外配送组件，用于展示海外配送等逻辑
 */
import { useEffect, useRef, useState } from 'react'
import SelectBody, { HeaderState } from '../../selector/SelectBody.tsx'
import Botton from '../../../ui/botton.tsx'
import { DeliveryHWJyDataIniter } from '../../initDeliveryData.ts'
import { ShipmentInfo } from '@app/typings/api_getBundleShipmentList_resp.js'
import Hwjy from './hwjy.tsx'
import { HwjySaveData, saveHwjy } from '../../saveDelivery.ts'
import { useMasterData } from '@app/context/masterContext.tsx'
import { useUpdateMasterData } from '@app/context/masterContext.tsx'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring.ts'
import { useFirstRenderLogger } from '../../hooks.ts'
import toast from '@app/common/toast/index.tsx'
export type Props = { deliveryHwjyDataIniter: DeliveryHWJyDataIniter; defaultTsx: () => React.ReactElement; onCancel: () => void }
export default ({ deliveryHwjyDataIniter, onCancel, defaultTsx }: Props) => {
  const [headerList, setHeaderList] = useState(deliveryHwjyDataIniter.getHeaderList())
  const [shipmentInfo, setShipmentInfo] = useState<ShipmentInfo | null>(null)
  const { balanceAddress } = useMasterData()?.body || {}
  const updateMasterData = useUpdateMasterData()
  const [selectValue, setSelectValue] = useState({} as any | null)

  const log = useFirstRenderLogger()

  useEffect(() => {
    setHeaderList(deliveryHwjyDataIniter.getHeaderList())
  }, [deliveryHwjyDataIniter])

  const changeHeader = (headerState: HeaderState<ShipmentInfo, null>) => {
    // console.log(headerState, "headerState.currentTopItem.targetValue");

    if (!headerState?.currentTopItem) return
    setShipmentInfo(headerState.currentTopItem.targetValue)
  }

  const hasUp = useRef(false)

  useEffect(() => {
    if (hasUp.current) return
    if (!shipmentInfo) return
    hasUp.current = true
    log.shipmentPOPExpo({
      toptabname: headerList.map((item) => item.text),
      second_tab_name: [],
      date: [],
      shipmentTitle: [],
      transportation_expenses: [],
      button_name: ['0', '1'],
    })
  }, [shipmentInfo, log, headerList])

  if (headerList.length === 0) {
    return defaultTsx()
  }

  const save = () => {
    // console.log(selectValue,shipmentInfo, "selectValue");
    if (!selectValue || !shipmentInfo) {
      onCancel()
      return
    }
    const saveData: HwjySaveData<any> = {
      baseData: deliveryHwjyDataIniter.initState,
      addrId: balanceAddress?.id.toString() || '',
      currentValue: selectValue,
      shipmentInfo: shipmentInfo,
    }
    saveHwjy(saveData)
      ?.then(() => {
        updateMasterData()
        onCancel()
      })
      .catch((e) => {
        toast.error('保存失败，请稍后再试')
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.Delivery,
          msg: {
            type: '配送模块',
            error: e,
            data: saveData,
            info: '保存失败',
          },
        })
        onCancel()
      })
  }

  return (
    <SelectBody
      headerList={headerList}
      onChange={(s) => {
        changeHeader(s)
      }}
      isDelivery={true}
    >
      {shipmentInfo && (
        <Hwjy
          overseasPickInfo={shipmentInfo?.overseasPickInfo}
          // shipmentType={deliveryHwjyDataIniter.initState.bundle.overseasShipmentTypeVO.promiseType}
          bundleId={deliveryHwjyDataIniter.initState.bundle.bundleId}
          onChange={(s) => {
            setSelectValue(s)
          }}
          transportCode={shipmentInfo.shipmentType}
          tips={deliveryHwjyDataIniter.getData(shipmentInfo)}
        />
      )}

      {
        <Botton
          onSure={() => {
            save()
            log.shipmentlayer('1', '', '')
            log.shipmentPOPClick({ button_name: '1' })
          }}
          onCancel={() => {
            onCancel()
            log.shipmentlayer('0', '', '')
            log.shipmentPOPClick({ button_name: '0' })
          }}
        />
      }
    </SelectBody>
  )
}
