import { currentInvoiceAtom, popInvoiceAtom, invoiceEditVoAtom, selfInvoiceAtom } from '@app/components/Invoice/atom/invoiceAtom'
import showToast from '@app/components/payment/components/toast'
import { saveInvoice } from '@app/services/api'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import { useAtomValue } from 'jotai'
import { useUpdateMasterData } from '@app/context/masterContext'
import { invoiceDataService } from '../services/invoice.data.service'
import { VenderType } from '@app/typings/invoice.d'
import { InvoiceAddPointParams, InvoiceLogger } from '../InvoiceLogger'
/**
 * 发票保存逻辑Hook
 */
export const useInvoiceSave = () => {
  const currentInvoice = useAtomValue(currentInvoiceAtom)
  const selfInvoice = useAtomValue(selfInvoiceAtom)
  const popInvoice = useAtomValue(popInvoiceAtom)
  const invoiceEditVo = useAtomValue(invoiceEditVoAtom)
  const updateMasterData = useUpdateMasterData()
  /**
   * 保存发票信息
   * @param saveInvoiceStatus 保存发票的状态，0-自营 1- pop 2- (pop + 自营)混单
   **/
  const handleSave = async (saveInvoiceStatus: number) => {
    try {
      // 根据当前选择的标签页确定是处理自营还是POP商品发票
      let invoiceParams
      /**
       * 自营商品tab切换到其他商品tab，客户端保存自营商品tab下的发票信息；
       */
      if (saveInvoiceStatus === VenderType.SELF) {
        // console.log('自营商品发票')
        // 自营商品发票
        invoiceParams = invoiceDataService.createSaveInvoiceParams(currentInvoice, null, invoiceEditVo)
      } else if (saveInvoiceStatus === VenderType.POP) {
        // console.log('POP商品发票')
        // POP商品发票
        invoiceParams = invoiceDataService.createSaveInvoiceParams(null, currentInvoice, invoiceEditVo)
      } else {
        // console.log('混合状态 - 自营+POP，当用户有POP商品且点击的是自营商品标签时')
        // 混合状态 - 自营+POP，当用户有POP商品且点击的是自营商品标签时
        invoiceParams = invoiceDataService.createSaveInvoiceParams(selfInvoice, popInvoice, invoiceEditVo)
      }

      // 添加发票保存埋点
      InvoiceLogger.invoiceEvent('invoiceLayer', {
        clickPos: '1',
        invoice_detail:
          'selectInvoiceTitle:' +
          invoiceParams?.selectInvoiceTitle?.toString() +
          '_' +
          'selectedInvoiceType:' +
          invoiceParams?.selectedInvoiceType?.toString() +
          '_' +
          'saveParams:' +
          JSON.stringify(invoiceParams),
      } as InvoiceAddPointParams)

      // 返回 API 调用的 Promise
      return saveInvoice(invoiceParams).then((res: any) => {
        if (res && res.code === '0') {
          updateMasterData()
          if (res.body.saveDefaultInvoice) {
            showToast({ title: '默认发票信息修改成功' }, { wrapperStyle: { zIndex: 9999 }, position: 'center-center' })
          }
          return res // 返回成功结果
        } else {
          // 显示更详细的错误信息
          const errorMsg = res?.message || '保存失败'
          const errorCode = res?.code
          monitoring({
            name: monitorName.Settlement,
            code: monitorCode.Invoice,
            msg: {
              type: '发票',
              action: '发票保存',
              error: errorMsg,
              code: errorCode,
              traceId: res?.traceId,
              requestId: res?.requestId,
            },
          })
          // 抛出业务错误
          throw new Error(errorMsg)
        }
      })
    } catch (error: any) {
      // 捕获并显示处理过程中的错误
      const errorMsg = error?.message || '未知错误'
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.Invoice,
        msg: {
          type: '发票',
          action: '发票保存',
          error: '发票组件保存异常:' + errorMsg,
        },
      })
      throw error
    }
  }

  return {
    handleSave,
  }
}

export default useInvoiceSave
