import { ABTestsMAP } from '../utils/abTest'
import { version } from '../../../../package.json'
export const API_APP_ID = window?.commonAppId || 'pctrade-core'
import { GATEWAY } from '@app/services/const'

interface ENV {
  appId?: string
  version?: string
  baseURL: string
  channel?: string
  appid?: string
  token?: string
  tenantCode?: string
  bizModeClientType?: string
  bizModelCode?: string
  loginType?: string | number
  externalLoginType?: string
  suffix?: string
  ump: number
  request: {
    publicKey: string
    publicKeyId: string
    apis: Array<{
      functionId: string
      preload?: {
        path: string
        transform?: (params: Record<string, string>) => Record<string, unknown>
      }
      crypto?: {
        input?: Array<string>
        output?: Array<string>
      }
      umps?: number
    }>
  }
}

const launchOptions = (() => {
  try {
    return {
      query: window.location.search
        .substring(1)
        .split('&')
        .reduce((pre, curr) => {
          const [key, value] = curr.split('=')
          return { ...pre, [key]: value }
        }, {}) as Record<string, string>,
    }
  } catch (e) {
    console.error(e)
  }
})()

const appEnv = launchOptions?.query?.env || launchOptions?.query?.app_env || import.meta?.env?.APP_ENV

const env: ENV = {
  appId: 'wxae3e8056daea8727',
  version,
  baseURL: (() => {
    return GATEWAY
    // if (process.env.NODE_ENV === 'development') {
    // return 'https://beta-api.m.jd.com'
    // }
    // return 'https://api.m.jd.com'
  })(),
  channel: appEnv?.split('.')[1],
  appid: {
    h5: 'm_core',
    pc: API_APP_ID || 'item-v3',
  }['pc'],
  token: {
    pc: 'yhDAHweDVCaZsYh+sU9/WwUgZI9wr8d1',
  }['pc'],
  tenantCode: {
    h5: 'jgm',
  }[import.meta.env.TARO_ENV],
  bizModeClientType: {
    h5: 'M',
  }[import.meta.env.TARO_ENV],
  bizModelCode: '3',
  loginType: {
    h5: 2,
    pc: 3,
  }['pc'],
  externalLoginType: {
    pc: '1',
  }['pc'],
  suffix: import.meta.env.TARO_ENV === 'h5' ? '_m' : undefined,
  ump: 3141,
  request: {
    publicKey:
      'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDm4BrWcEEb6Hjx7YhffrMEwijwatH65yB7N6uoiAr5YvNp0q5vvMzbaE2xvOThWEptP2NrTBjpQMkSrBQXtIrvKTAr2lOcqL0VEacUtm2e+NSa+5Tu9SpJo+yWyBfORb7BbK7YNa0Km1oJZYncs7ihGi5d40wqy/4Adu622WJBfQIDAQAB',
    publicKeyId: '1',
    apis: [
      {
        functionId: 'pc_address_cmpnt_queryAddress',
        preload: {
          path: 'list',
          transform: (params: AddressStandard.ListUrlParamsType) => {
            console.log(params, '预加载')
            return {
              jdCombineSign: !!params.shopIdsArr, // ** 到家商品传入，返回带有范围标识（结算场景使用）;和shopIdsArr同时存在
              shopIdsArr: params.shopIdsArr, //  ** Array<string>;门店ID集合（结算场景使用）；和jdCombineSign同时存在
              jdStoreSign: params.jdStoreSign, // @@ boolean;门祥到家融合 （该字段true 时，必须venderId、storeId）。【jdStoreSign,venderId,storeId,storeType四个一起】
              venderId: params.venderId, // @@ 店铺id 京东自营传JD（门详场景使用）。【jdStoreSign,venderId,storeId,storeType四个一起】
              storeId: params.storeId, // @@ 门店id（门详场景使用）。【jdStoreSign,venderId,storeId,storeType四个一起】
              storeType: params.storeType, // @@ number ;门店类型（门详场景使用）:门店来源 1. 达达 2. 青龙 3:伽利略 4:门店帮 6:医药（泰国传0） 8:前置仓门店 9:重货仓。【jdStoreSign,venderId,storeId,storeType四个一起】
              addressId: params.id, // ?: string;地址 传入地址,当前选中地址
              token: params.token, // 必须入参否则提示无权限访问 地址组件授权；地址组件接入规范：https://cf.jd.com/pages/viewpage.action?pageId=1002739550
              tenantCode: params.from, // 必须入参否则提示无权限访问 地址组件授权；地址组件接入规范：https://cf.jd.com/pages/viewpage.action?pageId=1002739550
              addressClients: params.addressClients, // 客户端传入地址集合Array<AddressClientInfoVO>;
              isInitialize: params.isInitialize, // 对于APP结算特殊场景字段，是否做初始化，"0":不初始化  ”1“初始化;
              addressBalanceAppVoList: params.addressBalanceAppVoList, // APP结算特殊场景字段 ?: Array<AddressBalanceAppVo>;
              homingWord: params.homingWord || 'seleted', // 归堆排序字段 枚举值：edit,delete,seleted,readonly;默认按照线上的是否选中排序
              usePoiAddress: params.usePoiAddress, // 当前是否要用POI地址逻辑
              hasO2OTag: params.hasO2OTag, // 是否包含 到家 || 天选 || 前置仓 业务
              abTests: [ABTestsMAP.ab_cacheCallbackSendMsg_v1.experiment].join(','), // ab实验入参
            }
          },
        },
        crypto: {
          input: ['latitudeString', 'longitudeString'],
          output: [
            'addressDetail',
            'latitudeString',
            'longitudeString',
            'name',
            'realMobile',
            'email',
            'idCard',
            'phone',
            'provinceName',
            'cityName',
            'countyName',
            'townName',
            'fullAddress',
            'shortAddress',
            'houseNumber',
            'where',
          ],
        },
        umps: 9,
      },
      {
        functionId: 'pc_address_cmpnt_addAddressPage', // 表单规则接口
        preload: {
          path: 'edit',
          transform: (params: AddressStandard.EditUrlParamsType) => {
            console.log(params, '表单规则预加载')
            return {
              action: params.type === 'add' ? '1' : '2',
              usePoiAddress: params.usePoiAddress,
              abTests: [ABTestsMAP.ab_cacheCallbackSendMsg_v1.experiment].join(','), // ab实验入参
            }
          },
        },
        crypto: {
          output: [
            // 指定前缀，是由于接口返回数据中，有多个对象包含以下字段名（意义不同）
            'addressLocateDetailVO.provinceName', // 根据IP推荐的四级地址
            'addressLocateDetailVO.cityName',
            'addressLocateDetailVO.countyName',
            'addressLocateDetailVO.townName',
          ],
        },
        umps: 2,
      },
      {
        functionId: 'address_cmpnt_querySingleAddress', // 根据单条id查询地址信息
        crypto: {
          output: [
            'addressDetail',
            'latitudeString',
            'longitudeString',
            'name',
            'realMobile',
            'mobile',
            'email',
            'idCard',
            'phone',
            'provinceName',
            'cityName',
            'countyName',
            'townName',
            'fullAddress',
            'shortAddress',
            'houseNumber',
            'where',
          ],
        },
        umps: 10,
      },
      {
        functionId: 'address_cmpnt_getRecommendAddress', // 推荐定位信息
        crypto: {
          input: ['latitudeString', 'longitudeString'],
          output: [
            // 指定前缀，是由于接口返回数据中，有多个对象包含以下字段名（意义不同）
            'address',
            'addressDetail',
            'latitudeString',
            'longitudeString',
            'provinceName',
            'cityName',
            'countyName',
            'townName',
            'title',
            'titleShow',
          ],
        },
        umps: 19,
      },
      {
        functionId: 'address_cmpnt_recommendAddressInfo', // 历史收货人推荐列表
        preload: {
          path: 'edit',
          transform: (params: AddressStandard.ListUrlParamsType) => {
            return {
              token: params.token, // 必须入参否则提示无权限访问 地址组件授权；地址组件接入规范：https://cf.jd.com/pages/viewpage.action?pageId=1002739550
              tenantCode: params.from, // 必须入参否则提示无权限访问 地址组件授权；地址组件接入规范：https://cf.jd.com/pages/viewpage.action?pageId=1002739550
            }
          },
        },
        crypto: {
          output: ['mobile', 'name'],
        },
        umps: 15,
      },
      {
        functionId: 'pc_address_cmpnt_getIntelligentText', // 智能识别接口
        crypto: {
          input: ['text'],
          output: [
            'provinceName',
            'cityName',
            'countyName',
            'townName',
            'addressDetail',
            'name',
            'realMobile',
            'mobile',
            'latitudeString',
            'longitudeString',
            'shortAddress',
            'houseNumber',
            'poiAddress',
          ],
        },
        umps: 7,
      },
      {
        functionId: 'pc_address_cmpnt_getAreaCodeList', // 区号列表接口
        preload: {
          path: 'areaCode',
          transform: (params: AddressStandard.ListUrlParamsType) => {
            return {
              token: params.token, // 必须入参否则提示无权限访问 地址组件授权；地址组件接入规范：https://cf.jd.com/pages/viewpage.action?pageId=1002739550
              tenantCode: params.from, // 必须入参否则提示无权限访问 地址组件授权；地址组件接入规范：https://cf.jd.com/pages/viewpage.action?pageId=1002739550
            }
          },
        },
        umps: 5,
      },
      {
        functionId: 'pc_address_cmpnt_addAddress', // 新增地址接口
        crypto: {
          // 需要加密的入参
          input: [
            'name',
            'addressDetail',
            'fullAddress',
            'mobile',
            'phone',
            'email',
            'idCard',
            'provinceName',
            'cityName',
            'countyName',
            'townName',
            'shortAddress',
            'houseNumber',
            'latitudeString',
            'longitudeString',
            'originProvinceName' /* 原始地址的省份名称 */,
            'originCityName' /* 原始地址的城市名称 */,
            'originCountyName' /* 原始地址的县/区名称 */,
            'originTownName' /* 原始地址的镇/街道名称 */,
            'originLat' /* 原始地址的纬度 */,
            'originLng' /* 原始地址的经度 */,
            'originAddress' /* 原始完整地址字符串 */,
            'matchProvinceName' /* 匹配后的省份名称 */,
            'matchCityName' /* 匹配后的城市名称 */,
            'matchCountyName' /* 匹配后的县/区名称 */,
            'matchTownName' /* 匹配后的镇/街道名称 */,
            'matchAddress' /* 匹配后的完整地址字符串 */,
            'matchLat' /* 匹配后地址的纬度 */,
            'matchLng' /* 匹配后地址的经度 */,
            'userProvinceName' /* 用户报告的省份名称 */,
            'userCityName' /* 用户报告的城市名称 */,
            'userCountyName' /* 用户报告的县/区名称 */,
            'userTownName' /* 用户报告的镇/街道名称 */,
            'userAddress' /* 用户报告的完整地址字符串 */,
            'userLat' /* 用户报告地址的纬度 */,
            'userLng' /* 用户报告地址的经度 */,
          ],
          output: [
            'addressDetail',
            'latitudeString',
            'longitudeString',
            'name',
            'realMobile',
            'mobile',
            'email',
            'idCard',
            'phone',
            'provinceName',
            'cityName',
            'countyName',
            'townName',
            'fullAddress',
            'shortAddress',
            'houseNumber',
            'where',
          ],
        },
        umps: 1,
      },
      {
        functionId: 'pc_address_cmpnt_updateAddress', // 编辑地址接口
        crypto: {
          // 需要加密的入参
          input: [
            'name',
            'addressDetail',
            'fullAddress',
            'mobile',
            'phone',
            'email',
            'idCard',
            'provinceName',
            'cityName',
            'countyName',
            'townName',
            'shortAddress',
            'houseNumber',
            'latitudeString',
            'longitudeString',
            'originProvinceName' /* 原始地址的省份名称 */,
            'originCityName' /* 原始地址的城市名称 */,
            'originCountyName' /* 原始地址的县/区名称 */,
            'originTownName' /* 原始地址的镇/街道名称 */,
            'originLat' /* 原始地址的纬度 */,
            'originLng' /* 原始地址的经度 */,
            'originAddress' /* 原始完整地址字符串 */,
            'matchProvinceName' /* 匹配后的省份名称 */,
            'matchCityName' /* 匹配后的城市名称 */,
            'matchCountyName' /* 匹配后的县/区名称 */,
            'matchTownName' /* 匹配后的镇/街道名称 */,
            'matchAddress' /* 匹配后的完整地址字符串 */,
            'matchLat' /* 匹配后地址的纬度 */,
            'matchLng' /* 匹配后地址的经度 */,
            'userProvinceName' /* 用户报告的省份名称 */,
            'userCityName' /* 用户报告的城市名称 */,
            'userCountyName' /* 用户报告的县/区名称 */,
            'userTownName' /* 用户报告的镇/街道名称 */,
            'userAddress' /* 用户报告的完整地址字符串 */,
            'userLat' /* 用户报告地址的纬度 */,
            'userLng' /* 用户报告地址的经度 */,
          ],
          output: [
            'addressDetail',
            'latitudeString',
            'longitudeString',
            'name',
            'realMobile',
            'mobile',
            'email',
            'idCard',
            'phone',
            'provinceName',
            'cityName',
            'countyName',
            'townName',
            'fullAddress',
            'shortAddress',
            'houseNumber',
            'where',
          ],
        },
        umps: 13,
      },
      {
        functionId: 'address_cmpnt_queryNearbyAddressesList', // 附近地址列表
        crypto: {
          // 需要加密的入参
          input: ['latitudeString', 'longitudeString'],
          output: ['latitudeString', 'longitudeString'],
        },
        umps: 16,
      },
      {
        functionId: 'address_cmpnt_queryCurrentAddress', // 获取当前定位
        crypto: {
          // 需要加密的入参
          input: ['latitudeString', 'longitudeString'],
          output: ['latitudeString', 'longitudeString'],
        },
        umps: 17,
      },
      {
        functionId: 'pc_address_cmpnt_addressSearch', // 推荐地址列表
        crypto: {
          // 需要加密的入参
          input: ['region', 'keyword', 'latitudeString', 'longitudeString'],
        },
        umps: 11,
      },
      {
        functionId: 'address_cmpnt_queryAddressIdInfo', // 推荐地址列表
        crypto: {
          // 需要加密的入参
          input: ['provinceName', 'cityName', 'countyName', 'addressDetail', 'shortAddress', 'longitudeString', 'latitudeString'],
          output: ['latitudeString', 'longitudeString'],
        },
        umps: 18,
      },
    ],
  },
}

export default env

if (process.env.NODE_ENV === 'development') {
  document.cookie = `pt_pin=jd_43e58bda65fa4`
  document.cookie = `pt_key=AAJllPinADC3PQicVbMClzbY9retrI_nNj6R7Oc2sa35pTiAPehkdBzKOphDtL6CNo51G4QWijs`
}
