/**
 * 虚拟组套价格
 */
import scope from '../index.module.scss'
import { BalanceSkuType } from '@app/typings/master_api_response'

const SuitPrice = ({ sku }: { sku: BalanceSkuType }) => {
  return (
    <div className={`${scope.right} ${scope.suit}`}>
      {/* 购买数量 | 套装单位 */}
      <div className={scope.quantity}>
        &times;{sku.buyNum}
        {sku.unit || ''}
      </div>
    </div>
  )
}

export default SuitPrice
