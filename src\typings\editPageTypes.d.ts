/**
 * 新增/编辑页 保存接口入参（表单存储的数据）
 */
declare namespace AddressStandard {
  /**
   * 保存地址接口入参
   */
  export interface EditFillInType {
    /** 姓名 */
    name?: string
    /** 备用电话 */
    phone?: string
    /** 详细地址 */
    addressDetail?: string
    /** 手机号，带星号 */
    mobile?: string
    /** 手机号 */
    realMobile?: string
    /**
     * ？
     */
    coordType?: number
    /**  0_0_0_0 */
    idList?: number[]
    /** 身份证号 */
    idCard?: string
    /** 邮箱 */
    email?: string
    /** 邮编 */
    postCode?: string
    /** 区号 */
    areaCode?: string
    nameCode?: string
    /** 省 */
    provinceId?: number
    /** 市 */
    cityId?: number
    /** 县 */
    countyId?: number
    /** 镇 */
    townId?: number
    provinceName?: string
    cityName?: string
    countyName?: string
    townName?: string
    retTag?: number
    tagSource?: number
    /** 标签名称 */
    userDefinedTag?: string
    /** 是否是默认地址 */
    defaultAddress?: boolean
    /** 地址中文名字 eg:北京朝阳区三环以内 */
    areaNameList?: Array<string>
    /** 是否是完整地址 */
    complete?: boolean
    /** 是否显示补充省市区县 */
    showAddrErrTip?: boolean
    /** 智能解析地址 */
    text?: string
    /** 地址id */
    addressId?: number
    /** 是否加密传输（true 邮箱 名字 地址详情 地址全称 加密） */
    supportEncode?: boolean
    longitude?: number
    latitude?: number
    /**
     * 加密后的经纬度
     */
    latitudeString?: string | boolean
    /**
     * 加密后的经纬度
     */
    longitudeString?: string | boolean
    tagName?: string
    /**
     * 短地址（加密）
     */
    shortAddress?: string
    /**
     * 门牌号（加密）
     */
    houseNumber?: string
    /**
     * 当前地址是否为poi地址
     */
    usePoi?: boolean
    /** 地址是否改变 */
    hasAddrChanged?: boolean
    /**
     * 当前编辑地址是否是选中的
     */
    selected?: boolean

    /**
     * 四级地址暂不选择时，需要回传这些数据
     */
    addressFeedbackVO?: AddressFeedbackVO
    // 地址错误类型 11为无错误
    addressErrorType?: string
    // 地址错误信息
    addressErrorMessage?: string
    // 地址错误详细信息
    addressErrorDetailMessage?: string
    // 纠偏类型：1：一键更正；2：一键精简 3：详细地址 4：门牌号
    addressUpdateType?: string
    // 纠偏提示
    addressTipsVo?: {
      type: string
      text: string
    }
    addressStandardVOList?: AddressStandard.Address[]
    // 控制是否展示编辑地址错误类型文案
    showAddressError?: boolean
    // 如果地址是完整的，四级地址不为0，隐藏级别设置为4; 否则，隐藏级别设置为3
    hideLevel?: string
    // 以下是推荐poi参数 -- start
    // 点选的poi id
    poiId?: string
    // 点选的poi类型
    category?: string
    // 国标区划编码
    adCode?: string
    region?: string
    keyword?: string
    // 是否推荐poi地址，首次传true，返回推荐poi后传入false
    isRecommendPoi?: boolean
    poiAddress?: string
    scene?: string
    // 以下是推荐poi参数 -- end
  }

  export interface AddressFeedbackVO {
    /* 原始地址信息 - 用户最初提供或系统采集的地址 */
    /* 原始完整地址字符串 */
    originAddress?: string
    /* 原始地址的省份编码 */
    originProvinceCode?: number
    /* 原始地址的省份名称 */
    originProvinceName?: string
    /* 原始地址的城市编码 */
    originCityCode?: number
    /* 原始地址的城市名称 */
    originCityName?: string
    /* 原始地址的县/区编码 */
    originCountyCode?: number
    /* 原始地址的县/区名称 */
    originCountyName?: string
    /* 原始地址的镇/街道编码 */
    originTownCode?: number
    /* 原始地址的镇/街道名称 */
    originTownName?: string
    /* 原始地址的纬度 */
    originLat?: string | number
    /* 原始地址的经度 */
    originLng?: string | number

    /* 匹配地址信息 - 系统处理后与原始地址匹配的结果 */
    /* 匹配后的省份编码 */
    matchProvinceCode?: number
    /* 匹配后的省份名称 */
    matchProvinceName?: string
    /* 匹配后的城市编码 */
    matchCityCode?: number
    /* 匹配后的城市名称 */
    matchCityName?: string
    /* 匹配后的县/区编码 */
    matchCountyCode?: number
    /* 匹配后的县/区名称 */
    matchCountyName?: string
    /* 匹配后的镇/街道编码 */
    matchTownCode?: number
    /* 匹配后的镇/街道名称 */
    matchTownName?: string
    /* 匹配后的完整地址字符串 */
    matchAddress?: string
    /* 匹配后地址的纬度 */
    matchLat?: string | number
    /* 匹配后地址的经度 */
    matchLng?: string | number

    /* 用户自报地址信息 - 用户主动提供的当前地址信息 */
    /* 用户报告的省份编码 */
    userProvinceCode?: number
    /* 用户报告的省份名称 */
    userProvinceName?: string
    /* 用户报告的城市编码 */
    userCityCode?: number
    /* 用户报告的城市名称 */
    userCityName?: string
    /* 用户报告的县/区编码 */
    userCountyCode?: number
    /* 用户报告的县/区名称 */
    userCountyName?: string
    /* 用户报告的镇/街道编码 */
    userTownCode?: number
    /* 用户报告的镇/街道名称 */
    userTownName?: string
    /* 用户报告的完整地址字符串 */
    userAddress?: string
    /* 用户报告地址的纬度 */
    userLat?: string | number
    /* 用户报告地址的经度 */
    userLng?: string | number
    /**
     * 用户的操作行为 1：接收 2：拒绝(修改地址)
     * 处理状态或方式 - 可能表示地址验证或处理的具体状态
     */
    treatment?: number
    /**
     * 匹配操作的基准ID，可能用于关联特定的匹配规则或过程
     * 列表携带过来的
     */
    baseId?: string
  }

  /**
   * 调用保存接口：四级地址不全，接口根据详细地址返回数据
   */
  export interface AddressCheckResultVo {
    /**
     * 区ID：51081
     */
    countyId?: number
    /**
     * 区名称:"北京经济技术开发区"
     */
    countyName?: string
    /**
     * 市/县。区：2810
     */
    cityId?: number
    /**
     * 市/县。区名称:"大兴区"
     */
    cityName?: string
    provinceId?: number
    provinceName?: string
    noSelectedTipAddressMessage?: string
    /**
     * 镇：51081
     */
    townId?: number
    /**
     * 镇名称：
     */
    townName?: string
    /**
     * 弹窗左侧按钮文案："否"
     */
    selectLeftMsg?: string
    /**
     * 弹窗右侧按钮文案："是"
     */
    selectRightMsg?: string
    /**
     * 弹窗内容："根据国家行政区域划分，识别到你的地址为北京经济技术开发区，为了更快配送，是否帮你更换？"
     */
    titleMsg: string
    /**
     * 详细地址（可能没有）
     */
    addressDetail?: string
    /**
     * :1
     */
    type: number
    /**
     * 加密后的经纬度
     */
    latitudeString?: string
    /**
     * 加密后的经纬度
     */
    longitudeString?: string
    /**
     * xx级嵌套-埋点所需
     */
    conflictLevel?: number | string
    /**
     * type上报的埋点值
     */
    reportCode?: string
    /**
     * 推荐精细化poi地址
     */
    shortAddress?: string
    /**
     * 推荐精细化poi门牌号
     */
    houseNumber?: string
  }

  /**
   * 接口下发的定位信息
   */
  export interface RecommendedAddressVO {
    /**
     * 地址
     */
    address: string
    /**
     * 详细地址
     */
    addressDetail: string
    /**
     * 四级地址ID
     */
    areaId: number
    /**
     * 四级地址马驹桥镇
     */
    areaName: string
    /**
     * 按钮文案
     */
    buttonMsg: string
    /**
     * 区ID
     */
    cityId: number
    /**
     * 区名称
     */
    cityName: string
    /** 县区域id */
    countyId: number
    /** 县区域名称 */
    countyName: string
    /** 乡镇级地址id */
    townId: number
    /** 乡镇级地址名称 */
    townName: string
    /**
     * ？
     */
    coordType: number
    /**
     * 是否海外
     */
    foreignOverSea: boolean
    /**
     * ？
     */
    fourthAddressConvertFail: boolean
    /**
     * 是否港澳台
     */
    gangAoTai: boolean
    /**
     * 经纬度
     */
    latitudeString: string
    /**
     * 经纬度
     */
    longitudeString: string
    /**
     * 文案前的文案，比如："当前定位："
     */
    prefixMsg: string
    /**
     * 省ID
     */
    provinceId: number
    /**
     * 省名称
     */
    provinceName: string
    title: string
    /**
     * 隐藏地址层级
     */
    hideLevel?: string
    /**
     * 区号
     */
    areaCode?: string
  }

  export interface SupportAddressSelectReqVO {
    longitude: number // 经度
    latitude: number // 纬度
    provinceId: number // 省份id
    cityId: number // 市ID
    countyId?: number // 区域id，地址信息中有就需要传
    townId?: number // 镇级地址id，地址信息中有就需要传
    ruleType?: string // 标识判断规则类型，固定值
    supplyRuleType?: string // 根据from来源，校验供给
  }

  export interface SupportAddressSelectVO {
    supportAddressSelect: number // 1 支持 2 不支持 0 异常
    message: string // 不成功时返回对应提示文案
  }

  // 保存或者更新时，提交的店铺信息对象，用于校验门店范围是否合规
  export interface IShopInfoVO {
    shopId: string
    shopType?: string
    venderId?: string
  }

  // 保存或者更新时，提交的经纬度信息对象，用于校验距离是否合规
  export interface IOldLocationInfoVo {
    lat: string
    lng: string
  }

  // 保存或者更新时，服务端下发的弹窗信息对象中的按钮对象
  export interface ISaveResButtonType {
    actionType: string
    text: string
  }

  // 保存或者更新时，服务端下发的弹窗信息对象
  export interface IFrameTips {
    resultType: string
    content: string
    data: {
      [propName: string]: any
    }
    button: ISaveResButtonType
    leftButton: ISaveResButtonType
    rightButton: ISaveResButtonType
    track: {
      type: number

      [propName: string]: any
    }
  }
  export interface DistanceCoordinate {
    lat: number
    lng: number
  }
  export interface GetDistanceReqVO {
    from: DistanceCoordinate
    to: DistanceCoordinate
    mode: string
  }
  export interface GetDistanceVO {
    distance: number
    distanceConfig: number
    text: string
  }
}
