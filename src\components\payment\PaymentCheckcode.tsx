import React, { useState, useMemo, useRef, useImperativeHandle } from 'react'

type PaymentCheckcodeProps = {
  showCheckCode?: boolean
  encryptClientInfo?: string
}

export type PaymentCheckcodeRef = {
  focus: VoidFunction
  blur: VoidFunction
  readonly value: {
    rid: string | null
    checkcode: string
  }
}

const PaymentCheckcode = React.forwardRef<PaymentCheckcodeRef, PaymentCheckcodeProps>((props, ref) => {
  const { showCheckCode, encryptClientInfo } = props
  const inputRef = useRef<HTMLInputElement>(null)
  const ridRef = useRef<{ rid?: string }>({})
  const [, forceUpdate] = useState({})

  useImperativeHandle(ref, () => {
    return {
      focus() {
        inputRef.current?.focus()
      },
      blur() {
        inputRef.current?.blur()
      },
      get value() {
        return {
          rid: ridRef.current?.rid || '',
          checkcode: inputRef.current?.value || '',
        }
      },
    }
  }, [])

  const handClick = useMemo(
    () => () => {
      forceUpdate({})
    },
    [],
  )

  if (!showCheckCode || !encryptClientInfo) {
    return null
  }

  const ALT_TEXT = '点击更换验证码'
  const rid = (ridRef.current.rid = getRid())
  const url = getImgUrl(rid, encryptClientInfo)

  return (
    <div className="payment-summary-checkcode">
      <div className="payment-summary-checkcode__title">验证码</div>
      <div className="payment-summary-checkcode__content">
        <img src={url} alt={ALT_TEXT} title={ALT_TEXT} onClick={handClick} />
        <input type="text" className="otp-input" ref={inputRef} />
      </div>
    </div>
  )
})

PaymentCheckcode.displayName = 'PaymentCheckcode'

function getRid() {
  return [Math.random().toString(), Math.random().toString()].join('_')
}

function getImgUrl(rid: string, encryptClientInfo: string) {
  return `//captcha.jd.com/verify/image?acid=${rid}&srcid=trackWeb&is=${encryptClientInfo}`
}

export default PaymentCheckcode
