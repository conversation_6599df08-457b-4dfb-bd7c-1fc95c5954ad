/*
 * @Author: ext.xuchao26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息标签和默认操作
 */
import React from 'react'
import { Component } from '@app/common/legao/libs'
import { Input, Button, Checkbox } from '@app/common/legao'
import './style.scss'
import { reportClick } from '@app/utils/event_tracking'
import { readCookie } from '../utils'

export default class JDTag extends Component {
  constructor(props) {
    super(props)
    this.saveTagInput = React.createRef()
    const { retTag, defaultAddress, tagSource, userDefinedTag } = props.value
    this.state = {
      retTags: [
        {
          label: '家',
          retTag: 2,
          tagSource: 1,
        },
        {
          label: '公司',
          retTag: 3,
          tagSource: 1,
        },
        {
          label: '学校',
          retTag: 1,
          tagSource: 1,
        },
      ],
      userDefinedTags: userDefinedTag ? [{ userDefinedTag, tagSource }] : [],
      inputVisible: false,
      inputValue: userDefinedTag,
      data: {
        defaultAddress: defaultAddress ?? false,
        tagSource,
        userDefinedTag,
        retTag,
      },
    }
    this.onKeyUp = this.onKeyUp.bind(this)
    this.onChange = this.onChange.bind(this)
    this.showInput = this.showInput.bind(this)
    this.handleInputConfirm = this.handleInputConfirm.bind(this)
  }

  onKeyUp(e) {
    if (e.keyCode === 13) {
      this.handleInputConfirm()
    }
  }

  onChange(value) {
    this.setState({ inputValue: value })
  }

  defaultAddressChange = (defaultAddress) => {
    reportClick('address', { skutype: '', clickPos: '2', addr: readCookie('ipLoc-djd') })
    reportClick('addaddress', {
      skutype: '',
      clickPos: defaultAddress ? '0' : '1',
      addr: readCookie('ipLoc-djd'),
      testgroup: '',
    })
    this.setState((prevState) => {
      const data = { ...prevState.data, defaultAddress }
      typeof this.props.onChange === 'function' && this.props.onChange(data)
      return { data }
    })
  }

  onClick = (value) => () => {
    const retTag = this.state.data.retTag === value ? undefined : value
    const tagSource = this.state.data.retTag === value ? undefined : 1
    this.setState((prevState) => {
      const data = { ...prevState.data, retTag, userDefinedTag: undefined, tagSource }
      typeof this.props.onChange === 'function' && this.props.onChange(data)
      return { data, inputVisible: false }
    })
  }

  handleClick =
    ({ tagSource, userDefinedTag }) =>
    () => {
      const newTagSource = this.state.data.tagSource === tagSource ? undefined : tagSource
      const newUserDefinedTag = this.state.data.tagSource === tagSource ? undefined : userDefinedTag
      this.setState((prevState) => {
        const data = { ...prevState.data, tagSource: newTagSource, retTag: undefined, userDefinedTag: newUserDefinedTag }
        typeof this.props.onChange === 'function' && this.props.onChange(data)
        return { data }
      })
    }

  showInput(e) {
    e.stopPropagation()
    this.setState({ inputVisible: true }, () => {
      this.saveTagInput.current?.focus()
    })
  }

  handleInputConfirm() {
    const { inputValue, tagSource } = this.state
    this.setState((prevState) => {
      const data = inputValue ? { ...prevState.data, tagSource: 2, retTag: undefined, userDefinedTag: inputValue } : { ...prevState.data }
      typeof this.props.onChange === 'function' && inputValue && this.props.onChange(data)
      return { data, userDefinedTags: inputValue ? [{ userDefinedTag: inputValue, tagSource: 2 }] : [], inputVisible: false }
    })
  }

  render() {
    const { data, retTags, userDefinedTags, inputVisible, inputValue } = this.state
    const { retTag, tagSource, defaultAddress } = data || {}
    return (
      <div>
        {retTags.map((tag) => {
          return (
            <Button className={this.classNames({ 'is-active': retTag === tag.retTag })} key={tag.retTag} onClick={this.onClick(tag.retTag)}>
              {tag.label}
            </Button>
          )
        })}
        {inputVisible ? (
          <Input
            className="input-new-tag"
            value={inputValue}
            ref={this.saveTagInput}
            size="mini"
            placeholder="请输入5字内标签名"
            maxLength={5}
            append={
              <>
                <i />
                <span onClick={this.handleInputConfirm}>确定</span>
              </>
            }
            onChange={this.onChange}
          />
        ) : userDefinedTags.length ? (
          userDefinedTags.map((m) => (
            <span
              className={this.classNames('button-new-tag', { active: tagSource === m.tagSource })}
              key={m.userDefinedTag}
              onClick={this.handleClick(m)}
            >
              {m.userDefinedTag}
              <i className="edit-icon w-12 h-12 is-inline-block ml-4" onClick={this.showInput} />
            </span>
          ))
        ) : (
          <span className="button-new-tag" onClick={this.showInput}>
            + 自定义
          </span>
        )}
        <Checkbox className="is-flex mt-14" checked={defaultAddress} onChange={this.defaultAddressChange}>
          设为默认收货地址，下单时会优先使用该地址
        </Checkbox>
      </div>
    )
  }
}
