/**
 * @file: useDeliverySave.ts
 * @description: 配送保存相关钩子，用于处理配送保存相关逻辑
 */
import { useCallback, useEffect, useRef } from 'react'
import { DeliveryDataIniter } from '../../../initDeliveryData'
import { PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp'
import { ComShipmentType } from '@app/typings/api_getBundleShipmentList_resp'
import { SaveData, saveDeliveryData, SaveType } from '../../../saveDelivery'
import { useMasterData, useUpdateMasterData } from '@app/context/masterContext'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import toast from '@app/common/toast'

export function useDeliverySave(deliveryDataIniter: DeliveryDataIniter, onCancel: () => void) {
  const { balanceAddress } = useMasterData()?.body || {}
  const updateMasterData = useUpdateMasterData()
  const onCancelRef = useRef(onCancel)
  useEffect(() => {
    onCancelRef.current = onCancel
  }, [onCancel])
  const save = useCallback(
    (selectValue: { type: SaveType; value: any } | null, promise: PromiseListItem | null, shipmentInfo: ComShipmentType | null) => {
      if (!selectValue || !promise || !shipmentInfo) {
        onCancelRef.current()
        return
      }

      const saveData: SaveData<any> = {
        baseData: deliveryDataIniter.initState,
        addrId: balanceAddress?.id.toString() || '',
        currentValue: selectValue.value,
        saveType: selectValue.type,
        promiseItem: promise,
        shipmentTypeItem: shipmentInfo,
        targetPromise: deliveryDataIniter.getTargetPromise(promise) || undefined,
        deliveryBaseData: deliveryDataIniter.getDeliveryInfo(),
      }

      const ret = saveDeliveryData(saveData)
      if (!ret) {
        onCancelRef.current()
        return
      }

      ret
        .then(() => {
          updateMasterData()
          onCancelRef.current()
        })
        .catch((e) => {
          toast.error('保存失败，请稍后再试')
          monitoring({
            name: monitorName.Settlement,
            code: monitorCode.Delivery,
            msg: {
              type: '配送模块',
              error: e,
              data: saveData,
              info: '保存失败',
            },
          })
          onCancelRef.current()
        })
    },
    [deliveryDataIniter, balanceAddress?.id, updateMasterData],
  )

  return { save }
}
