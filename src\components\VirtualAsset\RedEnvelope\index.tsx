/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-26 18:00:57
 * @LastEditTime: 2025-07-10 14:33:55
 * @LastEditors: ext.wangchao120
 * @Description: 红包
 * @FilePath: /pc_settlement/src/components/VirtualAsset/RedEnvelope/index.tsx
 */
import React, { useEffect } from 'react'
import Checkbox from '@app/common/checkbox'
import Tooltip from '@app/common/Tooltip'

import styles from './index.module.scss'
import useMasterData from '@app/hooks/useMasterData'
import type { AvailableRedPacketList, RedPacketResultVo } from '@app/typings/master_api_response'
import type { ChangeEvent } from 'react'
import useUpdateMasterData from '@app/hooks/useUpdateMasterData'
import { api_useRedPacket } from '@app/services/api'
import RightTip from '../components/RightTip'
import showToast from '@app/components/payment'
import { reportClick, reportExpose } from '@app/utils/event_tracking'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import type { BalanceUser } from '@app/typings/master_api_response'
import { CommonConfirmDialogs } from '@app/components/payment/components/confirm_dialog'
interface Props {
  tabKey: string
}
const RedEnvelope: React.FC<Props> = ({ tabKey }) => {
  // 主数据
  const masterData = useMasterData()?.body ?? {}
  const redPacketResultVO = (masterData?.redPacketResultVO as RedPacketResultVo) ?? {}
  const balanceUser = (masterData?.balanceUser as BalanceUser) ?? {}
  const updateMasterData = useUpdateMasterData()
  const availableRedPacketList = redPacketResultVO?.availableRedPacketList ?? []
  const onChange = (e: ChangeEvent<HTMLInputElement>) => {
    // 是否实名
    if (!balanceUser?.realName) {
      CommonConfirmDialogs?.confirmRealName()
      return
    }
    // 未设置密码
    if (!balanceUser?.fundsPwd) {
      CommonConfirmDialogs?.confirmPaymentPassword()
      return
    }
    const params = {
      use: e?.target?.checked,
    }
    api_useRedPacket(params)
      .then((res) => {
        // console.log('res', res)
        if (res?.code == '0') {
          // 更新融合数据
          updateMasterData(undefined, 'virtualAsset')
          // 点击红包上报埋点
          reportClick('virtualasset_hbClick', {
            tgstatus: e?.target?.checked ? '1' : '2', // 1:勾选，2:取消勾选
          })
        } else {
          showToast({ title: res?.message || '请稍后再试' })
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'balance_selectRedPacket_pc',
            error_type_txt: 'balance_selectRedPacket_pc接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
  }

  // 点击什么是红包上报埋点
  const onClick = () => {
    reportClick('virtualasset_hbClick', {
      tgstatus: redPacketResultVO?.selectRedPacket ? '1' : '2', // 1:勾选，2:取消勾选
      hlttext: '什么是红包',
    })
  }
  /** 红包本单抵扣明细 */
  const dropDownCont = (
    <div className={styles.dropDownCont}>
      <div className={styles.title}>本单抵扣明细</div>
      {
        <ul>
          {availableRedPacketList && availableRedPacketList.length > 0 ? (
            availableRedPacketList.map((item: AvailableRedPacketList) => {
              return (
                <li key={item?.activityId}>
                  <div className={styles.redPacket}>
                    <span>
                      {item?.activityName}
                      <em>
                        &yen;
                        {item?.balance}
                      </em>
                    </span>
                    <span>
                      可抵扣
                      <em>
                        &yen;
                        {item?.discountCurrentUsed}
                      </em>
                    </span>
                  </div>
                  <div className={styles.validTime}>
                    有效期: {item?.beginTime} - {item?.endTime}
                  </div>
                </li>
              )
            })
          ) : (
            <div className={styles.noRedPacket}>暂无红包</div>
          )}
        </ul>
      }
    </div>
  ) as React.ReactNode

  // 曝光
  useEffect(() => {
    if (tabKey !== 'redEnvelope') return
    reportExpose('virtualasset_hbEXPO', {
      // second_tab_name: tabsName,
      redbag: redPacketResultVO?.redPacketTotalUsableBalance,
      tatal_balance: redPacketResultVO?.redPacketTotalBalance,
    })
  }, [tabKey])
  return (
    <>
      <div className={styles.redEnvelope}>
        <Checkbox onChange={onChange} checked={redPacketResultVO?.selectRedPacket} />
        <div className={styles.text}>
          使用红包（账户当前余额：&yen;{redPacketResultVO?.redPacketTotalBalance}
          {redPacketResultVO?.selectRedPacket && <span>, 本次可用：&yen;{redPacketResultVO?.redPacketTotalUsableBalance}</span>}）
        </div>
        <Tooltip content={dropDownCont} placement="bottomRight" type="popover" tooltipCls={styles.tooltip}>
          <div className={styles.iconWrap}>
            <span>详情</span>
            <div className="icon-info"></div>
          </div>
        </Tooltip>
        <RightTip type="redPacketLink" className={styles.rightTip} onClick={onClick} />
      </div>
    </>
  )
}
export default RedEnvelope
