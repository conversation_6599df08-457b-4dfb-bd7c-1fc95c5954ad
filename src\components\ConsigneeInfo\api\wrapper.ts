import axios, { AxiosRequestConfig } from 'axios'
import qs from 'query-string'
import RsaEncrypt from '@plato/rsa'

import { crypto, sgm, env, appNetwork } from '../utils'
import cookie from 'js-cookie'
import { bizOperation } from '../constant'
import type { ExtraColorApiParams, ColorApiParams } from '@app/typings/color_api'

// const app = jdHybrid?.jdApp() // 京东app环境
// const appVersion = jdHybrid?.appVersion() // app版本

// import { getAccessInfoByTokenAndFrom } from '~/utils/bizAccessHelper'
// import { bizOperation } from '~/consts'

type Chain<T> = (options: any) => Promise<T>

type Interceptor = <T>(options: any, chain: Chain<T>) => Promise<T>

type AvailableMethods = 'get' | 'post' | 'POST' | 'GET'

interface RequestConfig<D = any> extends AxiosRequestConfig<D> {
  method?: AvailableMethods
  params?: ExtraColorApiParams
  data?: D // color网关的body参数写这里
  __encrypt?: boolean // 是否接口加固
}

const interceptors: Array<Interceptor> = []

export const interceptor = {
  register: (item: Interceptor) => {
    interceptors.push(item)
    return () => {
      const index = interceptors.indexOf(item)
      index !== -1 && interceptors.splice(index, 1)
    }
  },
  destory: (item: Interceptor) => {
    const index = interceptors.indexOf(item)
    index !== -1 && interceptors.splice(index, 1)
  },
}

const cryption = (secret: string, publicKey: string) => {
  return {
    secret: () => {
      return new RsaEncrypt(publicKey).encrypt(secret)
    },
    encrypt: (data: unknown, rules: Array<string>) => {
      // 克隆一份数据新地址存数据
      const cloneValue = JSON.parse(JSON.stringify(data))
      const loop = (path: string, value: object) => {
        if (value instanceof Array) {
          if (rules.includes(path)) {
            value.forEach((val, index) => {
              value[index] = typeof val === 'string' || typeof val === 'number' ? crypto.encrypt(`${val}`, secret) : val
            })
            return
          }
          value.forEach((val) => {
            loop([path, '[]'].filter(Boolean).join(''), val)
          })
        }
        Object.keys(value).forEach((key) => {
          const k = [path, key].filter(Boolean).join('.')
          const val = value[key]
          if (rules.includes(key) && (typeof val === 'string' || typeof val === 'number')) {
            value[key] = crypto.encrypt(`${val}`, secret)
          }
          typeof val === 'object' && loop(k, val)
        })
      }

      data && typeof data === 'object' && loop('', cloneValue)
      return cloneValue
    },
    decrypt: (data: unknown, rules: Array<string>) => {
      const loop = (path: string, value: object) => {
        if (value instanceof Array) {
          if (rules.includes(path)) {
            value.forEach((val, index) => {
              value[index] = typeof val === 'string' && val ? crypto.decrypt(val, secret) : val
            })
            return
          }
          value.forEach((val, index) => {
            loop(`${path}[${index}]`, val) // 传递正确的路径信息
          })
          return
        }

        Object.keys(value).forEach((key) => {
          const k = [path, key].filter(Boolean).join('.')
          const val = value[key]
          // rules.includes(key),在多层嵌套对象中，找到了该值，此时key长于规则名称
          // toFix : 优化此处。rules.includes(k)是为了避免一个数据对象中，有多个字段名符合，所以rules指定入口字段
          if ((rules.includes(key) || rules.includes(k)) && val && typeof val === 'string') {
            value[key] = crypto.decrypt(val, secret)
          }
          typeof val === 'object' && loop(k, val)
        })
      }
      data && typeof data === 'object' && loop('', data)
      return data
    },
  }
}

const getSystemInfo = (() => {
  let data: Record<string, unknown>

  const load = async (cookies: Record<string, string>): Promise<typeof data> => {
    // const { platform, version, system, screenWidth, screenHeight, wifiEnabled, brand, model, language, SDKVersion } =
    //   await Taro.getSystemInfo()
    return {
      client: 'pc',
      // env.version
      clientVersion: '1.0.0',
      xAPIScval2: cookies.appType || 'pc',
    }
  }

  return async (cookies: Record<string, string>) => {
    if (!data) {
      try {
        data = await load(cookies)
      } catch (e) {
        data = {}
        console.error(e)
      }
    }
    return data
  }
})()

const send = (config: Config, code: string, message?: string, timestamp?: number) => {
  const duration = timestamp && Date.now() - timestamp
  ;(config.umps.length === 0 ? [999] : config.umps).forEach((operation) => {
    // report.biz({
    //   operation,
    //   code,
    //   message: config.umps ? message : `${message || ''}-${config.functionId}`,
    //   duration,
    // })
  })
}

function getJsTokenAsync() {
  const { promise, resolve, reject } = Promise.withResolvers<{ jsToken: string; fp: string }>()
  if (window.getJsToken) {
    window.getJsToken(resolve, 200)
  } else {
    reject(new Error('getJsToken is not defined'))
  }
  return promise
}

async function buildSignParams(config: RequestConfig) {
  const { body, appid, functionId, client, clientVersion, t } = config as ColorApiParams
  const params: Partial<ColorApiParams> = { appid, functionId, client, clientVersion, t }
  try {
    if (body) {
      params.body = window.SHA256(body)
    }
    const { h5st } = await window.PSign.sign(params)
    const { jsToken } = await getJsTokenAsync()
    return { 'x-api-eid-token': jsToken, h5st }
  } catch (error) {
    return {}
  }
}

const request = async <T>(options: any) => {
  // const params = router.params()
  const params = {}
  const getAll = async (): Promise<Record<string, string>> => {
    const cookies: Record<string, string> = {}

    document.cookie?.split(';').forEach((item) => {
      const [key, value] = item.split('=')
      cookies[key?.trim()] = value?.trim()
    })

    return cookies
  }
  const cookies = await getAll()

  const openid = cookie.get('open_id')
  const uuid = cookie.get('__jda')?.split('.')[1]
  const tk = cookie.get('jd_eid')

  const info = await getSystemInfo(cookies)

  // 地址组件对齐APP时，接口有替换，均包含_cmpnt_，此时后缀不用追加_m。其它接口functionId维持原有
  const functionId =
    env.suffix && options.data.functionId.indexOf('_cmpnt_') === -1 ? `${options.data.functionId}${env.suffix}` : options.data.functionId

  // const pathName = window.location.pathname;
  // const pageId = pathName.substring(pathName.lastIndexOf('/') + 1);

  // const result = getAccessInfoByTokenAndFrom(params.token, params.from)

  // if (!result) {
  //     report.biz({
  //         operation: bizOperation.operation.BIZ_ACCESS_PARAMS,
  //         code: '-1',
  //         message: `type=biz_access_params_api;token=${params.token};from=${params.from};`,
  //     });
  // }

  let data = {
    appid: env.appid,
    t: Date.now(),
    uuid,
    loginType: env.loginType,
    ...info,
    openudid: openid,
    'x-api-eid-token': tk,
    ...options.data,
    functionId,
    // @ts-ignore
    scval: params?.from, // 渠道编码
    // xAPIScval2: result?.from || 'unknown',
    body: JSON.stringify({
      // @ts-ignore
      deviceUUID: cookies.visitkey || cookies.wid || 'unknown',
      appId: env.appId,
      // @ts-ignore
      tenantCode: params?.from,
      // from: result?.from || 'unknown',
      bizModelCode: env.bizModelCode,
      bizModeClientType: env.bizModeClientType,
      token: env.token,
      externalLoginType: env.externalLoginType,
      // pageId,
      // version: env.version,
      ...options.data?.body,
    }),
  }

  // return new Promise<T>((resolve, reject) => {
  //   $.ajax({
  //     type: 'POST',
  //     timeout: 15000,
  //     xhrFields: {
  //       withCredentials: true,
  //     },
  //     contentType: 'application/x-www-form-urlencoded',
  //     header: {
  //       cookie: Object.keys(cookies)
  //         .map((key) => `${key}=${encodeURIComponent(cookies[key])}`)
  //         .join(';'),
  //     },
  //     success: (response, textStatus, xhr) => {
  //       let requestId = xhr.getResponseHeader('x-api-request-id')
  //       response = { ...response, header: { 'x-api-request-id': requestId } }
  //       if (response?.body === undefined) {
  //         reject({
  //           message: 'empty response body',
  //           response,
  //         })
  //         return
  //       }
  //       if (response?.code !== '0') {
  //         reject({
  //           message: `response fail ${response.code}`,
  //           response,
  //         })
  //         return
  //       }
  //       if (response?.body?.errorCode) {
  //         reject({
  //           message: `response error code:${response.body.errorCode} reason:${response.body.errorReason}`,
  //           response,
  //         })
  //         return
  //       }
  //       resolve(response as T)
  //     },
  //     error: (response) => {
  //       reject(response)
  //     },
  //     ...options,
  //     data: qs.stringify(data, { encode: true }),
  //   })
  // })
  await buildSignParams(data)
    .then((signParams) => {
      data = {
        ...data,
        ...signParams,
      } as RequestConfig
    })
    .catch((error) => {
      // TODO: 前端监控
      console.error('buildSignParams error', error)
    })
  return new Promise<T>((resolve, reject) => {
    axios({
      method: 'post',
      timeout: 15000,
      withCredentials: true,
      headers: {
        ...options.headers,
        'Content-Type': 'application/x-www-form-urlencoded',
        // Cookie: Object.keys(cookies)
        //   .map((key) => `${key}=${encodeURIComponent(cookies[key])}`)
        //   .join('; '),
      },
      ...options,
      data: qs.stringify(data, { encode: true }),
    })
      .then((res) => {
        const { data, headers } = res
        const requestId = headers['x-api-request-id']
        const response = {
          ...data,
          header: { 'x-api-request-id': requestId },
        }
        if (response?.body === undefined) {
          reject({
            message: 'empty response body',
            response,
          })
          return
        }
        if (response?.code !== '0') {
          reject({
            message: `response fail ${response.code}`,
            response,
          })
          return
        }
        if (response?.body?.errorCode) {
          reject({
            message: `response error code:${response.body.errorCode} reason:${response.body.errorReason}`,
            response,
          })
          return
        }
        resolve(response as T)
      })
      .catch(reject)
  })
}

interface Config {
  functionId: string
  umps: Array<number>
  hideLoading?: boolean
}

const wrapper = <P, T>(config: Config): P extends undefined ? () => Promise<T> : (data: P) => Promise<T> => {
  return (async (body) => {
    console.log('wrapper:', config, body)
    const api = env.request?.apis?.find((item) => item.functionId === config.functionId)

    const preload = api?.preload && (window[`__preload_${api?.functionId}`] as { promise: Promise<unknown>; secureKey: string } | undefined)

    const key =
      preload?.secureKey ||
      Math.floor(Math.random() * 100000000)
        .toString()
        .padEnd(8, '0')
    const instance = cryption(key, env.request.publicKey)

    if (preload) {
      window[`__preload_${api?.functionId}`] = undefined
      // const loading = dom.loading()
      try {
        const data = await preload.promise
        // loading.destory()
        if (api?.crypto?.output) {
          return instance.decrypt(data, api.crypto.output)
        }
        return data
      } catch (e) {
        // loading.destory()
        if (e?.['errorCode']) {
          throw e
        }
      }
    }

    const loop = (options: any, chain: Chain<unknown>, index: number) => {
      if (index < 0) {
        return chain(options)
      }

      return interceptors[index](options, (options) => {
        return loop(options, chain, index - 1)
      })
    }
    return loop(
      {
        url: `${env.baseURL}client.action`,
        data: {
          functionId: config.functionId,
          body: {
            ...(api?.crypto?.input ? instance.encrypt(body, api.crypto.input) : body),
            ...(api?.crypto
              ? {
                  keyId: env.request.publicKeyId,
                  serialNumber: instance.secret() || '',
                }
              : {}),
          },
        },
      },
      (options: any) => {
        const timestamp = Date.now()
        // const loading = config.hideLoading ? undefined : dom.loading()
        // 上报参数中如果包含逗号，日志系统会以逗号分割，导致参数被截断，因此需要把逗号改成&符上报
        // 入参
        let parameter = ''
        // 失败数据返回
        let failResponse = ''
        try {
          if (body && Object.keys(body)?.length) {
            parameter = JSON.stringify(body).replace(/,/g, '&')
          }
        } catch (e) {
          console.log('接口入参异常', e)
        }
        const { host, pathname } = window.location
        // 当前url
        const href = `${host}${pathname}`

        // 默认走h5网络库，兜底固定页面走原生网络库
        // if (router?.params()?.network === '0' || router?.params()?.network === undefined) {
        // try {
        //   sgm?.customReport({ type: 3, code: `pc_request_${config.functionId}`, msg: 'request' })
        // } catch (e) {
        //   console.log('pc请求上报异常', e)
        // }
        return request(options)
          .then((response: any) => {
            if (api?.crypto?.output) {
              response.body = instance.decrypt(response?.body, api.crypto.output)
            }
            return response
          })
          .then((response) => {
            console.log('wrapper:', options, config.functionId, response?.header?.['x-api-request-id'], response?.body)
            // loading && loading.destory()

            try {
              const requestId = response?.header['x-api-request-id']
              const message = `type=api_success;functionId=${config.functionId};requestId=${requestId};clientVersion=${env.version};url=${href};`
              send(config, '0', message, timestamp)
              // sgm?.customReport({ type: 3, code: `pc_success_${config.functionId}`, msg: message })
            } catch (e) {
              console.log('zsf 接口调用成功数据出现异常', e)
            }
            return response?.body
          })
          .catch((e) => {
            // loading && loading.destory()

            const response = e?.response
            const data = response?.data
            const code = `${data?.body?.errorCode || data?.code || response?.statusCode || '-10'}`
            let reason = data?.body?.errorReason || data?.message || data?.echo || e?.message || e?.errMsg
            const requestId = response?.header?.['x-api-request-id']
            console.log('zsf wrapper catch', config.functionId, requestId, e)

            if (data && Object.keys(data)?.length) {
              failResponse = JSON.stringify(data).replace(/,/g, '&')
            }

            // 日志上报超时时间echo下发了5,000，因上报平台按照逗号做的分割，因此逗号后面的值无法上报，把逗号改成&可以正常上报
            if (reason?.length) {
              reason = reason.replace(/,/g, '&')
            }
            const message = `type=api_failed;code=${code};functionId=${config.functionId};reason=${reason};clientVersion=${env.version};url=${href};failResponse=${failResponse};requestId=${requestId};parameter=${parameter};`
            send(config, code === '0' ? '-1' : code, message, timestamp)

            sgm?.customReport({ type: 3, code: `pc_fail_${config.functionId}`, msg: message })
            // 页面参数
            const params = { currentWebPageType: 'delivery' }

            // h5页面sgm上报
            //   if (process.env.TARO_ENV === 'h5' && params?.currentWebPageType === 'list2') {
            //     sgm?.customReport({ type: 3, code: sgmCode?.ADDRESS_CMPNT_QUERYADDRESS, msg: message })
            //   }

            // 接口网络异常，列表接口进行重试
            // @ts-ignore
            if (params?.currentWebPageType === 'delivery' && config?.functionId === 'pc_address_cmpnt_queryAddress') {
              const {
                operation: { ADDRESS_CMPNT_QUERYADDRESS_FAIL, APP_NETWORK_SUCCESS, APP_NETWORK_FAIL },
              } = bizOperation
              // report.biz({
              //   operation: ADDRESS_CMPNT_QUERYADDRESS_FAIL,
              //   code,
              //   message: `type=delivery_page;routerParams=${message}`,
              // })
              return appNetwork({
                functionId: config.functionId,
                body: {
                  ...(api?.crypto?.input ? instance.encrypt(body, api.crypto.input) : body),
                  ...(api?.crypto
                    ? {
                        keyId: env.request.publicKeyId,
                        serialNumber: instance.secret() || '',
                      }
                    : {}),
                },
              })
                .then((response) => {
                  if (api?.crypto?.output) {
                    response.body = instance.decrypt(response?.body, api.crypto.output)
                  }
                  const code = response?.code || response?.body?.errorCode
                  // report.biz({
                  //   operation: APP_NETWORK_SUCCESS,
                  //   code,
                  // })
                  console.log('原生网络列表接口请求成功', response)
                  return response?.body
                })
                .catch((err) => {
                  console.log('原生网络列表接口请求失败', err)
                  const code = err?.response?.body?.errorCode || err?.response?.code || err?.status
                  // report.biz({
                  //   operation: APP_NETWORK_FAIL,
                  //   code,
                  // })
                  throw e
                })
            }
            throw e
          })
        // }
        // sgm上报
        // sgm?.customReport({ type: 3, code: `r_${config.functionId}`, msg: 'request' })

        // 页面参数
        // const pageParams = router?.params()
        // const pageParams = {}
        // return appNetwork({
        //   functionId: config.functionId,
        //   body: {
        //     ...(api?.crypto?.input ? instance.encrypt(body, api.crypto.input) : body),
        //     ...(api?.crypto
        //       ? {
        //           keyId: env.request.publicKeyId,
        //           serialNumber: instance.secret() || '',
        //         }
        //       : {}),
        //     ...{
        //       // @ts-ignore
        //       tenantCode: pageParams?.from,
        //       token: env.token,
        //       bizModelCode: env.bizModelCode,
        //       bizModeClientType: env.bizModeClientType,
        //       externalLoginType: env.externalLoginType,
        //     },
        //   },
        // })
        //   .then((response) => {
        //     sgm?.customReport({ type: 3, code: `s_${config.functionId}`, msg: 'success' })
        //     // loading && loading.destory()
        //     if (api?.crypto?.output) {
        //       response.data.body = instance.decrypt(response.data?.body, api.crypto.output)
        //     }
        //     return response?.data?.body
        //   })
        //   .catch((err) => {
        //     sgm?.customReport({ type: 3, code: `f_${config.functionId}`, msg: JSON.stringify(err) })
        //     // loading && loading.destory()
        //     // 原生网络请求失败，使用webview重新发起网络请求。
        //     sgm?.customReport({ type: 3, code: `r_r_${config.functionId}`, msg: 'request' })
        //     return request(options)
        //       .then((response: any) => {
        //         if (api?.crypto?.output) {
        //           response.body = instance.decrypt(response?.body, api.crypto.output)
        //         }
        //         return response
        //       })
        //       .then((response) => {
        //         sgm?.customReport({ type: 3, code: `r_s_${config.functionId}`, msg: 'success' })
        //         // loading && loading.destory()
        //         return response?.body
        //       })
        //       .catch((e) => {
        //         sgm?.customReport({ type: 3, code: `r_f_${config.functionId}`, msg: JSON.stringify(e) })
        //         // loading && loading.destory()
        //         throw e
        //       })
        //   })
      },
      interceptors.length - 1,
    )
  }) as never
}

export default wrapper
