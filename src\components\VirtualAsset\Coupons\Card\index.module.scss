.flex {
  display: flex;
  // align-items: center;
  flex-direction: column;
}
.cardWrap {
  position: relative;
  // padding-bottom: 20px;
}
.xfq {
  top: -4.5px;
  left: -0.5px;
  position: absolute;
  height: 17px;
  line-height: 17px;
  padding: 0 4px;
  border-radius: 6px 6px 6px 0px;
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 400;
  background: #FF0F23;

  &.bjxfq-icon {
    background: #FF0F23;
  }
  &.szxfq-icon {
    background: #FF0F23;
  }
  &.thjt-icon {
    background: #FF9A47;
  }
}
.card {
  width: 200px;
  height: 100px;
  border: 0.5px solid #FFDBE2;
  border-radius: 8px;
  background: #FFF7F9;
  position: relative;
  cursor: pointer;

  // overflow: hidden;
  &::after,
  &::before {
    content: "";
    display: block;
    position: absolute;
    z-index: 1;
    top: 62px;
    width: 10px;
    height: 12px;
    background-color: #FFFFFF;
  }

  &::after {
    left: -10.5px;
  }

  &::before {
    right: -10.5px;
  }

  &.active {
    border-color: #FF273B;

    .cardTop {
      color: #FF273B;

      &::after,
      &::before {
        border-color: #FF273B;
      }
    }
  }

  &.disabled {
    border-color: #E1E2E5;
    background-color: #F7F8FC;
    cursor: not-allowed;

    .cardTop {
      color: #888B94;

      &::after,
      &::before {
        border-color: #E1E2E5;
      }
    }

    .cardBottom {
      color: #888B94;
      border-color: rgba(0, 0, 0, 0.1);
    }
    .xfq {
      background-color: #888B94;
      color: #fff;
    }
  }

  >img {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 16px;
    height: 16px;
  }

  .cardTop {
    padding-top: 16px;
    padding-left: 16px;
    color: #FF3333;

    &::after,
    &::before {
      content: "";
      display: block;
      position: absolute;
      top: 62px;
      width: 12px;
      height: 12px;
      background-color: #FFFFFF;
      border: 0.5px solid #FFDBE2;
      box-sizing: border-box;
      border-radius: 50%;
    }

    &::after {
      left: -7px;
    }

    &::before {
      right: -7px;
    }

    .yen {
      font-size: 20px;
      font-weight: 700;
      margin-right: 6px;
      font-family: JDZhengHeiVHeavy2;
      i {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 600;
      }
    }

    .quota {
      font-size: 12px;
      font-weight: 400;
      i {
        font-family: JDZhengHeiVRegular2-1;
      }
    }
    >div {
      font-size: 12px;
      font-weight: 400;
      margin-top: 8px;
    }

    .icon {
      position: relative;
      top: 2px;
      margin-left: 4px;
    }
  }

  .cardBottom {
    color: #FF5C67;
    font-size: 12px;
    font-weight: 400;
    position: absolute;
    bottom: 0;
    left: 16px;
    right: 16px;
    height: 32px;
    line-height: 32px;
    border-top: 0.5px dashed #FFDBE2;
    box-sizing: border-box;

    >span {
     margin-right: 4px; 
    }
    >span:last-child  {
      margin-right: 0;
    }
  }

  .selfOperated {
    // padding-top: 12px;
  }

}
.tooltipTit {
  color: #888B94;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 8px;
}
.dropDownCont {

  // width: 100%;
  // width: auto;
  // display: flex;
  // flex-wrap: wrap;
  img {
    margin-right: 10px;
    width: 56px;
    height: 56px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 3.5px;
    background: #FFFFFF;
    cursor: pointer;
  }
}

.cardDesc {
  height: 21px;
  padding-top: 7px;
  box-sizing: border-box;
}
.desc {
  position: absolute;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;

}

.discount {
  // margin-top: 4px;
  color: #888B94;
  font-size: 12px;
  font-weight: 400;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 200px;
}
.limitDesc {
  // margin-top: 4px;
  font-size: 12px;
  color: #888B94;
  font-weight: 400;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 200px;
}