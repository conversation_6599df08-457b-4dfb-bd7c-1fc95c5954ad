/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-03 12:18:36
 * @LastEditors: ext.wangchao120
 * @Description: 礼品卡
 * @FilePath: /pc_settlement/src/components/VirtualAsset/GiftCard/index.tsx
 */
import React, { useEffect, useState, useRef } from 'react'
import styles from './index.module.scss'
import Tabs from '../components/Tabs'
import RedeemCode from '../components/RedeemCode'
import Card from './Card'

import useGift from '../hooks/useGift'
import NoData from '../components/NoData'
import { ExposeBuriedPoints } from '@app/utils/dataSet'
import { _batchExposeDom, domMap } from '../batchExpose'
import { giftTabKey } from '@app/components/VirtualAsset/atoms'
import { useAtom } from 'jotai'
interface PropsType {
  tabKey?: string
}
function GiftCard({ tabKey }: PropsType) {
  const [activeKey, seActiveKey] = useAtom(giftTabKey)
  const { handleSubmit, codeRef, giftCardList, notGiftCardList, getGiftCardList, cardClick } = useGift()
  const observer = useRef<ExposeBuriedPoints | null>(null)
  useEffect(() => {
    domMap.clear()
    if (activeKey === 'giftList' && tabKey === 'gift') {
      getGiftCardList('gift')
    }
  }, [tabKey, activeKey])

  const loadingDom = (id: string) => {
    domMap.set(id, id)
    if (giftCardList.length + notGiftCardList.length === domMap.size) {
      observer.current?.destory()
      observer.current = _batchExposeDom('virtualasset_CardEXPO', 'gift', 'cardid', {
        second_tab_name: ['礼品卡', '添加礼品卡'],
      })
    }
  }
  return (
    <>
      <Tabs activeKey={activeKey} onChange={(key: string) => seActiveKey(key)} type="button" maxHeight="300">
        <Tabs.Panel tabKey="giftList" tab="礼品卡">
          <div className={styles.cardList}>
            <NoData arr={[...giftCardList, ...notGiftCardList]} text="无可用礼品卡">
              {giftCardList &&
                giftCardList.map((item) => {
                  return (
                    <Card key={item.id} item={item} onClick={cardClick} loadingDom={loadingDom} tabKey={tabKey} activeKey={activeKey} />
                  )
                })}
              {notGiftCardList &&
                notGiftCardList.map((item) => {
                  return <Card key={item.id} item={item} disabled loadingDom={loadingDom} tabKey={tabKey} activeKey={activeKey} />
                })}
            </NoData>
          </div>
        </Tabs.Panel>
        <Tabs.Panel tabKey="add" tab="添加礼品卡">
          <div className={styles.subTitle}>请输入礼品卡（京东卡 / 图书卡 / 京东E卡）密码</div>
          <RedeemCode ref={codeRef} onClick={(code) => handleSubmit(code, 'gift')}>
            添加并使用
          </RedeemCode>
          <div className={styles.warningMessage}>
            温馨提示：京东商城是京东E卡唯一官方认可的销售平台。如您通过其他渠道购买，可能会造成京东E卡无法使用。
          </div>
        </Tabs.Panel>
      </Tabs>
    </>
  )
}

export default GiftCard
