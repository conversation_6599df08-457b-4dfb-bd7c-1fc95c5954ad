.toastWrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9999;
  box-sizing: border-box;
}

.toastOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 9999;
}

.toastContainer {
  box-sizing: border-box;
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  height: 44px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  width: max-content;
  animation: toastFadeIn 0.3s ease-out;
  pointer-events: auto;
}

.toastContent {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  white-space: nowrap;
}

.toastIcon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.toastText {
  color: #1A1A1A;
  font-family: PingFang SC;
  font-weight: 600;
  font-size: 14px;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes toastFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes toastFadeOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-10px) scale(0.9);
  }
}

/* 响应式适配 */
@media (max-width: 480px) {
  .toastContainer {
    margin: 0 16px;
    max-width: calc(100vw - 32px);
    width: auto;
  }
  
  .toastContent {
    white-space: normal;
  }
  
  .toastText {
    white-space: normal;
    word-break: break-word;
  }
} 