/**
 * 店铺信息
 */
import scope from './index.module.scss'
import { POP_SHOP_ICON } from './constant'

interface IProps {
  isSelf?: boolean // 是否自营
  name?: string // 店铺名称
  children?: React.ReactNode
}

const Shop: React.FC<IProps> = ({ name, isSelf, children }) => {
  return (
    <div className={`${scope.shop} flex-center-between`} style={{ paddingRight: '0px', gap: '50px' }}>
      <div className="flex-center">
        {isSelf && <div className={scope.self}>自营</div>}
        {!isSelf && <img src={POP_SHOP_ICON} className={`${scope['pop-shop-icon']}`} />}
        <div className={scope.name}>{name || '京东'}</div>
      </div>
      {/* 配送 */}
      <div className="top-delivery" style={{ minWidth: '100px', flex: 1, display: 'flex', paddingLeft: '0px', justifyContent: 'flex-end' }}>
        {children}
      </div>
    </div>
  )
}

export default Shop
