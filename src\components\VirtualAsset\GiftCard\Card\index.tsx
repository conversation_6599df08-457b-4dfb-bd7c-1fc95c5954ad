/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-23 18:52:11
 * @LastEditTime: 2025-07-03 14:53:32
 * @LastEditors: ext.wangchao120
 * @Description: 礼品卡卡片
 * @FilePath: /pc_settlement/src/components/VirtualAsset/GiftCard/Card/index.tsx
 */
import { ReactNode, FC, useEffect } from 'react'
import Tooltip from '@app/common/Tooltip'
import styles from './index.module.scss'
import classNames from 'classnames'
import { SELECTED_ICON } from '../../constants'

interface PropsType {
  active?: boolean
  item: any
  disabled?: boolean
  onClick?: (type: string, item: any) => void
  loadingDom?: (id: string) => void
  tabKey?: string
  activeKey?: string
}
/**
 * 根据type渲染不同的name
 */
const getNameByType = (type: number, cardBrandName: string) => {
  switch (type) {
    case -1:
      return `京品卡|${cardBrandName}`
    case 0:
      return '京东卡'
    case 1:
      return '京东E卡'
    case 2:
      return `京品卡|${cardBrandName}`
    case 4:
      return '储值卡'
    case 5:
      return `时珍卡|${cardBrandName}`
    default:
      return '京东卡'
  }
}

const Card: FC<PropsType> = ({ item, disabled, onClick, loadingDom, tabKey, activeKey }) => {
  // 下拉框内容
  const dropDownCont = (
    <>
      <div className={styles.tips}>{disabled ? item?.disableDesc : item?.ableDesc}</div>
    </>
  ) as ReactNode

  // 面值
  const limit = (
    <div className={styles.limit}>
      <span>面值</span>
      <i>&yen;</i>
      {item?.amount}
    </div>
  ) as ReactNode

  // 本次使用
  const curUsedMoney = (
    <div className={styles.curUsedMoney}>
      <i>本次使用</i>
      <span>&yen;{item?.curUsedMoney}</span>
    </div>
  ) as ReactNode

  useEffect(() => {
    if (tabKey === 'gift' && activeKey === 'giftList') {
      loadingDom && loadingDom(item?.id)
    }
  }, [tabKey, activeKey])

  return (
    <div className={classNames(styles.cardWarp, 'gift')} data-point-id={item?.id}>
      <div className={classNames(styles.cardItem, disabled && styles.disabled)} onClick={() => onClick?.('gift', item)}>
        <div className={styles.title}>
          <div className={styles.flex}>
            <div className={styles.icon}></div>
            <span>{getNameByType(item?.showType, item?.cardBrandName)}</span>
          </div>
          {item?.selected && (
            <img src="https://img13.360buyimg.com/img/jfs/t1/281869/32/9231/2553/67e2739eF94465711/7ee9d27c8af463fe.png" />
          )}
          {/* 已勾选 */}
          {item?.selected && <img src={SELECTED_ICON} />}
        </div>
        <div className={styles.balance}>
          <span>余</span>
          <div className={styles.price}>
            <i>&yen;</i>
            {item?.leaveMoney}
          </div>

          {/* 面值 */}
          {limit}
        </div>

        <div className={styles.date}>有效期至{item?.timeEnd}</div>

        <Tooltip width={254} padding={'16px'} content={dropDownCont} type="popover" className={styles.tooltip}>
          <div className={styles.selfOperated}>
            大部分自营商品可用
            <i></i>
          </div>
        </Tooltip>
      </div>

      {/* 当选择且有抵扣金额时显示 */}
      {item?.selected && item?.curUsedMoney && curUsedMoney}
    </div>
  )
}

export default Card
