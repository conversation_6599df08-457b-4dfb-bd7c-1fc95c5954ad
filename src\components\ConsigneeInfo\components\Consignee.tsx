/*
 * @Author: ext.xuchao26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息
 */

import { useEffect, useMemo, memo, useRef, useState, FC, ReactNode } from 'react'
import classNames from 'classnames'
import styles from '../index.module.scss'
import { readCookie } from '../utils'
import { reportClick } from '@app/utils/event_tracking'
import { EllipsisTooltip } from '../components'
// 定义状态类型
interface ConsigneeProps {
  onExpand: () => void
  onShow: (v?: any) => void
  addressInfoList: Array<{ [key: string]: boolean }>
  consigneeTip?: string
  children: ReactNode
  isExpand: boolean
}

// 类型定义
type MediaQueryName = 'mediumScreen' | 'smallScreen'
type MediaResult = Record<`is${Capitalize<MediaQueryName>}`, boolean>

const Consignee: FC<ConsigneeProps> = memo(({ onExpand, onShow, children, addressInfoList, consigneeTip, isExpand }) => {
  const [mediaResults, setMediaResults] = useState<Record<string, boolean>>({})
  const cleanupFunctions = useRef<(() => void)[]>([])
  const areaMgmtUrl = 'https://easybuy.jd.com/address/getEasyBuyList.action'

  useEffect(() => {
    // 创建媒体查询对象
    const createMediaQuery = (query: string): MediaQueryList | null => {
      if (typeof window === 'undefined' || !window.matchMedia) return null
      try {
        return window.matchMedia(query)
      } catch (error) {
        console.error(error)
        return null
      }
    }
    const mediaQueries = {
      mediumScreen: createMediaQuery('(min-width: 1440px) and (max-width: 1659.98px)'),
      smallScreen: createMediaQuery('(max-width: 1239.98px)'),
    }
    const handleMediaChange = () => {
      const mediaResults: MediaResult = {
        isMediumScreen: mediaQueries?.mediumScreen?.matches ?? false,
        isSmallScreen: mediaQueries?.smallScreen?.matches ?? false,
      }
      setMediaResults(mediaResults)
    }

    try {
      // 批量添加监听器
      Object.values(mediaQueries).forEach((mq: any) => {
        if (mq) {
          mq.addEventListener('change', handleMediaChange)
          cleanupFunctions.current.push(() => {
            mq.removeEventListener('change', handleMediaChange)
          })
        }
      })
      // 初始状态更新
      handleMediaChange()
    } catch (error) {
      console.error(error)
    }
    return () => cleanupFunctions.current.forEach((cleanup) => cleanup()) // 批量清理监听器
  }, [])

  const isTwoColumnsExpandStyle = useMemo(() => {
    const { isMediumScreen, isSmallScreen } = mediaResults
    if (isMediumScreen || isSmallScreen) {
      return addressInfoList?.length > 2
    }
    return addressInfoList?.length > 3
  }, [mediaResults, addressInfoList])

  const isTwoColumnsConsigneeStyle = useMemo(() => {
    const { isMediumScreen, isSmallScreen } = mediaResults
    if (isMediumScreen || isSmallScreen) {
      return addressInfoList?.length > 6
    }
    return addressInfoList?.length > 9
  }, [mediaResults, addressInfoList])

  const isTwoColumnsHeightStyle = useMemo(() => {
    const { isMediumScreen, isSmallScreen } = mediaResults
    if (isMediumScreen || isSmallScreen) {
      if (addressInfoList?.length >= 2 && addressInfoList?.length <= 4) {
        return { height: 'h256' }
      } else if (addressInfoList?.length <= 2) {
        return { height: 'h162' }
      } else {
        return { height: '' }
        // return { height: 'h384' }
      }
    }
    if (addressInfoList?.length >= 4 && addressInfoList?.length <= 6) {
      return { height: 'h256' }
    } else if (addressInfoList?.length <= 3) {
      return { height: 'h162' }
    } else {
      return { height: '' }
      // return { height: 'h384' }
    }
  }, [mediaResults, addressInfoList])

  return (
    <div
      className={classNames(`${styles['consignee']} pl-16 pr-5 mb-16 ftx11`, {
        [styles['expand']]: isExpand,
        'h-168': !addressInfoList?.length,
        [styles['isTwoColumns']]: isTwoColumnsConsigneeStyle,
        [styles[isTwoColumnsHeightStyle['height']]]: !!isTwoColumnsHeightStyle,
      })}
    >
      <div className={`${styles['consignee-header']} is-flex justify-between h-46 pb-10`}>
        <p className="ftx12 font-16 leading-20 h-20 font-semibold is-flex pt-20">
          <span className="shrink-0">收货人信息</span>
          {consigneeTip && (
            <EllipsisTooltip content={consigneeTip}>
              <span className={classNames(`${styles['consignee-tip']} ml-8 ftx03 font-14 font-normal pr-10 is-ellipsis`)}>
                {consigneeTip}
              </span>
            </EllipsisTooltip>
          )}
        </p>
        <div className="ftx11 font-14 is-flex pt-20 shrink-0">
          <span
            className={classNames('h-14 leading-14 cursor-pointer', { 'mr-6': !addressInfoList?.length })}
            onClick={() => onShow({ type: 'add' })}
          >
            新增
          </span>
          {!!addressInfoList?.length && (
            <>
              <span className={`h-12 ${styles['divider']} mx-12`}></span>
              <a
                className="h-14 leading-14 cursor-pointer mr-11 ftx11"
                onClick={reportClick.bind(null, 'skuaddress', { clickPos: '1', addr: readCookie('ipLoc-djd') })}
                href={areaMgmtUrl}
                target="_blank"
              >
                管理
              </a>
            </>
          )}
        </div>
      </div>
      {children}
      {isTwoColumnsExpandStyle && (
        <div
          className={classNames(`h-50 is-flex leading-14 cursor-pointer ${styles['consignee-expand']}`, {
            [styles['collapse']]: !isExpand,
          })}
        >
          <span className="font-14 ftx11 mt-16" onClick={onExpand}>
            {isExpand ? '收起全部地址' : '展开全部地址'}
            <i />
          </span>
        </div>
      )}
    </div>
  )
})

export default Consignee
