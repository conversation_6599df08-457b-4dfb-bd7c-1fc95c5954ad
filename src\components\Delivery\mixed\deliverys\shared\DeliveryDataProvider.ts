/**
 * @file: DeliveryDataProvider.ts
 * @description: 国内配送数据提供者，用于提供国内各个类型组件数据
 */
import { PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp'
import { DeliveryDataIniter, promiseToComponentData } from '../../initDeliveryData'

export class DeliveryDataProvider {
  private dataMaker: promiseToComponentData
  private initer: DeliveryDataIniter

  constructor(initer: DeliveryDataIniter) {
    this.dataMaker = new promiseToComponentData()
    this.initer = initer
  }

  getComponentTypeAndData(promise: PromiseListItem) {
    const typeInfo = this.initer.getDeliveryTypeInfo(promise)
    if (!typeInfo) return { type: null, data: null }

    switch (typeInfo.componentType) {
      case 'simple': {
        const data: { displayedString: string } = this.dataMaker.getSimple(promise)
        return { type: 'simple', data }
      }
      case 'timePicker':
        return { type: 'timePicker', data: this.dataMaker.getSimpleTimeList(promise) }
      case 'install': {
        const targetData = this.initer.getTargetPromise(promise)
        return { type: 'install', data: (targetData && this.dataMaker.getInstallData(promise, targetData)) || [] }
      }
      case 'timeList':
        return { type: 'timeList', data: null }
      case 'loading':
        return { type: 'loading', data: null }
      case 'smzt':
        return { type: 'smzt', data: null }
      case 'mdzt':
        return { type: 'mdzt', data: null }
      default:
        return { type: typeInfo.componentType, data: null }
    }
  }
}
