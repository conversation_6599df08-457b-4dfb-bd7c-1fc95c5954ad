.el-select-dropdown {
  position: absolute;
  z-index: 1001;
  border: 1px solid #d1dbe5;
  border-radius: 2px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,.12),0 0 6px rgba(0,0,0,.04);
  box-sizing: border-box;
  margin: 5px 0
}

.el-select-dropdown .el-scrollbar.is-empty .el-select-dropdown__list {
  padding: 0
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: #20a0ff;
  background-color: #fff
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover,.el-select-dropdown__item.hover,.el-select-dropdown__item:hover {
  background-color: #F7F8FC;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  position: absolute;
  right: 10px;
  font-family: element-icons;
  content: "\E608";
  font-size: 11px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.el-select-dropdown__empty {
  padding: 10px 0;
  margin: 0;
  text-align: center;
  color: #999;
  font-size: 14px
}

.el-select-dropdown__wrap {
  max-height: 274px
}

.el-select-dropdown__list {
  list-style: none;
  padding: 4px 4px 0 4px;
  margin: 0;
  box-sizing: border-box
}

.el-select-dropdown__item {
  font-size: 14px;
  padding: 8px 10px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #1A1A1A;
  height: 36px;
  line-height: 1.5;
  box-sizing: border-box;
  cursor: pointer
}

.el-select-dropdown__item.selected {
  color: #FF0F23;
  background-color: #fff;
}

.el-select-dropdown__item.selected.hover {
  // background-color: #1c8de0
}

.el-select-dropdown__item span {
  line-height: 1.5!important
}

.el-select-dropdown__item.is-disabled {
  color: #bfcbd9;
  cursor: not-allowed
}

.el-select-dropdown__item.is-disabled:hover {
  background-color: #fff
}

.el-select-group {
  margin: 0;
  padding: 0
}

.el-select-group .el-select-dropdown__item {
  padding-left: 10px
}

.el-select-group__wrap {
  list-style: none;
  margin: 0;
  padding: 0
}

.el-select-group__title {
  padding-left: 10px;
  font-size: 12px;
  color: #999;
  height: 30px;
  line-height: 30px
}

.el-select {
  display: inline-block;
  position: relative
}

.el-select:hover .el-input__inner {
  border-color: #FF8595;
}

.el-select .el-input__inner {
  cursor: pointer;
  padding-right: 35px
}

.el-select .el-input__inner:focus {
  border-color: #FF8595;
}

.el-select .el-input .el-input__icon {
  color: #bfcbd9;
  font-size: 12px;
  transition: transform .3s;
  transform: translateY(-50%) rotateZ(180deg);
  line-height: 16px;
  width: 16px;
  height: 16px;
  top: 50%;
  right: 8px;
  cursor: pointer;
    background: url(https://img13.360buyimg.com/imagetools/jfs/t1/287615/19/3572/446/6819d5ceFf4e25646/b4ad5c909daa811f.png) center / 8px 8px no-repeat;
  &.el-icon-circle-close {
    background: url(https://img12.360buyimg.com/imagetools/jfs/t1/275638/17/11200/598/67e669f0F62ee6ede/bf00226f8543b19b.png) center / 8px 8px no-repeat;
  }
  &:hover {
    transform: translateY(-50%);
  }
}

.el-select .el-input .el-input__icon.is-show-close {
  transition: transform .3s;
  // width: 16px;
  // height: 16px;
  font-size: 14px;
  // right: 8px;
  text-align: center;
  transform: translateY(-50%) rotateZ(180deg);
  border-radius: 100%;
  color: #bfcbd9
}

.el-select .el-input .el-input__icon.is-show-close:hover {
  color: #97a8be
}

.el-select .el-input .el-input__icon.is-reverse {
  transform: translateY(-50%)
}

.el-select .el-input.is-disabled .el-input__inner {
  cursor: not-allowed
}

.el-select .el-input.is-disabled .el-input__inner:hover {
  border-color: #d1dbe5
}

.el-select>.el-input {
  display: block
}

.el-select .el-tag__close {
  margin-top: -2px
}

.el-select .el-tag {
  height: 24px;
  line-height: 24px;
  box-sizing: border-box;
  margin: 3px 0 3px 6px
}

.el-select__input {
  border: none;
  outline: 0;
  padding: 0;
  margin-left: 10px;
  color: #666;
  font-size: 14px;
  vertical-align: baseline;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: 28px;
  background-color: transparent
}

.el-select__input.is-mini {
  height: 14px
}

.el-select__close {
  cursor: pointer;
  position: absolute;
  top: 8px;
  z-index: 1000;
  right: 25px;
  color: #bfcbd9;
  line-height: 18px;
  font-size: 12px
}

.el-select__close:hover {
  color: #97a8be
}

.el-select__tags {
  position: absolute;
  line-height: normal;
  white-space: normal;
  z-index: 1;
  top: 50%;
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

.el-select__tag {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  font-size: 14px;
  border-radius: 4px;
  color: #fff;
  background-color: #20a0ff
}

.el-select__tag .el-icon-close {
  font-size: 12px
}