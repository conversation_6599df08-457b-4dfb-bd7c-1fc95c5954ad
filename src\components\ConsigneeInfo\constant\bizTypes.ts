import { sgmCode } from '../constant'

export const PROVINCE_ID = {
  FOREIGN: 53283,
  GANG_AO: 52993,
  TAI_WAN: 32,
}

export const EDIT_PAGE_TYPE = {
  ADD: 'add',
  EDIT: 'edit',
}

export const PAGE_TYPRE_MAP = {
  [EDIT_PAGE_TYPE.EDIT]: 2,
  [EDIT_PAGE_TYPE.ADD]: 1,
}

export const BASE_EDIT_NAMES = {
  name: '姓名',
  phone: '手机号',
  addressDetail: '详细地址',
  houseNumber: '门牌号',
  email: '邮箱',
  mobile: '手机号',
  postCode: '邮政编码',
}
export const EDIT_SGMNAMES_MAP = {
  name: sgmCode.NOPASS_NAME,
  phone: sgmCode.NOPASS_PHONE,
  addressDetail: sgmCode.NOPASS_ADDRESSDETAIL,
  houseNumber: sgmCode.NOPASS_HOUSENUMBER,
  email: sgmCode.NOPASS_EMAIL,
  mobile: sgmCode.NOPASS_MOBILE,
  postCode: sgmCode.NOPASS_POSTCODE,
  cascadeComplete: sgmCode.CASCADE_COMPLETE,
  mapComplete: sgmCode.MAP_COMPLETE,
}

export const SHOWBOX_SGM_MAP = {
  showBox: sgmCode.SHOWBOX,
  showBoxNoChoose: sgmCode.SHOWBOXNOCHOOSE,
  showBoxNoAutoComp: sgmCode.SHOWBOXNOAUTOCOMP,
  showBoxNoChooseFail: sgmCode.SHOWBOXNOCHOOSEFAIL,
  showBoxForceSave: sgmCode.SHOWBOXFORCESAVE,
  showBoxRecommendPoi: sgmCode.SHOWBOXRECOMMENDPOI,
}
// 选址类型
export enum SelectAddressType {
  MAP = 'map',
  CASCADE = 'cascade',
}

export const editPageValidateRules = {
  [SelectAddressType.MAP]: {
    [PROVINCE_ID.GANG_AO]: ['name', 'mobile', 'cascadeComplete', 'houseNumber', 'email', 'phone'],
    [PROVINCE_ID.TAI_WAN]: ['name', 'mobile', 'cascadeComplete', 'houseNumber', 'email', 'postCode', 'phone'],
    default: ['name', 'mobile', 'cascadeComplete', 'houseNumber'],
  },
  [SelectAddressType.CASCADE]: {
    [PROVINCE_ID.GANG_AO]: ['name', 'mobile', 'cascadeComplete', 'addressDetail', 'email', 'phone'],
    [PROVINCE_ID.TAI_WAN]: ['name', 'mobile', 'cascadeComplete', 'addressDetail', 'email', 'postCode', 'phone'],
    [PROVINCE_ID.FOREIGN]: ['name', 'mobile', 'cascadeComplete', 'addressDetail', 'email', 'postCode', 'phone'],
    default: ['name', 'mobile', 'cascadeComplete', 'addressDetail'],
  },
}

export const ADDRESS_MAX_LIMIT_LENGTH = 25

// 地址包含以下信息，表示复制的地址满足地址条件
export const VERIFY_ADRESS_INFO = [
  '收货人',
  '收件人',
  '收货地址',
  '黑龙江',
  '吉林',
  '辽宁',
  '河北',
  '甘肃',
  '青海',
  '陕西',
  '河南',
  '山东',
  '山西',
  '安徽',
  '湖北',
  '湖南',
  '江苏',
  '四川',
  '贵州',
  '云南',
  '浙江',
  '江西',
  '广东',
  '福建',
  '台湾',
  '海南',
  '新疆',
  '内蒙古',
  '宁夏',
  '广西',
  '西藏',
  '北京',
  '上海',
  '天津',
  '重庆',
  '香港',
  '澳门',
  '栋',
  '幢',
  '单元',
  '户',
  '室',
  '房',
]

// 银行卡信息
export const VERIFY_BANK = ['银行卡', '银行', '行']
// 地图产品李琦要求接入地图时候传递sourceType https://joyspace.jd.com/pages/5BKxjIOJ1Iwtw7UsurSC 新建编辑写死，其他透传
export const TO_MAP_SOURCE_TYPE = {
  ADD: 'newLocation',
  EDIT: 'modifyLocation',
}

export const PAGE_TYPE = {
  LOCATION: 'location',
  DELIVERY: 'delivery',
} as const
