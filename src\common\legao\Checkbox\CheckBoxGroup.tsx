import React from 'react'
import { Component, PropTypes } from '../libs'
import { CheckboxGroupContext } from './CheckBox'

interface CheckboxGroupProps {
  min?: string | number
  max?: string | number
  size?: string
  fill?: string
  textColor?: string
  value?: Array<string | number>
  onChange?: (options: Array<string | number>) => void
}

interface CheckboxGroupState {
  options: Array<string | number>
}

export default class CheckboxGroup extends Component<CheckboxGroupProps, CheckboxGroupState> {
  constructor(props: CheckboxGroupProps) {
    super(props)
    this.state = {
      options: this.props.value || [],
    }
  }

  componentDidUpdate(prevProps: CheckboxGroupProps): void {
    if (prevProps.value !== this.props.value) {
      this.setState({
        options: this.props.value,
      })
    }
  }

  onChange(value: string | number, checked: boolean): void {
    let { options } = this.state
    const index = options.indexOf(value)

    if (checked) {
      if (index === -1) {
        options = [...options, value]
      }
    } else {
      options = options.filter((_, i) => i !== index)
    }

    this.setState({ options }, () => {
      if (this.props.onChange) {
        this.props.onChange(this.state.options)
      }
    })
  }

  render(): JSX.Element {
    const { options } = this.state

    const children = React.Children.map(this.props.children, (child, index) => {
      if (!child) {
        return null
      }

      const { elementType } = child.type as { elementType: string }
      // 过滤非Checkbox和CheckboxButton的子组件
      if (elementType !== 'Checkbox' && elementType !== 'CheckboxButton') {
        return null
      }

      return React.cloneElement(child, {
        key: index,
        checked:
          child.props.checked || options.indexOf(child.props.value) >= 0 || options.indexOf(child.props.label) >= 0,
        onChange: this.onChange.bind(this, child.props.value ?? (child.props.value === 0 ? 0 : child.props.label)),
      })
    })

    return (
      <CheckboxGroupContext.Provider value={{ ElCheckboxGroup: this }}>
        <div style={this.style()} className={this.className('el-checkbox-group')}>
          {children}
        </div>
      </CheckboxGroupContext.Provider>
    )
  }
}

CheckboxGroup.propTypes = {
  min: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  max: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  size: PropTypes.string,
  fill: PropTypes.string,
  textColor: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
}
