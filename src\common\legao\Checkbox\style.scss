.el-checkbox {
  color: #1f2d3d;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  &:hover {
    .el-checkbox__input .el-checkbox__inner {
      visibility: visible;
    }
    .el-checkbox__label {
      color: #FF0F23;
    }
    .el-checkbox__input {
      border-color: #FF0F23
    }
  }
}

.el-checkbox+.el-checkbox {
  margin-left: 15px
}

.el-checkbox__input {
  outline: 0;
  vertical-align: middle
}

.el-checkbox, .el-checkbox__input {
  cursor: pointer;
  display: inline-flex;
  position: relative;
  white-space: nowrap;
}

.el-checkbox__input.is-indeterminate {
  background-color: #20a0ff;
  border-color: #0190fe
}

.el-checkbox__input.is-indeterminate::before {
  content: '';
  position: absolute;
  display: block;
  border: 1px solid #fff;
  margin-top: -1px;
  left: 3px;
  right: 3px;
  top: 50%
}

.el-checkbox__input.is-indeterminate::after {
  display: none
}

.el-checkbox__input.is-focus {
  border-color: #FF0F23;
}

.el-checkbox__input.is-checked {
  border-color: #FF0F23;
  + .el-checkbox__label {
    color: #FF0F23;
  }
  .el-checkbox__inner {
    position: absolute;
    top: -1px;
    left: -1px;
    width: 14px;
    height: 14px;
    border: 1px solid #FF0F23;
    border-radius: 50%;
    visibility: hidden;
    animation: jdCheckboxEffect .36s ease-in-out;
    animation-fill-mode: backwards;
    z-index: 1;
    transform: scale(0.9);
  }
}

.el-checkbox__input.is-checked::after {
  transform: translate(-50%, -50%) scale(0.9);
}

.el-checkbox__input.is-disabled {
  background-color: #eef1f6;
  border-color: #d1dbe5;
  cursor: not-allowed
}

.el-checkbox__input.is-disabled::after {
  cursor: not-allowed;
  border-color: #eef1f6
}

.el-checkbox__input.is-disabled+.el-checkbox__label {
  cursor: not-allowed
}

.el-checkbox__input.is-disabled.is-checked {
  background-color: #d1dbe5;
  border-color: #d1dbe5
}

.el-checkbox__input.is-disabled.is-checked::after {
  border-color: #fff
}

.el-checkbox__input.is-disabled.is-indeterminate {
  background-color: #d1dbe5;
  border-color: #d1dbe5
}

.el-checkbox__input.is-disabled.is-indeterminate::before {
  border-color: #fff
}

.el-checkbox__input.is-disabled+.el-checkbox__label {
  color: #bbb;
  cursor: not-allowed
}

.el-checkbox__input {
  display: inline-block;
  position: relative;
  border: 1px solid #bfbfbf;
  border-radius: 50%;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  background-color: #fff;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}


.el-checkbox__input::after {
  width: 16px;
  height: 16px;
  background: url(https://img12.360buyimg.com/imagetools/jfs/t1/262811/17/16930/344/67a31d47F4eced4d0/66b189e4ff6534c8.png) center / 12px auto no-repeat #FF0F23;
  border-radius: 50%;
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  transform-origin: center;
}

.el-checkbox__original {
  opacity: 0;
  outline: 0;
  position: absolute;
  margin: 0;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

.el-checkbox-button,.el-checkbox-button__inner {
  position: relative;
  display: inline-block
}

.el-checkbox__label {
  font-size: 14px;
  margin-left: 5px;
  height: 16px;
  line-height: 16px;
  display: inline-block;
}

.el-checkbox-button.is-checked .el-checkbox-button__inner {
  color: #fff;
  background-color: #FF0F23;
  border-color: #FF0F23;
  box-shadow: -1px 0 0 0 #FF0F23;
}

.el-checkbox-button.is-disabled .el-checkbox-button__inner {
  color: #bfcbd9;
  cursor: not-allowed;
  background-image: none;
  background-color: #eef1f6;
  border-color: #d1dbe5;
  box-shadow: none
}

.el-checkbox-button__inner,.el-transfer-panel {
  background: #fff;
  vertical-align: middle;
  box-sizing: border-box
}

.el-checkbox-button.is-focus .el-checkbox-button__inner {
  border-color: #20a0ff
}

.el-checkbox-button:first-child .el-checkbox-button__inner {
  border-left: 1px solid #bfcbd9;
  border-radius: 4px 0 0 4px;
  box-shadow: none!important
}

.el-checkbox-button:last-child .el-checkbox-button__inner {
  border-radius: 0 4px 4px 0
}

.el-checkbox-button__inner {
  line-height: 1;
  white-space: nowrap;
  border: 1px solid #bfcbd9;
  border-left: 0;
  color: #1f2d3d;
  margin: 0;
  cursor: pointer;
  transition: all .3s cubic-bezier(.645,.045,.355,1);
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 0
}

.el-checkbox-button__inner:hover {
  color: #20a0ff
}

.el-checkbox-button__inner [class*=el-icon-] {
  line-height: .9
}

.el-checkbox-button__inner [class*=el-icon-]+span {
  margin-left: 5px
}

.el-checkbox-button__original {
  opacity: 0;
  outline: 0;
  position: absolute;
  margin: 0;
  left: -999px
}

.el-checkbox-button--large .el-checkbox-button__inner {
  padding: 11px 19px;
  font-size: 16px;
  border-radius: 0
}

.el-checkbox-button--small .el-checkbox-button__inner {
  padding: 7px 9px;
  font-size: 12px;
  border-radius: 0
}

.el-checkbox-button--mini .el-checkbox-button__inner {
  padding: 4px;
  font-size: 12px;
  border-radius: 0
}

@keyframes jdCheckboxEffect {
  0% {
      transform: scale(1);
      opacity: .5
  }

  to {
      transform: scale(1.6);
      opacity: 0
  }
}