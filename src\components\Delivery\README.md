## 📁 组件关系结构

```
🚪 index.tsx (主入口, 主要依赖主接口BundleType (/src/typings/master_api_response.d.ts)包裹信息  作为初始数据)
├── ErrorBoundary (错误边界)
└── 🌍 DeliveryProvider (配送楼层上下文)
    │
    ├── 🔀 SelectorV2 (业务路由分发器) - 当 !hasDelivery
    │   ├── 📦 DeliveryModule (配送模块容器)
    │   │   ├── 🇨🇳 国内配送分支
    │   │   │   └── 📋 Delivery2 (国内配送组件)
    │   │   │       └── 🏭 DeliveryFactory (国内配送工厂)
    │   │   │           ├── 📦 Normal (标准配送组件)
    │   │   │           ├── 🏪 Smzt (上门自提组件)
    │   │   │           ├── 🏬 Mdzt (门店自提组件)
    │   │   │           ├── ⏰ TimePicker (时间选择器)
    │   │   │           └── 🎛️ Selector2 (简单选择器)
    │   │   │
    │   │   └── 🌏 海外配送分支
    │   │       └── 🌏 DeliveryHwjy (海外配送组件)
    │   │           └── 🚛 Hwjy (海外配送子组件) - 直接使用，无工厂
    │   │
    │   ├── 🔧 InstallModule (安装服务模块)
    │   ├── 🔄 DingQiGouModule (定期购模块)
    │   │   └── 🔄 DingQiGou (定期购组件)
    │   └── ✍️ InputModule (留言输入模块)
    │
```

## 📁 目录层级架构

```
src/components/Delivery/
├── 📁 入口层 (Entry Layer)
│   ├── index.tsx                     # 🚪 主入口组件
│   ├── context.tsx                   # 🌍 全局Context状态管理
│   ├── deliveryLogger.ts             # 📊 统一日志记录工具
│   └── types.ts                      # 📋 根级类型定义
│
├── 📁 业务逻辑层 (Business Logic Layer)
│   └── mixed/
│       ├── 📁 主模块容器 (Main Modules)
│       │   └── main/
│       │       ├── DeliveryModule.tsx      # 📦 配送模块容器
│       │       ├── InstallModule.tsx       # 🔧 安装服务模块
│       │       ├── DingQiGouModule.tsx     # 🔄 定期购模块
│       │       ├── InputModule.tsx         # ✍️ 留言输入模块
│       │       ├── hooks.ts                # 🪝 模块公共Hooks
│       │       └── utils.tsx               # 🛠️ 工具函数
│       │
│       ├── 📁 业务路由分发 (Selector)
│       │   └── selector/
│       │       ├── SelectorV2.tsx          # 🔀 业务路由分发器
│       │       ├── SelectItemV2.tsx        # 📋 选择项组件
│       │       ├── SelectBody.tsx          # 📄 选择器主体
│       │       ├── initSelectData.ts       # 🔄 选择器数据初始化
│       │       └── types.ts                # 📋 选择器类型定义
│       │
│       ├── 📁 配送组件工厂 (Delivery Components Factory)
│       │   └── deliverys/
│       │       ├── dingQiGou.tsx           # 🔄 定期购组件 (独立业务模块)
│       │       │
│       │       ├── 📁 domestic/ (国内配送目录)
│       │       │   ├── delivery2.tsx       # 📋 国内配送主组件
│       │       │   ├── deliveryFactory.tsx # 🏭 国内配送工厂 (架构核心)
│       │       │   ├── normal.tsx          # 📦 标准配送组件
│       │       │   ├── smzt.tsx            # 🏪 上门自提组件
│       │       │   ├── mdzt.tsx            # 🏬 门店自提组件
│       │       │   ├── types.ts            # 📋 国内配送类型定义
│       │       │   ├── hooks/              # 🪝 国内配送专用Hooks
│       │       │   │   ├── useDeliveryState.ts     # 🔄 状态管理Hook
│       │       │   │   ├── useDeliverySave.ts      # 💾 保存逻辑Hook
│       │       │   │   └── useDeliveryAnalytics.ts # 📊 埋点分析Hook
│       │       │   └── components/         # 🎨 国内配送专用组件
│       │       │       └── DeliveryTips.tsx        # 💡 配送提示组件
│       │       │
│       │       ├── 📁 overseas/ (海外配送目录)
│       │       │   ├── deliveryHwjy.tsx    # 🌏 海外配送主组件
│       │       │   └── hwjy.tsx            # 🚛 海外配送子组件
│       │       │
│       │       └── 📁 shared/ (真正共享的组件)
│       │           ├── DeliveryDataProvider.ts # 📊 数据提供者
│       │           └── types.ts            # 📋 配送工厂类型定义
│       │
│       ├── 📁 数据处理层 (Data Processing)
│       │   ├── initDeliveryData.ts         # 🔄 配送数据初始化
│       │   ├── saveDelivery.ts             # 💾 配送保存逻辑
│       │   ├── input.tsx                   # ✍️ 输入组件
│       │   ├── types.ts                    # 📋 混合模块类型定义
│       │   └── hooks.ts                    # 🪝 通用Hooks
│       │
│       └── 📁 纯UI组件层 (UI Components Layer)
│           └── ui/
│               ├── 📁 选择器UI (Selector UI)
│               │   └── Selector/
│               │       ├── index.tsx       # 🎛️ 选择器主组件
│               │       ├── Item.tsx        # 📋 选择项组件
│               │       └── types.ts        # 📋 选择器UI类型定义
│               │
│               ├── 📁 时间选择器 (Time Selector)
│               │   └── TimeSelector/
│               │       ├── timePicker.tsx      # 📅 时间选择器
│               │       ├── timeSelector.tsx    # ⏰ 时间选择组件
│               │       ├── selector2.tsx       # 🎛️ 选择器V2
│               │       ├── PreciseTime.tsx     # 🕐 京准达精确时间
│               │       ├── addrSelector.tsx    # 📍 地址选择器
│               │       └── types.ts            # 📋 时间选择器类型
│               │
│               ├── 📁 提示组件 (Tips)
│               │   └── Tips/
│               │       ├── tips.tsx            # 💡 提示组件
│               │       └── promptText.tsx      # 📝 提示文本
│               │
│               └── 📁 基础UI组件 (Basic UI)
│                   ├── botton.tsx              # 🔘 按钮组件
│                   ├── header.tsx              # 📋 头部组件
│                   ├── input.tsx               # ✍️ 输入框组件
│                   ├── noData.tsx              # 📭 无数据组件
│                   └── skeleton.tsx            # 💀 骨架屏组件
```


### 纯业务数据处理 ts 文件
```
src/components/Delivery/
└── mixed
    ├── selector/
    │   └── initSelectData.ts             # 楼层选择器数据初始化
    ├── initDeliveryData.ts               # 配送数据初始化
    ├── saveDelivery.ts                   # 配送保存逻辑
```

#### 接口说明

主要数据依赖如下
   调用配送方式接口  balance_getBundleShipmentList_pc
   国内外自提点列表接口 balance_transportPickSiteListBundle_pc
   上门自提点时效选择列表接口 balance_getBundlePickDateList_pc
   门店自提列表接口 balance_getDeliveryStoreList_pc
   保存时效接口 balance_saveBundleShipment_pc
   定期购请求详情接口 balance_getRegularBuyPromise_pc

   主接口 BundleType 包裹字段 (/src/typings/master_api_response.d.ts)
            留言根据主接口newRemarkVOMap对象 
            配送楼层依赖主接口 balanceVendorBundleList.bundleList 字段的comShipmentTypeVO,overseasShipmentTypeVO和 deliveryInfoVO 对象展示信息


### 主要业务场景列举 (业务类型定义 /src/components/Delivery/mixed/types.ts)

45318841914 配送-门店自取
10151888612480  配送-loc
100074955690  配送-自提点自提
100051970030 配送-安装
848851  配送-同城速配
100118874251(地址选国外港澳)  海外官方直邮等等配送
100107772232  极速达
100135378662 定期购




#### 补充说明

calendarList.timeList 判断是否是大件安装那种简单时间选择
主接口 alanceVendorBundleList.bundleList deliveryInfoVO下面的promiseList如果长度为 2 表示有安装楼层