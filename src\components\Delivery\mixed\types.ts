export namespace DeliveryEnum {
  export enum DeliveryFloorType {
    SMALL_ITEM = '1', //中小件送货时间
    BIG_ITEM = '2', //大件送货时间
    BIG_ITEM_INSTALL = '3', //大件安装时间
    SMALL_ITEM_INSTALL = '4', //中小件安装时间
    PICK_ITEM = '5', //自提时效楼层名称
    DELIVERY_ITEM = '6', //门店自提
    COMBINE_ITEM = '7', //合并送货
    CONSOLIDATOR_ITEM = '8', //选择集运服务商
    ZSD_BIG_ITEM = '9', //家部重装大件送货时间
    ZSD_BIG_ITEM_INSTALL = '10', //家部重装大件安装时间
  }
  export enum PromiseEnum {
    Week = 0, //"工作日、双休日与节假日均可送货"
    BZD = 4, //标准达
    JSD = 5, //极速达
    JZD = 6, //京准达
    ZT = 8, //自提
    GSD = 7, //光速达
  }

  export enum ShipmentTypeEnum {
    SMZT = 64, //上门自提
    JZD = 99, //京尊达
    JDKD = 65, //京东快递
    DKYS = 67, //快递运输
    MDZQ = 73, //门店自提
    TCPS = 71, //同城配送
  }

  export enum HwShipmentTypeEnum {
    GFZY = 'THIRD_PARTY', //"官方直邮"
    SFHY = 'TCO', //"三方集运-海运"
    SFKY = 'TCA', //"三方集运-空运"
    SFBZ = 'TCS', //"三方集运-标准"
  }
}

export type DeliveryTypeInfo = {
  shipmentType: number
  promiseType: number
  deliveryFloorType: string
  componentType: DeliveryComponentType
}

export type DeliveryComponentType = 'simple' | 'timeList' | 'address' | 'timePicker' | 'loading' | 'hwjy' | 'smzt' | 'mdzt' | 'install'
