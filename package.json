{"name": "pc_settlement", "private": true, "version": "0.0.3", "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite --host dev.jd.com", "build": "vite build", "build:pre": "vite build && node script/pre_upload.cjs", "lint": "eslint . --fix", "preview": "vite preview"}, "packageManager": "pnpm@10.4.1", "dependencies": {"@plato/rsa": "^0.0.1", "ahooks": "^3.8.4", "axios": "^1.8.1", "buffer": "^6.0.3", "des.js": "^1.1.0", "hoist-non-react-statics": "3.3.2", "jotai": "^2.11.0", "js-cookie": "^3.0.5", "jsonp": "^0.2.1", "md5": "^2.3.0", "prop-types": "15.8.1", "query-string": "^9.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-transform-runtime": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/js": "^9.19.0", "@jd/upload-oss-tools": "1.1.23", "@types/md5": "^2.3.5", "@types/node": "^22.13.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "babel-loader": "^10.0.0", "classnames": "^2.5.1", "core-js": "^3.40.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^15.14.0", "husky": "^9.1.7", "postcss": "^8.5.2", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.1.3", "prettier": "^3.5.0", "rollup-plugin-delete": "^3.0.1", "sass-embedded": "^1.86.1", "terser": "^5.36.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-html": "^3.2.2", "vite-plugin-require-transform": "^1.0.21"}}