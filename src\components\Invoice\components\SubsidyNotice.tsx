import { FC } from 'react'
import { selectedInvoiceTypeAtom } from '@app/components/Invoice/atom/invoiceAtom'
import { useAtomValue } from 'jotai'
import { InvoiceType } from '@app/typings/invoice.d'
/**
 * 补贴通知组件
 * TODO:国补样式还未确定应该怎么做，后端和产品还有设计在沟通
 */
const SubsidyNotice: FC = () => {
  const selectedInvoiceType = useAtomValue(selectedInvoiceTypeAtom)
  // 只在电子普通发票时显示
  if (selectedInvoiceType !== InvoiceType.ELECTRONIC) {
    return null
  }

  return (
    <div className="National-subsidy" style={{ position: 'relative' }}>
      <img
        style={{ width: '14px', height: '14px' }}
        src={'//img11.360buyimg.com/ling/jfs/t1/307938/26/7386/623/6842c748F20cf29d5/f87dc63c7f521945.png'}
        alt="rightArrowIcon"
      />
      <div style={{ width: '552px', height: 'auto' }}>
        <p>
          含有国家补贴订单不支持开具专用发票及企业抬头普票
          {/* 根据现行税收政策，电子普通发票和纸质普通发票具备同等法律效力，可支持报销入账，订单完成后24小时内开具，京东自营全面启用电子普通发票，非京东自营发票由第三方商家实际开具。
          <a>电子普通发票相关信息</a> */}
        </p>
      </div>
    </div>
  )
}

export default SubsidyNotice
