import { FC, useCallback } from 'react'
import { currentInvoiceTitlesAtom, selectedInvoiceTitleAtom } from '@app/components/Invoice/atom/invoiceAtom'
import { useAtomValue } from 'jotai'
import { Form } from '@app/common/legao'
/**
 * 发票抬头选择器组件
 */
interface InvoiceTitleSelectorProps {
  onChange: (invoiceTitle: number) => void
}

const InvoiceTitleSelector: FC<InvoiceTitleSelectorProps> = ({ onChange }) => {
  // 自动根据业务场景获取正确的发票相关类型
  const invoiceTitles = useAtomValue(currentInvoiceTitlesAtom)
  const selectedTitleValue = useAtomValue(selectedInvoiceTitleAtom)
  const currentInvoiceTitles = useAtomValue(currentInvoiceTitlesAtom)?.find((item) => item.selected)
  /**
   * 处理发票抬头变化
   */
  const handleTitleChange = useCallback(
    (invoiceTitle: number) => {
      if (invoiceTitle === selectedTitleValue) return
      onChange(invoiceTitle)
    },
    [onChange, selectedTitleValue],
  )

  return (
    <Form.Item label="发票抬头" required>
      <div className="radio-group" style={{ flexGrow: 1 }}>
        {invoiceTitles?.map((title) => (
          <div
            key={title.value}
            className={`radio-button ${title.selected ? 'active' : ''}`}
            onClick={() => handleTitleChange(title.value)}
          >
            {title.content}
          </div>
        ))}
      </div>
      {/* 发票开票方式提示：当发票开票方式为空时，不显示提示 */}
      {currentInvoiceTitles && currentInvoiceTitles.descLabel && currentInvoiceTitles.descLabel !== '' && (
        <div className="invoice-note">
          {/* <img src={'//img13.360buyimg.com/ling/jfs/t1/319798/31/6255/735/683ee395Fbd94e235/be602901616ec835.png'} alt="noticeArrow" /> */}

          <span dangerouslySetInnerHTML={{ __html: currentInvoiceTitles.descLabel }}></span>
        </div>
      )}
    </Form.Item>
  )
}

export default InvoiceTitleSelector
