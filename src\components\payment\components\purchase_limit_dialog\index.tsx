import './index.scss'
import React from 'react'
import { confirm } from '../confirm_dialog'

type Wares = Partial<{
  name: string
  img: string
  desc: string
  price: string
  originalPrice: string
  count: string
}>[]

const PurchaseLimitWares: React.FC<{ wares?: Wares }> = (props) => {
  const { wares = [] } = props
  const _wares = Array.isArray(wares) ? wares : []

  return (
    <div className="purchase-limit">
      {_wares.map((ware, index) => (
        <div className="purchase-limit-item" key={index}>
          <img src={ware.img} />
          <div className="purchase-limit-item__inner">
            <div className="purchase-limit-item-name">{ware.name}</div>
            <div className="purchase-limit-item-description">{ware.desc}</div>
            <div className="purchase-limit-item-amount">
              <span className="purchase-limit-item-price">{ware.price}</span>
              <span className="purchase-limit-item-origin">{ware.originalPrice}</span>
              <span className="purchase-limit-item-count">x{ware.count}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

const confirmPurchaseLimitWares = (wares: Wares, props?: Parameters<typeof confirm>[0]) => {
  confirm({
    title: '抱歉，以下商品为限购商品，请返回购物车修改',
    content: <PurchaseLimitWares wares={wares} />,
    okText: '返回购物车',
    cancelText: '关闭',
    ...props,
  })
}

export default confirmPurchaseLimitWares
