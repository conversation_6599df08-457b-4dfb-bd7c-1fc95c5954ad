import React, { useEffect } from 'react'
/** 国补提示高度 CSS变量 */
const CSS_VARIBLE = '--payment-state-subsidy-height'

type PaymentStateSubsidyProps = {
  /** 国补提示*/
  govSubsidTips: string
  /** 未领取国补资格跳转链接*/
  unHitGovSubsidySkuJumpUrl?: string
}
const PaymentStateSubsidy: React.FC<PaymentStateSubsidyProps> = (props) => {
  const { govSubsidTips, unHitGovSubsidySkuJumpUrl } = props

  /** 监听组件挂载和卸载，设置CSS变量 用于适配付款详情模块吸顶时的高度 */
  useEffect(() => {
    document.documentElement.style.setProperty(CSS_VARIBLE, '56px')
    return () => {
      document.documentElement.style.removeProperty(CSS_VARIBLE)
    }
  }, [])

  return (
    <div className="payment-state-subsidy">
      <div className="payment-state-subsidy__text" dangerouslySetInnerHTML={{ __html: govSubsidTips }} />
      {unHitGovSubsidySkuJumpUrl && (
        <div className="payment-state-subsidy__action" onClick={() => window.open(unHitGovSubsidySkuJumpUrl)}>
          去领取
        </div>
      )}
    </div>
  )
}

export default PaymentStateSubsidy
