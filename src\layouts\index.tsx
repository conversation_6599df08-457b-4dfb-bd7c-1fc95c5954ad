/**
 * @file: index.tsx
 * @description: 页面主体布局
 */
import classNames from 'classnames'
import React, { useState, useRef, useEffect } from 'react'
import Head from './head'
import Side from './side'
import useIsNarrowScreen from '@app/hooks/useIsNarrowScreen'

/** 页面滚动方向: -1 表示向上滚动 0 表示未滚动 1 表示向下滚动 */
type ScrollDirection = -1 | 0 | 1
type LayoutProps = { children?: React.ReactNode }

/** 宽屏左右布局时aside容器的top属性值，与样式设置同步 */
const TOP = 10

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isSticky, setIsSticky] = useState<boolean>()
  const siderRef = useRef<HTMLDivElement>(null)
  const scrollDirectionRef = useRef<ScrollDirection>(0)

  useEffect(() => {
    const sider = siderRef.current
    const elevator = document.querySelector('#elevator-2024')
    let prevScrollY = window.scrollY
    handleScroll()
    window.addEventListener('scroll', handleScroll)
    elevator?.addEventListener('click', handleClick)
    return () => {
      window.removeEventListener('scroll', handleScroll)
      elevator?.removeEventListener('click', handleClick)
    }

    function handleScroll() {
      /** 页面滚动方向 */
      const scrollY = window.scrollY
      if (scrollY > prevScrollY) {
        scrollDirectionRef.current = 1
      } else if (scrollY < prevScrollY) {
        scrollDirectionRef.current = -1
      } else {
        scrollDirectionRef.current = 0
      }
      prevScrollY = scrollY
      /** 侧边栏是否sticky */
      if (!sider) return
      const { position } = window.getComputedStyle(sider)
      const { top } = sider.getBoundingClientRect()
      setIsSticky(position === 'sticky' && top <= TOP)
    }

    function handleClick(evt: Event) {
      /** 是否点击"回顶部"元素 */
      if (isBackTopElement(evt.target as HTMLElement, elevator as HTMLDivElement)) {
        scrollDirectionRef.current = -1
      }
    }
  }, [])

  /** 是否是窄屏 */
  const isNarrowScreen = useIsNarrowScreen()
  const [isIntersecting, setIsIntersecting] = useState<boolean>()

  useEffect(() => {
    const sider = siderRef.current!
    const footer = document.getElementById('footer-2024')!
    const observer = new IntersectionObserver(
      ([entry], observer) => {
        // console.log(entry, 'entry', scrollDirectionRef.current)
        const { target, isIntersecting, boundingClientRect } = entry
        if (scrollDirectionRef.current > -1 && target === footer && isIntersecting) {
          setIsIntersecting(true)
          observer.disconnect()
          observer.observe(sider)
        }

        if (scrollDirectionRef.current < 1 && target === sider && boundingClientRect.top + 10 > window.innerHeight) {
          setIsIntersecting(false)
          observer.disconnect()
          observer.observe(footer)
        }
        /** 卡页面刷新时偶现的bug */
        if (scrollDirectionRef.current > -1 && target === sider && !isIntersecting && boundingClientRect.top > window.innerHeight) {
          setIsIntersecting(false)
          observer.disconnect()
          observer.observe(footer)
        }
      },
      { root: null, threshold: 0 },
    )
    footer && observer.observe(footer)
    return () => observer.disconnect()
  }, [])

  /** 元素类名设置 */
  const nstb = { nstb: isIntersecting && isNarrowScreen }

  const list = (Array.isArray(children) ? children : [children]).filter(Boolean)
  const _children = []
  let head = null
  let side = null

  for (const child of list) {
    if (child.type === Head) {
      head = child
      continue
    }

    if (child.type === Side) {
      side = child
      continue
    }
    _children.push(child)
  }

  return (
    <div className="layout">
      <div className="header">{head}</div>
      <div className={classNames('main', { ...nstb })}>{_children}</div>
      <div className={classNames('aside', { 'is-sticky': isSticky, ...nstb })} ref={siderRef}>
        {side}
      </div>
    </div>
  )
}

Layout.displayName = 'Layout'

export default Layout

/** 是否"回顶部"元素 */
function isBackTopElement(target: HTMLElement, root: HTMLDivElement) {
  while (target && target !== root) {
    if (target.classList.contains('elevator_totop')) {
      return true
    }
    target = target.parentElement!
  }
  return false
}
