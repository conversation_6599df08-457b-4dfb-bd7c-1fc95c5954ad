/**
 * @file: DingQiGouModule.tsx
 * @description: 定期购模块，用于展示定期购等逻辑
 */
import SelectItemV2 from '../selector/SelectItemV2'
import DingQiGou from '../deliverys/dingQiGou'
import { selectDataProcess } from '../selector/initSelectData'
import { useDeliveryHooks } from './hooks'

export default function DingQiGouModule() {
  const { wrapperRef, isOpen, setIsOpen, initState } = useDeliveryHooks()

  const { dingQiGouItem } = selectDataProcess(initState.bundle)

  if (!dingQiGouItem) {
    return null
  }

  return (
    <div ref={wrapperRef}>
      <SelectItemV2 noBorder={true} canOpen={true} isOpen={isOpen} items={dingQiGouItem.items} onClick={() => setIsOpen(!isOpen)}>
        {isOpen && (
          <DingQiGou
            onCancel={() => setIsOpen(false)}
            regularBuyCalendarList={initState.bundle.regularBuyCalendarList || []}
            regularBuyPlan={initState.bundle.regularBuyPlan || ''}
            bundleUUID={initState.bundle.bundleId}
            regularBuyContentVO={initState.bundle.regularBuyContentVO || {}}
          />
        )}
      </SelectItemV2>
    </div>
  )
}
