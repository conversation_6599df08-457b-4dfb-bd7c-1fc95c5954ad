import { FC, useState } from 'react'
import InvoiceTitleSelector from './InvoiceTitleSelector'
import InvoiceContentPanel from './InvoiceContentPanel'
import { Input, Form } from '@app/common/legao'
import { ElectroInvoiceEditVO, InvoiceTitle } from '@app/typings/invoice.d'
import { useAtomValue, useSetAtom } from 'jotai'
import { invoiceCodeDescAtom, selectedInvoiceTitleAtom } from '@app/components/Invoice/atom/invoiceAtom'
import { handleInvoiceTitleChangeAtom, handleInvoicePutTypeChangeAtom } from '@app/components/Invoice/atom/invoiceAction'
import InvoicePutTypeSelector from './InvoicePutTypeSelector'
import CompanySelect from './CompanySelect'
import { electroInvoiceFields } from '@app/components/Invoice/form/formFieldsConfig'
import CustomTooltip from '@app/common/Tooltip/index'

/**
 * 电子普通发票表单组件
 */
interface ElectronicInvoiceFormProps {
  // 表单字段 - 使用Jotai状态一致的字段名
  electroInvoice: Partial<ElectroInvoiceEditVO>

  // 表单事件处理 - 使用Jotai状态一致的字段名
  onPersonalNameChange: (val: string) => void
  onElectroCompanyNameChange: (val: string) => void
  onPhoneChange: (val: string) => void
  onEmailChange: (val: string) => void
  onRegAddressChange: (val: string) => void
  onRegTelChange: (val: string) => void
  onRegBankChange: (val: string) => void
  onRegAccountChange: (val: string) => void
  onInvoiceCodeChange: (val: string) => void
}

const ElectronicInvoiceForm: FC<ElectronicInvoiceFormProps> = ({
  electroInvoice,
  onPersonalNameChange,
  onElectroCompanyNameChange,
  onPhoneChange,
  onEmailChange,
  onRegAddressChange,
  onRegTelChange,
  onRegBankChange,
  onRegAccountChange,
  onInvoiceCodeChange,
}) => {
  const [isMoreInfoExpanded, setIsMoreInfoExpanded] = useState(false)

  // 使用派生状态获取当前选中的发票抬头值
  const selectInvoiceTitle = useAtomValue(selectedInvoiceTitleAtom)
  const handleInvoiceTitleChange = useSetAtom(handleInvoiceTitleChangeAtom)
  const handleInvoicePutTypeChange = useSetAtom(handleInvoicePutTypeChangeAtom)
  const invoiceCodeDesc = useAtomValue(invoiceCodeDescAtom)
  return (
    <>
      <InvoicePutTypeSelector onChange={(invoicePutType) => handleInvoicePutTypeChange(invoicePutType)} />

      {/* 发票抬头选择 - 传递invoiceTitles和正确的onChange处理函数 */}
      <InvoiceTitleSelector onChange={(invoiceTitle) => handleInvoiceTitleChange(invoiceTitle)} />

      {/* 个人发票信息 */}
      {selectInvoiceTitle === InvoiceTitle.PERSONAL && (
        <>
          <Form.Item {...electroInvoiceFields['electroInvoice.personalName']}>
            <Input
              type="text"
              placeholder={'请填写"个人"或您的姓名'}
              value={electroInvoice.personalName || ''}
              onChange={onPersonalNameChange}
            />
          </Form.Item>

          {/* 使用发票内容面板组件 */}
          <InvoiceContentPanel hasNotBookSku={true} />

          <Form.Item {...electroInvoiceFields['electroInvoice.phone']}>
            <Input
              type="text"
              placeholder="请填写11位手机号"
              value={electroInvoice.phone || ''}
              onChange={(val: string) => {
                // 过滤非数字字符
                const numericValue = val.replace(/\D/g, '')
                onPhoneChange(numericValue)
              }}
              maxLength={11}
            />
          </Form.Item>

          <Form.Item {...electroInvoiceFields['electroInvoice.email']}>
            <Input
              type="text"
              placeholder="选填，用来接收数电票和电子发票邮件"
              value={electroInvoice.email || ''}
              onChange={onEmailChange}
            />
          </Form.Item>
        </>
      )}

      {/* 单位发票信息 */}
      {selectInvoiceTitle === InvoiceTitle.COMPANY && (
        <>
          <Form.Item {...electroInvoiceFields['electroInvoice.electroCompanyName']}>
            <CompanySelect
              value={electroInvoice.electroCompanyName}
              onChange={(company) => {
                onElectroCompanyNameChange(company.name)
                onInvoiceCodeChange(company.creditCode || electroInvoice.invoiceCode || '')
              }}
              placeholder="请填写单位"
            />
          </Form.Item>

          <Form.Item
            {...electroInvoiceFields['electroInvoice.invoiceCode']}
            label={
              <>
                纳税人识别号
                <CustomTooltip
                  content={
                    <div
                      style={{ maxWidth: '400px', padding: '8px 12px', lineHeight: '1.5', fontSize: '12px' }}
                      dangerouslySetInnerHTML={{ __html: invoiceCodeDesc }}
                    />
                  }
                  placement="top"
                  trigger="hover"
                >
                  <i className="icon-info" style={{ marginLeft: '4px', verticalAlign: 'bottom' }} />
                </CustomTooltip>
              </>
            }
          >
            <Input type="text" placeholder="请填写纳税人识别号" value={electroInvoice.invoiceCode || ''} onChange={onInvoiceCodeChange} />
          </Form.Item>

          {/* 更多信息切换按钮 */}
          <Form.Item label="更多选填项">
            <div
              className="more-info-field"
              onClick={() => setIsMoreInfoExpanded(!isMoreInfoExpanded)}
              style={{
                display: 'flex',
                alignItems: 'center',
                borderRadius: '4px',
                height: '36px',
                boxSizing: 'border-box',
                cursor: 'pointer',
              }}
            >
              <span style={{ color: '#888B94' }}>单位地址、电话、开户银行及账号</span>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <img
                  src={'//img14.360buyimg.com/ling/jfs/t1/306908/16/12689/359/685d890dFac920fe0/1d9a48ad2b1928d7.png'}
                  style={{
                    width: '10px',
                    height: '10px',
                    transform: isMoreInfoExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.3s',
                  }}
                  alt={isMoreInfoExpanded ? '收起' : '展开'}
                />
              </div>
            </div>
          </Form.Item>

          {/* 额外单位信息表单项 */}
          {isMoreInfoExpanded && (
            <>
              <Form.Item {...electroInvoiceFields['electroInvoice.regAddress']}>
                <Input type="text" placeholder="请填写单位注册地址" value={electroInvoice.regAddress || ''} onChange={onRegAddressChange} />
              </Form.Item>

              <Form.Item {...electroInvoiceFields['electroInvoice.regTel']}>
                <Input type="text" placeholder="请填写单位注册电话" value={electroInvoice.regTel || ''} onChange={onRegTelChange} />
              </Form.Item>

              <Form.Item {...electroInvoiceFields['electroInvoice.regBank']}>
                <Input type="text" placeholder="请填写单位开户银行" value={electroInvoice.regBank || ''} onChange={onRegBankChange} />
              </Form.Item>

              <Form.Item {...electroInvoiceFields['electroInvoice.regAccount']}>
                <Input type="text" placeholder="请填写单位银行账户" value={electroInvoice.regAccount || ''} onChange={onRegAccountChange} />
              </Form.Item>
            </>
          )}

          {/* 使用发票内容面板组件 */}
          <InvoiceContentPanel hasNotBookSku={true} />

          {/* 个人发票信息 */}
          <Form.Item {...electroInvoiceFields['electroInvoice.phone']}>
            <Input
              type="text"
              placeholder="请填写11位手机号"
              value={electroInvoice.phone || ''}
              onChange={(val: string) => {
                // 过滤非数字字符
                const numericValue = val.replace(/\D/g, '')
                onPhoneChange(numericValue)
              }}
              maxLength={11}
            />
          </Form.Item>

          <Form.Item {...electroInvoiceFields['electroInvoice.email']}>
            <Input
              type="text"
              placeholder="选填，用来接收数电票和电子发票邮件"
              value={electroInvoice.email || ''}
              onChange={onEmailChange}
            />
          </Form.Item>
        </>
      )}
    </>
  )
}

export default ElectronicInvoiceForm
