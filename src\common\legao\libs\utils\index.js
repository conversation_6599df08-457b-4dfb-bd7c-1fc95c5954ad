export function createPropType(validate) {
  // Chainable isRequired
  function checkType(isRequired, props, propName, componentName) {
    componentName = componentName || '<<anonymous>>'
    if (props[propName] == null) {
      if (isRequired) {
        return new Error('Required `' + propName + '` was not specified in ' + ('`' + componentName + '`.'))
      }
      return null
    } else {
      return validate(props, propName, componentName)
    }
  }

  let chainedCheckType = checkType.bind(null, false)
  chainedCheckType.isRequired = checkType.bind(null, true)

  return chainedCheckType
}

export const dom = {
  getRoot: (from) => {
    const root = (() => {
      const elements = document.getElementById('root')
      if (from) {
        let parent = 'parentElement' in from && from.parentElement
        while (parent) {
          for (let i = 0; i < elements.length; i++) {
            const element = elements[i]
            if (element === parent) {
              return element
            }
          }
          parent = parent.parentElement
        }

        for (let i = 0; i < elements.length; i++) {
          const element = elements[i]
          if ((element['ctx'] || element['__page'])?.['$taroPath'] === from['$taroPath']) {
            return element
          }
        }
      }

      return elements
    })()

    // if (process.env.TARO_ENV === 'h5') {
    //   return root
    // }

    if (!root) {
      return undefined
    }

    let element
    for (let i = 0; i < root.children.length; i++) {
      const node = root.children[i]
      if (node.getAttribute('root') === 'root') {
        element = node
        break
      }
    }
    if (!element) {
      element = document.createElement('custom-wrapper')
      element.setAttribute('root', 'root')
      root.appendChild(element)
    }
    return element
  },
  loading: (page) => {
    try {
      const root = dom.getRoot(page)
      if (!root) {
        return {
          destory: () => undefined,
        }
      }
      const target = (() => {
        const loading = root.getElementsByClassName('loading')[0]
        if (loading) {
          return loading
        }
        const element = document.createElement('div')
        element.setAttribute('class', 'loading')
        element.setAttribute('data-count', '0')
        root.appendChild(element)
        return element
      })()

      if (target.dataset) {
        target.dataset.count = `${parseInt(target.dataset.count || '0', 10) + 1}`
      }

      return {
        destory: () => {
          setTimeout(() => {
            if (target.dataset) {
              target.dataset.count = `${parseInt(target.dataset.count || '1', 10) - 1}`
              if (target.dataset.count !== '0') {
                return
              }
            }
            target.remove()
          }, 100)
        },
      }
    } catch (e) {
      return {
        destory: () => undefined,
      }
    }
  },
}
