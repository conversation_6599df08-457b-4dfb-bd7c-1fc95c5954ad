/**
 * @file: parameters.ts
 * @description: 接口公共参数处理
 */
import { hasOwn } from '@app/helpers/isPlainObject'
import { extend, isPlainObject } from '@app/helpers'
import { PAGE_QUERY_PARAMS as PQP, PAGE_FILE_NAME } from './const'
import type { MasterApiQueryParams } from '@app/typings/master_api_params'

/** 页面链接参数解析和转换 */
let giftbuy
if (PQP.flowType === '2') {
  giftbuy = true
}

let immediatelyBuy,
  supportTenBillion = false,
  agreeContinueSubmit,
  action,
  preSalePaymentTypeInOptional,
  regularBuy
const source = PQP.source ?? ''
if (source === 'common') {
  immediatelyBuy = true
} else if (source === 'bybt') {
  immediatelyBuy = true
  supportTenBillion = true
  agreeContinueSubmit = true
} else if (source === 'presale' || PAGE_FILE_NAME === 'getPresalInfo.action') {
  action = 4
  preSalePaymentTypeInOptional = 2
} else if (source === 'dqg') {
  regularBuy = true
}

let overseaMerge
if (PQP.overseaMerge === '1') {
  overseaMerge = 1
}

let hasGb
if (PQP.hasGb === '1') {
  hasGb = true
}

/** 默认的主接口参数声明 */
const DEFAULT_MASTER_API_PARAMS: MasterApiQueryParams = {
  overseaMerge,
  balanceCommonOrderForm: {
    source,
    hasGb,
    regularBuy,
    // giftbuy,
    immediatelyBuy,
    supportTenBillion,
    agreeContinueSubmit,
    overseaMerge: overseaMerge == 1 ? true : undefined,
    action,
    preSalePaymentTypeInOptional,
    /** 以下参数暂时写死 */
    useBestCoupon: true, // 除了对优惠券和切换支付方式外，其他接口调用都传true
    // giftMoney: true,
    overseasTransport: true,
    // checkJdNewRule: true,
    // invoicedNewRule: true,
    // supportCalendarExtendDaySwitch: true,
    // jdBean: true,
    // stateSubsidiesSwitch: true,
    // govSubsidyDateSwitch: true,
    supportTransfer: true,
  },
}

/** 默认参数副本，用于积累参数？ */
const MASTER_API_PARAMS = extend({}, DEFAULT_MASTER_API_PARAMS)

/**
 * getMasterApiParams
 * @param flag 为真时获取默认参数
 * @returns
 */
export const getMasterApiParams = (flag?: boolean) => {
  return flag ? DEFAULT_MASTER_API_PARAMS : MASTER_API_PARAMS
}

/**
 * setMasterApiParams
 * @param params
 * @param flag 是否积累参数
 * @returns
 */
export const setMasterApiParams = (params: MasterApiQueryParams, flag?: boolean) => {
  if (flag) {
    return extend(true, MASTER_API_PARAMS, params)
  } else {
    return extend(true, {}, DEFAULT_MASTER_API_PARAMS, params)
  }
}

/**
 * mergeParams
 * @param source
 * @returns
 */
export function mergeParams<K extends T, T extends Record<string, any> = MasterApiQueryParams>(
  source?: K,
  target: T = MASTER_API_PARAMS as T,
) {
  if (!isPlainObject(source)) {
    return target
  }
  const tmp = {} as T
  for (const key in source) {
    if (!hasOwn.call(source, key)) continue
    const val = source[key]
    if (hasOwn.call(target, key)) {
      if (isPlainObject(val)) {
        tmp[key] = mergeParams(val, target[key])
      } else {
        target[key] = val
      }
    } else {
      tmp[key] = val
    }
  }

  return extend(true, tmp, target)
}
