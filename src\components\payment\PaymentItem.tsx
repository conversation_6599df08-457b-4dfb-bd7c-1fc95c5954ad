import classNames from 'classnames'
import React, { useState, useMemo } from 'react'
import Tooltip from '@app/common/Tooltip'
import PaymentItemDetail from './PaymentItemDetail'

export type PaymentItemProps = Partial<{
  name: string
  bold: boolean
  info: React.ReactNode
  value: string | number
  children: React.ReactNode
  summary: string
  supportSkus: {
    id: number
    name: string
    imgUrl: string
  }[]
  addition: string
  enableActionIcon: boolean
  defaultShowDetail: boolean
}>

const PaymentItem: React.FC<PaymentItemProps> = (props) => {
  const { name, bold, info, value, children: _children, summary, supportSkus, addition, enableActionIcon = true, defaultShowDetail } = props
  const children = _children ? _children : supportSkus ? <PaymentItemDetail {...{ summary, supportSkus }} /> : null
  const [showDetail, setShowDetail] = useState(defaultShowDetail ?? false)
  const toggle = useMemo(() => (children ? () => setShowDetail((s) => !s) : undefined), [children])
  const isRedColor = useMemo(() => {
    if (typeof value === 'string') {
      return value.indexOf('-') > -1
    } else {
      return value && value < 0
    }
  }, [])

  return (
    <div className="payment-summary-item">
      <div className="payment-summary-item__title">
        {/* 费用名称 */}
        {bold ? <em>{name}</em> : <span>{name}</span>}
        {/* 费用名称后括号内的内容 */}
        {addition && <span>{addition}</span>}
        {/* tooltip提示 */}
        {info && (
          <Tooltip
            content={<div className="payment-method-item__tip" dangerouslySetInnerHTML={{ __html: info }} />}
            placement="top"
            width={typeof info == 'string' && info.replace(/[^\u4E00-\u9FFF]+/g,'').length > 100 ? 400 : 280}
            padding="12px"
            arrow
          >
            <i className="icon-info" />
          </Tooltip>
        )}
      </div>
      <div
        className={classNames('payment-summary-item__price', {
          'payment-summary-item__price--red': isRedColor,
          'payment-summary-item__price-icon': !!children && enableActionIcon,
          'payment-summary-item__price--r180': showDetail,
        })}
        onClick={enableActionIcon ? toggle : undefined}
      >
        {/* 费用额度 */}
        {value}
      </div>
      {/* 费用明细内容 */}
      {showDetail ? children : null}
    </div>
  )
}
PaymentItem.displayName = 'PaymentItem'
export default PaymentItem
