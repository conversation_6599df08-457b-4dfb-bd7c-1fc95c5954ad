/**
 * @file: app.tsx
 * @description: 结算页应用入口
 */
import React from 'react'
import Layout from './layouts'
import Head from './layouts/head'
import Side from './layouts/side'
import PageTitle from './components/PageTitle'
import TopTips from './components/TopTips'
import Payment from './components/payment'
import ConsigneeInfo from './components/ConsigneeInfo'
import Presale from './components/Presale'
import VirtualAsset from './components/VirtualAsset'
import Shopping from '@app/components/Shopping'
import BeautyPlusGift from '@app/components/BeautyPlusGift'
import Invoice from './components/Invoice'
import { MasterContextProvider } from './context/masterContext'
import '@app/services/event_subscibe'

const App: React.FC = () => {
  return (
    <MasterContextProvider>
      <Layout>
        <Head>
          {/* 结算页LOGO+标题 */}
          <PageTitle />
        </Head>
        {/* 顶部提示 */}
        <TopTips />
        {/* 收货人信息 */}
        <ConsigneeInfo />
        {/* 预售楼层 */}
        <Presale />
        {/* 美妆加赠 */}
        <BeautyPlusGift />
        {/* 订单信息 */}
        <Shopping />
        {/* 发票信息 */}
        <Invoice />
        {/* 虚拟资产 */}
        <VirtualAsset />
        <Side>
          {/* 付款详情 */}
          <Payment />
        </Side>
      </Layout>
    </MasterContextProvider>
  )
}

App.displayName = 'App'

export default App
