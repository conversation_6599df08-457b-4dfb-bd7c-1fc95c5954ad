.promotion {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;

  .tag {
    padding: 2px 5px;
    border-radius: 2px;
    border: 0.33px solid #ffa199;
    color: #fa2c19;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
  }

  .name {
    color: #1a1a1a;
    font-size: 14px;
    margin: 0 8px 0 0;
  }
}

/* 窗口宽度在 1440px 到 1660px 之间 */
@media (max-width: 1659px) and (min-width: 1440px) {
  .promotion {
    .name {
      max-width: 500px;
    }
  }
}

/* 窗口宽度小于 1240px */
@media (max-width: 1239px) {
  .promotion {
    .name {
      max-width: 500px;
    }
  }
}
