import { use<PERSON>tom, useSet<PERSON>tom } from 'jotai'
import {
  disclaimerCheckedAtom,
  showNoticeModalAtom,
  invoiceConsigneeEditVOAtom,
  popInvoiceAtom,
  selfInvoiceAtom,
} from '@app/components/Invoice/atom/invoiceAtom'
import {
  updateFieldAtom,
  handleInvoiceTypeChangeAtom,
  handleInvoiceTitleChangeAtom,
  handleInvoiceContentChangeAtom,
} from '@app/components/Invoice/atom/invoiceAction'

/**
 * 使用jotai实现的发票表单hook
 */
export function useInvoiceJotai() {
  // 获取对象原子
  const [selfInvoice, setSelfInvoice] = useAtom(selfInvoiceAtom)
  const [popInvoice, setPopInvoice] = useAtom(popInvoiceAtom)
  const [invoiceConsigneeEditVO, setInvoiceConsigneeEditVO] = useAtom(invoiceConsigneeEditVOAtom)

  // 获取操作原子的setter
  const updateField = useSetAtom(updateField<PERSON>tom)
  const handleInvoiceTypeChange = useSetAtom(handleInvoiceTypeChangeAtom)
  const handleInvoiceTitleChange = useSetAtom(handleInvoiceTitleChangeAtom)
  const handleInvoiceContentChange = useSetAtom(handleInvoiceContentChangeAtom)

  // UI状态
  const [disclaimerChecked, setDisclaimerChecked] = useAtom(disclaimerCheckedAtom)
  const [showNoticeModal, setShowNoticeModal] = useAtom(showNoticeModalAtom)

  return {
    // 状态
    disclaimerChecked,
    showNoticeModal,
    invoiceConsigneeEditVO,
    selfInvoice,
    popInvoice,

    // 状态更新方法
    updateField,
    setDisclaimerChecked,
    setShowNoticeModal,
    setSelfInvoice,
    setPopInvoice,
    setInvoiceConsigneeEditVO,
    // 业务方法
    handleInvoiceTypeChange,
    handleInvoiceTitleChange,
    handleInvoiceContentChange,
  }
}
