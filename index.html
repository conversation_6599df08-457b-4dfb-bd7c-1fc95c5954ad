<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link rel="icon" href="//www.jd.com/favicon.ico" type="image/x-icon" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>订单结算页 -京东商城</title>
  <!-- 公共组件：头尾、侧边栏样式  -->
  <link href="//storage.360buyimg.com/retail-mall/mall-common-component/pre/0.0.12/css/index.13a847e1.css"
    rel="stylesheet" />
  <script src="//misc.360buyimg.com/jdf/lib/jquery-1.6.4.js"></script>
  <!-- getJsToken -->
  <script src="//gias.jd.com/js/pc-tk.js?v=2024-06-20-17"></script>
  <!-- SHA256 摘要算法 -->
  <script src="//storage.360buyimg.com/retail-mall/lib/sha256-new.js?v=2024-06-20-17"></script>
  <!-- ParamsSign -->
  <script src="//storage.360buyimg.com/webcontainer/js_security_v3_0.1.6.js?v=2024-06-20-17"></script>
  <!-- 初始化脚本：为削减本HTML文件的大小，将页面中的脚本抽离到单独的文件中 -->
  <script src="/init.js"></script>
</head>
<body>
  <noscript>请您使用高版本浏览器，推荐谷歌或者火狐~</noscript>
  <div id="shortcut-2024"></div>
  <div id="root"></div>
  <div id="elevator-2024"></div>
  <%- footer %>
  <!-- 埋点数据上报 -->
  <script src="//wl.jd.com/wl.js"></script>
  <!-- 事件总线 公共组件脚本对其有引用 -->
  <script type="text/javascript" src="https://storage.360buyimg.com/retail-mall/lib/emiter.js"></script>
  <!-- 无障碍辅助功能 -->
  <script src="https://static.360buyimg.com/item/assets/oldman/wza1/aria.js?appid=bfeaebea192374ec1f220455f8d5f952"></script>
  <!-- 公共组件：头尾、侧边栏  -->
  <script src="//storage.360buyimg.com/retail-mall/mall-common-component/pre/0.0.12/js/index.13a847e1.js" defer></script>
  <!-- 滑动验证码 虚拟资产模块对其有引用 -->
  <script src="//jcap.m.jd.com/home/<USER>"></script>
  <script type="module" src="/src/main.tsx"></script>
  <script type="text/javascript">
    window.jaq = [];
    jaq.push(['autoLogPv', false]); // 关闭自动上报PV
  </script>
  <script>
    try {
      var pageid = 'JD_Trademain'
      window.JA.tracker.bloading(pageid, JSON.stringify({ view_type: 1 }))
      document.addEventListener('visibilitychange', function () {
        if (document.visibilityState === 'visible') {
          window.JA.tracker.bloading(pageid, JSON.stringify({ view_type: 2 }))
        }
      })
    } catch (e) {
      console.log('浏览上报异常', e)
    }
  </script>
</body>
</html>
