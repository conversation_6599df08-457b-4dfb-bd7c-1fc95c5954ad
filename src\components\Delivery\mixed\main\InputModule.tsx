/**
 * @file: InputModule.tsx
 * @description: 留言模块，用于展示留言等逻辑
 */
import SelectItemV2 from '../selector/SelectItemV2'
import Input from '../input'
import { useAtom } from 'jotai'
import { noteBundleIdMap } from '@app/atoms/masterAtom'
import { selectDataProcess } from '../selector/initSelectData'
import { useDeliveryHooks } from './hooks'
import { useEffect } from 'react'

export default function InputModule() {
  const { wrapperRef, isOpen, setIsOpen, initState, deliveryLogger } = useDeliveryHooks()

  const { inputItem } = selectDataProcess(initState.bundle)
  const inputState = inputItem
  const [map] = useAtom(noteBundleIdMap)
  // 留言内容动态处理
  if (map) {
    const v = map.get(initState.bundle.bundleId)
    if (v && inputState) {
      inputState.items = [inputState.items[0], { text: v.remark || '建议先与商家沟通确认', type: 'gray', data: [] }]
    }
  }

  const isEmpty = !inputState || initState.isSelfVendor

  useEffect(() => {
    if (!isEmpty) {
      deliveryLogger.messageEXPO()
    }
  }, [isEmpty])

  if (isEmpty) return null

  return (
    <div ref={wrapperRef}>
      <SelectItemV2
        onClick={() => {
          setIsOpen(!isOpen)
          deliveryLogger.messageClick()
          deliveryLogger.orddetail('2')
        }}
        canOpen={inputState.canOpen}
        items={inputState.items}
        isOpen={isOpen}
      >
        <Input onClose={() => setIsOpen(false)} />
      </SelectItemV2>
    </div>
  )
}
