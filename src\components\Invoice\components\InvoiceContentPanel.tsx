import { FC } from 'react'
import { useAtomValue } from 'jotai'
import { currentBookInvoiceContentsAtom } from '@app/components/Invoice/atom/invoiceAtom'
import InvoiceContentSelector from './InvoiceContentSelector'

interface InvoiceContentPanelProps {
  hasBookSku?: boolean
  hasNotBookSku?: boolean
}

/**
 * 发票内容面板组件
 * 根据商品类型（图书、非图书）展示不同的发票内容选择器-后端hasBook写死的，这边直接写死为true
 */
const InvoiceContentPanel: FC<InvoiceContentPanelProps> = ({ hasBookSku = false, hasNotBookSku = true }) => {
  const bookInvoiceContents = useAtomValue(currentBookInvoiceContentsAtom)
  const hasBookContents = bookInvoiceContents && bookInvoiceContents.length > 0

  return (
    <>
      {/* 普通商品发票内容 */}
      {hasNotBookSku && (
        <InvoiceContentSelector contentType="normal" customLabel={hasBookSku && hasNotBookSku ? '非图书发票内容' : '发票内容'} />
      )}

      {/* 图书商品发票内容 */}
      {hasBookSku && hasBookContents && (
        <InvoiceContentSelector contentType="book" customLabel={hasNotBookSku ? '图书发票内容' : '发票内容'} />
      )}
    </>
  )
}

export default InvoiceContentPanel
