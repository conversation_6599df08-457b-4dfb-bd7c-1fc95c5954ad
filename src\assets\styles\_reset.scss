html {
  // width: 100%;
  height: 100%;
}
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
div,
dl,
dt,
dd,
ul,
ol,
li,
p,
blockquote,
pre,
hr,
figure,
table,
caption,
th,
td,
form,
fieldset,
legend,
input,
button,
textarea,
menu {
  margin: 0;
  padding: 0;
}
header,
footer,
section,
article,
aside,
nav,
hgroup,
address,
figure,
figcaption,
menu,
details {
  display: block;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
caption,
th {
  text-align: left;
  font-weight: normal;
}
html,
body,
fieldset,
img,
iframe,
abbr {
  border: 0;
}
i,
cite,
em,
var,
address,
dfn {
  font-style: normal;
}
[hidefocus],
summary {
  outline: 0;
}
li {
  list-style: none;
}
h1,
h2,
h3,
h4,
h5,
h6,
small {
  font-size: 100%;
}
sup,
sub {
  font-size: 83%;
}
pre,
code,
kbd,
samp {
  font-family: inherit;
}
q:before,
q:after {
  content: none;
}
textarea {
  overflow: auto;
  resize: none;
}
label,
summary {
  cursor: default;
}
a,
button {
  cursor: pointer;
}
h1,
h2,
h3,
h4,
h5,
h6,
em,
strong,
b {
  font-weight: bold;
}
del,
ins,
u,
s,
a,
a:hover {
  text-decoration: none;
}

body {
  width: 100%;
  height: 100%;
}
a,
a:hover {
  color: #333;
}

input::-webkit-input-placeholder {
  color: #c9c9c9;
}
input:focus {
  outline: none;
}

:root {
  --fontSize: 26px;
  --sFontSize: 24px;
  --lFontSize: 30px;
  --xlFontSize: 34px;
  --fontWeight: 400;
  --iconSize: 26px;
  --sIconSize: 18px; //
  --iconSizeW: 30px;
  --clamp: 3;
  --lineHeight: 37px;
  --block: inline;
  --paddingLeft: 0;

  --custom-color-black: #1a1a1a;
  --custom-color-black2: #505259;
  --custom-color-black3: #7f7f7f;
  --custom-color-grey: #C2C4CC;
  --custom-color-grey1: #888B94;
  --custom-color-red: #FF0F23;
}

/*  火狐浏览器 */
@-moz-document url-prefix() {
  * {
    scrollbar-width: thin;
    scrollbar-color: #C2C4CC transparent;
    ::-moz-scrollbar {
      width: 6px;
    }
  }
}
@supports (-moz-appearance: none) {
  * {
    scrollbar-width: thin;
    scrollbar-color: #C2C4CC transparent;
    ::-moz-scrollbar {
      width: 6px;
    }
  }
}

/*  滚动条整体部分，可以设置宽度 */
::-webkit-scrollbar {
  width: 6px; /* 纵向滚动条宽度 */
  height: 6px; /* 横向滚动条高度 */
  background-color: #fff;
}

/* 定义滚动条轨道 内阴影+圆角 */
::-webkit-scrollbar-track {
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  /*border-radius: 6px;*/
  /*background-color: #f5f5f5;*/
}

/* 定义滑块 内阴影+圆角 */
::-webkit-scrollbar-thumb {
  cursor: pointer;
  border-radius: 3px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #C2C4CC;
}
