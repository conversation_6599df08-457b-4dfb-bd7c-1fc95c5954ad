/**
 * @file: useDeliveryAnalytics.ts
 * @description: 配送埋点相关钩子，用于处理配送埋点相关逻辑
 */
import { useCallback, useRef, useEffect } from 'react'
import { DeliveryDataIniter, promiseToComponentData } from '../../../initDeliveryData'
import { PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp'
import { DeliveryEnum } from '../../../types'
import { useFirstRenderLogger } from '../../../hooks'
import { List as HeaderList } from '../../../../ui/header'
import { ComShipmentType } from '@app/typings/api_getBundleShipmentList_resp'

export function useDeliveryAnalytics(
  deliveryDataIniter: DeliveryDataIniter,
  headerList: HeaderList<ComShipmentType, PromiseListItem>,
  promise: PromiseListItem | null,
  isEmpty: boolean,
) {
  const log = useFirstRenderLogger(deliveryDataIniter.isInstall)
  const hasUp = useRef(false)

  const up = useCallback(() => {
    const toptabname = headerList?.map((item) => item.text) || []
    const second_tab_name = headerList?.find((item) => item.isSelected)?.sonList.map((item) => item.text) || []

    let date: string[] = []
    let shipmentTitle: string[] = []
    let transportation_expenses: any[] = []

    if (promise) {
      if (promise?.promiseType === DeliveryEnum.PromiseEnum.JZD) {
        second_tab_name.push('配送时间精确到: 2 小时')
      }

      const deliveryTypeInfo = deliveryDataIniter.getDeliveryTypeInfo(promise)
      const componentDataMaker = new promiseToComponentData()

      if (deliveryTypeInfo?.componentType === 'timeList') {
        const list = componentDataMaker.getTimeList(promise) || []
        date = list?.map((item) => item.text) || []
        shipmentTitle = list?.find((item) => item.isSelected)?.items?.map((i) => i.textList.reduce((v, p) => v + p.text, '')) || []
        transportation_expenses =
          (list?.find((item) => item.isSelected)?.items?.map((i) => i.targetValue?.freight) || []).filter((i) => i) || []
      }

      if (deliveryTypeInfo?.componentType === 'timePicker') {
        const list = componentDataMaker.getSimpleTimeList(promise) || []
        list?.forEach((item) => {
          item.items.forEach((i) => {
            date.push(i.text)
          })
        })
      }

      if (deliveryTypeInfo?.componentType === 'simple') {
        const list = componentDataMaker.getSimple(promise) || []
        date.push(list.displayedString)
      }
    }

    log.shipmentPOPExpo({
      toptabname,
      second_tab_name,
      date,
      shipmentTitle,
      transportation_expenses,
      button_name: ['0', '1'],
    })
  }, [deliveryDataIniter, headerList, promise, log])

  useEffect(() => {
    if (deliveryDataIniter.isInstall) {
      return
    }
    if (hasUp.current) {
      return
    }
    if (!isEmpty && !promise) {
      return
    }
    hasUp.current = true
    try {
      console.log(promise, 'isEmpty')
      up()
    } catch (e) {
      console.log(e)
    }
  }, [deliveryDataIniter.isInstall, isEmpty, promise, up])

  return { log }
}
