import { FC, useEffect, useMemo, useRef, useState, useCallback } from 'react'
import InvoiceDialog from './Modal/InvoiceDialog'
import InvoiceTypeSelector from './components/InvoiceTypeSelector'
import ElectronicInvoiceForm from './components/ElectronicInvoiceForm'
import SpecialInvoiceForm from './components/SpecialInvoiceForm'
import InvoiceProductList from './components/InvoiceProductList'
import InvoiceDisclaimer from './components/InvoiceDisclaimer'
import { useInvoiceJotai } from './hooks/useInvoiceJotai'
import { useInvoiceQuery } from './hooks/useInvoiceQuery'
import InvoiceNoticeModal from './Modal/InvoiceNoticeModal'
import { Form } from '@app/common/legao'
import { useAtomValue, useAtom } from 'jotai'
import {
  activeInvoiceTabAtom,
  selectedInvoiceType<PERSON>tom,
  selectedInvoiceTitle<PERSON>tom,
  specialInvoiceStep<PERSON>tom,
  currentInvoice<PERSON>tom,
  selectedInvoice<PERSON>tatus<PERSON>tom,
  isBpin<PERSON>tom,
  popInvoice<PERSON>tom,
  selfInvoice<PERSON>tom,
  isFormModifiedAtom,
  isSelfMedicineCheckedAtom,
} from '@app/components/Invoice/atom/invoiceAtom'
import NormalInvoiceForm from './components/NormalInvoiceForm'

import InvoiceSkeleton from './skeleton/InvoiceContentSkeleton'
import './InvoiceModel.scss'
import DefaultInvoiceCheckbox from './components/DefaultInvoiceCheckbox'
import { generateFormRules } from './form/validationRules'
import { useInvoiceSave } from './hooks/userInvoiceSave'
import showToast from '../payment/components/toast'
import { InvoiceType, VenderType } from '@app/typings/invoice.d'
import { InvoiceLayerPointParams, InvoiceLogger } from './InvoiceLogger'
import useMasterData from '@app/hooks/useMasterData'
import { BalanceInvoiceDesc } from '@app/typings/master_api_response'
import useIntersectionObserverOnce from '@app/hooks/useIntersectionObserverOnce'

/**
 * 发票类型选择弹窗组件
 */
export interface InvoiceTypeModalProps {
  isOpen: boolean
  onClose: () => void
}

/**
 * 发票内容组件 - 使用jotai自动获取状态
 */
interface InvoiceContentProps {
  isOpen: boolean
  onClose: () => void
}

// 定义FormRefType类型
interface FormRefType {
  validate: (callback: (valid: boolean) => void) => void
  validateField: (field: string) => void
  resetFields: () => void
  state?: {
    fields: Array<{
      props: {
        prop: string
      }
      validate: (trigger: string, callback?: (errors?: any) => void) => void
      resetField: () => void
      setState: (state: { error?: string; valid?: boolean }) => void
    }>
  }
}

/**
 * 发票内容组件
 */
const InvoiceContent: FC<InvoiceContentProps> = ({ isOpen, onClose }) => {
  const { disclaimerChecked, showNoticeModal, setDisclaimerChecked, setShowNoticeModal, handleInvoiceTypeChange, updateField } =
    useInvoiceJotai()

  // 获取所有可能需要的状态
  const [specialInvoiceStep, setSpecialInvoiceStep] = useAtom(specialInvoiceStepAtom)
  const isBpin = useAtomValue(isBpinAtom)
  const popInvoice = useAtomValue(popInvoiceAtom)
  const selfInvoice = useAtomValue(selfInvoiceAtom)
  const masterData = useMasterData()
  // 使用派生状态获取当前选中的发票类型和抬头
  const selectedInvoiceType = useAtomValue(selectedInvoiceTypeAtom)
  const selectedInvoiceTitle = useAtomValue(selectedInvoiceTitleAtom)
  // 从全局状态获取发票编辑视图数据和业务场景信息
  const currentInvoice = useAtomValue(currentInvoiceAtom)
  const selectedInvoiceStatus = useAtomValue(selectedInvoiceStatusAtom)
  const isSelfMedicineChecked = useAtomValue(isSelfMedicineCheckedAtom)
  // 表单引用
  const formRef = useRef<FormRefType>(null)
  // 获取当前活动的标签页
  const [activeInvoiceTab, setActiveInvoiceTab] = useAtom(activeInvoiceTabAtom)

  // 引入发票查询hook, 传入updateInvoiceState函数
  const { fetchInvoiceInfo } = useInvoiceQuery()
  const { handleSave: saveInvoice } = useInvoiceSave()

  // 添加加载状态
  const [loading, setLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  // const [viewportWidth, setViewportWidth] = useState(window.innerWidth)
  const [viewportHeight, setViewportHeight] = useState(window.innerHeight)
  const [isFormModified, setIsFormModified] = useAtom(isFormModifiedAtom)

  // 使用返回的处理函数
  const handleSave = useCallback(async () => {
    if (isSaving) return
    if (formRef.current) {
      formRef.current.validate((valid: boolean) => {
        // 验证自营药品是否勾选
        if (!isSelfMedicineChecked) {
          showToast({ title: `请阅读并勾选确认相关法律法规要求` }, { wrapperStyle: { zIndex: 9999 }, position: 'center-center' })
          return
        }
        // 验证未通过时，直接返回，不保存
        if (!valid) return
        // 验证通过后，设置保存状态为true
        setIsSaving(true)
        saveInvoice(selectedInvoiceStatus)
          .then(() => {
            // 保存成功后重置表单修改状态
            setIsFormModified(false)
          })
          .finally(() => {
            // 保存之后，不管成功还是失败都需要重置状态
            setTimeout(() => {
              setIsSaving(false)
              onClose()
              setSpecialInvoiceStep(1)
            }, 1000)
          })
      })
    }
  }, [isSaving, saveInvoice, onClose, selectedInvoiceStatus, setSpecialInvoiceStep, setIsFormModified, isSelfMedicineChecked])

  /**
   * 切换tab保存切换前tab下的发票信息，
   * eg.自营商品tab切换到其他商品tab，客户端保存自营商品tab下的发票信息；
   * 切换tab校验当前发票信息是否填写完成，如果用户没有完成当前页面发票信息的填写，
   * 弹toast提示同时不允许用户切换tab
   * 流程优化：
   * 如果没有进行修改表单则不进行保存操作，直接切换tab
   */
  const handleInvoiceTabChange = useCallback(
    (tab: number) => {
      if (tab === activeInvoiceTab) return
      if (isSaving) return
      if (!formRef.current) return
      // 验证自营药品是否勾选
      if (!isSelfMedicineChecked) {
        showToast({ title: `请阅读并勾选确认相关法律法规要求` }, { wrapperStyle: { zIndex: 9999 }, position: 'center-center' })
        return
      }

      // 如果表单未修改，直接切换tab
      if (!isFormModified) {
        formRef.current.validate((valid: boolean) => {
          if (!valid) {
            showToast({ title: `请填写完整发票信息` }, { wrapperStyle: { zIndex: 9999 }, position: 'center-center' })
            return
          }
        })
        setActiveInvoiceTab(tab)
        setSpecialInvoiceStep(1)
        return
      }

      formRef.current.validate((valid: boolean) => {
        // 验证未通过时，直接返回，不保存, 弹toast提示同时不允许用户切换tab
        if (!valid) {
          showToast({ title: `请填写完整发票信息` }, { wrapperStyle: { zIndex: 9999 }, position: 'center-center' })
          return
        }
        setIsSaving(true)
        saveInvoice(activeInvoiceTab)
          .then(() => {
            // 保存发票失败不切换tab
            // setActiveInvoiceTab(tab)
            // 保存成功后重置表单修改状态
            setIsFormModified(false)
            setSpecialInvoiceStep(1)
          })
          .catch((error) => {
            console.error('保存发票失败:', error)
            setSpecialInvoiceStep(1)
          })
          .finally(() => {
            // 保存之后，不管成功还是失败都切换tab
            setIsSaving(false)
          })
        setActiveInvoiceTab(tab)
      })
    },
    [
      isSaving,
      isFormModified,
      activeInvoiceTab,
      isSelfMedicineChecked,
      setIsSaving,
      saveInvoice,
      setIsFormModified,
      setActiveInvoiceTab,
      setSpecialInvoiceStep,
    ],
  )
  // 元素露出50%，进行曝光
  const observerRef = useIntersectionObserverOnce(async () => {
    await new Promise<void>((resolve) => {
      setTimeout(() => resolve(), 500)
    })
    const skuInfoList = popInvoice.skuInfoList?.concat(selfInvoice.skuInfoList || []) || []
    const balanceInvoiceDesc = masterData?.body?.balanceInvoiceDesc || ({} as BalanceInvoiceDesc)
    await Promise.resolve().then(() => {
      // 添加发票楼层曝光埋点
      InvoiceLogger.invoiceExpo('invoiceLayerExpo', {
        clickPos: '2',
        invoice_detail: 'skuInfoList:' + JSON.stringify(skuInfoList) + 'balanceInvoiceDesc:' + JSON.stringify(balanceInvoiceDesc),
      } as InvoiceLayerPointParams)
    })
  })

  // 使用ref跟踪弹窗上一次的打开状态
  const prevOpenRef = useRef(false)
  useEffect(() => {
    // 只在弹窗从关闭变为打开时获取数据
    if (isOpen && !prevOpenRef.current) {
      const loadInvoiceInfo = async () => {
        try {
          setLoading(true)
          await fetchInvoiceInfo()
          // 加载完成后重置表单修改状态
          setIsFormModified(false)

          setLoading(false)
        } catch (error) {
          console.error('获取发票信息失败:', error)
          setTimeout(() => {
            setLoading(false)
            onClose()
            showToast({ title: `获取发票信息失败` }, { wrapperStyle: { zIndex: 9999 }, position: 'center-center' })
          }, 1500)
        }
      }
      loadInvoiceInfo()
    }
    // 更新ref的值
    prevOpenRef.current = isOpen
  }, [
    isOpen,
    isBpin,
    popInvoice,
    selfInvoice,
    masterData,
    currentInvoice,
    selectedInvoiceType,
    selectedInvoiceStatus,
    selectedInvoiceTitle,
    onClose,
    fetchInvoiceInfo,
    setIsFormModified,
  ])

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setViewportHeight(window.innerHeight)
      // setViewportWidth(window.innerWidth)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  // 处理专用发票下一步逻辑
  const handleSpecialInvoiceNext = () => {
    // 第一步：验证单位信息
    if (selectedInvoiceType === InvoiceType.VAT && specialInvoiceStep === 1) {
      // 使用Form验证
      validateForm().then(
        () => {
          // 验证通过，切换到第二步
          setSpecialInvoiceStep(2)
        },
        () => {
          // 验证失败，保持在第一步
          console.log('表单验证失败，请检查必填字段')
        },
      )
    }
    InvoiceLogger.invoiceEvent('invoiceLayer', {
      clickPos: '1',
      invoice_detail: '',
    } as InvoiceLayerPointParams)
  }

  /**
   * 关闭开票须知弹窗
   */
  const handleCloseNoticeModal = () => {
    setShowNoticeModal(false)
    InvoiceLogger.invoiceEvent('invoiceLayer', {
      clickPos: '2',
      invoice_detail: '',
    } as InvoiceLayerPointParams)
  }

  /**
   * 取消发票弹窗
   */
  const handleCancel = () => {
    onClose()
    // 添加发票弹窗取消埋点
    InvoiceLogger.invoiceEvent('invoiceLayer', {
      clickPos: '0',
      invoice_detail: '',
    } as InvoiceLayerPointParams)
  }

  // 验证表单方法
  const validateForm = () => {
    return new Promise<void>((resolve, reject) => {
      if (!formRef.current) {
        resolve()
        return
      }

      formRef.current.validate((valid: boolean) => {
        if (valid) {
          resolve()
        } else {
          reject(new Error('表单验证失败'))
        }
      })
    })
  }

  /**
   * 点击开票须知
   */
  const handleNoticeClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setShowNoticeModal(true)
  }

  // 获取当前表单类型对应的验证规则
  const formRules = useMemo(() => {
    if (selectedInvoiceType === InvoiceType.ELECTRONIC) {
      return generateFormRules('electroInvoice')
    } else if (selectedInvoiceType === InvoiceType.NORMAL) {
      return generateFormRules('normalInvoice')
    } else if (selectedInvoiceType === InvoiceType.VAT) {
      // 专用发票根据步骤生成不同的验证规则
      return generateFormRules('vat', specialInvoiceStep)
    } else if (selectedInvoiceType === InvoiceType.E_VAT) {
      return generateFormRules('eleVat')
    }
    return {}
  }, [selectedInvoiceType, specialInvoiceStep])

  const formSize = useMemo(() => {
    return {
      width: 724,
      height: Math.round(viewportHeight * 0.9) > 900 ? 900 : Math.round(viewportHeight * 0.9),
    }
  }, [viewportHeight])

  // 渲染发票内容
  const renderInvoiceContent = () => {
    return (
      <Form ref={formRef} model={currentInvoice} className="invoice-form" inline={false} rules={formRules} labelWidth={130}>
        {/* 国家补贴信息提示-暂时隐藏，文案采用其他方式进行展示 */}
        {/* <SubsidyNotice /> */}

        {/* 商品列表 */}
        <InvoiceProductList activeTab={activeInvoiceTab} />

        <div className="invoice-form-section" ref={observerRef()}>
          <InvoiceTypeSelector onChange={(invoiceType) => handleInvoiceTypeChange(invoiceType)} />
          {/* 电子普通发票表单 - 仅在选择电子普通发票(3)时显示 */}
          {selectedInvoiceType === InvoiceType.ELECTRONIC && (
            <ElectronicInvoiceForm
              electroInvoice={currentInvoice.electroInvoice || {}}
              onInvoiceCodeChange={(val) => updateField({ field: 'electroInvoice.invoiceCode', value: val })}
              onPersonalNameChange={(val) => updateField({ field: 'electroInvoice.personalName', value: val })}
              onElectroCompanyNameChange={(val) => {
                updateField({ field: 'electroInvoice.electroCompanyName', value: val })
              }}
              onPhoneChange={(val) => updateField({ field: 'electroInvoice.phone', value: val })}
              onEmailChange={(val) => updateField({ field: 'electroInvoice.email', value: val })}
              onRegAddressChange={(val) => updateField({ field: 'electroInvoice.regAddress', value: val })}
              onRegTelChange={(val) => updateField({ field: 'electroInvoice.regTel', value: val })}
              onRegBankChange={(val) => updateField({ field: 'electroInvoice.regBank', value: val })}
              onRegAccountChange={(val) => updateField({ field: 'electroInvoice.regAccount', value: val })}
            />
          )}
          {/* 普通发票表单 - 仅在选择普通发票(1)时显示 */}
          {selectedInvoiceType === InvoiceType.NORMAL && (
            <NormalInvoiceForm
              normalInvoice={currentInvoice.normalInvoice || {}}
              onCompanyNameChange={(val) => {
                updateField({ field: 'normalInvoice.companyName', value: val })
              }}
              onInvoiceCodeChange={(val) => updateField({ field: 'normalInvoice.invoiceCode', value: val })}
              onPersonalNameChange={(val) => updateField({ field: 'normalInvoice.personalName', value: val })}
              onRegAddressChange={(val) => updateField({ field: 'normalInvoice.regAddress', value: val })}
              onRegTelChange={(val) => updateField({ field: 'normalInvoice.regTel', value: val })}
              onRegBankChange={(val) => updateField({ field: 'normalInvoice.regBank', value: val })}
              onRegAccountChange={(val) => updateField({ field: 'normalInvoice.regAccount', value: val })}
            />
          )}

          {/* 专用发票表单 - 仅在选择增值税专用发票(2)或电子专用发票(22)时显示 */}
          {(selectedInvoiceType === InvoiceType.VAT || selectedInvoiceType === InvoiceType.E_VAT) && (
            <SpecialInvoiceForm
              specialInvoiceStep={specialInvoiceStep}
              vat={currentInvoice.vat || {}}
              onVatCompanyNameChange={(val) => updateField({ field: 'vat.companyName', value: val })}
              onVatRegAddrChange={(val) => updateField({ field: 'vat.regAddr', value: val })}
              onVatRegPhoneChange={(val) => updateField({ field: 'vat.regPhone', value: val })}
              onVatRegBankChange={(val) => updateField({ field: 'vat.regBank', value: val })}
              onVatRegBankAccountChange={(val) => updateField({ field: 'vat.regBankAccount', value: val })}
              onVatEmailChange={(val) => updateField({ field: 'vat.email', value: val })}
              onConsigneeNameChange={(val) => updateField({ field: 'vat.invoiceConsigneeEditVO.consigneeName', value: val })}
              onConsigneePhoneChange={(val) => updateField({ field: 'vat.invoiceConsigneeEditVO.phone', value: val })}
              onConsigneeAddressChange={(val) => updateField({ field: 'vat.invoiceConsigneeEditVO.address', value: val })}
              onVatCodeChange={(val) => updateField({ field: 'vat.code', value: val })}
            />
          )}
          {/* 自营商品默认发票选项 */}
          <DefaultInvoiceCheckbox />
          {/* 自营药品-单位开票时的免责声明 */}
          <InvoiceDisclaimer disclaimerChecked={disclaimerChecked} onDisclaimerChange={setDisclaimerChecked} />
        </div>
      </Form>
    )
  }

  // 渲染标签页头部
  const renderTabsHeader = () => {
    // 混单状态下不显示发票信息以及开票须知
    if (selectedInvoiceStatus !== VenderType.MIX) {
      return (
        <>
          <div className="invoice-header-tabs">
            <h3>发票</h3>
            <div className="invoice-info-notice" onClick={handleNoticeClick}>
              开票须知
              <i className="icon-info" style={{ marginLeft: '4px' }} />
            </div>
          </div>
        </>
      )
    } else {
      return (
        <div className="invoice-header-tabs">
          <div
            className={`invoice-header-tab ${activeInvoiceTab === VenderType.SELF ? 'active' : ''}`}
            onClick={() => handleInvoiceTabChange(VenderType.SELF)}
          >
            自营商品发票
            {activeInvoiceTab === VenderType.SELF && <div className="header-tab-indicator"></div>}
          </div>
          <div
            className={`invoice-header-tab ${activeInvoiceTab === VenderType.POP ? 'active' : ''}`}
            onClick={() => handleInvoiceTabChange(VenderType.POP)}
          >
            其他商品发票
            {activeInvoiceTab === VenderType.POP && <div className="header-tab-indicator"></div>}
          </div>
        </div>
      )
    }
  }
  //渲染底部按钮
  const renderFooter = () => {
    return (
      <>
        {/* 在专用发票第二步时显示"返回"按钮替代"取消"按钮 */}
        {selectedInvoiceType === InvoiceType.VAT && specialInvoiceStep === 2 ? (
          <button
            className="btn btn-default btn-middle invoice-btn"
            onClick={() => {
              // 清除第二步相关字段的验证错误
              if (formRef.current && formRef.current.state?.fields) {
                formRef.current.state.fields.forEach((field) => {
                  // 只清除收票人相关字段的验证错误
                  if (field.props.prop && field.props.prop.includes('invoiceConsigneeEditVO')) {
                    field.setState({ error: '', valid: true })
                  }
                })
              }
              setSpecialInvoiceStep(1)
            }}
          >
            返回
          </button>
        ) : (
          <button className="btn btn-default btn-middle invoice-btn" onClick={handleCancel}>
            取消
          </button>
        )}
        {/* 加入保存状态，避免重复保存 */}
        {selectedInvoiceType === InvoiceType.ELECTRONIC ||
        selectedInvoiceType === InvoiceType.E_VAT ||
        selectedInvoiceType === InvoiceType.NORMAL ? (
          <button className="btn btn-primary btn-middle invoice-btn" onClick={handleSave} disabled={isSaving}>
            {isSaving ? '保存中...' : '保存'}
          </button>
        ) : specialInvoiceStep === 1 ? (
          <button className="btn btn-primary btn-middle invoice-btn" onClick={handleSpecialInvoiceNext} disabled={isSaving}>
            下一步
          </button>
        ) : (
          <button className="btn btn-primary btn-middle invoice-btn" onClick={handleSave} disabled={isSaving}>
            {isSaving ? '保存中...' : '保存'}
          </button>
        )}
      </>
    )
  }

  return (
    <>
      {loading ? (
        // 加载中时显示骨架屏弹窗，传入onClose属性
        <InvoiceSkeleton onClose={onClose} width={formSize.width} height={formSize.height} />
      ) : (
        // 加载完成后显示实际弹窗
        <InvoiceDialog
          isOpen={isOpen}
          onClose={onClose}
          title="发票"
          width={formSize.width}
          height={formSize.height}
          headerTabs={renderTabsHeader()}
          className="invoice-model"
          footer={renderFooter()}
        >
          {renderInvoiceContent()}
        </InvoiceDialog>
      )}
      <InvoiceNoticeModal isOpen={showNoticeModal} onClose={handleCloseNoticeModal} />
    </>
  )
}

/**
 * 发票类型选择弹窗组件
 */
const InvoiceModal: FC<InvoiceTypeModalProps> = ({ isOpen, onClose }) => {
  // 当组件不显示时不渲染内容，防止状态初始化冲突
  if (!isOpen) return null
  return <InvoiceContent isOpen={isOpen} onClose={onClose} />
}

export default InvoiceModal
