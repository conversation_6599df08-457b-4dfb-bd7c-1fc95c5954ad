export const debounce = <T extends (...args: any[]) => any>(func: T, delay: number = 200, immediate: boolean = false) => {
  let timer: ReturnType<typeof setTimeout> | null = null
  return (...args: Parameters<T>) => {
    if (timer) {
      clearTimeout(timer)
    }
    if (immediate) {
      const callNow = !timer
      timer = setTimeout(() => {
        timer = null
      }, delay)
      if (callNow) {
        func(...args)
      }
    } else {
      timer = setTimeout(() => {
        func(...args)
      }, delay)
    }
  }
}

// export function debounce<T extends (...args: any[]) => any>(fn: T): (...args: Parameters<T>) => Promise<ReturnType<T>> {
//   let pending: Promise<ReturnType<T>> | undefined
//   return function (...args: Parameters<T>): Promise<ReturnType<T>> {
//     if (!pending) {
//       pending = new Promise<ReturnType<T>>((resolve) => {
//         Promise.resolve().then(() => {
//           pending = undefined
//           resolve(fn(...args))
//         })
//       })
//     }
//     return pending
//   }
// }
