.time_selector {
  margin-top: 12px;
  width: 449px;
  height: 300px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;

  .left {
    overflow-y: scroll;
    overflow-x: hidden;
    width: 156px;
    height: 300px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    gap: 0px;

    .item {
      width: 156px;
      min-height: 44px;
      //padding: 5px 18px;
      display: flex;
      flex-direction: row;
      align-items: center;
      // justify-content: center;
      padding-left: 25px;
      font-size: 14px;
      font-weight: 600;
      text-align: center;
      cursor: pointer;
    }

    .item_selected {
      color: #FF0F23;
      div{
        color: #FF0F23!important;
      }
    }
  }

  .line {
    //width: 1px;
    height: 300px;
    border: 1px solid rgba(0, 0, 0, 0.06);
  }

  .right {
    width: 268px;
    height: 300px;
    overflow: scroll;
    // overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 8px;

    .item {
      cursor: pointer;
      width: 260px;
      min-width: 100px;
      min-height: 36px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10px;
      padding-left: 16px;
      border-radius: 4px;
      font-size: 14px;
      line-height: 14px;
      letter-spacing: 0px;
      text-align: center;

      .none {
        font-size: 14px;
      }

      .gray {
        font-size: 14px;
        color: #999;
      }
    }

    .item_selected {
      color: red;
      border: 0.5px solid #FF8595;
      font-weight: 700;
      background: #FFF7F9;

    }
  }
}

.time_picker {
  margin: 12px 0;
  width: 580px;
  //height: 212px;
  max-height: 312px;
  overflow: scroll;
  overflow-x: hidden;
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  background: #FFFFFF;
  display: flex;

  .item {
    width: 83px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .item2 {
      cursor: pointer;
      box-sizing: border-box;
      width: 82.29px;
      height: 44px;
      display: flex;
      flex-direction: row;
      flex-grow: 1;
      align-items: center;
      justify-content: center;
      gap: 4px;
      background: #FFFFFF;
      border: .5px solid rgba(0, 0, 0, 0.06);
      ;
    }

    .item_selected {
      background: #FFEBEF;
      color: #FF0F23;

    }

    .week {
      width: 82.29px;
      height: 36px;
      display: flex;
      flex-direction: row;
      flex-grow: 1;
      align-items: center;
      justify-content: center;
      gap: 4px;
      background: #F7F8FC;
    }

    //height: 36px;
  }
}

.addr_wrap {
  cursor: pointer;
  box-sizing: border-box;

  .tab_wrap {
    width: 448px;
    height: 50px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    margin-bottom: 20px;
    margin-top: 20px;

    .tab {
      height: 50px;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 6px;
      box-sizing: border-box;
      border: 1px solid rgba(0, 0, 0, 0.06);
      border-radius: 8px;
      background: #F7F8FC;
      overflow: hidden;

      // box-shadow: 0px 12px 24px rgba(0, 0, 0, 0.1);
      .top {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 600;
        line-height: 14px;
      }

      .bottom {
        font-size: 12px;
        padding:0 6px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 12px;
        white-space: nowrap; /* 禁止文字换行 */
        overflow: hidden; /* 隐藏超出容器的内容 */
        text-overflow: ellipsis; /* 超出部分用省略号表示 */
        width: 100%; /* 确保容器宽度足够 */;
        text-align:center
      }
    }

    .selected {
      color: #FF0F23;
      background: #FFF7F9;
      border-color: #FF0F23;
    }
  }

  .addr_select_wrap {
    margin-bottom: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    .label {
      color: #1A1A1A;
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 400;
      line-height: 14px;
      text-align: right;
      padding-right: 12px;
    }

    .input_wrap {
      flex: 1;
    }
  }


  .addr_list_wrap {
    overflow: hidden;
    overflow-y: scroll;
    max-height: 400px;
    .item_wrap {
      cursor: pointer;
      width: 448px;
      height: 94px;
      padding: 16px 16px 16px 12px;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: flex-start;
      gap: 12px;
      box-sizing: border-box;
      border: 1px solid rgba(0, 0, 0, 0.06);
      border-radius: 8px;
      background: #FFFFFF;
      margin-bottom: 20px;
      margin-top: 10px;
  
      >.left {
        width: 18px;
        height: 18px;
        box-sizing: border-box;
        // border: 1px solid #BFBFBF;
        // border-radius: 10px;
        // background: #FFFFFF;
      }
  
      >.middle {
        box-sizing: border-box;
  
        width: 330px;
        overflow: hidden;
        height: 62px;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        align-items: flex-start;
        justify-content: flex-start;
        gap: 8px;
  
        >.top {
          width: 390px;
          height: 18px;
          display: flex;
          flex-direction: row;
          flex-grow: 1;
          align-items: center;
          justify-content: space-between;
          gap: 236px;
          color: var(--色彩变量 Color/文本 Text/color-title, #1A1A1A);
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 600;
          line-height: 18px;
          letter-spacing: 0px;
        }
  
        >.middle {
          width: 100%;
          height: 14px;
          white-space: nowrap; /* 禁止文字换行 */
          overflow: hidden; /* 隐藏超出容器的内容 */
          text-overflow: ellipsis; /* 超出部分用省略号表示 */
          // display: flex;
          flex-direction: row;
          flex-grow: 1;
          align-items: flex-start;
          justify-content: flex-start;
          gap: 4px;
          color: #888B94;
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 14px;
          letter-spacing: 0px;
        }
  
        >.bottom {
          width: 390px;
          height: 14px;
          display: flex;
          flex-direction: row;
          flex-grow: 1;
          align-items: flex-start;
          justify-content: flex-start;
          gap: 4px;
          color: #888B94;
          font-size: 14px;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 14px;
          letter-spacing: 0px;
        }
      }
  
      >.right {
        width: 108px;
        height: 18px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;
  
        color: #BFBFBF;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 14px;
        letter-spacing: 0px;
      }
    }
    .time_picker_wrap {
      max-width: 450px;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap; // 添加这行实现自动换行
  
      justify-content: flex-start;
      gap: 12px;
      margin-top: 12px;
      border-style: solid;
      border-color: rgba(0, 0, 0, 0.06);
      border-width: 0px 0px 0px 0px;
      .item{
        cursor: pointer;
        width: 138px;
        height: 36px;
        // padding: 9px 16px;
        //padding: 9px 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 4px;
        box-sizing: border-box;
        border: 0.5px solid rgba(0, 0, 0, 0.06);
        border-radius: 4px;
        background: #F7F8FC;
        color: #1F1F1F;
        font-size: 14px;
        font-family: 'JDZhengHei V2.0';
        font-weight: 400;
        line-height: 14px;
        letter-spacing: 0px;
        box-sizing: border-box;
      }
      .item_selected{
        font-weight: 700;
        color: #FF0F23;
        border: 1px solid #FF0F23;
        background: #FFF7F9;
      }
    }
  }
}


.precise{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 20px;
  font-size: 14px;
  padding-left: 16px;
  .item{
    cursor: pointer;
    width: 106px;
    height: 36px;
    //padding: 9px 32px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 4px;
    box-sizing: border-box;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background: #F7F8FC;

    color: #1F1F1F;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 14px;
    letter-spacing: 0px;
    text-align: center;
  }
  .item_selected{
    color: #FF0F23;
    border: 1px solid #FF0F23;
    background: #FFF7F9;
  }
}


.skeleton_wrap {
  .skeleton-container {
    max-width: 500px;
    margin: 0 auto;
    background-color: white;
    border-radius: 8px;
    padding: 20px;
  }
  
  /* 骨架屏基础样式 */
  .skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
  }
  
  @keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  .skeleton-text {
    height: 16px;
    width: 100%;
    margin-bottom: 10px;
  }
  
  .skeleton-text.small {
    width: 70%;
  }
  
  .skeleton-image {
    height: 190px;
    width: 100%;
    margin: 15px 0;
  }
  
  .skeleton-button {
    height: 40px;
    width: 120px;
  }
  
  .skeleton-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin: 15px 0;
  }
  
  .skeleton-grid-item {
    height: 80px;
  }
}