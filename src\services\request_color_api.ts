/**
 * @file: request_color_api.ts
 * @description: 请求color网关的封装，包括网关参数、业务公参、数据加固、异常处理，前端监控上报
 */
import axios from 'axios'
import Cookie from 'js-cookie'
import { mergeParams } from './parameters'
import { COLOR_API_ORIGIN, HAS_COMMON_PARAMS_APIS } from './const'
import { handleBizException, handleNetException } from './exception'
import type { AxiosRequestConfig } from 'axios'
import type { ExtraColorApiParams, ColorApiParams } from '@app/typings/color_api'
import reportException from './report_exception'

const _axios = axios.create({
  baseURL: COLOR_API_ORIGIN,
  // timeout: 1e4,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
})

_axios.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

_axios.interceptors.response.use(
  (response) => {
    const code = response.data?.code
    if (`${code}` !== '0') {
      handleBizException(response)
      // return Promise.reject(
      //   new axios.AxiosError(
      //     response.data.message || response.data.echo || '网络走失了，刷新试试',
      //     code,
      //     response.config,
      //     response.request,
      //     response,
      //   ),
      // )
    }

    return response
  },
  (error) => {
    handleNetException(error)
    return Promise.reject(error)
  },
)

type AvailableMethods = 'get' | 'post' | 'POST' | 'GET'
type Data = Record<string, any>

interface RequestConfig<D = any> extends AxiosRequestConfig<D> {
  method?: AvailableMethods
  params?: ExtraColorApiParams
  data?: D // color网关的body参数写这里
  __encrypt?: boolean // 是否接口加固
}

function validateMethod(method?: string): method is AvailableMethods {
  return ['get', 'post'].includes((method || '').toLowerCase())
}

export default async function request_color_api<T = unknown, D extends Data = Data>(config: RequestConfig<D>) {
  const { method, params, __encrypt = true } = config
  config.url = config.url ?? '/api'
  config.params = getColorParams(params) as ColorApiParams

  if (!validateMethod(method)) {
    config.method = 'get'
  } else {
    config.method = method.toLowerCase() as AvailableMethods
  }

  // if (HAS_COMMON_PARAMS_APIS.includes(config.params.functionId)) {
  config.data = mergeParams(config.data) as D
  // }

  if (__encrypt) {
    delete config.__encrypt
    await buildSignParams(config)
      .then((signParams) => {
        config.params = {
          ...config.params,
          ...signParams,
        } as ColorApiParams
      })
      .catch((error) => {
        reportException({ 'buildSignParams error': error })
      })
  }

  const { data } = config
  if (data && typeof data === 'object') {
    if (config.method === 'post') {
      config.data = { body: JSON.stringify(data) } as unknown as D
    } else {
      config.data = void 0
      config.params = {
        ...config.params,
        body: JSON.stringify(data),
      }
    }
  }

  return _axios<T>(config)
}

function getColorParams(extraParams?: ExtraColorApiParams) {
  return {
    appid: 'pctrade-core',
    client: 'pc',
    clientVersion: '1.0.0',
    uuid: Cookie.get('__jda') || '',
    loginType: '3',
    t: +new Date(),
    ...extraParams,
  }
}

function getJsTokenAsync() {
  const promise = new Promise<{ jsToken: string; fp: string }>(function (resolve, reject) {
    if (window.getJsToken) {
      window.getJsToken(resolve, 200)
    } else {
      reject(new Error('getJsToken is not defined'))
    }
  })
  return promise
}

async function buildSignParams(config: RequestConfig) {
  const { params, data } = config
  const { appid, functionId, client, clientVersion, t } = params as ColorApiParams
  const wantParams: Partial<ColorApiParams> = { appid, functionId, client, clientVersion, t }
  if (data) {
    wantParams.body = window.SHA256(JSON.stringify(data))
  }
  const { h5st } = await window.PSign.sign(wantParams)
  const { jsToken } = await getJsTokenAsync()
  return { 'x-api-eid-token': jsToken, h5st }
}
