/**
 * @file: event_subscibe.ts
 * @description: 网络层异常处理
 */
import { LOGIN_URL, CART_URL } from './const'
import { GlobalEventEmitter } from '@app/helpers'
import { confirm } from '@app/components/payment/components/confirm_dialog'
import showToast from '@app/components/payment/components/toast'

GlobalEventEmitter.on('NO_LOGIN', () => {
  window.location.href = LOGIN_URL
})

let isLocked = false
GlobalEventEmitter.on('CART_EMPTY', () => {
  if (isLocked) return
  isLocked = true
  confirm({
    title: '您的购物车为空',
    description: '请先去购物车中添加商品',
    // cancelText: '取消',
    okText: '去购物车',
    onOk: () => {
      isLocked = false
      window.location.href = CART_URL
    },
  })
})

GlobalEventEmitter.on('SHOW_TOAST', (value: string) => {
  showToast({ title: value })
})
