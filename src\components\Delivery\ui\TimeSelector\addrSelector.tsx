import Checkbox from '@app/common/checkbox'
import styles from './index.module.scss'
import { useEffect, useReducer } from 'react'
// import Form from '@app/common/legao/Form/Form'
import { Area } from '@app/components/ConsigneeInfo/components'
import { UiAddrSelector } from './types'
import { Component } from 'react'
import NoData from '../noData'
import { Tips2 } from '../Tips/tips'

const AddrCard = <T,>(props: UiAddrSelector.addInfo<T> & { isChecked: boolean; onChange: (v: boolean) => void }) => {
  return (
    <div className={styles.item_wrap} onClick={() => !props.isDisabled && props.onChange(props.isChecked)} style={{}}>
      <div className={styles.left}>
        {!props.isDisabled && <Checkbox disabled={props.isDisabled} label={''} checked={props.isChecked} onChange={(e) => e} />}
      </div>
      <div className={styles.middle}>
        <div className={styles.top}>
          <div style={{ lineHeight: '18px', overflow: 'hidden', height: '18px', fontSize: '14px' }}>
            {props.closest && (
              <span
                style={{
                  color: '#FF0F23',
                  marginRight: '7px',
                  padding: '0px 3px',
                  border: '1px solid #FFD6E1',
                  borderRadius: '5px',
                  background: '#FFEBF1',
                  boxSizing: 'border-box',
                  fontSize: '12px',
                  display: 'inline-block',
                  height: '18px',
                }}
              >
                最近
              </span>
            )}
            {props.isDisabled && (
              <span
                style={{
                  color: '#FFFFFF',
                  marginRight: '7px',
                  padding: '0px 3px',
                  border: '1px solid #CCCCCC',
                  borderRadius: '5px',
                  background: '#CCCCCC',
                  boxSizing: 'border-box',
                  fontSize: '12px',
                  display: 'inline-block',
                  height: '18px',
                }}
              >
                无货
              </span>
            )}
            <span style={{ height: '18px', boxSizing: 'border-box', display: 'inline' }}>{props.pickName}</span>
          </div>
        </div>
        <div className={styles.middle}>地址:{props.pickAddress}</div>
        {!props.pickPhone && props.businessHours && (
          <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }} className={styles.bottom}>
            营业时间:{props.businessHours}
          </div>
        )}
        {!props.businessHours && props.pickPhone && (
          <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }} className={styles.bottom}>
            联系方式:{props.pickPhone}
          </div>
        )}
      </div>
      <div className={styles.right} style={{ minWidth: '100px' }}>
        {(!String(props.distance).startsWith('0') || String(props.distance).startsWith('0.')) && (
          <>
            <img
              width={'14px'}
              height={'14px'}
              src="https://img12.360buyimg.com/img/jfs/t1/288587/3/4857/1013/685cc674F600e57dc/57c3ba8168cd3ad8.png"
            />
            <div style={{ fontSize: '14px', color: '#888B94' }}>{props.distance.replace(' ', '')}</div>
          </>
        )}
      </div>
    </div>
  )
}

const Loading = () => {
  return (
    <div className={styles.skeleton_wrap}>
      <div className={styles['skeleton-container']}>
        <div style={{ height: '80px', width: '400px', marginTop: '0px' }} className={`${styles.skeleton} ${styles['skeleton-text']}`}></div>
        <div
          style={{ height: '80px', width: '400px', marginTop: '40px' }}
          className={`${styles.skeleton} ${styles['skeleton-text']}`}
        ></div>
        <div
          style={{ height: '80px', width: '400px', marginTop: '40px' }}
          className={`${styles.skeleton} ${styles['skeleton-text']}`}
        ></div>
      </div>
    </div>
  )
}

type State = {
  currentTab: 'addr' | 'time'
  selectAddrIndex: number
  selectTimeIndex?: number
}
type Action = {
  type: 'setCurrentTab' | 'setSelectAddrIndex' | 'setSelectTimeIndex' | 'initTimeList' | 'initAddr'
  tab?: 'addr' | 'time'
  addrIndex?: number
  timeIndex?: number
}

const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case 'setCurrentTab':
      return {
        ...state,
        currentTab: action.tab || 'addr',
      }
    case 'setSelectAddrIndex':
      return {
        ...state,
        selectAddrIndex: action.addrIndex ?? 0,
      }
    case 'setSelectTimeIndex':
      return {
        ...state,
        selectTimeIndex: action.timeIndex,
      }
    case 'initAddr':
      return {
        ...state,
        selectAddrIndex: action.addrIndex && action.addrIndex >= 0 ? action.addrIndex : 0,
      }
    case 'initTimeList':
      return {
        ...state,
        selectTimeIndex: action.timeIndex && action.timeIndex >= 0 ? action.timeIndex : 0,
      }
  }
  return { ...state }
}
interface YourComponentProps {
  // 其他属性
  children?: React.ReactNode
}
class ErrorBoundary extends Component<YourComponentProps> {
  state = {
    hasError: false,
  }

  static getDerivedStateFromError() {
    return { hasError: true }
  }

  render() {
    if (this.state.hasError) {
      return null
    }

    return this.props.children
  }
}

export default function AddrSelector<T, U>({ initState, onChange, onAreaChange }: UiAddrSelector.Props<T, U>) {
  const { hasAddrInput, addrList, pickTimeList, inLoading, noDataStr = '暂无数据' } = initState

  const [state, dispatch] = useReducer<(state: State, action: Action) => State, State>(
    reducer,
    {
      currentTab: 'addr',
      selectAddrIndex: 0,
      selectTimeIndex: undefined,
    },
    (s) => s,
  )
  const selectedAddrStr = addrList[state.selectAddrIndex]?.pickAddress || ''
  let selectedTimeStr = ''
  if (pickTimeList && pickTimeList.length > 0 && state.selectTimeIndex !== undefined) {
    selectedTimeStr = (pickTimeList[state.selectTimeIndex]?.date || '') + (pickTimeList[state.selectTimeIndex]?.weekName || '')
  }

  useEffect(() => {
    onChange({
      addr: getAddrTargetData(),
      time: getTimeTargetData() || null,
    })
    console.log(state, 'addrstate')
  }, [state])

  useEffect(() => {
    console.log(addrList, 'addrList')

    const setClosest = () => {
      function convertToMeters(distanceStr: string) {
        const value = parseFloat(distanceStr)
        if (distanceStr.includes('千米')) {
          return value * 1000
        } else if (distanceStr.includes('米')) {
          return value
        }
        return 0
      }
      const map = new Map<UiAddrSelector.addInfo<any>, number>()
      addrList?.forEach((item) => {
        map.set(item, convertToMeters(item.distance))
      })
      // 找出值最小的元素
      let minItem: UiAddrSelector.addInfo<any> | null = null
      let minDistance = Infinity
      map.forEach((distance, item) => {
        if (distance < minDistance) {
          minDistance = distance
          minItem = item
        }
      })
      if (minItem) {
        ;(minItem as { closest: boolean }).closest = true
      }
    }
    setClosest()
    dispatch({ type: 'initAddr', addrIndex: addrList.findIndex((item) => item.isSelected) })
  }, [addrList])

  useEffect(() => {
    const i = pickTimeList?.findIndex((item) => item.isSelected) || 0
    dispatch({ type: 'initTimeList', timeIndex: i < 0 ? 0 : i })
  }, [pickTimeList])

  console.log(state, 'addrState')

  const getAddrTargetData = () => {
    return addrList[state.selectAddrIndex]?.targetData
  }

  const getTimeTargetData = () => {
    if (state.selectTimeIndex === undefined || !pickTimeList) {
      return undefined
    }
    return pickTimeList[state.selectTimeIndex]?.targetData
  }

  const areaValue = `${initState.areaValue?.provinceId || ''}-${initState.areaValue?.cityId || ''}`

  const getAddrTsx = () => {
    if (inLoading) {
      return <Loading />
    }
    if (addrList && addrList.length === 0) {
      return <NoData styles={{ width: '100%', height: '200px' }} text={noDataStr} />
    }
    return addrList.map((item, index) => {
      return (
        <AddrCard
          key={index}
          {...item}
          isChecked={state.selectAddrIndex === index}
          onChange={() => dispatch({ type: 'setSelectAddrIndex', addrIndex: index })}
        />
      )
    })
  }

  return (
    <div className={styles.addr_wrap}>
      {pickTimeList && (
        <div className={styles.tab_wrap}>
          <div
            onClick={() => {
              dispatch({ type: 'setCurrentTab', tab: 'addr' })
            }}
            className={`${styles.tab} ${state.currentTab === 'addr' ? styles.selected : ''}`}
          >
            <div className={styles.top}>1.选择自提地点</div>
            <div className={styles.bottom}>{selectedAddrStr}</div>
          </div>
          <div
            onClick={() => {
              dispatch({ type: 'setCurrentTab', tab: 'time' })
            }}
            className={`${styles.tab} ${state.currentTab === 'time' ? styles.selected : ''}`}
          >
            <div className={styles.top}>2.选择自提时间</div>
            <div className={styles.bottom}>{selectedTimeStr}</div>
          </div>
        </div>
      )}
      <ErrorBoundary>
        {hasAddrInput && (
          <div className={styles.addr_select_wrap}>
            <div className={styles.label}>
              <span style={{ color: 'red' }}>*</span>详细地址
            </div>
            <div className={styles.input_wrap}>
              {
                // delveryState.state.selectState?.delivery?.isOpen &&
                onAreaChange && initState.areaValue && (
                  <Area
                    topClassName="jd-delivery"
                    showWaitData="all"
                    value={areaValue}
                    writeCookie={false}
                    placement={'bottom'}
                    isUpScroll={false}
                    scopeLevel={3}
                    openMunicipality={false}
                    reLevel={false}
                    onChange={(e, full) => {
                      onAreaChange(full)
                    }}
                  />
                )
              }
            </div>
          </div>
        )}
      </ErrorBoundary>

      <div className={styles.addr_list_wrap}>
        {state.currentTab === 'addr' && getAddrTsx()}
        {state.currentTab === 'time' && pickTimeList && (
          <div className={styles.time_picker_wrap}>
            {pickTimeList.map((item, index) => {
              const getMonthAndDay = (dateString: string) => {
                const date = new Date(dateString)
                if (!isNaN(date.getTime())) {
                  const month = String(date.getMonth() + 1).padStart(2, '0')
                  const day = String(date.getDate()).padStart(2, '0')
                  return `${month}-${day}`
                }
                return ''
              }
              const dateStr = getMonthAndDay(item.date) || item.date
              const isNotTime = !getMonthAndDay(item.date) && pickTimeList.length === 1
              return (
                <div
                  // onClick={() => 0}
                  key={index}
                  className={`${styles.item} ${index === state.selectTimeIndex ? styles.item_selected : ''}`}
                  onClick={() => dispatch({ type: 'setSelectTimeIndex', timeIndex: index })}
                  style={isNotTime ? { width: '100%', padding: '8px', lineHeight: '20px', height: 'auto' } : {}}
                >
                  <span>{dateStr}</span> <span style={index === state.selectTimeIndex ? {} : { color: '#888B94' }}>{item.weekName}</span>
                </div>
              )
            })}
          </div>
        )}
      </div>
      {state.currentTab === 'time' && (
        <>
          <div style={{ paddingTop: '20px' }}></div>{' '}
          <Tips2
            title="温馨提示："
            contexts={[
              '1、您选择的时间可能会因库存不足等因素导致订单延迟，请您谅解！',
              '2、我们会在您选定提货日期的前一天处理您的订单，在此之前您的订单处于暂停状态。',
            ]}
          />
        </>
      )}
    </div>
  )
}
