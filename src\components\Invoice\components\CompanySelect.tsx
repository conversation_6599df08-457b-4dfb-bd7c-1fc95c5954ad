import React, { useState } from 'react'
import { Select } from '@app/common/legao'
import { searchCredit } from '@app/services/api'
import { CompanyInvoiceInfoDO } from '@app/typings/invoice.d'

interface CompanySelectProps {
  // 当前输入值
  value?: string
  // 值变化回调
  onChange?: (company: { name: string; creditCode: string }) => void
  // 是否禁用
  disabled?: boolean
  // 占位符文本
  placeholder?: string
  // Select组件样式
  style?: React.CSSProperties
  // 最小搜索字符数
  minSearchLength?: number
  // 允许清除选项
  allowClear?: boolean
  // 是否允许直接创建条目（自定义输入）
  allowCreate?: boolean
  // 输入框失焦回调
  onBlur?: () => void
  // 输入框聚焦回调
  onVisibleChange?: () => void
}

/**
 * 公司名称下拉搜索组件
 * 基于Select组件，提供企业名称搜索联想功能
 * 支持自定义输入
 */
const CompanySelect: React.FC<CompanySelectProps> = ({
  value,
  onChange,
  disabled = false,
  placeholder = '请填写公司名称',
  style,
  minSearchLength = 2,
  allowCreate = true,
  onBlur,
  onVisibleChange,
}) => {
  const [companyOptions, setCompanyOptions] = useState<CompanyInvoiceInfoDO[]>([])
  const [loading, setLoading] = useState(false)

  /**
   * 移除HTML标签的辅助函数
   */
  const removeHtmlTags = (html: string) => {
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html
    return tempDiv.textContent || tempDiv.innerText || ''
  }

  /**
   * 搜索企业联想信息
   */
  const handleSearchCompany = async (queryString: string) => {
    if (!queryString || queryString.length < minSearchLength) {
      setCompanyOptions([])
      return
    }

    try {
      setLoading(true)
      const response = await searchCredit({ queryString })
      if (response && response.body && response.body.length > 0) {
        // 使用后端返回的完整数据
        setCompanyOptions(response.body)
      } else {
        // 如果没有匹配结果，提供空数据
        setCompanyOptions([])
      }
    } catch (error) {
      console.error('企业名称搜索失败:', error)
      setCompanyOptions([])
    } finally {
      setLoading(false)
    }
  }

  /**
   * 处理选择公司
   */
  const handleSelectCompany = (selectedValue: string) => {
    // 查找选中的公司数据
    const selectedCompany = companyOptions.find((item) => {
      const cleanName = removeHtmlTags(item.entName || '')
      return cleanName === selectedValue || item.entName === selectedValue
    })

    if (onChange) {
      onChange({
        name: selectedCompany ? removeHtmlTags(selectedCompany.entName || '') : selectedValue,
        creditCode: selectedCompany?.creditCode || '',
      })
    }
  }

  return (
    <Select
      className="company-name-select"
      value={value}
      filterable
      reserveKeyword
      remote
      onChange={handleSelectCompany}
      remoteMethod={handleSearchCompany}
      loading={loading}
      disabled={disabled}
      placeholder={placeholder}
      style={{ width: '100%', ...style }}
      autoComplete="off"
      onBlur={onBlur}
      onVisibleChange={onVisibleChange}
    >
      {companyOptions.map((item) => (
        <Select.Option
          key={item.creditCode || item.entName}
          label={removeHtmlTags(item.entName || '')}
          value={removeHtmlTags(item.entName || '')}
          style={{ height: '50px' }}
        >
          <div>
            <div className="is-ellipsis" style={{ fontWeight: 'bold' }} dangerouslySetInnerHTML={{ __html: item.entName || '' }} />
            <div className="is-ellipsis" style={{ fontSize: 12, color: '#888B94' }}>
              {item.creditCode || '无纳税人识别号'}
            </div>
          </div>
        </Select.Option>
      ))}
      {/* 如果允许创建且没有匹配项，添加当前输入作为选项 */}
      {allowCreate && value && companyOptions.length === 0 && (
        <Select.Option key="custom" label={value} value={value} style={{ height: '50px' }}>
          <div>
            <div className="is-ellipsis" style={{ fontWeight: 'bold' }}>
              {value}
            </div>
            <div className="is-ellipsis" style={{ fontSize: 12, color: '#888B94' }}>
              无纳税人识别号
            </div>
          </div>
        </Select.Option>
      )}
    </Select>
  )
}

export default CompanySelect
