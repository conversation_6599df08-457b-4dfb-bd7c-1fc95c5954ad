import React from 'react'
import getImgUrl from '@app/utils/images'

type Props = Partial<{
  summary: string
  supportSkus: {
    id: number
    name: string
    imgUrl: string
  }[]
}>

const PaymentItemDetail: React.FC<Props> = ({ summary, supportSkus }) => {
  supportSkus = (Array.isArray(supportSkus) ? supportSkus : []).filter(Boolean)
  return (
    <div className="payment-summary-item__detail">
      <div className="payment-summary-item__detail-desc">{summary}</div>
      <div className="payment-summary-item__detail-items">
        {supportSkus.map((sku, index) => {
          const imgUrl = sku?.imgUrl
          return (
            imgUrl && (
              <div key={index}>
                <img src={getImgUrl(imgUrl)} alt={sku?.name} title={sku?.name} />
              </div>
            )
          )
        })}
      </div>
    </div>
  )
}
PaymentItemDetail.displayName = 'PaymentItemDetail'
export default PaymentItemDetail
