export type SuccessApiQueryParams = Partial<{
  pin: string
  fq: string
  overseaMerge: string
  isInternational: string
  orderType: string
  jc: boolean
  enterpriseBuy: string
}>

// 通用响应参数
export type SuccessApiResponse = Partial<{
  /**错误码*/
  code: string
  /**错误信息*/
  message: string
  /**服务器时间*/
  timestamp: number
  /**响应数据*/
  body: OrderSuccessVO
  /**pfinder的tradeId*/
  traceId: string
  /**color的requestId*/
  requestId: string
}>

// 响应数据
export interface OrderSuccessVO {
  /**是否跳转*/
  goPay?: boolean
  /**跳转地址*/
  payJumpUrl?: string
  /** 非支付跳转地址 */ 
  notPayJumpUrl?: string
  /**成功页展示的model*/
  successInfoShowView?: SuccessInfoShowView
}

// 成功页展示的model
export interface SuccessInfoShowView {
  promptType?: string
  /**父订单号*/
  orderId: number
  /**子单信息*/
  childOrderInfoDTOList?: ChildOrderInfoDTO[]
  /**条件提示*/
  tipCondition?: TipCondition
  /**是否有混合支付*/
  mixPay: boolean
  /**得到商品详细信息，在成功页面统计时候使用,格式(skuId:num,skuId2:num2,可以有多个)*/
  orderDetail?: string
  /**所有商品id，多个以','隔开*/
  skuIds?: string
  /**是否显示下载电子书连接*/
  showDownBook: boolean
  /**再次购买按钮URL*/
  rebuyButtonUrl?: string
}

// 子单信息
export interface ChildOrderInfoDTO {
  /**订单号*/
  orderId: number
  /**订单类型*/
  orderType: number
  /**支付方式id*/
  paymentId: number
  /**支付方式名称*/
  paymentName?: string
  /**订单价格*/
  price?: string
  /**在线支付的URL*/
  onlinePayUrl?: string
  /**订单详情页面Url*/
  orderDetailUrl?: string
  /**配送方式列表*/
  shipmentInfoList?: ShipmentInfo[]
  /**获取支付方式类型*/
  paymentNameById?: string
}

// 配送信息
export interface ShipmentInfo {
  /**配送方式id(1:京东快寄，2:快寄运输，3:自提点)*/
  shipmentType: number
  /**是否是预约配送*/
  promise: boolean
  /**预约配送日期*/
  promiseDate?: string
  /**工作日配送,节假日配送*/
  codeTimeDesc?: string
  /**配送时间类型*/
  codeTime: number
  /**配送方式名称*/
  shipmentName?: string
  /**自提信息*/
  pickSiteInfo?: PickSiteInfo
}

// 自提信息
export interface PickSiteInfo {
  /**自提点id*/
  pickSiteId: number
  /**自提点名称*/
  pickSiteName?: string
  /**自提点地址*/
  pickSiteAddress?: string
  /**自提点地图连接*/
  pickSiteUrl?: string
  /**自提日期*/
  pickDate?: string
}

//条件提示
export interface TipCondition {
  /**大家电提示*/
  bigItemTip: boolean
  /**支票方式提示*/
  useCheckTip: boolean
  /**食品商品提示*/
  foodTip: boolean
  /**自提点物品提示*/
  pickSiteTip: boolean
  /**LBP订单提示*/
  orderTypeLBPTip: boolean
  /**货到付款提示*/
  afterPayTip: boolean
}
