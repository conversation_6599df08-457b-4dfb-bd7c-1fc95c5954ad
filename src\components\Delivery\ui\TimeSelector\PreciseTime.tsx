import styles from './index.module.scss'
export default ({
  onClick,
  selectList,
}: {
  onClick?: (s: { promiseTagName: string; promiseTagType: string; selected: boolean }) => void
  selectList: { promiseTagName: string; promiseTagType: string; selected: boolean }[]
}) => {
  return (
    <div style={{ marginTop: '10px' }} className={`${styles.precise}`}>
      <div> 配送时间精确到 </div>
      {selectList.map((item) => (
        <div
          key={item.promiseTagType}
          onClick={() => onClick && onClick(item)}
          className={`${styles.item} ${item.selected ? styles.item_selected : ''}`}
        >
          {item.promiseTagName}
        </div>
      ))}
    </div>
  )
}
