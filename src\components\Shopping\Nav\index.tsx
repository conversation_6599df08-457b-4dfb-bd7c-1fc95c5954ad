/**
 * 顶部导航  标题｜价格说明｜返回购物车
 */
import Popover from '../components/Popover'
import scope from './index.module.scss'
import { BalanceSkuPriceDescType, PageLinkConfigType } from '@app/typings/master_api_response'

interface IProps {
  balanceSkuPriceDesc?: BalanceSkuPriceDescType
  pageLinkConfig?: PageLinkConfigType
}

const Nav: React.FC<IProps> = ({ pageLinkConfig, balanceSkuPriceDesc }) => {
  // 返回购物车 跳转地址后端配置
  const backCart = () => {
    location.href = pageLinkConfig?.backToCartLink || 'https://cart.jd.com/cart_index'
  }

  return (
    <div className={`${scope.nav} flex-center-between`}>
      <div className={scope.title}>订单信息</div>
      <div className={`${scope.extra} flex-center`}>
        <div className={`${scope.price} flex-end`}>
          <Popover
            content={
              <div>
                {balanceSkuPriceDesc?.bodyInformation}
                <br />
                {balanceSkuPriceDesc?.contactInformation}
              </div>
            }
            placement="top"
            trigger="hover"
          >
            <div className="flex-center">
              <span>价格说明</span>
              <i className="icon-info"></i>
            </div>
          </Popover>
        </div>
        <div className={scope.line}></div>
        <div className={scope['go-cart']} onClick={backCart}>
          返回购物车
        </div>
      </div>
    </div>
  )
}

export default Nav
