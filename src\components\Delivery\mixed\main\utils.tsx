import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import Selector2 from '../../ui/TimeSelector/selector2'
import <PERSON>tton from '../../ui/botton'

export function getDefaultTsx(showStr: string, targetData: any, onClick: () => void) {
  monitoring({
    name: monitorName.Settlement,
    code: monitorCode.Delivery,
    msg: {
      type: '配送模块',
      targetData,
      showStr,
      info: '显示默认值, defaultTsx',
    },
  })
  return (
    <div style={{ paddingTop: '0px' }}>
      <br />
      <Selector2 showStr={showStr} />
      <p style={{ color: '#1A1A1A', textAlign: 'center', fontSize: '16px', fontWeight: 400, lineHeight: '20px', marginBottom: '20px' }}>
        服务繁忙，请稍后重试～{' '}
      </p>
      <Botton onSure={onClick} onCancel={onClick} />
    </div>
  )
}
