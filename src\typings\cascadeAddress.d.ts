/**
 * 级联地址接口相关数据
 */
declare namespace AddressStandard {
  /**
   * 级联地址接口下发的数据结构
   */
  export interface CascadeAddress {
    /**
     * 降级控制数组
     */
    abResult?: IABResult[]
    areas: Array<Areas>
    hotOverseaAreas: Array<Areas>
    hotMainlandAreas: Array<Areas>
    topAreas: Array<Areas>
    otherAreas: Array<Areas>
    resultFlag: boolean
    /**
     * 字母归堆列表
     */
    wordCodeResult: Array<{
      /**
       * 所属字母
       */
      key: string
      /**
       * 所属字母地区列表
       */
      value: Array<Partial<Areas>>
    }>
    /**
     * 侧边字母快速定位数据
     */
    wordList: Array<string>
  }

  /**
   * 地区数据
   */
  export interface Areas {
    /** 是否请求下一级地区数据 */
    showNext?: boolean
    addrId: number
    diaoYuDao: boolean
    foreignOverSea: boolean
    gangAoTai: boolean
    isSupCOD: boolean
    levelType: number
    name: string
    areaCode: string
    nameCode: string
    parentId: number
    parentName: string
    sortCode: string
    active?: boolean
  }
}
