/**
 * 地址列表接口相关：
 * functionId：address_cmpnt_queryAddress
 * 接口文档：https://joyspace.jd.com/pages/juIeAtsBEZ7kcnKHZfxu
 */

declare namespace AddressStandard {
  import { IABResult } from '../utils/abTest'

  /**
   * 接口入参
   */
  export interface QueryAddressInParams {
    /**
     * 到家商品传入，返回带有范围标识（结算场景使用）
     */
    jdCombineSign?: boolean
    /**
     * 门店ID集合（结算场景使用）
     */
    shopIdsArr?: Array<string>
    /**
     * 门祥到家融合 （该字段true 时，必须venderId、storeId）
     */
    jdStoreSign?: boolean
    /**
     * 店铺id 京东自营传JD（门详场景使用）
     */
    venderId?: string
    /**
     * 门店id（门详场景使用）
     */
    storeId?: string
    /**
     * 门店类型（门详场景使用）:门店来源 1. 达达 2. 青龙 3:伽利略 4:门店帮 6:医药（泰国传0） 8:前置仓门店 9:重货仓
     */
    storeType?: number
    /**
     * 加密序号 地址列表会按seriaNumber解密keyj进行加密
     */
    serialNumber?: string
    /**
     * 加密id
     */
    keyId?: string
    /**
     * 地址 传入地址,当前选中地址
     */
    addressId?: number
    /**
     * 传入经纬度，地址列表会按距离排序
     */
    latitude?: string
    /**
     * 传入经纬度，地址列表会按距离排序
     */
    longitude?: string
    /**
     * 含义。1?
     */
    externalLoginType?: number
    /**
     * 地址组件接入规范：https://cf.jd.com/pages/viewpage.action?pageId=1002739550
     * 必须入参否则提示无权限访问 地址组件授权
     */
    token?: string
    /**
     * 客户端传入地址集合
     */
    addressClients?: Array<AddressClientInfoVO>
    /**
     * 对于APP结算特殊场景字段，是否做初始化，"0":不初始化  ”1“初始化
     */
    isInitialize?: string
    /**
     * APP结算特殊场景字段
     */
    addressBalanceAppVoList?: Array<AddressBalanceAppVo>
    /**
     * 归堆排序字段 枚举值：edit,delete,seleted,readonly
     * 默认按照线上的是否可选排序
     */
    homingWord?: 'edit' | 'delete' | 'seleted' | 'readonly'
    /**
     * 当前是否要用POI地址逻辑
     */
    usePoiAddress?: boolean
    /**
     * 是否包含 到家 || 天选 || 前置仓 业务
     */
    hasO2OTag?: boolean
    /**
     * 列表页的版本，用于服务端区分新老列表页
     */
    listVersion?: number
    /**
     * 列表版本ab测试入参
     */
    abArr?: string
    /**
     * 新建的版本，用于服务端区分新老编辑页
     */
    editVersion?: number
  }

  /**
   * 客户端传入地址集合
   */
  export interface AddressClientInfoVO {
    /**
     * 地址id
     */
    addressId?: string

    /**
     * 经度
     */
    longitude?: string

    /**
     * 纬度
     */
    latitude?: string

    /**
     * 地址类型 （1:当前缓存地址 2：当前定位地址 3：距离最近的用户收货地址 4：无可用地址)
     */
    addressType?: string
  }

  /**
   * APP结算特殊场景字段:AddressBalanceAppVo
   */
  export interface AddressBalanceAppVo {
    /**
     * 类型 1:整体 2:地址ID 3:海外 4:港澳台 5:超配
     */
    type?: number
    /**
     * 地址ID集合
     */
    addressIdList?: Array<string>
    /**
     * 属性
     */
    attribute?: AddressAttributeVo
  }

  /**
   * 属性
   */
  export interface AddressAttributeVo {
    /**
     * 是否可编辑；默认是true
     */
    edit?: boolean
    /**
     * 是否可删除；默认是true
     */
    delete?: boolean
    /**
     * 是否可选；默认是true,若有超配则按照超配设置
     */
    readonly?: boolean
    /**
     * 是否选中；默认一条选中
     */
    selected?: boolean
  }

  /**
   * 列表接口返回值
   */
  export interface AddressInfo {
    /** 顶部提示信息 */
    addressTipsVoList?: Array<{ text: string }>
    /** 地址信息列表 */
    addressInfoList?: Array<Address>
    /**
     * 地址信息提示文案
     */
    defaultAddressMsg?: string
    /**
     * 地址提示头
     */
    defaultAddressTitle?: string
    /** 以下地址超出当前门店配送范围 */
    overhangTitle?: string
    /**
     * 地址增加或修改返回的优化项 （最优地址选项）
     */
    addressInfo?: Address
    /** 地址限制条数 */
    addressLimit: number
    /** 是否支持定位（降级使用） */
    supportGis: boolean
    /** 是否支持智能文本识别（降级使用） */
    supportIntelliGenText: boolean
    /** 是否支持地址联想(降级使用) */
    supportSuggestion: boolean
    /** 返回标识 失败或成功 */
    resultFlag: boolean
    /** 是否使用poi样式 */
    usePoiAddress?: boolean
    /**
     * 返回code
     */
    resultCode?: string
    /**
     * 返回类型：0 效验失败 1 提示替换优化信息
     */
    resultType?: number
    /**
     * 返回提示信息
     */
    message?: string
    /**
     * 是否在范围内的地址
     */
    rangeFlag?: boolean
    /**
     * 顶部提示条文案
     */
    tips?: string
    /**
     * 埋点参数
     * "tagaddqty":"含标签的地址总数",
     * "addrtag":[{"tagtext":"地址标签","addrqty":"设置该标签的地址数量"}],
     * "addqty":"地址总数",
     * "defaultadd":"是否已设置默认地址",
     * "grayaddqty":"不可选地址数",
     */
    addressPoIntVo?: {
      addqty?: number
      addrtag?: Array<{ addrqty?: string; tagtext?: string }>
      defaultadd?: boolean
      grayaddqty?: number
      tagaddqty?: number
    }
    /**
     * 是否为风险用户
     */
    riskUser?: boolean
    /**
     * 降级控制逻辑
     */
    abResult?: IABResult[]
  }

  /**
   * 单条地址信息
   */
  export interface Address {
    /** 地址详情 */
    addressDetail: string
    /** 地址id */
    addressId: number
    /** 地址别名 */
    addressName: string
    /** 地址uuid */
    addressUUID: string
    /** 地址类型1：常用地址 2：轻松购 */
    addressType: string
    /** 电话区号 */
    areaCode: string
    // 地址错误类型 11为无错误
    addressErrorType?: string
    // 地址错误信息
    addressErrorMessage?: string
    // 地址错误详细信息
    addressErrorDetailMessage?: string
    // 纠偏类型：1：一键更正；2：一键精简 3：详细地址 4：门牌号
    addressUpdateType?: string
    // 纠偏提示
    addressTipsVo?: {
      type: string
      text: string
    }
    addressStandardVOList?: Address[]
    /** 是否可选择 */
    clickAble: boolean
    /** 是否可删除 */
    delete: boolean
    /** 是否可编辑 */
    edit: boolean
    /** 坐标类型 */
    coordType: number
    /** 是否默认地址 */
    defaultAddress: boolean
    /** 邮箱 */
    email: string
    /** 是否加密 */
    encryFlag: boolean
    /** 是否海外地址 */
    foreignOverSea: boolean
    /** 收货人地址全称(省市县+收货人地址) */
    fullAddress: string
    /** 是否港澳台 */
    gangAoTai: boolean
    /** 身份证号 */
    idCard: string
    /** 纬度 */
    latitude: number
    /** 加密纬度 */
    latitudeString: string
    /** 经度 */
    longitude: number
    /** 加密经度 */
    longitudeString: string
    /** 收货人手机号加* */
    mobile: string
    /** 收货人姓名 */
    name: string
    /** 国家简码 */
    nameCode: string
    /** 修改时间 */
    operateTime: string
    /** 1：代表在配送范围内 2：代表不在配送范围内 */
    operationType: number
    /** 固定电话号码 */
    phone: string
    /** 邮编 */
    postCode: string
    /** 省份一级地址ID */
    provinceId: number
    /** 省份名称 */
    provinceName: string
    /** 城市id */
    cityId: number
    /** 城市名称 */
    cityName: string
    /** 县区域id */
    countyId: number
    /** 县区域名称 */
    countyName: string
    /** 乡镇级地址id */
    townId: number
    /** 乡镇级地址名称 */
    townName: string
    /** 收货人手机号加密 */
    realMobile: string
    /** 选择的地址标签id （1.学校，2.家，3.公司）如果是自定义标签不需要传 */
    retTag: number
    /** 是否被选中 */
    selected: boolean
    sortTime: string
    /** 选择的地址标签类型 1.腾讯poi(家，学校，公司对应的类型) 2.用户自定义 */
    tagSource: number
    updateTime: string
    /**
     * 用户自定义的标签名称（ 最大10个字符 用户自定义的标签是需要传，默认标签（家，学校，公司）不需要传）,
     * 自定义的就是 tagSource=2 useDefinedTag="自定义" retTag不传
     * 如果是腾讯的  retTag= 1 2 3 tagSource=1   usedefinedTag不传
     */
    userDefinedTag: string
    /**
     * 是否港澳台
     */
    isGangAoTai: boolean
    /**
     * 是否海外地址
     */
    isForeignOverSea: boolean
    /**
     * 级联地址list
     */
    areaList: Array<number>
    /**
     * 根据传入经纬度计算出的距离
     */
    distance?: string
    /**
     * 前端自定义参数：是否可禁用
     *
     */
    disabled: boolean
    /** 详细地址（不包括1-4级地址） */
    detailAddress?: string
    /**
     * https://joyspace.jd.com/pages/QgOtgos3HF5cJ4n5bOgE新增
     * 是否包含 到家 || 天选 || 前置仓 业务
     */
    hasO2OTag?: boolean
    /**
     * https://joyspace.jd.com/pages/QgOtgos3HF5cJ4n5bOgE新增
     * 当前是否要用POI地址逻辑，作为进入新增地址页入参
     * poi地址标识.控制是否展示门牌号输入框，点地区直接唤起地图。列表接口或者接入方传入。唤起列表时，router入参需新增该字段
     */
    usePoiAddress?: boolean
    /**
     * https://joyspace.jd.com/pages/QgOtgos3HF5cJ4n5bOgE新增
     * 短地址
     */
    shortAddress?: string
    /**
     * https://joyspace.jd.com/pages/QgOtgos3HF5cJ4n5bOgE新增
     * 门牌号
     */
    houseNumber?: string
    /**
     * https://joyspace.jd.com/pages/QgOtgos3HF5cJ4n5bOgE新增；
     * 当前地址是否为poi地址
     */
    usePoi?: boolean
    /**
     * 静默升级成功，下发的id。
     */
    baseId?: string
    /**
     * 不可点击时， 点击地址作出响应
     * “”toast
     */
    addressTipsVo?: {
      /**
       * 响应类型，1-toast
       */
      type?: string
      /**
       * 响应文案
       */
      text?: string
    }

    /**
     * 是否为风险用户
     */
    riskUser: boolean
    // 如果地址是完整的，四级地址不为0，隐藏级别设置为4; 否则，隐藏级别设置为3
    hideLevel?: string
    /**
     * 地址poiId
     */
    poiId?: string
    /**
     * poiAddress的长地址，包含1-4级
     */
    poiAddress?: string
  }

  /**
   * 控制列表降级的回调函数
   */
  export interface IListVersionChangeData {
    version: number
  }
}
