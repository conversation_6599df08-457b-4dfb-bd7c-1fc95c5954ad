import styles from './index.module.scss'

export type BaseItem<S> = {
  isSelected: boolean
  text: string
  targetValue: S
}

// 定义列表项的通用类型
export type ListItem<P, S> = BaseItem<P> & {
  sonList: BaseItem<S>[]
}

export type List<P, S> = ListItem<P, S>[]

// 定义组件属性类型
export type HeaderProps<P, S> = {
  list: List<P, S>
  clickHeader1Handler: (item: ListItem<P, S>) => void
  clickHeader2Handler: (item: BaseItem<S>) => void
}

// 提取公共的渲染逻辑
const renderList = <T,>(list: BaseItem<T>[], clickHandler: (item: BaseItem<T>) => void) => {
  return list.map((item, index) => (
    <div onClick={() => clickHandler(item)} key={index} className={`${styles.item} ${item.isSelected ? styles.item_selected : ''}`}>
      {item.text}
    </div>
  ))
}

const Header = <P = any, S = any>({ list, clickHeader1Handler, clickHeader2Handler }: HeaderProps<P, S>) => {
  const hasHead2 = list.find((item) => item.isSelected)?.sonList?.length
  return (
    <>
      <div className={styles.header}>
        {list.map((item, index) => (
          <div
            onClick={() => clickHeader1Handler(item)}
            key={index}
            className={`${styles.item} ${item.isSelected ? styles.item_selected : ''}`}
          >
            {item.text}
          </div>
        ))}
      </div>
      {!!hasHead2 && (
        <div className={styles.header2}>
          {list.map((item) => {
            if (item.isSelected) {
              return renderList(item.sonList, clickHeader2Handler)
            }
          })}
        </div>
      )}
    </>
  )
}

export default Header
