.products-container {
  background: #f7f8fc;
  margin-bottom: 15px;
  padding: 12px;
  border-radius: 8px;
}

/* 添加商品列表标题样式 */
.products-header {
  color: #888b94;
  font-size: 12px;
  font-weight: 400;
  line-height: 12px;
  margin-bottom: 12px;
}

/* 商品列表样式 */
.products-list {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 10px;
  border-radius: 8px;
  flex-wrap: wrap;
}

.products-list::-webkit-scrollbar {
  height: 4px;
}

.products-list::-webkit-scrollbar-thumb {
  background: #e6e6e6;
  border-radius: 4px;
}

.products-list::-webkit-scrollbar-track {
  background: transparent;
}

.product-item {
  width: 56px;
  height: 56px;
  position: relative;
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 6px;
  background: #ffffff;
  transition: all 0.2s ease;
  overflow: hidden;
}

.product-container {
  position: relative;
  width: 100%;
  height: 100%;
}
// 不可开票样式
/* .product-image:hover {
  border-color: #FF0F23;
  box-shadow: 0 2px 5px rgba(255, 15, 35, 0.1);
  transform: translateY(-2px);
} */

.product-image {
  object-fit: contain;
  width: 100%;
  height: 100%;
}

.products-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #888b94;
  font-weight: 400;
  line-height: 12px;
  margin-top: 10px;
  cursor: pointer;
}

.products-footer i {
  background: url(https://img11.360buyimg.com/imagetools/jfs/t1/282168/24/9592/475/67e364d2F1e63cf8e/092ca24b69787928.png) center / contain
    no-repeat;
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-left: 4px;
  transform: rotate(0deg);
}

.product-status {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 3.5px;
}

.statusText {
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 1;
  color: rgba(255, 255, 255, 0.96);
  text-align: center;
}

.helpIcon {
  width: 12px;
  height: 12px;
}
