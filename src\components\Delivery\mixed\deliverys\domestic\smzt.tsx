/**
 * @file: smzt.tsx
 * @description: 上门自提组件，用于展示上门自提等逻辑
 */
import { api_balance_transportPickSiteListBundle_pc, balance_getBundlePickDateList_pc } from '@app/services/api'
import { useEffect, useState } from 'react'
import AddrSelector from '../../../ui/TimeSelector/addrSelector'
import { UiAddrSelector } from '../../../ui/TimeSelector/types'
import { DeliveryPickSiteInfo } from '@app/typings/delivery_api'
import { useDelivery } from '../../../context'
import { PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp'
import { Tips } from '../../../ui/Tips/tips'

export default function Smzt({ onChange, promise }: { promise: PromiseListItem; onChange: (v: { addr: any; time: any }) => void }) {
  const [state, setState] = useState<UiAddrSelector.InitState<any, any>>({
    addrList: [],
    pickTimeList: [],
    hasAddrInput: false,
    noDataStr: '暂无数据',
    inLoading: true,
  })
  const deliveryState = useDelivery()

  const [selectAddr, setAddr] = useState<{ addr: { pickId: number }; time: { date: string } } | null>(null)

  const { bundle } = deliveryState.state.wrapInitState

  const setCurrentSelectValue = (value: { addr: any; time: any }) => {
    onChange(value)
    if (!value.addr) {
      return
    }
    setAddr(value)
    if (selectAddr && value.addr.pickId === selectAddr.addr?.pickId) {
      console.log(value, 'value')
      return
    }
    function convertDateToWeekday(dateString: string) {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        // 处理无效日期的情况，这里简单返回空字符串，你可以根据需求修改
        return ''
      }
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      const weekdayIndex = date.getDay()
      return weekdays[weekdayIndex]
    }
    balance_getBundlePickDateList_pc({
      bundleUuid: deliveryState.state.wrapInitState.bundle?.bundleId || '',
      pickSiteId: value.addr.pickId || 0,
    }).then((res) => {
      let defaultTime = ''
      if (promise && promise.promiseDate && promise.pickId === value.addr?.pickId) {
        defaultTime = promise.promiseDate
      }
      const list = res?.body?.pickDateList?.map((item: string) => {
        return {
          date: item,
          weekName: convertDateToWeekday(item),
          targetData: item,
          isSelected: defaultTime !== '' && item === defaultTime,
        }
      })
      setState({ ...state, pickTimeList: list || [] })
    })
  }
  // console.log(state, "smztState");

  useEffect(() => {
    if (!promise) {
      return
    }
    setState((s) => {
      return {
        ...s,
        inLoading: true,
      }
    })
    const defaultPickId = promise.pickId
    api_balance_transportPickSiteListBundle_pc({
      overseasTransport: false,
      formatBundleUUID: bundle.bundleId,
    })
      .then((res) => {
        const list: UiAddrSelector.addInfo<any>[] = res?.body
          .map((item: DeliveryPickSiteInfo) => {
            return {
              pickAddress: item.address,
              pickPhone: item.telephone,
              pickName: item.pickName,
              targetData: item,
              distance: item.distance + ' 千米',
              isSelected: defaultPickId !== 0 && item.pickId === defaultPickId,
            }
          })
          .sort((a, b) => {
            if (a.isSelected === b.isSelected) {
              return 0
            }
            return a.isSelected ? -1 : 1
          })
        setState((s) => ({ ...s, addrList: list || [] }))
      })
      .finally(() => {
        setState((s) => {
          return {
            ...s,
            inLoading: false,
          }
        })
      })
  }, [promise, bundle.bundleId])

  return (
    <>
      <Tips contentList={['京东将根据您的收货地址显示其范围内的自提点，请确保您的收货地址正确填写']} />
      {<AddrSelector<string, string> onChange={(e) => setCurrentSelectValue(e)} initState={state} />}
    </>
  )
}
