/**
 * @file: hooks.ts
 * @description: 模块通用钩子，用于处理整个模块的通用逻辑
 */
import { useRef, useEffect, useState, useCallback } from 'react'
import { useDelivery } from '../../context'

/**
 * 通用点击外部关闭弹窗钩子
 * - isOpen/ref 都由钩子内部管理
 * - 只要点击 .ui-area-wrap 区域不算外部点击
 */
export function useDeliveryHooks(defaultOpen = false) {
  const { state, deliveryLogger } = useDelivery()
  const initState = state.wrapInitState
  const [isOpen, setIsOpen] = useState(defaultOpen)
  const wrapperRef = useRef<HTMLDivElement | null>(null)
  const [errorMessage, setErrorMessage] = useState('')

  const close = useCallback(() => setIsOpen(false), [])
  const open = useCallback(() => setIsOpen(true), [])
  const toggle = useCallback(() => setIsOpen((v) => !v), [])

  /**
   * 点击外部关闭弹窗 (待优化 应该可以提升到父组件)
   */
  useEffect(() => {
    if (!isOpen) {
      return
    }
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        // 检查点击的元素或其祖先元素是否包含 ui-area-wrap 类
        const targetElement = event.target as HTMLElement
        if (!targetElement.closest('.ui-area-wrap')) {
          const elements = document.querySelectorAll('.ui-area-content-tab')
          if (elements && elements.length > 0 && getComputedStyle(elements[0]).left !== '0px') {
            return
          }
          setIsOpen(false)
        }
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  useEffect(() => {
    setIsOpen(false)
  }, [initState])

  const defErrMsg = '服务繁忙，请稍后重试～'

  return { wrapperRef, isOpen, setIsOpen, open, close, toggle, initState, deliveryLogger, errorMessage, setErrorMessage, defErrMsg }
}
