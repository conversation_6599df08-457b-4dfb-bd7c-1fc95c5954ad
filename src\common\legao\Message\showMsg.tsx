import * as React from 'react'
import * as ReactDOM from 'react-dom'
import Message, { IMessageProps } from './Message'

export default function showMsg(props: IMessageProps) {
  const div = document.createElement('div')
  document.body.appendChild(div)

  function close() {
    ReactDOM.unmountComponentAtNode(div)
    if (div && div.parentNode) {
      div.parentNode.removeChild(div)
    }
  }

  const { onClose, ...restProps } = props
  const callBack = onClose
    ? () => {
        onClose()
        close()
      }
    : close

  ReactDOM.render(<Message onClose={callBack} {...restProps} />, div)

  return {
    close,
  }
}
