import ReactDOM from 'react-dom'
import Dialog, { IDialogProps } from './Dialog'

const confirm = (Props: IDialogProps) => {
  const { title, children } = Props
  if (!title && !children) {
    return {
      close: () => {},
    }
  }

  const div = document.createElement('div')
  document.body.appendChild(div)

  function close() {
    ReactDOM.unmountComponentAtNode(div)
    if (div && div.parentNode) {
      div.parentNode.removeChild(div)
    }
  }

  ReactDOM.render(<Dialog isOpen={true} onClose={close} {...Props} />, div)

  return {
    close,
  }
}

export default confirm
