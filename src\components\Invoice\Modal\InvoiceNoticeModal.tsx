import { FC, useEffect, useMemo, useRef, useState } from 'react'
import InvoiceDialog from './InvoiceDialog.tsx'
import './InvoiceNoticeModal.scss'
import { useMasterData } from '@app/context/masterContext'

/**
 * 开票须知弹窗组件
 */
interface InvoiceNoticeModalProps {
  isOpen: boolean
  onClose: () => void
}

const InvoiceNoticeModal: FC<InvoiceNoticeModalProps> = ({ isOpen, onClose }) => {
  const invoiceUseRule = useMasterData()?.body?.rules?.invoiceUseRule

  const [viewportHeight, setViewportHeight] = useState(window.innerHeight)
  const noticeContentRef = useRef<HTMLDivElement>(null)
  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setViewportHeight(window.innerHeight)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const dialogHeight = useMemo(() => {
    if (noticeContentRef && noticeContentRef.current) {
      return Math.round(viewportHeight * 0.9) > 624 ? noticeContentRef.current.clientHeight + 160 : Math.round(viewportHeight * 0.9)
    }
    return 624
  }, [viewportHeight])

  const renderNoticeContent = () => {
    return (
      <div className="notice-content" ref={noticeContentRef}>
        {invoiceUseRule?.map((item: string, index: number) => <p key={index} dangerouslySetInnerHTML={{ __html: item }} />)}
      </div>
    )
  }

  const renderFooter = () => {
    return (
      <button className="btn btn-primary invoice-btn" onClick={onClose}>
        我知道了
      </button>
    )
  }

  return (
    <InvoiceDialog
      isOpen={isOpen}
      onClose={onClose}
      title={'开票须知'}
      width={524}
      height={dialogHeight}
      closeButton={true}
      hasMask={true}
      children={renderNoticeContent()}
      footer={renderFooter()}
      outsideClose={false}
      className="invoice-notice-modal"
    />
  )
}

export default InvoiceNoticeModal
