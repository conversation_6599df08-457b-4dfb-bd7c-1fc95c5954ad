@use "../../assets/styles/lib.scss";

#JD-consigneeInfo {
  width: 1552px;
  margin: 12px auto 16px;
}

.consignee {
  width: auto;
  height: 206px;
  border-radius: 8px;
  background-color: lib.$colorWhite;
  position: relative;
  &-header {
    .divider {
      border: 0.5px solid #0000000F;
    }
  }
  
  &.expand {
    height: 468px;
    .consignee-items {
      height: 384px;
      overflow-y: auto;
    }
    &.h256 {
      height: 342px;
      .consignee-items {
        height: 256px;
      }
    }
    &.h162 {
      height: 206px;
      .consignee-items {
        height: 162px;
      }
    }
    .consignee-expand {
      box-shadow: 0px -4px 12px rgba(0, 0, 0, 0.06);
      i {
        transform: rotate(180deg);
      }
    }
    &.isTwoColumns {
      height: 520px;
      .consignee-items {
        height: 426px;
      }
    }
  }
  &-items {
    margin-top: -10px;
    flex-wrap: wrap;
    overflow-y: hidden;
    // height: 118px;
    height: 162px;
    // &:hover {
    //   height: 152px;
    //   &+.consignee-expand {
    //     &.collapse {
    //       display: none;
    //     }
    //   }
    // }
  }
  &-item {
    width: 315px;
    height: 68px;
    border: 1px solid #0000000F;
    border-radius: 8px;
    padding: 16px 16px 16px 12px;
    position: relative;
    cursor: pointer ;
    &-icon {
      background: url(https://img12.360buyimg.com/imagetools/jfs/t1/278862/35/10144/1944/67e364d3Fc26f804f/97777d7525634bde.png) center / contain no-repeat;
    }
    &-addressDetail {
      width: 283px;
      &-name {
        max-width: 196px;
        display: inline-block;
      }
    }
    .tag {
      border: 0.5px solid lib.$color05;
      font-size: 12px;
      margin-right: 4px;
      padding: 0 2px;
      height: 16px;
      // line-height: 12px;
      color: lib.$color08;
      flex-shrink: 0;
      border-radius: 2px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
    }
    &.active, &:hover {
      border-color: lib.$color01;
    }
    &.active {
      border-color: lib.$color01;
      .consignee-item-icon {
        background: url(https://img14.360buyimg.com/imagetools/jfs/t1/279305/27/9656/1485/67e364d2Fdb97b218/0ff12e7e2178d138.png) center / contain no-repeat;
      }
    }
    .tag.is-default {
      background-color: lib.$color13;
      border-color: lib.$color02;
      color: lib.$color01;
    }
    .edit-btns {
      display: none;
      position: absolute;
      z-index: 3;
      right: 0;
      bottom: -42px;
      padding-bottom: 16px;
      // width: 283px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      // background-color: #FFEBEF;
      // color: #FF3333;
      color: #1A1A1A;
      // background-color: #fff;
      // border-top-left-radius: 8px;
      // border-bottom-right-radius: 8px;
      border-radius: 8px;
      .checkbox {
        transform: translateX(46px);
      }
      .edit-btn {
        &:hover {
          // background-color: lib.$color01;
          color: lib.$color01;
        }
      }
    }
    &-wrap {
      position: relative;
      padding-top: 10px;
      margin-right: 16px;
      margin-bottom: 16px;
      height: 102px;
      &:nth-child(2n) {
        margin-right: 0;
        @media (min-width: 1660px) {
          margin-right: 16px;
        }
        @media (max-width: 1439px) and (min-width: 1240px) {
          margin-right: 16px;
        }
        @media (max-width: 1659px) and (min-width: 1440px) {
          margin-right: 0 !important;
        }
        @media (max-width: 1239px) { 
          margin-right: 0 !important;
        }
      }
      &:nth-child(3n) {
        @media (min-width: 1660px) {
          margin-right: 0;
        }
        @media (max-width: 1439px) and (min-width: 1240px) {
          margin-right: 0;
        }
        @media (max-width: 1659px) and (min-width: 1440px) {
          margin-right: 16px;
        }
        @media (max-width: 1239px) { 
          margin-right: 16px;
        }
      }
      .edit-btns-wrap {
        background-color: #fff;
        display: none;
        position: absolute;
        width: 100%;
        width: calc(100% - 2px);
        height: 142px;
        border: 1px solid red;
        box-shadow: 2px 4px 16px #00000029;
        border-radius: 8px;
        top: -1px;
        top: 10px;
        left: -0;
        z-index: 3;
      }
      &:hover {
        .edit-btns-wrap {
          display: block;
          z-index: 4;
        }
        .edit-btns {
          display: flex;
          z-index: 4;
        }
        .consignee-divider {
          display: block;
        }
        .consignee-item {
          border-color: transparent !important;
          z-index: 4;
        }
        .consignee-item-wrap {
          z-index: 4;
        }
        .consignee-item-warning {
          z-index: 5;
        }
      }
    }
    &-warning {
      position: absolute;
      top: 0;
      right: 0;
      background-color: #FFEBEF;
      color: #FF3333;
      padding: 4px 5px;
      border-radius: 10px 8px 0px 10px;
      white-space: nowrap;
      z-index: 2;
    }
  }
  &-expand {
    width: calc(100% - 16px);
    margin-left: -16px;
    padding-left: 16px;
    i {
      background: url(https://img11.360buyimg.com/imagetools/jfs/t1/282168/24/9592/475/67e364d2F1e63cf8e/092ca24b69787928.png)  center / contain no-repeat;
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-left: 4px;
    }
    position: absolute;
    bottom: -2px;
    background-color: #fff;
    z-index: 3;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
  &-divider {
    display: none;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.06);
  }
  &-tip {
    width: 898px;
  }
 }
 .tip-area-icon {
    background: url(https://img30.360buyimg.com/img/jfs/t1/317479/29/13373/868/6866e058Fb023de85/79c169ad03cb148a.png) center / contain no-repeat;
 }
 .tip-area-btn {
    border: 0.5px solid #00000099;
  }
/* 窗口宽度在 1440px 到 1660px 之间 */
@media (max-width: 1659px) and (min-width: 1440px) {
  .consignee-item {
    width: 360px;
    &-addressDetail {
      width: 328px;
      &-name {
        max-width: 241px;
      }
    }
  }
  .consignee-tip {
    width: 626px;
  }
}
/* 窗口宽度小于 1240px */
@media (max-width: 1239px) {
  .consignee-item {
    width: 360px;
    &-addressDetail {
      width: 328px;
      &-name {
        max-width: 241px;
      }
    }
  }
  .consignee-tip {
    width: 626px;
  }
}