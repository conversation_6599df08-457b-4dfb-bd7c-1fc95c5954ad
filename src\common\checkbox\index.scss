/* 基础样式 */
.checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-inner {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-color: #fff;
  border: 1px solid #bfbfbf;
  border-radius: 50%;
  /* 圆形 */
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  position: relative;
}

.checkbox-inner::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  /* 圆形勾号 */
  background-image: url('//img12.360buyimg.com/imagetools/jfs/t1/262811/17/16930/344/67a31d47F4eced4d0/66b189e4ff6534c8.png');
  background-repeat: no-repeat;
  background-size: 12px auto;
  background-position: center;
  background-color: #fa2c19;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.checkbox-input:checked+.checkbox-inner {
  border-color: #fa2c19;
}

.checkbox-input:checked+.checkbox-inner::after {
  transform: translate(-50%, -50%) scale(1);
}

.checkbox-input:focus+.checkbox-inner {
  // box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  // border-color: #fa2c19;
}

.checkbox-label {
  margin-left: 8px;
  color: #1a1a1a;
  font-size: 14px;
}

.checkbox-label:hover {
  // color: rgba(0, 0, 0, 0.85);
}

/* Indeterminate 状态样式 */
.checkbox-input:indeterminate+.checkbox-inner {
  border-color: #fa2c19;
}

.checkbox-input:indeterminate+.checkbox-inner::after {
  background-image: none;
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 2px;
  background-color: #fa2c19;
  transform: translate(-50%, -50%);
  border-radius: 0;
  border: none;
}

.checkbox-input:disabled+.checkbox-inner {
  background-color: #bfbfbf;
  border-color: #bfbfbf;
  cursor: not-allowed;
}

.checkbox-input:disabled~.checkbox-label {
  cursor: not-allowed;
}

.checkbox-input:disabled+.checkbox-inner::after {
  background-color: #bfbfbf;
}

.checkbox.indeterminate {
  .checkbox-input:disabled+.checkbox-inner::after {
    background-color: #fff;
  }
}