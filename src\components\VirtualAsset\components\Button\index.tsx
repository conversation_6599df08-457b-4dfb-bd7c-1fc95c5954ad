import classnames from 'classnames'

import styles from './index.module.scss'
import type { IType, SizeType } from '@app/typings/component'
interface ButtonProps {
  /**
   * 按钮类型
   * 可选值：default | primary
   */
  type?: IType
  /**
   * 按钮尺寸
   * 可选值：small | mini | large
   */
  size?: SizeType
  /**
   * 点击事件
   */
  onClick?: () => void
  /**
   * 按钮内容
   */
  children?: any
  /**
   * 是否块级元素
   */
  block?: boolean
  /**
   * 自定义样式
   */
  className?: string
}

/**
 * 通用按钮
 * */
const Button = ({ type = 'default', size = 'small', onClick, children, block, className = '' }: ButtonProps) => {
  return (
    <div
      onClick={() => {
        onClick && onClick()
      }}
      className={classnames(
        styles['common-button'],
        styles[`common-button--${type}`],
        styles[`common-button--${size}`],
        block && styles['common-button--block'],
        className as any,
      )}
    >
      {children}
    </div>
  )
}

export default Button
