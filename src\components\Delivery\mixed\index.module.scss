.body_warp{
  min-width: 496px;
  //height: 452px;
  //box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  background: #FFFFFF;
  box-shadow: 0px 12px 24px rgba(0, 0, 0, 0.1);
  padding: 8px 24px;
}

.selector_wrap {
  max-width: 100%;
  display: inline-flex;
  flex-wrap: wrap;
  flex-direction: row;
  align-items: center;
  justify-content: start;
  gap: 8px;
  box-sizing: border-box;
  border-radius: 4px;
}
  //background: #F7F8FC;
  //cursor: pointer;
  .selector {
    position: relative;
  }

  .selector_item_wrap {
    cursor: pointer;

    height: 36px;
    padding: 9px 12px;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-sizing: border-box;
    // max-width: 315px;
    border-radius: 4px;
    background: #F7F8FC;
    border: 0.5px solid rgba(0, 0, 0, 0.06);

    .item {
      color: #1A1A1A;
      font-size: 14px;
      font-family: 'PingFang SC';
      font-weight: 500;
      letter-spacing: 0px;
      text-align: right;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
    }
    .ellipsis{
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }
  }
  .selected {
    border: 0.5px solid #FF8595!important;
  }
