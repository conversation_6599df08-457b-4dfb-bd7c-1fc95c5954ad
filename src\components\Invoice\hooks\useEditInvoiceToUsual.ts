import { UsualInvoiceItem, InvoiceToUsualForm, InvoiceTitle } from '@app/typings/invoice.d'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import { editInvoiceToUsual } from '@app/services/api'
import { useAtomValue } from 'jotai'
import { currentInvoiceAtom, selectedInvoiceTitleAtom, selectedInvoiceTypeAtom } from '@app/components/Invoice/atom/invoiceAtom'

interface UseEditInvoiceToUsualParams {
  updateInvoiceItem: (updatedItem: UsualInvoiceItem) => void
}

/**
 * 编辑发票抬头的Hook
 */
const useEditInvoiceToUsual = ({ updateInvoiceItem }: UseEditInvoiceToUsualParams) => {
  const currentInvoice = useAtomValue(currentInvoiceAtom)
  const selectedInvoiceTitle = useAtomValue(selectedInvoiceTitleAtom)
  const selectedInvoiceType = useAtomValue(selectedInvoiceType<PERSON>tom)
  return async (originalItem: UsualInvoiceItem, newContent: string, newInvoiceCode?: string) => {
    if (!newContent?.trim()) {
      return {
        success: false,
        message: '发票抬头内容不能为空',
      }
    }

    // 构建更新后的发票对象
    const updatedItem: UsualInvoiceItem = {
      ...originalItem,
      content: newContent.trim(),
      invoiceCode: newInvoiceCode || originalItem.invoiceCode,
    }

    try {
      // 调用API编辑常用发票
      let apiParams = {
        selectedInvoiceType: selectedInvoiceType,
        norCode: updatedItem.invoiceCode || '',
        norRegAddr: currentInvoice.normalInvoice?.regAddress || '',
        norRegPhone: currentInvoice.normalInvoice?.regTel || '',
        norRegBank: currentInvoice.normalInvoice?.regBank || '',
        norRegBankAccount: currentInvoice?.normalInvoice?.regAccount || '',
        selectInvoiceTitle: selectedInvoiceTitle, // 默认为单位发票
        selectBookInvoiceContent: '',
        selectNormalInvoiceContent: '1',
        usualInvoiceId: originalItem.id?.toString() || '',
      } as InvoiceToUsualForm
      if (selectedInvoiceTitle == InvoiceTitle.PERSONAL) {
        apiParams = { ...apiParams, norCompanyName: updatedItem.content }
      } else {
        apiParams = { ...apiParams, norPersonalName: updatedItem.content }
      }

      // 调用API
      const response = await editInvoiceToUsual(apiParams)

      // 检查API返回结果
      if (response && response?.code === '0') {
        // 更新本地发票列表
        updateInvoiceItem(updatedItem)

        return {
          success: true,
          data: updatedItem,
        }
      } else {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.Invoice,
          msg: {
            type: '发票',
            action: '编辑发票抬头',
            error: '编辑发票抬头异常:' + response?.message,
            requestParms: apiParams,
            responseData: response,
          },
        })
        throw new Error(response?.message || '编辑发票抬头失败')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      console.error('编辑发票抬头错误:', error)
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.Invoice,
        msg: {
          type: '发票',
          action: '编辑发票抬头',
          error: '编辑发票抬头异常:' + errorMsg,
        },
      })
      return {
        success: false,
        message: errorMsg,
      }
    }
  }
}

export default useEditInvoiceToUsual
