@charset "utf-8";
//== @subsection 常用颜色 ==//
$color01: #ff0f23; //红色
$color02: #FFD6E1; //红色
$color03: #888b94; //灰色
$color04: #FFEBEF; //红色
$color05: #66C7FF; //蓝色
$color06: #666; //深灰色
$color07: #333; //黑色
$color08: #0090FF; //深蓝色
$color09: #ffaa71; //橘黄色 新人
$color10: #999; //深灰色
$color11: #505259; //深灰色
$color12: #1A1A1A; //黑色
$color13: #FFEBF1; //红色
$color14: #C2C4CC;
$color15: #F7F8FC;
$colorBorder: #f0f0f0; // 边框统一颜色
$colorWhite: #ffffff;
$colorBlack: #000000;

/* 清楚浮动 */
%clearfix {
  &:before,
  &:after {
    content: "";
    display: block;
    clear: both;
  }
}
@mixin clearfix {
  @extend %clearfix;
}