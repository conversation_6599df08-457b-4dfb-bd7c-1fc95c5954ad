import classNames from 'classnames'
export default function Loading({ loading }) {
  return (
    <>
      {loading ? (
        <>
          <div className="jd-loading empty">
            <span className="jd-spin-dot jd-spin-dot-spin">
              <i className="jd-spin-dot-item"></i>
              <i className="jd-spin-dot-item"></i>
              <i className="jd-spin-dot-item"></i>
              <i className="jd-spin-dot-item"></i>
            </span>
          </div>
          <div className={classNames('jd-loading-mask', {'jd-loading-blur':loading} )}></div>
        </>
      ) : null}
    </>
  );
}
