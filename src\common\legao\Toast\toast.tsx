import React, { useState, useEffect } from 'react'
import ReactDOM from 'react-dom'
import classNames from 'classnames'
import styles from './toast.module.scss'
export const ToastContext: any = React.createContext({})

interface Props {
  children?: any
}

function ToastProvider(props: Props) {
  const [toastParams, setToastParams] = useState({ type: '', message: '', timer: 2000, fn: () => {}, effect: 'light', inline: true })
  const [show, setShow] = useState(false)

  function showToast(params: any) {
    setToastParams((pre) => ({ ...pre, ...params }))
    setShow(true)
  }

  function hideToast() {
    setShow(false)
    typeof toastParams.fn == 'function' && toastParams.fn()
  }

  // 使用 useEffect 来自动隐藏 toast
  useEffect(() => {
    if (show) {
      const timer = setTimeout(() => {
        hideToast()
      }, toastParams.timer)

      return () => clearTimeout(timer)
    }
  }, [show])

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {props.children}
      {show &&
        ReactDOM.createPortal(
          <div className={classNames(styles.toast, styles[toastParams.effect], { [styles.inline]: toastParams.inline })}>
            {styles[toastParams.type] && <i className={styles[toastParams.type]}></i>}
            <div className={styles.title}>{toastParams.message}</div>
          </div>,
          document.body,
        )}
    </ToastContext.Provider>
  )
}

export default ToastProvider

// import { Toast } from '@app/common/legao'
{
  /* <Toast></Toast> */
}
// import { ToastContext } from '../../common/legao/Toast/toast'
// const { showToast } = useContext(ToastContext) as { showToast: (s: object) => void; hideToast: () => void }
