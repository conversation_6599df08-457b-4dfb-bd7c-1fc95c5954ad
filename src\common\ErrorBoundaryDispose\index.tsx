import { ComponentType, useState } from 'react'
import { ErrorBoundary } from 'react-error-boundary'

type FallbackProps = {
  error: any
  resetErrorBoundary: (...args: any[]) => void
}

interface Props {
  children: any
  FallbackComponent: ComponentType<FallbackProps>
  logErrorToService: (error: any, info: any) => void
}

//错误边界函数
const ErrorBoundaryDispose = (props: Props) => {
  const [resetKey, setResetKey] = useState(0)

  const handleReset = () => {
    setResetKey((prevKey) => prevKey + 1)
  }

  return (
    <ErrorBoundary
      resetKeys={[resetKey]}
      FallbackComponent={props?.FallbackComponent}
      onError={props?.logErrorToService}
      onReset={handleReset}
    >
      {props?.children}
    </ErrorBoundary>
  )
}

export default ErrorBoundaryDispose
