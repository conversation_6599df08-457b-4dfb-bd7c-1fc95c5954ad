import { FC, useCallback } from 'react'
import {
  currentInvoiceAtom,
  currentInvoicePutTypesAtom,
  selectedInvoicePutTypeAtom,
  selectedInvoiceTypeAtom,
} from '@app/components/Invoice/atom/invoiceAtom'
import { useAtomValue } from 'jotai'
import { InvoiceType } from '@app/typings/invoice.d'
import { Form } from '@app/common/legao'

/**
 * 发票抬头选择器组件
 */
interface InvoicePutTypeSelectorProps {
  onChange: (invoicePutType: number) => void
}

const InvoicePutTypeSelector: FC<InvoicePutTypeSelectorProps> = ({ onChange }) => {
  // 自动根据业务场景获取正确的发票开票方式
  const invoicePutTypes = useAtomValue(currentInvoicePutTypesAtom)
  const selectedPutTypeValue = useAtomValue(selectedInvoicePutTypeAtom)
  const currentInvoicePutTypes = useAtomValue(currentInvoicePutTypesAtom)?.find((item) => item.selected)
  const selectedInvoiceType = useAtomValue(selectedInvoiceTypeAtom)
  const currentInvoice = useAtomValue(currentInvoiceAtom)
  /**
   * 处理发票开票方式变化
   */
  const handlePutTypeChange = useCallback(
    (invoicePutType: number) => {
      if (invoicePutType === selectedPutTypeValue) return
      onChange(invoicePutType)
    },
    [onChange, selectedPutTypeValue],
  )
  if (selectedInvoiceType === InvoiceType.ELECTRONIC && !currentInvoice?.electroInvoice?.invoicePutFlag) return null
  if (selectedInvoiceType === InvoiceType.VAT && !currentInvoice?.vat?.invoicePutFlag) return null
  return (
    <Form.Item label="开票方式" required>
      <div>
        <div className="radio-group" style={{ flexGrow: 1 }}>
          {invoicePutTypes?.map((way) => (
            <div key={way.value} className={`radio-button ${way.selected ? 'active' : ''}`} onClick={() => handlePutTypeChange(way.value)}>
              {way.content}
            </div>
          ))}
        </div>
        {/* 发票开票方式提示：当发票开票方式为空时，不显示提示 */}
        {currentInvoicePutTypes && currentInvoicePutTypes.descLabel && currentInvoicePutTypes.descLabel !== '' && (
          <div className="invoice-note">
            {/* <img src={'//img13.360buyimg.com/ling/jfs/t1/319798/31/6255/735/683ee395Fbd94e235/be602901616ec835.png'} alt="noticeArrow" /> */}
            <span dangerouslySetInnerHTML={{ __html: currentInvoicePutTypes.descLabel }}></span>
          </div>
        )}
      </div>
    </Form.Item>
  )
}

export default InvoicePutTypeSelector
