import { OverseasPickInfo } from "./master_api_response";

// 配送类型列表项
interface ComShipmentType {
    shipmentTypeName: string;
    shipmentType: number;
    selected: boolean;
    honorFreight?: number;
}

// 时间列表项
export interface TimeListItem {
    timeRange: string;
    enable: boolean;
    sendpay: string;
    batchId: number;
    selected: boolean;
    type: number;
    freight: string;
    cutOrder: number;
    mark: number;
    disable: boolean;
    timeRangeType?: number;
}

// 日历列表项
export interface CalendarListItem {
    displayedString: string;
    dateStr: string;
    timeOffset?: number;
    weekString: string;
    today: boolean;
    timeList: TimeListItem[];
    selected: boolean;
    supportTimeRangTypeList?: {
        timeRangType: number;
        timeRangName: string;
    }[];
    supportPromiseTagList?: {
        selected: boolean;
        promiseTagType: string;
        promiseTagName: string;
    }[];
}

// 承诺列表项
export interface PromiseListItem {
    shipmentType: number;
    promiseTypeName: string;
    promiseType: number;
    promiseDate: string;
    promiseTimeRange: string;
    promiseSendPay: string;
    batchId: number;
    selected: boolean;
    calendarList: CalendarListItem[];
    timeOffset?: number;
    promiseFormat: number;
    deliveryFloorType: string;
    deliveryFloorTypeName: string;
    promiseMsg: string;
    carriageMoneyMsg: string;
    pickAddress?: string;
    pickName?: string;
    pickPhone?: string;
    pickDateList?: string[];
    venderSelfDeliveryStoreInfo?: StoreInfo;
    promiseTip?:string,
    pickId?:number
    bigItemInstallDateMap?: object
}

export interface StoreInfoType {
    venderId: number;
    storeId: number;
    warehouseId: number;
    name: string;
    businessHours: string;
    longitude: number;
    latitude: number;
    storeMark: number;
    recentlyMark: number;
    stockStatus: number;
    distance: string;
    storeAddress: string;
    idProvince: number;
    idCity: number;
    idArea: number;
    idTown: number;
    vendSource: string;
    venderStoreStockTab: number;
}

// 配送信息
interface DeliveryInfo {
    promiseList: PromiseListItem[];
    venderUuidMap: string;
    secondDelivery: false; 
    shopClosing: boolean
}

// 响应体
interface ResponseBody {
    comShipmentTypeList: ComShipmentType[];
    deliveryInfo: DeliveryInfo;
    seasShipmentTypeList: ShipmentInfo[]
}
export interface ShipmentInfo {
    shipmentTypeName: string;
    shipmentType: string;
    selected: boolean;
    venderUuidMap: string;
    freight: number;
    selfPickFreight: number;
    freightInner: number;
    transportMsg: string;
    promiseType: string;
    promiseMsg: string;
    disabled: boolean;
    overseasPickInfo?: OverseasPickInfo
}

// 完整响应
interface ApiGetBundleShipmentListResponse {
    code: string;
    message: string;
    timestamp: number;
    body: ResponseBody;
}

export type { ApiGetBundleShipmentListResponse, ComShipmentType };