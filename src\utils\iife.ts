import Cookies from 'js-cookie'

/**
 * 检查变量是否为布尔类型
 */
function isBoolean(value: unknown): value is boolean {
  return value === true || value === false || Object.prototype.toString.call(value) === '[object Boolean]'
}

/**
 * 判断是否支持 avif
 */
const checkAVIF = (function (): () => Promise<boolean> {
  let webpPromise: Promise<boolean> | null = null
  const KEY = 'avif'
  return function () {
    if (webpPromise) {
      return webpPromise
    }

    webpPromise = new Promise((resolve) => {
      const localValue = Cookies.get(KEY)

      if (isBoolean(localValue)) {
        return resolve(localValue)
      }

      const img = new Image()

      img.onload = function () {
        const webpSupported = img.width > 0 && img.height > 0
        Cookies.set(KEY, webpSupported)
        resolve(webpSupported)
      }

      img.onerror = function () {
        Cookies.set(KEY, false)
        resolve(false)
      }

      img.src =
        'data:image/avif;base64,AAAAHGZ0eXBtaWYxAAAAAG1pZjFhdmlmbWlhZgAAAPFtZXRhAAAAAAAAACFoZGxyAAAAAAAAAABwaWN0AAAAAAAAAAAAAAAAAAAAAA5waXRtAAAAAAABAAAAHmlsb2MAAAAABEAAAQABAAAAAAEVAAEAAAAeAAAAKGlpbmYAAAAAAAEAAAAaaW5mZQIAAAAAAQAAYXYwMUltYWdlAAAAAHBpcHJwAAAAUWlwY28AAAAUaXNwZQAAAAAAAAABAAAAAQAAABBwYXNwAAAAAQAAAAEAAAAVYXYxQ4EgAAAKBzgABpAQ0AIAAAAQcGl4aQAAAAADCAgIAAAAF2lwbWEAAAAAAAAAAQABBAECg4QAAAAmbWRhdAoHOAAGkBDQAjITFkAAAEgAAAB5TNw9UxdXU6F6oA == '
    })
    return webpPromise
  }
})()

/**
 * 判断是否支持 webp
 */
const checkWebp = (function (): () => Promise<boolean> {
  let webpPromise: Promise<boolean> | null = null
  const KEY = 'webp'
  return function () {
    if (webpPromise) {
      return webpPromise
    }

    webpPromise = new Promise((resolve) => {
      const localValue = Cookies.get(KEY)

      if (isBoolean(localValue)) {
        return resolve(localValue)
      }

      const img = new Image()

      img.onload = function () {
        const webpSupported = img.width > 0 && img.height > 0
        Cookies.set(KEY, webpSupported)
        resolve(webpSupported)
      }

      img.onerror = function () {
        Cookies.set(KEY, false)
        resolve(false)
      }

      img.src = 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA'
    })
    return webpPromise
  }
})()

try {
  checkAVIF()
  checkWebp()
} catch (e) {
  console.warn('自执行方法异常', e)
}
