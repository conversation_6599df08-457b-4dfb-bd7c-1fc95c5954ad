export namespace UiTimePicker {
  export type ListState<T> = ListItem<T>[]
  export type ListItem<T> = {
    weekName: string
    items: Item<T>[]
  }
  export type Item<T> = {
    text: string
    type: string
    targetData: T
  }
}
export namespace UiTimeSelector {
  export type TimeList<T, U> = TimeItem<T, U>[]

  export type TimeItem<T, U> = {
    isSelected: boolean
    text: string
    targetValue: T
    items: TimeItemSon<U>[]
  }
  export type TimeItemSon<U> = {
    textList: { style: 'none' | 'gray'; text: string }[]
    targetValue: U
    isSelected: boolean
    isDisabled?: boolean
  }

  export type Props<T, U> = {
    list: TimeList<T, U>
    onClick?: (s: string, is2?: boolean, target?: U) => void
    onChange: (listIndex: TimeItem<T, U>, itemIndex?: TimeItemSon<U>) => void
  }
}

export namespace UiAddrSelector {
  export type addInfo<T> = {
    pickAddress: string
    pickName: string
    pickPhone?: string
    targetData: T
    businessHours?: string
    distance: string
    isSelected?: boolean
    isDisabled?: boolean
    closest?: boolean
  }
  type pickTime<T> = {
    date: string
    weekName: string
    targetData: T
    isSelected?: boolean
  }
  export type InitState<T, U> = {
    hasAddrInput: boolean
    addrList: addInfo<T>[]
    pickTimeList?: pickTime<U>[]
    areaValue?: { provinceId: string; cityId: string; districtId: string }
    inLoading: boolean
    noDataStr?: string
  }

  export type Props<T, U> = {
    initState: InitState<T, U>
    onChange: (P: { time: U | null; addr: T }) => void
    onAreaChange?: (P: { provinceId: string; cityId: string; districtId: string }) => void
  }
}
