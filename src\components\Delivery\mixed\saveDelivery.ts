/**
 * @file: saveDelivery.ts
 * @description: 保存配送数据，用于保存配送数据
 */
import { balance_saveBundleShipment_pc } from '@app/services/api'
import { DeliveryWrap } from '../types'
import { CalendarListItem, ComShipmentType, PromiseListItem, ShipmentInfo, TimeListItem } from '@app/typings/api_getBundleShipmentList_resp'
import { DeliveryPickSiteInfo } from '@app/typings/delivery_api'
import { RegularBuyCalendarItem } from '@app/typings/master_api_response'

export type SaveData<T> = {
  baseData: DeliveryWrap.InitState
  addrId: string
  currentValue: T
  saveType: SaveType
  promiseItem: PromiseListItem
  shipmentTypeItem: ComShipmentType
  targetPromise?: PromiseListItem
  deliveryBaseData: object
}

export type SaveType = 'mdzt' | 'smzt' | 'default' | 'hwjy' | 'timeList' | 'timePicker' | 'install' | 'dqg'

/**
 * @description: 生成配送数据
 * @param saveData 保存数据
 * @param currentValue 当前选择的值
 * @returns 配送数据
 */
const makeShipmentAndDeliveryInfoSaveData = (saveData: SaveData<any>, currentValue: object) => {
  const calendar = {
    displayedString: '',
    dateStr: '',
    weekString: '',
    selected: true,
    timeOffset: 0,
  }
  const promiseItem = saveData.baseData.bundle.deliveryInfoVO.promiseList[1]
  if (promiseItem) {
    if (promiseItem.weekString) {
      calendar.displayedString = promiseItem.promiseDate + `[${promiseItem.weekString}]`
    }
    calendar.dateStr = promiseItem.promiseDate
    calendar.weekString = promiseItem.weekString || ''
    calendar.timeOffset = promiseItem.timeOffset || 0
  }
  const promiseList: object[] = [
    {
      ...saveData.promiseItem,
      selected: true,
      ...currentValue,
    },
  ]
  if (saveData.baseData.bundle.deliveryInfoVO.promiseList[1]) {
    promiseList.push({
      ...saveData.baseData.bundle.deliveryInfoVO.promiseList[1],
      deliveryFloorType: saveData.targetPromise?.deliveryFloorType,
      selected: true,
      calendarList: [promiseItem ? calendar : {}],
    })
  }
  const saveShipmentParam = {
    comShipmentType: {
      ...saveData.shipmentTypeItem,
      selected: true,
    },
    deliveryInfo: {
      ...saveData.deliveryBaseData,
      promiseList,
    },
  }
  return saveShipmentParam
}

/**
 * @description: 生成安装数据
 * @param saveData 保存数据
 * @param currentValue 当前选择的值
 * @returns 安装数据
 */
const makeShipmentAndDeliveryInfoSaveDataForInstall = (saveData: SaveData<any>, currentValue: CalendarListItem) => {
  const promiseList: object[] = []
  if (saveData.baseData.bundle.deliveryInfoVO.promiseList[1]) {
    promiseList.push({
      ...saveData.baseData.bundle.deliveryInfoVO.promiseList[0],
      deliveryFloorType: saveData.targetPromise?.deliveryFloorType,
      selected: true,
    })
  }
  promiseList.push({
    ...saveData.promiseItem,
    bigItemInstallDateMap: undefined,
    ...currentValue,
    selected: true,
    calendarList: [{ ...currentValue, selected: true }],
  })
  const saveShipmentParam = {
    comShipmentType: {
      ...saveData.shipmentTypeItem,
      selected: true,
    },
    deliveryInfo: {
      ...saveData.deliveryBaseData,
      promiseList,
    },
  }
  return saveShipmentParam
}

/**
 * @description: 保存门店自提数据
 * @param saveData 保存数据
 * @returns 门店自提数据
 */
export const saveMdzt = (saveData: SaveData<{ addr: any; time: any }> & { type: 'mdzt' }) => {
  const saveShipmentParam = makeShipmentAndDeliveryInfoSaveData(saveData, {
    venderSelfDeliveryStoreInfo: saveData.currentValue.addr,
  })

  return saveShipmentParam
}

/*
* 配送保存方法
*/
export const saveDeliveryData = (saveData: SaveData<any>) => {
  if (!saveData.promiseItem || !saveData.shipmentTypeItem) {
    return
  }

  let saveShipmentParam = null
  if (saveData.saveType === 'mdzt') {
    saveShipmentParam = saveMdzt(saveData as SaveData<{ addr: any; time: any }> & { type: 'mdzt' })
  }

  if (saveData.saveType === 'default') {
    saveShipmentParam = makeShipmentAndDeliveryInfoSaveData(saveData, {})
  }

  if (saveData.saveType === 'timeList' || saveData.saveType === 'timePicker') {
    const parent = (saveData.currentValue.p?.targetValue as CalendarListItem) || (saveData.currentValue as CalendarListItem)
    if (!parent) {
      return
    }
    const son = saveData.currentValue.s?.targetValue as TimeListItem
    let timeList: TimeListItem[] = []
    if (son) {
      console.log(son, 'son')
      timeList = [{ ...son, selected: true }]
    }
    const calendar = { ...parent, selected: true, timeList: timeList }

    const tmpSaveData = {
      ...saveData,
      promiseItem: { ...saveData.promiseItem, timeOffset: calendar.timeOffset || undefined, promiseDate: parent.dateStr, selected: true },
    }
    if (son) {
      tmpSaveData.promiseItem.batchId = son.batchId
      son.timeRange && (tmpSaveData.promiseItem.promiseTimeRange = son.timeRange)
    }
    saveShipmentParam = makeShipmentAndDeliveryInfoSaveData(tmpSaveData, {
      calendarList: [calendar],
    })
  }

  if (saveData.saveType === 'dqg') {
    const value = saveData.currentValue as RegularBuyCalendarItem
    saveShipmentParam = makeShipmentAndDeliveryInfoSaveData(saveData, {
      promiseDate: value.fullDate,
      midPromiseFormatType: value.midPromiseFormatType,
      midPromiseType: value.midPromiseType,
      midShipmentTimeOffset: value.midShipmentTimeOffset,
      offset: value.offset,
      promiseMsg: undefined,
      weekString: undefined,
    })
  }

  if (saveData.saveType === 'smzt' || saveData.saveType === 'hwjy') {
    console.log(saveData.currentValue, 'smzt')
    const smztAddress = (saveData.currentValue.addr as DeliveryPickSiteInfo) || {}
    const smztTime = (saveData.currentValue.time as string) || ''

    saveShipmentParam = makeShipmentAndDeliveryInfoSaveData(saveData, {
      // 自提点id
      pickId: smztAddress.pickId,
      // 自提点名称
      pickName: smztAddress.pickName,
      // 自提点地址
      pickAddress: smztAddress.address,
      // 当前选中自提点时间列表
      pickDateList: [smztTime],
      // 自提点类型
      siteType: smztAddress.siteType,
      promiseDate: smztTime,
    })
  }

  if (saveData.saveType === 'install') {
    console.log(saveData.currentValue, 'install')
    saveShipmentParam = makeShipmentAndDeliveryInfoSaveDataForInstall(saveData, {
      ...saveData.currentValue,
      promiseDate: saveData.currentValue.dateStr,
    } as CalendarListItem)
  }

  if (saveShipmentParam) {
    return balance_saveBundleShipment_pc({
      venderId: saveData.baseData.venderId,
      bundleUuid: saveData.baseData.bundle.bundleId,
      addrId: saveData.addrId,
      saveShipmentParam: saveShipmentParam,
      isZyVender: !!saveData.baseData.isSelfVendor,
    })
  }
}

export type HwjySaveData<T> = {
  baseData: DeliveryWrap.InitState
  addrId: string
  currentValue: T
  shipmentInfo: ShipmentInfo
}
export const saveHwjy = (data: HwjySaveData<any>) => {
  console.log(data, 'hwjy')
  // return;
  const smztAddress = data.currentValue as DeliveryPickSiteInfo

  const saveShipmentParam = {
    deliveryInfo: {
      // ...(data.shipmentInfo),

      // shipmentType: undefined,
      // ...data.deliveryBaseData,
      venderUuidMap: data.shipmentInfo.venderUuidMap,
      // overseasPickInfo: undefined,
      // 自提点id
      promiseList: smztAddress?.pickId
        ? [
            {
              // ...smztAddress,
              ...(smztAddress?.pickId
                ? {
                    pickId: smztAddress.pickId,
                    // 自提点名称
                    pickName: smztAddress.pickName,
                    // 自提点地址
                    pickAddress: smztAddress.address,
                    selected: true,
                    // 当前选中自提点时间列表
                    // pickDateList: [smztTime],
                    // 自提点类型
                    siteType: smztAddress.siteType,
                  }
                : {}),
              // promiseDate: smztTime,
            },
          ]
        : [{ selected: false }],
    },
    // isZyVender:!!data.baseData.isSelfVendor,
  }
  return balance_saveBundleShipment_pc({
    venderId: data.baseData.venderId,
    bundleUuid: data.baseData.bundle.bundleId,
    addrId: data.addrId,
    saveShipmentParam: saveShipmentParam,
    isZyVender: !!data.baseData.isSelfVendor,
    transportCode: data.shipmentInfo.shipmentType,
    balanceCommonOrderForm: {
      supportTransport: true,
      overseasTransport: true,
    },
  })
}
