/**
 * @file: DeliveryModule.tsx
 * @description: 配送模块，用于展示配送等逻辑
 */
import { useEffect, useState } from 'react'
import SelectItemV2 from '../selector/SelectItemV2'
import Delivery from '../deliverys/domestic/delivery2'
import DeliveryHwjy from '../deliverys/overseas/deliveryHwjy'
import Skeleton from '../../ui/skeleton'
import { api_GetBundleShipmentList } from '@app/services/api'
import { DeliveryDataIniter, DeliveryHWJyDataIniter } from '../initDeliveryData'
import { selectDataProcess } from '../selector/initSelectData'
import { useDeliveryHooks } from './hooks'
import { getDefaultTsx } from './utils'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'

export default function DeliveryModule() {
  const { wrapperRef, isOpen, setIsOpen, initState, deliveryLogger, setErrorMessage, errorMessage, defErrMsg } = useDeliveryHooks()

  const { deliveryItem, transportCtrl } = selectDataProcess(initState.bundle)
  const [deliveryDataIniter, setDeliveryDataIniter] = useState<DeliveryDataIniter | null>(null)
  const [hwjyDataIniter, setHwjyDataIniter] = useState<DeliveryHWJyDataIniter | null>(null)

  useEffect(() => {
    setErrorMessage('')
    if (!isOpen) {
      setDeliveryDataIniter(null)
      setHwjyDataIniter(null)
      return
    }
    api_GetBundleShipmentList({
      bundleUuid: initState.bundle.bundleId,
      venderId: initState.venderId,
      transportCtrl: transportCtrl,
    })
      .then((res) => {
        let initer = null
        if (transportCtrl) {
          initer = new DeliveryHWJyDataIniter(initState, res)
          setHwjyDataIniter(initer)
          setDeliveryDataIniter(null)
        } else {
          initer = new DeliveryDataIniter(initState, res)
          setDeliveryDataIniter(initer)
          setHwjyDataIniter(null)
        }
        if (initer.getErrorMessage()) {
          setErrorMessage(defErrMsg)
        }
      })
      .catch((error) => {
        setErrorMessage(defErrMsg)
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.Delivery,
          msg: {
            type: '配送模块',
            error: error,
            data: initState.bundle,
            info: '请求配送信息失败',
          },
        })
      })
  }, [isOpen, initState, transportCtrl])

  const deliveryTime = deliveryItem?.items.reduce((prev, curr) => {
    return prev + curr.text
  }, '')

  useEffect(() => {
    if (deliveryTime) {
      deliveryLogger.shipmentEXPO(deliveryTime)
    }
  }, [deliveryTime])

  const defaultString = deliveryItem?.items.slice(2).reduce((prev, curr) => prev + curr.text, '') || ''
  const defaultTsx = () => getDefaultTsx(defaultString, { hwjyDataIniter, deliveryDataIniter }, () => setIsOpen(false))

  const getDeliveryTsx = () => {
    if (!isOpen) return null
    if (errorMessage) return defaultTsx()
    if (!(hwjyDataIniter || deliveryDataIniter)) return <Skeleton onClick={() => setIsOpen(false)} />
    if (transportCtrl && hwjyDataIniter) {
      return <DeliveryHwjy defaultTsx={() => defaultTsx()} onCancel={() => setIsOpen(false)} deliveryHwjyDataIniter={hwjyDataIniter} />
    }
    if (!transportCtrl && deliveryDataIniter) {
      return <Delivery defaultTsx={() => defaultTsx()} onCancel={() => setIsOpen(false)} deliveryDataIniter={deliveryDataIniter} />
    }
    return null
  }
  if (!deliveryItem) return null

  return (
    <div ref={wrapperRef} style={{ display: 'flex', justifyContent: 'flex-start', maxWidth: '100%' }}>
      <SelectItemV2
        onClick={() => {
          setIsOpen(!isOpen)
          deliveryLogger.shipmentClick(deliveryTime || '')
          deliveryLogger.orddetail('0')
        }}
        isOpen={isOpen}
        canOpen={!!deliveryItem?.canOpen}
        items={deliveryItem?.items}
        noBorder={true}
        defalutAlignRight={true}
      >
        {getDeliveryTsx()}
      </SelectItemV2>
    </div>
  )
}
