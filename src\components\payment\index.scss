.payment {
  overflow: hidden;
  position: relative;
  padding: 3px;
  background-color: #fff;
  border-radius: 8px;
}

.payment__bg {
  user-select: none;
  position: absolute;
  content: '';
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.payment-summary {
  position: relative;
  z-index: 1;
}

.payment-summary-inner {
  scrollbar-width: none;
  scroll-behavior: smooth;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.payment-summary-inner>div {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.payment-summary-title {
  padding: 21px 13px 16px 13px;

  em {
    color: #1a1a1a;
    font-size: 20px;
    line-height: 20px;
  }

  span {
    margin-left: 8px;
    color: #505259;
    font-size: 14px;
    line-height: 16px;
  }

  i {
    font-family: JDZhengHeiVRegular2-1;
  }
}

.payment-summary-item {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  padding: 0 13px;
  color: #1a1a1a;
  font-size: 14px;
  line-height: 1;
  margin-bottom: 16px;
}

.payment-summary-item__title {
  display: inline-flex;
  align-items: center;

  em {
    font-size: 16px;
  }

  i {
    margin-left: 4px;
  }
}

.payment-summary-item__price {
  font-family: JDZhengHeiVRegular2-1;
}

.payment-summary-item__price--red {
  color: #FF0F23;
}

.payment-summary-item__price-icon {
  display: inline-flex;
  align-items: center;

  &::after {
    content: '\E605';
    display: inline-block;
    width: 12px;
    height: 12px;
    // background-image: url('//img12.360buyimg.com/ling/jfs/t1/276140/25/10969/303/67e4c142Fc0a2ebb3/b17a948fbd3aaea8.png');
    // background-size: 8px 8px;
    // background-repeat: no-repeat;
    margin-left: 3px;
    -webkit-text-stroke-width: .2px;
    -moz-osx-font-smoothing: grayscale;
    font-family: iconfont, sans-serif;
    font-weight: bold;
    font-size: 12px;
  }

  cursor: pointer;
}

.payment-summary-item__price--r180 {
  &::after {
    transform: rotate(180deg) translateY(1px);

  }
}

.payment-summary-item__detail {
  // display: none;
  width: 100%;
  background-color: #F7F8FC;
  padding: 12px;
  border-radius: 8px;
  margin-top: 16px;
}

.payment-summary-item__detail-desc {
  color: #888B94;
  font-size: 12px;
  line-height: 1.3;

  span {
    &::after {
      display: inline-block;
      content: "|";
      padding: 0 6px;
      font-size: 10px;
      vertical-align: top;
    }

    &:last-child::after {
      display: none;
    }
  }
}

.payment-summary-item__detail-items {
  padding-top: 8px;
  margin-right: -12px;

  &:last-child {
    margin-bottom: -8px;
  }

  div {
    display: inline-block;
    box-sizing: border-box;
    overflow: hidden;
    width: 48px;
    height: 48px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    margin-right: 8px;
    margin-bottom: 8px;

    img {
      width: 100%;
      vertical-align: top;
    }
  }
}

.payment-summary-method {
  // padding: 4px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  margin: 0 13px;
}

.payment-method-item {
  display: flex;
  align-items: center;
  margin: 16px 0;

  i {
    margin-left: 4px;
  }

  &__tip {
    color: #fff;

    a {
      color: #33ADFF;

      &:hover {
        color: #F30213;
      }
    }
  }
}

.payment-method-item__disabled,
.payment-method-item__addition {
  color: #888B94;
  font-size: 14px;
  margin-left: 8px;
  display: none;
}

.checkbox.indeterminate~.payment-method-item__disabled,
.payment-method-item__addition {
  display: block;
}

.payment-summary-agreement {
  // padding: 4px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  margin: 0 13px;
}

.payment-agreement-item {
  display: flex;
  align-items: center;
  margin: 16px 0;
}

.payment-agreement-item__desc {
  color: #1a1a1a;
  font-size: 14px;
  line-height: 16px;
  // margin-left: 8px;

  a {
    color: #0073FF;

    &:hover {
      color: #0073FF;
      text-decoration: underline;
    }
  }

  .agreement-presale-tips {
    color: #888B94;
    margin-left: 4px;
  }
}

.payment-summary-plus {
  padding: 0 13px;

  img {
    width: 100%;
    vertical-align: top;
  }
}

.payment-summary-password {
  background-color: #F7F8FC;
  padding: 16px 12px;
  border-radius: 8px;
  margin: 0 13px;
}

.payment-summary-password__title {
  display: flex;
  justify-content: space-between;
  color: #1a1a1a;
  font-size: 14px;
  line-height: 16px;

  a {
    color: #888B94;

    &:hover {
      color: #888B94;
      text-decoration: underline;
    }
  }
}

.payment-summary-password__input {
  padding-top: 8px;

  .otp-input {
    flex: 1;
  }

  .long-passwd {
    .otp-input {
      width: 100%;
      padding: 0 10px;
      text-align: left;
    }
  }
}

/*
.payment-summary-checkcode {
  display: flex;
  align-items: center;
  padding: 8px 13px;
  line-height: 1;
}

.payment-summary-checkcode__title {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: bold;
}

.payment-summary-checkcode__content {
  margin-left: auto;

  img {
    display: inline-block;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    cursor: pointer;
  }

  input {
    margin-left: 8px;
    width: 140px;
  }
}
*/

.payment-action {
  position: relative;
  z-index: 1;
  padding: 21px 13px;
}

.payment-action__inner {
  display: flex;
  height: 48px;
}

.payment-action-total {
  flex: 1 97px;
  width: 97px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 5px;
}

.payment-action-total__title {
  color: #1A1A1A;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: 0px;
  user-select: none;
}

.payment-action-toggle {
  display: inline-flex;
  margin-left: 8px;
  color: #505259;
  cursor: pointer;

  &::before {
    content: '展开明细';
  }

  &::after {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url('https://img14.360buyimg.com/img/jfs/t1/211436/12/37156/597/67922113F28313c32/c831b563e9c60d7c.png');
    background-size: 8px auto;
    background-position: center;
    background-repeat: no-repeat;
  }
}

.payment-action-total__price,
.payment-action-total__eprice {
  color: #fa2c19;
  font-family: JDZhengHeiVRegular2-1;
  font-weight: bold;
  font-size: 20px;
  line-height: 20px;
  // text-indent: -4px;
}

.payment-action-total__eprice {
  line-height: 1;
  text-indent: 0;
  font-size: 12px;
  font-weight: normal;
}

.payment-action-submit {
  // flex: 2 220px;
  width: 220px;
  display: flex;
  justify-content: flex-end;

  button {
    flex: 1;
    padding: 0 12px;
    font-weight: 600;
  }

  button+button {
    margin-left: 6px;
  }

  .orange-style {
    border-color: #FF7219;
    background: #FF7219;

    &:hover {
      @extend .orange-style
    }
  }
}

.payment--expanded {
  .payment-summary {
    display: block;
  }

  .payment-action-toggle {
    &::before {
      content: '收起明细';
    }

    &::after {
      transform: rotate(180deg);
    }
  }
}

.payment-state-subsidy {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #F0FAF4;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 12px;
  line-height: 1;
}

.payment-state-subsidy__text {
  color: #1a1a1a;

  &::before {
    display: inline-block;
    content: '';
    width: 49px;
    height: 16px;
    background: url(//img14.360buyimg.com/ling/jfs/t1/315130/23/15168/2309/686c8ce1Ff8740a33/cb89694def365ed4.png) no-repeat center/43px 10px #0AAD48;
    border-radius: 2px;
    margin-right: 6px;
    vertical-align: sub;
  }

  span:nth-child(1) {
    &::before {
      content: '【';
      margin-left: -3px;
    }

    &::after {
      content: '】';
      margin-right: -3px;
    }
  }

  span:nth-child(2) {
    color: #0AAD48;
    font-family: JDZhengHeiVRegular2-1;
  }
}

.payment-state-subsidy__action {
  margin-left: auto;
  color: #0AAD48;
  cursor: pointer;
  white-space: nowrap;

  &::after {
    content: '\e601';
    font-family: iconfont, sans-serif;
    font-weight: bold;
  }
}