import { AUTHAUTH_PAGE_URL } from '@app/services/const'
import ConfirmDialog from './ConfirmDialog'
import { popup } from '../popup'
import type { ModalProps } from '../popup'
import type { ConfirmDialogProps } from './ConfirmDialog'

const defaultOptions: ModalProps['options'] = {
  mask: true,
  position: 'center-center',
  wrapperStyle: { zIndex: 100 },
}

export const confirm = (props?: ConfirmDialogProps, options?: ModalProps['options']) =>
  popup(<ConfirmDialog {...props} />, { ...defaultOptions, ...options })

const CommonConfirmDialogConfig = [
  [
    'confirmUserAgreement',
    {
      title: '请查阅并同意协议后继续下单',
      okText: '我知道了',
    },
  ],
  [
    'confirmRealName',
    {
      title: '实名认证未开启',
      description: '为保障您的账户资金安全，请先开启实名认证',
      okText: '前往开启',
      onOk: () => {
        window.open(AUTHAUTH_PAGE_URL)
      },
      // cancelText: '下次说',
    },
  ],
  [
    'confirmBindBankcard',
    {
      title: '绑定银行卡',
      description: '有机会享政府&京东至少10%双重购新补贴',
      okText: '去完善',
      cancelText: '下次说',
    },
  ],
  [
    'confirmPaymentPassword',
    {
      title: '支付密码未开启',
      description: '为保障您的账户资金安全，请先开启支付密码',
      okText: '前往开启',
      cancelText: '关闭',
    },
  ],
] as const

export const CommonConfirmDialogs: Record<(typeof CommonConfirmDialogConfig)[number][0], typeof confirm> = CommonConfirmDialogConfig.reduce(
  (acc, [func_name, _props]) => ({
    ...acc,
    [func_name]: (props?: ConfirmDialogProps, options?: ModalProps['options']) => confirm({ ..._props, ...props }, options),
  }),
  {} as Record<(typeof CommonConfirmDialogConfig)[number][0], typeof confirm>,
)
