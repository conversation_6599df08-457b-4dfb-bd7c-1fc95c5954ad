import React from 'react'
import styles from './index.module.scss'

interface PropsType {
  arr?: any[]
  children?: React.ReactNode
  text?: string
}

const NoData: React.FC<PropsType> = ({ arr, children, text = '没有更多了~' }) => {
  // 有数据显示数据，无数据提示文案
  if (Array.isArray(arr) && arr.length > 0) {
    return children
  } else {
    return (
      <div className={styles.noData}>
        <span>{text}</span>
      </div>
    )
  }
}

export default NoData
