interface ItemPointObj {
  pointData: any
  setData: (key: string, value: any) => void
  getData: (key: string) => any
}
type StringOrNumber = string | number
type ExposeKey = StringOrNumber | Element
type ESObject = Record<string, any>

// 回调函数中拼接的参数。

interface ExposureReportCallbackResult {
  key?: string
  dom: HTMLElement
  logType: string
  data: Dictionary
  exposalUrl?: string
}

interface IExposeDomInfo {
  // 标识dom的唯一key值
  key: ExposeKey
  // 需要曝光的dom元素
  dom?: HTMLElement
  // dom元素相关的上报参数
  params: ESObject | string | null
  // 上报类型 埋点文档的logtype
  logType?: string
  extParams?: ESObject | string | null
}

// 补全埋点公共参数的类型 返回值的类型
interface ClickReportCallback {
  (commonPoint: Dictionary): { logType: string; data: Dictionary }
}

interface ExposureReportCallback {
  (commonPoint: Dictionary): ExposureReportCallbackResult
}

interface ExposureDataFormater {
  <T>(storage: ExposeBuriedPointsStorage): T
}

type ExposeBuriedPointsStorage = Map<ExposeKey, IExposeDomInfo>
