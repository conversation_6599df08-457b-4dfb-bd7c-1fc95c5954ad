/**
 * @file: SelectBody.tsx
 * @description: 模块弹窗内容组件,主要包含头部选择组件
 */
import { useDelivery } from '../../context.tsx'
import Header from '../../ui/header.tsx'
import { BaseItem as UiHeaderBaseItem, List as HeaderList, ListItem as TopHeaderItem } from '../../ui/header.tsx'
import { useEffect, useState } from 'react'
export const chooseHeaderItem = <P, S>(
  headerList: HeaderList<P, S>,
  headerItem: UiHeaderBaseItem<S> | TopHeaderItem<P, S>,
): HeaderState<P, S> => {
  let sonItem: UiHeaderBaseItem<S> = null as unknown as UiHeaderBaseItem<S>
  let parent = null as unknown as TopHeaderItem<P, S>
  if ('sonList' in headerItem) {
    parent = headerItem
    headerList.forEach((item) => {
      if (item === headerItem) {
        item.isSelected = true
      } else {
        item.isSelected = false
      }
    })
    if (headerItem.sonList.length > 0) {
      const c = headerItem.sonList.find((item) => item.isSelected)
      if (!c) {
        headerItem.sonList[0].isSelected = true
        sonItem = headerItem.sonList[0]
      } else {
        sonItem = c
      }
    }
  } else {
    parent = headerList.find((item) => item.isSelected) as TopHeaderItem<P, S>
    if (parent) {
      parent.sonList?.forEach((item) => {
        if (item === headerItem) {
          item.isSelected = true
        } else {
          item.isSelected = false
        }
      })
    }
    sonItem = headerItem
  }
  return { headerList: [...headerList], currentTopItem: parent, currentItem: sonItem }
}

export const getDefalutItem = <P, S>(headerList: HeaderList<P, S>): HeaderState<P, S> | null => {
  let sonItem = undefined as unknown as UiHeaderBaseItem<S>
  const parent = headerList.find((item) => item.isSelected)
  if (!parent) {
    return null
  }
  parent.sonList?.forEach((item) => {
    if (item.isSelected) {
      sonItem = item
    }
  })
  return { headerList: headerList, currentTopItem: parent, currentItem: sonItem }
}

export type HeaderState<P, S> = { headerList: HeaderList<P, S>; currentTopItem: TopHeaderItem<P, S>; currentItem?: UiHeaderBaseItem<S> }

export type Props<P, S> = {
  children?: React.ReactNode
  isDelivery?: boolean
  headerList: HeaderList<P, S>
  onChange: (s: HeaderState<P, S>) => void
}

export default <P, S>({ headerList, children, onChange, isDelivery }: Props<P, S>) => {
  const [state, setState] = useState<HeaderState<P, S> | null>(getDefalutItem(headerList))

  useEffect(() => {
    if (!headerList) return
    setState(getDefalutItem(headerList))
  }, [headerList])

  useEffect(() => {
    onChange && state && onChange(state)
  }, [state?.currentItem, state?.currentTopItem])

  const myOnChange = (item: UiHeaderBaseItem<S> | TopHeaderItem<P, S>) => {
    if (!state) return
    setState(chooseHeaderItem(state.headerList, item))
  }

  const { deliveryLogger } = useDelivery()

  return (
    state &&
    state.headerList && (
      <>
        <Header<P, S>
          list={state.headerList}
          clickHeader1Handler={(item) => {
            myOnChange(item)
            isDelivery && deliveryLogger.shipmentlayer('2', item.text, '')
            isDelivery && deliveryLogger.shipmentPOPClick({ toptabname: item.text })
          }}
          clickHeader2Handler={(item) => {
            myOnChange(item)
            isDelivery && deliveryLogger.shipmentPOPClick({ second_tab_name: item.text })
          }}
        />
        <div style={{ marginTop: '20px' }}></div>
        {children}
        {/* <br /> */}
        {/* <br /> */}
      </>
    )
  )
}
