.presale {
  padding: 24px 16px;
  border-radius: 8px;
  background-color: #fff;
}

.presale-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
}

.presale-contact,
.presale-desc {
  display: flex;
  align-items: center;
  height: 36px;
  line-height: 36px;
  margin-top: 24px;
  color: #1a1a1a;
  font-size: 14px;

  &>* {
    margin-left: 12px;
  }
}

.presale-contact-tel {
  font-weight: bold;

  i {
    cursor: pointer;
    display: inline-block;
    margin-left: 8px;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    background-size: 12px 12px;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAVxJREFUSEu9lc1RwkAUgL+HDaQErEDsACpww8lwwgrUCixBOnC4AF7gWQFjBdCBKcGzM+Q5gYEhZIMJBPe6u9+3+352hROHc70WjdULJgHGg+ok9qHkFP4GzhwsWO83Ysw6PkllQQZuDEFuEGsVSSoJDuE6G/ed6wfIz7xIUlrgg2/Dm5XYh84mbjtXSnAMvpN0e29gfbBPnU7apQXV4PJNktzuJ/voDarD6aiOlvuVWSioA56KvIK64F5BnfCcwLn7Jo3GYt2hxjCt88NOd7tqSROaj/nh+kyIXDd6Al5BVKej8Fx4/gZhNEB4hORZp++DfUHVk3v7wIXRHKFNQqg61nwTlQtLYZm6sLdYvykJIaygcdXE7A6RNlSH+0K0uUFunAYvqCJJQxMgtiSRGCEmMS36UP76T0o9dn9Bjs3/n8B1oy+gec5pd3uNWGfj60wOLi6o5eQeyMVz8AuRzf8Zj/HCYwAAAABJRU5ErkJggg==);

    &:hover {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAVpJREFUSEu9ld9Rg0AQh38/CgBKQBhnfDN2kFSg6SBWoFZgCaYDJx2QCjJWkLw7REqA+KYD60DE8OcgQIj3evB9t3u7e0TPJeblCBI/AzDB6J6B76tQ7MP/ha9SeLrEB+OJStJZUIATC4hcAxzVSToJynAG3kxMy4RoqzpJa4EKnqW3IBEsufPusr1Wgib4n0R3XkHMALwx9MatBR3hARjd5C+7MYLucG3C4H2Tr8xawRDwRKQUDAVXCoaEVwRiXlmQ7/W+/bFI6rzc6XKolgCs5rz8fSFFotuPIF8AuAy96anwagS6PQf5AJEn7rbzvKDryZV9IIaTDLAx4mjKT99VNFGrtNSWqRj2Op0pcbRPDzUL5G0qBTrDqynKIqjO8F7wmir6cgGaEGzSEQz40GK37kE59p60GnbHIE37/ycQw/kAYJ1y2sO/4jPcXhTu4OyCYU5epZz9Dn4AhETlGVKVdpkAAAAASUVORK5CYII=);
    }
  }
}

.presale-contact-tel-input {
  position: relative;
  display: inline-flex;
  box-sizing: border-box;
  width: 240px;
  height: 36px;
  border: 0.5px solid #ff8595;
  border-radius: 4px;

  input {
    border: 0;
    outline: 0;
    border-radius: 5px;
    text-indent: 12px;
  }

  span {
    margin-left: auto;
    cursor: pointer;
    position: relative;
    width: 52px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    color: #FF0F23;
    font-weight: 600;

    &::before {
      content: '';
      position: absolute;
      z-index: 11;
      top: 12px;
      left: 0px;
      width: 1px;
      height: 12px;
      background-color: rgb(255, 235, 239);
    }
  }

  .error-msg { 
    position: absolute;
    left: 0;
    top: 40px;
    height: 12px;
    line-height: 1;
    color: #FF0F23;
    font-size: 12px;
  }
}