import { useEffect, useState, useRef, useCallback } from 'react'
import { throttle } from '@app/utils/throttledebounce'

const useEllipsisDetect = () => {
  const ref = useRef<HTMLElement>(null)
  const [hasEllipsis, setHasEllipsis] = useState(false)

  // 使用 useCallback 缓存检测函数
  const detectEllipsis = useCallback(() => {
    const el = ref.current
    if (el) {
      const style = window.getComputedStyle(el)
      // 增加检测条件，确保元素设置了 overflow: hidden 和 white-space: nowrap
      const isEllipsisActive =
        style.textOverflow === 'ellipsis' && style.overflow === 'hidden' && style.whiteSpace === 'nowrap' && el.scrollWidth > el.offsetWidth
      setHasEllipsis(isEllipsisActive)
    }
  }, [])

  // 监听窗口大小变化，更新省略号状态
  useEffect(() => {
    detectEllipsis()

    const handleResize = throttle(100, () => {
      detectEllipsis()
    })

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [detectEllipsis])

  return { ref, hasEllipsis }
}

export default useEllipsisDetect
