import { useCallback } from 'react'
// import { GetInvoiceForm, InvoiceInfoEditVO, InvoiceResult } from '@app/typings/invoice'
import { GetInvoiceForm } from '@app/typings/invoice'
import { getInvoice } from '@app/services/api'
import { useInvoiceStateUpdater } from './useInvoiceStateUpdater'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
// mock数据
// import res from '../mock/response.json'

/**
 * @description 发票查询hook，用于调用查询接口并更新状态
 * @param updateInvoiceState - 更新发票状态的函数，应从父组件传入
 * @param onError - 错误处理函数，可选
 * @param monitoring - 监控函数，可选
 * @returns 查询相关的状态和方法
 */
export function useInvoiceQuery() {
  const { updateInvoiceState } = useInvoiceStateUpdater()

  /**
   * @description 查询发票信息
   * @param {GetInvoiceForm | undefined} params - 查询参数，如果为undefined则使用默认参数
   * @param {boolean} loading - 是否在加载中
   * @param {() => void} setLoading - 设置加载状态
   */
  const fetchInvoiceInfo = useCallback(
    async (params?: GetInvoiceForm) => {
      const requestParams = params || ({} as GetInvoiceForm)

      try {
        const response = await getInvoice(requestParams)
        // const response = res as unknown as InvoiceResult<InvoiceInfoEditVO>
        if (response?.code === '0') {
          // 使用传入的状态更新函数
          updateInvoiceState(response as any)
          return response
        } else {
          // 处理业务错误
          if (monitoring && monitorName && monitorCode) {
            monitoring({
              name: monitorName.Settlement,
              code: monitorCode.Invoice,
              msg: {
                type: '发票',
                action: '发票选择',
                error: response?.message,
                code: response?.code,
                requestParms: requestParams,
                responseData: response,
                traceId: response?.traceId,
                requestId: response?.requestId,
              },
            })
          }
          throw new Error(response?.message || '获取发票信息失败')
        }
      } catch (err) {
        console.error('获取发票信息失败:', err)
        // 处理异常
        if (monitoring && monitorName && monitorCode) {
          monitoring({
            name: monitorName.Settlement,
            code: monitorCode.Invoice,
            msg: {
              type: '发票',
              action: '发票选择',
              error: '发票查询异常:' + err,
              requestParms: requestParams,
              responseData: err,
            },
          })
        }
        throw err
      }
    },
    [updateInvoiceState],
  )

  return { fetchInvoiceInfo }
}
