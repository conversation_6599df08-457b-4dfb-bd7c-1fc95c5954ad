import { FC } from 'react'
import { InvoiceTypeVO } from '@app/typings/invoice'
import { useAtomValue, useSetAtom } from 'jotai'
import { currentNormalInvoiceContentsAtom, currentBookInvoiceContentsAtom } from '@app/components/Invoice/atom/invoiceAtom'
import { handleInvoiceContentChangeAtom } from '@app/components/Invoice/atom/invoiceAction'
import { Form } from '@app/common/legao'

interface InvoiceContentSelectorProps {
  contentType?: 'normal' | 'book'
  customLabel?: string
}

/**
 * 发票内容选择器组件
 * 根据发票类型和商品类型显示对应的内容选项
 */
const InvoiceContentSelector: FC<InvoiceContentSelectorProps> = ({ contentType = 'normal', customLabel }) => {
  // 使用派生状态获取当前选中的发票内容
  const normalContents = useAtomValue(currentNormalInvoiceContentsAtom)
  const bookContents = useAtomValue(currentBookInvoiceContentsAtom)
  const handleInvoiceContentChange = useSetAtom(handleInvoiceContentChangeAtom)

  // 根据contentType确定使用哪个内容
  const currentContents = contentType === 'normal' ? normalContents : bookContents
  /**
   * 根据选择的内容获取提示信息
   */
  const getContentNote = (content: InvoiceTypeVO[]) => {
    const selectedOption = content.find((option) => option.selected) || content[0] || {}
    return selectedOption?.descLabel || ''
  }

  /**
   * 点击处理函数
   */
  const handleContentChange = (value: number) => {
    // 避免重复点击已选中的选项
    const isAlreadySelected = currentContents?.find((item) => item.value === value)?.selected
    if (isAlreadySelected) {
      return
    }
    // 触发状态更新
    handleInvoiceContentChange({ contentValue: value, contentType })
  }

  /**
   * 生成标签文本
   */
  const getLabelText = () => {
    if (customLabel) return customLabel
    return contentType === 'normal' ? '发票内容' : '图书发票内容'
  }

  return (
    <>
      {/* 如果内容数组为空，则不显示发票内容选择器 */}
      {currentContents && currentContents.length > 0 && (
        <Form.Item label={getLabelText()} required>
          <div>
            <div className="radio-group">
              {currentContents.map((option) => (
                <div
                  key={option.value}
                  className={`radio-button ${option.selected ? 'active' : ''}`}
                  onClick={() => handleContentChange(option.value)}
                >
                  {option.content}
                </div>
              ))}
            </div>
            {/* 发票内容提示：当发票内容为空时，不显示提示 */}
            {currentContents && getContentNote(currentContents) !== '' && (
              <div className="invoice-note">
                {/* <img
                  src={'//img13.360buyimg.com/ling/jfs/t1/319798/31/6255/735/683ee395Fbd94e235/be602901616ec835.png'}
                  alt="noticeArrow"
                /> */}
                <span className="invoice-note-text"> {getContentNote(currentContents)} </span>
              </div>
            )}
          </div>
        </Form.Item>
      )}
    </>
  )
}

export default InvoiceContentSelector
