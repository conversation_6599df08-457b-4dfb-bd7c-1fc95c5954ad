/**
 * @file: useDeliveryState.ts
 * @description: 配送状态相关钩子，用于处理配送状态相关逻辑
 */
import { useState, useEffect, useRef } from 'react'
import { DeliveryDataIniter } from '../../../initDeliveryData'
import { ComShipmentType, PromiseListItem } from '@app/typings/api_getBundleShipmentList_resp'
import { HeaderState } from '../../../selector/SelectBody'
import { SaveType } from '../../../saveDelivery'
import { List as HeaderList } from '../../../../ui/header'

export function useDeliveryState(deliveryDataIniter: DeliveryDataIniter) {
  const [headerList, setHeaderList] = useState<HeaderList<ComShipmentType, PromiseListItem>>(deliveryDataIniter.getHeaderList())
  const [promise, setPromise] = useState<PromiseListItem | null>(null)
  const [shipmentInfo, setShipmentInfo] = useState<ComShipmentType | null>(null)
  const [selectValue, setSelectValue] = useState<{ type: SaveType; value: any } | null>(null)

  const isEmpty = useRef(false)

  // 更新 headerList
  useEffect(() => {
    setHeaderList(deliveryDataIniter.getHeaderList())
  }, [deliveryDataIniter])

  // 处理头部选择变化
  const changeHeader = (headerState: HeaderState<ComShipmentType, PromiseListItem>) => {
    if (!headerState?.currentTopItem) {
      isEmpty.current = true
      setPromise(null)
      setSelectValue(null)
      return
    }

    setShipmentInfo(headerState.currentTopItem.targetValue)

    if (!headerState?.currentItem) {
      const _pList = deliveryDataIniter.getPromiseByShipmentType(headerState.currentTopItem.targetValue)
      if (_pList?.length === 1) {
        setPromise(_pList[0])
      } else {
        setPromise(null)
        setSelectValue(null)
        isEmpty.current = true
      }
    } else {
      setPromise(headerState.currentItem.targetValue)
    }
  }

  // 处理选择值变化
  const handleSelectValue = (type: SaveType | null, v: any) => {
    if (!type) {
      setSelectValue(null)
    } else {
      setSelectValue({ type, value: v })
    }
  }

  return {
    headerList,
    promise,
    shipmentInfo,
    selectValue,
    isEmpty: isEmpty.current,
    changeHeader,
    handleSelectValue,
    setSelectValue,
  }
}
