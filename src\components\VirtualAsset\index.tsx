/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-02 17:28:54
 * @LastEditors: ext.wangchao120
 * @Description: 虚拟资产楼层入口
 * @FilePath: /pc_settlement/src/components/VirtualAsset/index.tsx
 */

import React, { useState } from 'react'
import styles from './index.module.scss'
import Tabs from './components/Tabs'
import Coupons from './Coupons'
import JdBean from './JdBean'
import GiftCard from './GiftCard'
import DeliveryCode from './DeliveryCode'
import RedEnvelope from './RedEnvelope'
import Title from './title'
import VirtualAssetSkeleton from './Skeleton'
import Alert from './components/Alert'
import Balance from './Balance'
import useMasterData from '@app/hooks/useMasterData'
import type { BalanceVirtualAssetsVO } from '@app/typings/master_api_response'
import ErrorBoundaryDispose from '@app/common/ErrorBoundaryDispose'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import { useAtomValue } from 'jotai'
import { loadingAtom } from '@app/atoms/loadingAtom'
import { virtualAssetLoading } from '@app/components/VirtualAsset/atoms'
import { Loading } from '@app/common/legao'

const VirtualAsset: React.FC = () => {
  // 主数据
  const masterData = useMasterData()?.body ?? {}
  const balanceVirtualAssetsVO = (masterData?.balanceVirtualAssetsVO as BalanceVirtualAssetsVO) ?? {}
  const [tabKey, setTabKey] = useState<string>('coupons')
  const loading = useAtomValue(virtualAssetLoading)
  const onChange = (key: string) => {
    setTabKey(key)
  }
  return (
    <>
      <div className={styles.virtualAsset}>
        {/* loading 组件 */}
        <Loading loading={loading} />
        <Title />
        <Tabs onChange={onChange}>
          <Tabs.Panel tabKey="coupons" tab="优惠劵" dot={(masterData?.couponVOList ?? []).length > 0}>
            <Coupons tabKey={tabKey} />
          </Tabs.Panel>
          <Tabs.Panel tabKey="jdBean" tab="京豆" dot={balanceVirtualAssetsVO?.disableJdBeanShow} hiden={!balanceVirtualAssetsVO?.jdBean}>
            <>
              {/* 国补提示 */}
              <Alert msg={balanceVirtualAssetsVO?.virtualAssetsConfigVO?.gbJdbeanPayTip} />
              <JdBean />
            </>
          </Tabs.Panel>
          <Tabs.Panel tabKey="gift" tab="礼品卡" dot={balanceVirtualAssetsVO?.availableGiftNum > 0}>
            <>
              {/* 国补提示 */}
              <Alert msg={balanceVirtualAssetsVO?.virtualAssetsConfigVO?.gbCardTip} />
              <GiftCard tabKey={tabKey} />
            </>
          </Tabs.Panel>
          <Tabs.Panel tabKey="deliveryCode" tab="领货码" dot={balanceVirtualAssetsVO?.availableConsignmentGiftNum > 0}>
            <>
              {/* 国补提示 */}
              <Alert msg={balanceVirtualAssetsVO?.virtualAssetsConfigVO?.gbCardTip} />
              <DeliveryCode tabKey={tabKey} />
            </>
          </Tabs.Panel>
          <Tabs.Panel
            tabKey="redEnvelope"
            tab="红包"
            hiden={!balanceVirtualAssetsVO?.forbidRedPacket}
            dot={balanceVirtualAssetsVO?.forbidRedPacket}
          >
            <>
              {/* 国补提示 */}
              <Alert msg={balanceVirtualAssetsVO?.virtualAssetsConfigVO?.gbRedPayTip} />
              <RedEnvelope tabKey={tabKey} />
            </>
          </Tabs.Panel>
          <Tabs.Panel tabKey="balance" tab="余额" hiden={!balanceVirtualAssetsVO?.balanceShow} dot={balanceVirtualAssetsVO?.balanceRedShow}>
            <Balance tabKey={tabKey} />
          </Tabs.Panel>
        </Tabs>
      </div>
    </>
  )
}

const DefaultVirtualAsset: React.FC = () => {
  const traceId = useMasterData()?.traceId ?? ''
  const requestId = useMasterData()?.requestId ?? ''
  const masterData = useMasterData()?.body
  const loading = useAtomValue(loadingAtom)
  // const FallbackComponent = () => {
  //   return <div>虚拟资产组件异常</div>
  // }
  const errorToMonitoring = (error: any, errInfo: any) => {
    try {
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.VirtualAsset,
        msg: {
          description: '虚拟资产组件异常',
          error: JSON.stringify(error),
          info: JSON.stringify(errInfo),
        },
        extra: {
          traceId,
          requestId,
        },
      })
    } catch (error) {
      console.error(error)
    }
  }
  return (
    <ErrorBoundaryDispose FallbackComponent={VirtualAssetSkeleton} logErrorToService={errorToMonitoring}>
      {loading || !masterData ? <VirtualAssetSkeleton /> : <VirtualAsset />}
    </ErrorBoundaryDispose>
  )
}
export default DefaultVirtualAsset
