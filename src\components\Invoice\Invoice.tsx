import InvoiceNoticeModal from './Modal/InvoiceNoticeModal'
import { useAtom, useAtomValue } from 'jotai'
import { showInvoiceModalAtom, showNoticeModalAtom, invoiceInfoMessageAtom, isBpinAtom } from '@app/components/Invoice/atom/invoiceAtom'
import useMasterData from '@app/hooks/useMasterData'
import { useState, useEffect } from 'react'
import InvoiceModal from './InvoiceModel'
import InvoiceSkeleton from './skeleton/InvoiceSkeleton'
import './Invoice.scss'
import { BalanceInvoiceDesc, BalanceVendorBundleType } from '@app/typings/master_api_response'
import monitoring, { monitorName, monitorCode } from '@app/utils/monitoring'
import ErrorBoundaryDispose from '@app/common/ErrorBoundaryDispose'
import { InvoiceLayerPointParams, InvoiceLogger, InvoicePointParams } from './InvoiceLogger'
import useIntersectionObserverOnce from '@app/hooks/useIntersectionObserverOnce'

/**
 * 发票楼层组件
 */
const Invoice = () => {
  // UI状态
  const [showInvoiceModal, setShowInvoiceModal] = useAtom(showInvoiceModalAtom)
  const [showNoticeModal, setShowNoticeModal] = useAtom(showNoticeModalAtom)
  // 引入主接口数据
  const masterData = useMasterData()
  const isBpin = useAtomValue(isBpinAtom)
  // 获取保存的发票信息
  const [invoiceInfoMessage, setInvoiceInfoMessage] = useAtom(invoiceInfoMessageAtom)
  // 添加loading状态
  const [loading, setLoading] = useState(true)
  const [defaultSelected, setDefaultSelected] = useState(false)
  const observerRef = useIntersectionObserverOnce<string>(async (message) => {
    await new Promise<void>((resolve) => {
      setTimeout(() => resolve(), 500)
    })

    // 构建埋点参数
    const skuInfoList =
      masterData?.body?.balanceVendorBundleList?.flatMap(
        (item: BalanceVendorBundleType) =>
          item.bundleList?.flatMap(
            (bundle) =>
              bundle.productionList?.flatMap((production) =>
                production.balanceSkuList?.map((sku) => ({
                  skuid: sku.id.toString(),
                  skuStatus: item.isSelfVendor ? '0' : '1',
                })),
              ) || [],
          ) || [],
      ) || []
    await Promise.resolve().then(() => {
      // 添加发票楼层曝光埋点
      InvoiceLogger.invoiceExpo('invoiceExpo', {
        pin: isBpin ? '0' : '1',
        is_invoice: String(masterData?.body?.balanceInvoiceDesc?.useInvoiceStatus) || '',
        text: message || '',
        skuinfo: skuInfoList as any,
        testgroup: 'test-off、test-show、base',
      } as InvoicePointParams)
    })
  })

  useEffect(() => {
    // 成功加载完成时设置loading为false
    if (masterData && masterData?.code?.toString() === '0') {
      const balanceInvoiceDesc = masterData.body?.balanceInvoiceDesc || ({} as BalanceInvoiceDesc)

      // 处理发票信息
      let message = balanceInvoiceDesc?.bodyInformation

      // 如果不支持开发票且有相关提示文案，优先显示该文案
      if (balanceInvoiceDesc.showUnInvoiceTop && balanceInvoiceDesc.unInvoiceTop) {
        message = balanceInvoiceDesc.unInvoiceTop
      }

      // 如果是默认发票，则显示默认发票文案
      if (balanceInvoiceDesc.defaultSelected && balanceInvoiceDesc.defaultSelected) {
        setDefaultSelected(balanceInvoiceDesc.defaultSelected)
      }

      // 如果不支持开发票且没有指定文案，显示默认文案
      if (balanceInvoiceDesc.supportInvoice === false && !message) {
        message = '不支持开具发票'
      }
      setInvoiceInfoMessage({
        message,
        iconMessage: '',
        imageLink: '',
      })
      setLoading(false)
    } else {
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.Invoice,
        msg: {
          type: '发票',
          action: '发票选择',
          code: masterData?.code?.toString(),
          traceId: masterData?.traceId,
          requestId: masterData?.requestId,
        },
      })
    }
  }, [masterData, setInvoiceInfoMessage, isBpin])
  // 点击修改发票类型
  const handleClick = () => {
    setShowInvoiceModal(true)
    const skuInfoList =
      masterData?.body?.balanceVendorBundleList?.flatMap(
        (item: BalanceVendorBundleType) =>
          item.bundleList?.flatMap(
            (bundle) =>
              bundle.productionList?.flatMap((production) =>
                production.balanceSkuList?.map((sku) => ({
                  skuid: sku.id.toString(),
                  skuStatus: item.isSelfVendor ? '0' : '1',
                })),
              ) || [],
          ) || [],
      ) || []
    // 添加点击修改发票类型，事件埋点
    InvoiceLogger.invoiceEvent('invoice', {
      pin: isBpin ? '0' : '1',
      is_invoice: String(masterData?.body?.balanceInvoiceDesc?.useInvoiceStatus) || '',
      text: message || masterData?.body?.balanceInvoiceDesc?.bodyInformation || '',
      skuinfo: skuInfoList as any,
      testgroup: 'test-off、test-show、base',
    } as InvoicePointParams)
  }
  /**
   * 点击开票须知
   */
  const handleNoticeClick = () => {
    setShowNoticeModal(true)
    // 添加开票须知点击埋点
    InvoiceLogger.invoiceEvent('invoiceLayer', {
      clickPos: '3',
      invoice_detail: '',
    } as InvoiceLayerPointParams)
  }

  /**
   * 关闭发票弹窗
   */
  const handleCloseInvoiceModal = () => {
    setShowInvoiceModal(false)
    // 添加发票弹窗关闭埋点
    InvoiceLogger.invoiceEvent('invoiceLayer', {
      clickPos: '2',
      invoice_detail: '',
    } as InvoiceLayerPointParams)
  }

  /**
   * 关闭开票须知弹窗
   */
  const handleCloseNoticeModal = () => {
    setShowNoticeModal(false)
    // 添加开票须知关闭埋点
    InvoiceLogger.invoiceEvent('invoiceLayer', {
      clickPos: '2',
      invoice_detail: '',
    } as InvoiceLayerPointParams)
  }

  /**
   * 获取发票信息
   */
  const getInvoiceInfo = () => {
    if (invoiceInfoMessage && invoiceInfoMessage.message) return invoiceInfoMessage
    // 兜底方案：使用不开发票
    return {
      message: '不开发票',
      iconMessage: '',
      imageLink: '',
    }
  }

  // 如果正在加载，显示骨架屏
  if (loading) {
    return <InvoiceSkeleton />
  }

  const { message } = getInvoiceInfo()

  /**
   * 错误边界组件
   */
  const FallbackComponent = () => {
    return <InvoiceSkeleton />
  }
  /**
   * 错误监控
   */
  const errorToMonitoring = (error: any, errInfo: any) => {
    try {
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.Invoice,
        msg: {
          description: '发票组件异常',
          error: JSON.stringify(error),
          info: JSON.stringify(errInfo),
        },
        extra: {
          traceId: masterData?.traceId,
          requestId: masterData?.requestId,
        },
      })
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <ErrorBoundaryDispose FallbackComponent={FallbackComponent} logErrorToService={errorToMonitoring}>
      <div className="invoice-info" ref={observerRef(message)}>
        <div className="invoice-info-content">
          <div className="invoice-info-title">
            发票信息 <span className="invoice-info-tips">开企业抬头发票须填写纳税人识别号，以免影响报销</span>
          </div>
          {/* {iconMessage && ( */}
          <div className="invoice-info-notice" onClick={handleNoticeClick}>
            开票须知
            <i style={{ marginLeft: '4px' }} className="icon-info" />
          </div>
          {/* )} */}
        </div>
        <div className="invoice-info-details">
          {/* 保证展示并且有链接才展示图片 */}
          {defaultSelected && masterData?.body?.balanceInvoiceDesc?.defaultSelectedLink ? (
            <div className="invoice-type">
              <img
                style={{ height: '16px', lineHeight: '16px' }}
                src={masterData?.body?.balanceInvoiceDesc?.defaultSelectedLink}
                alt="defaultSelected"
              />
              {message}
            </div>
          ) : (
            <div className="invoice-type">{message}</div>
          )}
          {masterData?.body?.balanceInvoiceDesc?.supportInvoice !== false && (
            <div className="invoice-edit" onClick={handleClick}>
              修改发票类型
              <img
                className="arrow-right"
                src="//img20.360buyimg.com/ling/jfs/t1/300271/1/16021/356/685d54a1Ff8e6b51c/af3ee256f74c9156.png"
              />
            </div>
          )}
        </div>
      </div>
      {/* 发票类型选择弹窗 */}
      <InvoiceModal isOpen={showInvoiceModal} onClose={handleCloseInvoiceModal} />
      {/* 开票须知弹窗 */}
      <InvoiceNoticeModal isOpen={showNoticeModal} onClose={handleCloseNoticeModal} />
    </ErrorBoundaryDispose>
  )
}

export default Invoice
