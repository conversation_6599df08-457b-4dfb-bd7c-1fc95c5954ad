import { useState, useEffect, useRef } from 'react'
import classnames from 'classnames'
import type { FC, ChangeEvent, FocusEvent } from 'react'
import './index.scss'

type Props = {
  label?: string
  className?: string
  style?: React.CSSProperties
  name?: string
  value?: string
  autoFocus?: boolean
  checked?: boolean
  defaultChecked?: boolean
  disabled?: boolean
  indeterminate?: boolean
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void
  onBlur?: (event: FocusEvent<HTMLInputElement>) => void
  onFocus?: (event: FocusEvent<HTMLInputElement>) => void
}

const Checkbox: FC<Props> = ({
  label,
  className,
  style,
  name,
  value,
  checked,
  autoFocus = false,
  defaultChecked = false,
  disabled = false,
  indeterminate = false,
  onChange,
  onBlur,
  onFocus,
}) => {
  const [internalChecked, setInternalChecked] = useState(checked ?? defaultChecked)
  const inputRef = useRef<HTMLInputElement | null>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.indeterminate = indeterminate
    }
  }, [indeterminate])

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newChecked = event.target.checked
    setInternalChecked(newChecked)
    if (onChange) {
      onChange(event)
    }
  }

  const handleBlur = (event: FocusEvent<HTMLInputElement>) => {
    if (onBlur) {
      onBlur(event)
    }
  }

  const handleFocus = (event: FocusEvent<HTMLInputElement>) => {
    if (onFocus) {
      onFocus(event)
    }
  }

  return (
    <label className={classnames('checkbox', className, { indeterminate })} style={style}>
      <input
        type="checkbox"
        checked={checked !== undefined ? checked : internalChecked}
        onChange={handleChange}
        disabled={disabled}
        className="checkbox-input"
        aria-checked={checked !== undefined ? checked : internalChecked}
        aria-disabled={disabled}
        name={name}
        value={value}
        autoFocus={autoFocus}
        onBlur={handleBlur}
        onFocus={handleFocus}
        ref={inputRef}
      />
      <span className="checkbox-inner"></span>
      {label && <span className="checkbox-label">{label}</span>}
    </label>
  )
}

export default Checkbox