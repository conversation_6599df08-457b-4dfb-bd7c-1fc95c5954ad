# 发票模块 (Invoice Module)

## 模块概述

发票模块是PC结算系统中负责处理发票相关业务的核心模块，主要功能包括：

- **发票类型选择**：支持电子普通发票、纸质普通发票、增值税专用发票、电子专用发票等多种发票类型
- **发票信息管理**：支持个人和企业发票抬头的创建、编辑、保存和历史记录管理
- **多商家支持**：支持自营商品和POP商品的发票分别处理，以及混合订单的发票管理
- **表单验证**：提供完整的发票信息验证机制，确保数据的准确性和合规性
- **状态管理**：基于Jotai实现的响应式状态管理，支持复杂的业务场景
- **埋点监控**：集成完整的用户行为埋点和错误监控机制

## 目录结构说明

```
src/components/Invoice/
├── Invoice.tsx                       # 发票楼层主组件
├── Invoice.scss                      # 发票楼层样式
├── InvoiceModel.tsx                  # 发票弹窗主组件
├── InvoiceModel.scss                 # 发票弹窗样式
├── InvoiceLogger.ts                  # 发票埋点服务
├── index.ts                          # 模块导出文件
├── Modal/                            # 弹窗组件目录
│   ├── InvoiceDialog.tsx             # 通用发票对话框组件
│   ├── InvoiceDialog.scss            # 对话框样式
│   ├── InvoiceNoticeModal.tsx        # 发票须知弹窗组件
│   └── InvoiceNoticeModal.scss       # 须知弹窗样式
├── atom/                             # 状态管理目录
│   ├── invoiceAtom.ts                # 发票状态原子定义
│   └── invoiceAction.ts              # 发票状态操作原子
├── components/                       # 子组件目录
│   ├── CompanySelect.tsx             # 公司选择组件
│   ├── DefaultInvoiceCheckbox.tsx    # 默认发票复选框组件
│   ├── DefaultInvoiceCheckbox.scss
│   ├── ElectronicInvoiceForm.tsx     # 电子普通发票表单
│   ├── HistoricalInvoiceHeaders.tsx  # 历史发票抬头组件
│   ├── HistoricalInvoiceHeaders.scss
│   ├── InvoiceContentPanel.tsx       # 发票内容面板
│   ├── InvoiceContentSelector.tsx    # 发票内容选择器
│   ├── InvoiceDisclaimer.tsx         # 发票免责声明
│   ├── InvoiceProductList.tsx        # 发票商品列表
│   ├── InvoiceProductList.scss
│   ├── InvoicePutTypeSelector.tsx    # 开票方式选择器
│   ├── InvoiceTitleSelector.tsx      # 发票抬头选择器
│   ├── InvoiceTypeSelector.tsx       # 发票类型选择器
│   ├── NormalInvoiceForm.tsx         # 纸质普通发票表单
│   ├── SpecialInvoiceForm.tsx        # 增值税专用发票表单
│   └── SubsidyNotice.tsx             # 补贴提示组件
├── form/                             # 表单配置目录
│   ├── formFieldsConfig.ts           # 表单字段配置
│   └── validationRules.ts            # 表单验证规则
├── hooks/                            # 自定义Hook目录
│   ├── index.ts                      # Hook导出文件
│   ├── useAddInvoiceToUsual.ts       # 添加常用发票Hook
│   ├── useDeleteInvoiceFromUsual.ts  # 删除常用发票Hook
│   ├── useEditInvoiceToUsual.ts      # 编辑常用发票Hook
│   ├── useInvoiceJotai.ts            # 发票Jotai状态Hook
│   ├── useInvoiceQuery.ts            # 发票查询Hook
│   ├── useInvoiceStateUpdater.ts     # 发票状态更新Hook
│   └── userInvoiceSave.ts            # 发票保存Hook
├── services/                         # 服务层目录
│   ├── invoice.data.service.ts       # 发票数据处理服务
│   ├── invoice.validation.service.ts # 发票验证服务
│   └── validation.constants.ts       # 验证常量定义
└── skeleton/                         # 骨架屏组件目录
    ├── InvoiceContentSkeleton.tsx    # 发票内容骨架屏
    ├── InvoiceContentSkeleton.scss
    ├── InvoiceSkeleton.tsx           # 发票骨架屏
    └── InvoiceSkeleton.scss
```

## 文件功能描述

### 核心组件

#### Invoice.tsx
- **功能**：发票楼层主组件，显示在结算页面中
- **主要职责**：
  - 展示当前发票状态信息
  - 处理发票楼层的点击事件
  - 管理发票弹窗的显示/隐藏
  - 集成发票相关的埋点监控
- **输入参数**：无（通过全局状态获取数据）
- **输出结果**：渲染发票楼层UI

#### InvoiceModel.tsx
- **功能**：发票选择和编辑的主弹窗组件
- **主要职责**：
  - 管理发票类型切换（自营/POP商品）
  - 协调各个发票表单组件
  - 处理发票保存逻辑
  - 管理表单验证状态

#### InvoiceLogger.ts
- **功能**：发票模块的埋点服务
- **主要方法**：
  - `invoiceEvent()`: 发票事件埋点
  - `invoiceExpo()`: 发票曝光埋点
- **埋点类型**：
  - 发票楼层曝光
  - 发票弹窗操作
  - 发票保存行为

### 表单组件

#### ElectronicInvoiceForm.tsx
- **功能**：电子普通发票表单组件
- **支持功能**：
  - 个人/企业抬头选择
  - 开票方式选择
  - 发票内容选择
  - 联系信息填写（手机号、邮箱）
  - 企业信息填写（公司名称、纳税人识别号等）

#### NormalInvoiceForm.tsx
- **功能**：纸质普通发票表单组件
- **支持功能**：
  - 个人/企业抬头选择
  - 历史抬头快速选择
  - 发票内容选择
  - 企业信息填写

#### SpecialInvoiceForm.tsx
- **功能**：增值税专用发票表单组件
- **支持功能**：
  - 分步骤填写（基本信息 → 收票人信息）
  - 完整的企业资质信息填写
  - 收票人地址信息管理
  - 开票方式选择

### 状态管理

#### atom/invoiceAtom.ts
- **功能**：定义发票模块的所有状态原子
- **主要状态**：
  - 发票基础信息（类型、抬头、内容等）
  - 自营和POP商品发票数据
  - UI状态（弹窗显示、表单修改状态等）
  - 派生状态（当前业务场景下的发票数据）

#### atom/invoiceAction.ts
- **功能**：定义发票状态的操作原子
- **主要操作**：
  - 表单字段更新
  - 发票类型切换
  - 发票抬头变更
  - 常用发票列表管理

### 服务层

#### services/invoice.data.service.ts
- **功能**：发票数据处理服务
- **主要方法**：
  - `collectInvoiceData()`: 收集发票表单数据
  - `createSaveInvoiceParams()`: 创建保存发票的请求参数
  - `createMixedInvoiceParams()`: 处理混合订单发票参数

#### services/invoice.validation.service.ts
- **功能**：发票信息验证服务
- **验证类型**：
  - 企业名称验证
  - 纳税人识别号验证
  - 联系方式验证（手机号、邮箱、电话）
  - 地址信息验证
  - 银行信息验证

### 自定义Hooks

#### useInvoiceSave.ts
- **功能**：发票保存逻辑封装
- **主要功能**：
  - 根据业务场景选择保存策略
  - 处理自营/POP/混合订单的不同保存逻辑
  - 集成埋点和错误处理

#### useInvoiceQuery.ts
- **功能**：发票数据查询Hook
- **主要功能**：
  - 获取发票基础信息
  - 获取常用发票列表
  - 处理查询错误和加载状态

#### useInvoiceStateUpdater.ts
- **功能**：发票状态更新Hook
- **主要功能**：
  - 根据查询结果更新发票状态
  - 处理自营/POP/混合场景的状态初始化
  - 设置默认选中状态

## 业务流程描述

### 1. 发票信息初始化流程

```
用户进入结算页面 
→ Invoice.tsx 组件挂载
→ 调用 useInvoiceQuery 获取发票信息
→ useInvoiceStateUpdater 更新状态
→ 显示发票楼层信息
```

### 2. 发票编辑流程

```
用户点击发票楼层
→ 打开 InvoiceModel 弹窗
→ 根据商品类型显示对应的发票表单
→ 用户填写/修改发票信息
→ 表单验证通过后保存
→ 更新发票楼层显示
→ 关闭弹窗
```

### 3. 发票类型切换流程

```
用户选择不同发票类型
→ handleInvoiceTypeChange 触发
→ 更新 selectedInvoiceType 状态
→ 根据新类型渲染对应表单组件
→ 重置相关表单字段
→ 更新验证规则
```

### 4. 混合订单处理流程

```
检测到自营+POP商品
→ 设置 selectedInvoiceStatus = 2
→ 显示商品类型切换Tab
→ 分别管理自营和POP发票信息
→ 保存时合并两种商品的发票参数
```

### 5. 常用发票管理流程

```
用户选择历史抬头
→ 调用对应的常用发票Hook
→ 自动填充表单字段
→ 用户可编辑后保存为新的常用发票
→ 更新常用发票列表
```

## 组件交互关系

### 状态流转关系
- **InvoiceModel** 作为容器组件，协调各个子组件
- **表单组件** 通过 Jotai 状态与 InvoiceModel 通信
- **Hook** 负责业务逻辑处理和状态更新
- **Service** 提供数据处理和验证服务

### 数据流向
```
API数据 → useInvoiceQuery → invoiceAtom → 表单组件 → 用户交互 → invoiceAction → 状态更新 → useInvoiceSave → API保存
```

### 组件依赖关系
- InvoiceModel 依赖所有表单组件
- 表单组件依赖选择器组件和验证服务
- Hook 依赖状态原子和API服务
- 所有组件都可能依赖埋点服务

### 状态管理使用
```tsx
import { useAtom } from 'jotai'
import { selectedInvoiceTypeAtom } from '@app/components/Invoice/atom/invoiceAtom'

const MyComponent = () => {
  const [selectedType, setSelectedType] = useAtom(selectedInvoiceTypeAtom)
  
  return (
    <div>当前选中的发票类型: {selectedType}</div>
  )
}
```
