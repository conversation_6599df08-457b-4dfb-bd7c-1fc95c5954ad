import hoistNonReactStatic from 'hoist-non-react-statics'
import { Component, createRef } from 'react'

export default function enhanceWithClickOutside(WrappedComponent) {
  const componentName = WrappedComponent.displayName || WrappedComponent.name

  class EnhancedComponent extends Component {
    constructor(props) {
      super(props)
      this.wrappedRef = createRef()
      this.handleClickOutside = this.handleClickOutside.bind(this)
    }

    componentDidMount() {
      document.addEventListener('click', this.handleClickOutside, true)
    }

    componentWillUnmount() {
      document.removeEventListener('click', this.handleClickOutside, true)
    }

    handleClickOutside = (e) => {
      const domNode = this.wrappedRef.current.referenceRef.current
      // ReactDOM.findDOMNode(this.wrappedRef.current)
      if (
        (!domNode || !domNode.contains(e.target)) &&
        this.wrappedRef.current &&
        typeof this.wrappedRef.current.handleClickOutside === 'function'
      ) {
        this.wrappedRef.current.handleClickOutside(e)
      }
    }

    render() {
      const { wrappedRef, ...rest } = this.props

      return (
        <WrappedComponent
          {...rest}
          ref={(e) => {
            this.wrappedRef.current = e
            wrappedRef && wrappedRef(e)
          }}
        />
      )
    }
  }

  EnhancedComponent.displayName = `clickOutside(${componentName})`

  return hoistNonReactStatic(EnhancedComponent, WrappedComponent)
}
