/*
 * @Author: ext.xuch<PERSON>26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息
 */
import { Dialog, InnerContent, Loading } from '@app/common/legao'
import { Consignee<PERSON><PERSON>, J<PERSON><PERSON>, Consignee } from './components'
import { useEffect, useReducer, useMemo, useRef, useCallback, RefObject } from 'react'
import './style.scss'
import apis from '../ConsigneeInfo/api'
import { saveConsigneeAddress } from '@app/services/api'
import { setMasterApiParams } from '@app/services/parameters'
import Skeleton from './components/Skeleton'
import { useMasterData, useUpdateMasterData } from '@app/context/masterContext'
import { BalanceAddressDo } from '@app/typings/master_api_response'
import { useAtomValue } from 'jotai'
// import { useSetAtom } from 'jotai'
import { methodsAtom } from '@app/atoms/masterAtom'
import { createC<PERSON>ie, readCookie, addrTags } from './utils'
import { commonConfigAtom } from '@app/atoms/commonConfigAtom'
import { reportClick, reportExpose } from '@app/utils/event_tracking'
type TimeoutId = ReturnType<typeof setTimeout>

// 定义状态类型
interface State {
  isExpand: boolean
  isShow: boolean
  type: string
  addressInfoList: Array<any>
  data: object
  loading: boolean
  spinning: boolean
  mediaResults: Record<string, boolean>
}

type ReducerAction = Partial<State> | ((prev: State) => Partial<State>)

type consigneeItemRefType = {
  tipRef: RefObject<HTMLDivElement | null>
  innerRef: RefObject<HTMLDivElement | null>
}

// 初始化状态
const initState: State = {
  isExpand: false,
  isShow: false,
  type: 'add',
  addressInfoList: [],
  data: {},
  loading: true,
  spinning: false,
  mediaResults: {},
}
function ConsigneeInfo() {
  // const { showToast } = (useContext(ToastContext) as { showToast: (s: object) => void; hideToast: () => void }) || {}
  const masterData = useMasterData()
  const updateMasterData = useUpdateMasterData()
  const methods = useAtomValue(methodsAtom)
  const commonConfig = useAtomValue(commonConfigAtom)
  const consigneeTip = useMemo(() => masterData?.body?.tipsVOList?.find((m) => m.type === 1)?.tip, [masterData?.body?.tipsVOList])
  // 国补嵌套地址标识
  const isAddressChunked = useMemo(() => !!masterData?.body?.balanceAddress?.addressChunked, [masterData?.body?.balanceAddress])
  // 无地址判断
  const isNoAddress = useMemo(() => masterData?.body?.matchAddress === 2, [masterData?.body?.matchAddress])
  // const setMethods = useSetAtom(methodsAtom)
  // const methodA = useCallback(() => {
  //   console.log('ComponentA 的方法执行')
  // }, [])
  // useEffect(() => {
  //   setMethods((prev) => ({ ...prev, componentA: methodA }))
  //   return () => {
  //     setMethods((prev) => {
  //       const { componentA, ...rest } = prev
  //       return rest
  //     })
  //   }
  // }, [methodA, setMethods])

  // 初始化 reducer
  const [state, dispatch] = useReducer((s: State, a: ReducerAction) => ({ ...s, ...a }), initState)
  const formRef = useRef(null)
  const isExposedIcons = useRef<{ [key: string]: any }>({})
  const consigneeItem = useRef<consigneeItemRefType>(null)
  const icons = { 首页: '0', 我的: '1', 客服: '2', 反馈: '3', 回顶部: '4', 收起: '5' }
  const { isExpand, isShow, addressInfoList = [], type, data, loading, spinning } = state
  const titleMap: any = {
    add: '新增收货人信息',
    edit: '编辑收货人信息',
  }
  const title: string = titleMap?.[type]
  const onExpand = useCallback(() => {
    !isExpand && reportClick('address', { skutype: '', clickPos: '0', addr: readCookie('ipLoc-djd') })
    !isExpand && reportClick('skuaddress', { clickPos: '2', addr: readCookie('ipLoc-djd') })
    dispatch({ isExpand: !isExpand })
    if (!Element.prototype.scrollTo) {
      Element.prototype.scrollTo = function (options) {
        if (options?.top !== undefined) {
          this.scrollTop = options.top
        }
      }
    }
    isExpand &&
      consigneeItem.current?.innerRef?.scrollTo?.({
        top: 0,
      })
  }, [isExpand])

  const onShow = useCallback(
    ({ type = 'add', ...data }) => {
      dispatch({ data, isShow: !isShow })
      type && dispatch({ type })
      type === 'add' && consigneeItem.current?.tipRef?.current?.onClose?.()
      const { retTag, userDefinedTag, provinceId, cityId, countyId, townId, addressId } = data
      const addr = [provinceId, cityId, countyId, townId + '.' + addressId].join('-')
      type === 'edit' &&
        reportClick('writeAddress', { addr, tagtext: addrTags?.find((m) => m.value === retTag)?.label || userDefinedTag || '-100' })
      reportClick('address', { skutype: '', clickPos: type === 'add' ? '1' : '3', addr })
      type === 'add' && reportClick('skuaddress', { clickPos: '0', addr })
    },
    [isShow],
  )
  const closeDialog = useCallback(() => {
    if (isNoAddress) return
    formRef.current?.handleReset()
    dispatch({ isShow: false })
  }, [isShow])
  const dialogSubmit = useCallback(() => {
    formRef.current
      ?.handleSubmit()
      .then((res) => {
        console.log(res, 'success submit!!')
        // showToast({ type: 'success', message: '保存成功', timer: 3000 })
        // dispatch({ isShow: false })
      })
      .catch((message) => {
        // if (message) {
        //   showToast({ type: 'fail', message: message || '保存失败', timer: 5000 })
        // }
        console.warn(message, 'error submit!!')
      })
      .finally(() => {
        reportClick('addaddress', {
          skutype: '',
          clickPos: '4',
          addr: readCookie('ipLoc-djd'),
          testgroup: '',
        })
      })
  }, [isShow])

  async function queryConsigneeAddress({ addressId }: { addressId?: number } = {}) {
    try {
      dispatch({ spinning: true })
      const { addressInfoList = [] } = await apis.queryAddress({ addressId, jdCombineSign: false, listVersion: 2 })
      dispatch({ addressInfoList })
    } catch (e) {
      console.error(e)
    } finally {
      dispatch({ loading: false, spinning: false })
    }
  }

  const onSelectAddress = useCallback(
    async (data: any, index: number) => {
      const { provinceId = 0, cityId = 0, countyId = 0, townId = 0, addressId } = data
      const params = setMasterApiParams({ addressId, balanceCommonOrderForm: { useBestCoupon: true } })
      try {
        dispatch({ spinning: true })
        createCookie({ name: 'ipLoc-djd', days: -1 })
        const res = await saveConsigneeAddress(params)
        const { areaIdFromCookie } = res?.body || {}
        createCookie({
          name: 'ipLoc-djd',
          value: areaIdFromCookie || [provinceId, cityId, countyId, townId + '.' + addressId].join('-'),
        })
        dispatch({ isShow: false })
        await updateMasterData()
        index === 0 && (await queryConsigneeAddress({ addressId }))
        isExpand && onExpand()
        Object.values(methods).forEach((fn) => fn())
        const addr = [provinceId, cityId, countyId, townId + '.' + addressId].join('-')
        reportClick('skuaddress', { clickPos: '3', addr })
      } catch (e) {
        console.error(e)
      } finally {
        dispatch({ spinning: false })
      }
    },
    [isExpand],
  )

  useEffect(() => {
    if (masterData?.body) {
      const { body } = masterData || {}
      const { id, areaIdFromCookie } = (body?.balanceAddress as BalanceAddressDo) || {}
      if (isNoAddress) {
        onShow({ type: 'add' })
        return dispatch({ loading: false })
      }
      queryConsigneeAddress({ addressId: id })
      reportExpose('addressExpo', {
        addr: areaIdFromCookie,
      })
    }
  }, [JSON.stringify(masterData?.body?.balanceAddress || null)])

  const sidebarReportInit = () => {
    // 侧边栏埋点的曝光和点击
    function expose({ name }: { [key: string]: string }) {
      !isExposedIcons.current[name] &&
        reportExpose('sidebarExpo', {
          type: icons[name],
        })
      isExposedIcons.current[name] = name
    }
    function onClick({ name }) {
      reportClick('sidebar', {
        type: icons[name],
      })
    }
    typeof window.elevator === 'function' && window.elevator({ expose, onClick })
  }

  useEffect(() => {
    // 轮询配置
    const MAX_ATTEMPTS = 75 // timeout=15000ms
    const POLL_INTERVAL = 200 // ms

    let timer: TimeoutId | null = null
    let attempt = 0

    const getTagsPool = () => masterData?.body?.abDataResult?.tagsPool

    // 轮询函数
    const poll = () => {
      const tagsPool = getTagsPool()

      // 数据存在或达到最大尝试次数时终止轮询
      if (tagsPool || attempt >= MAX_ATTEMPTS) {
        timer && clearTimeout(timer)
        sidebarReportInit()
        return
      }

      // 继续轮询
      attempt++
      timer = setTimeout(poll, POLL_INTERVAL)
    }

    // 立即开始首次检查
    poll()

    // 清理函数
    return () => {
      if (timer) {
        clearTimeout(timer)
      }
    }
  }, [masterData?.body?.abDataResult?.tagsPool])

  const dialogProps = {
    title,
    closeButton: true,
    outsideClose: true,
    hasMask: true,
    width: 624,
    height: type === 'add' ? 710 : 590,
    isOpen: isShow,
    className: 'consignee-dialog',
    children: (
      <InnerContent
        cancelBtnText="取消"
        submitBtnText="确定"
        dialogCancel={() => {
          closeDialog()
          reportClick('addaddress', {
            skutype: '',
            clickPos: '2',
            addr: readCookie('ipLoc-djd'),
            testgroup: '',
          })
        }}
        dialogSubmit={dialogSubmit}
        innerText={
          <JDForm
            ref={formRef}
            commonConfig={commonConfig}
            isAddressChunked={isAddressChunked}
            data={data}
            type={type}
            dispatch={dispatch}
            onSelectAddress={onSelectAddress}
          />
        }
      />
    ),
    onClose: () => {
      closeDialog()
      reportClick('addaddress', {
        skutype: '',
        clickPos: '5',
        addr: readCookie('ipLoc-djd'),
        testgroup: '',
      })
    },
  }

  return loading ? (
    <Skeleton />
  ) : (
    <Consignee onShow={onShow} onExpand={onExpand} addressInfoList={addressInfoList} consigneeTip={consigneeTip} isExpand={isExpand}>
      {addressInfoList.length ? (
        <ConsigneeItem
          ref={consigneeItem}
          addressInfoList={addressInfoList}
          isAddressChunked={isAddressChunked}
          isShow={isShow}
          onShow={onShow}
          onSelectAddress={onSelectAddress}
        />
      ) : (
        <div className="h-56 leading-56 mt-20 mr-6 a-center ftx03">暂未添加收货地址～</div>
      )}
      <Loading loading={spinning} />
      <Dialog {...dialogProps} />
    </Consignee>
  )
}

export default ConsigneeInfo
