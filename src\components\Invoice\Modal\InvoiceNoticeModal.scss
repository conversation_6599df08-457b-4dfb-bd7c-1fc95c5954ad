// 定义变量
$font-family: PingFang SC;
$primary-color: #1a1a1a;
$secondary-color: #505259;
$link-color: #0073ff;

.notice-content {
  padding: 10px;
  height: auto;

  h3 {
    font-size: 14px;
    color: $primary-color;
    margin: 0 0 12px 0;
    font-weight: 500;
    font-family: $font-family;
    line-height: 14px;
    letter-spacing: 0;
  }

  p {
    margin-bottom: 12px;
    line-height: 18px;
    font-size: 12px;
    color: $secondary-color;
    font-family: $font-family;
    font-weight: 400;
    letter-spacing: 0;
  }

  a {
    color: $link-color;
    text-decoration: none;
    cursor: pointer;
    font-family: $font-family;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0;

    &:hover {
      text-decoration: underline;
    }
  }
}
