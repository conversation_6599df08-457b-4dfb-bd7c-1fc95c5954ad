.jdBean {
  color: #1A1A1A;
  font-size: 14px;

  &Prompt {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 400;
    color: #888B94;
  }

  &Select {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 20px;

    .msg {
      color: var(--custom-color-red);
    }
  }
}

.jdBeanDropDown {
  width: 280px;
  margin-left: 12px;
  margin-bottom: 4px;
  margin-right: 8px;
  cursor: pointer;
}

.jdBeanTitle {
  width: 280px;
  height: 36px;
  padding: 11px 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background: #FFFFFF;
  font-size: 14px;
  color: #C2C4CC;
  >span {
    color: #1A1A1A;
    font-weight: 400;
  }
  .arrow {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-left: 4px;
    background: url('https://img13.360buyimg.com/img/jfs/t1/279772/29/13118/468/67eb8487F6b8dc32b/eececae01bd4cbf5.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  &Active {
    color: #1A1A1A;

    .arrow {
      background: url('https://img10.360buyimg.com/img/jfs/t1/273064/3/14027/195/67eb5b91Fa0f215c7/89ddf7580ea459ab.png');
    }
  }

}

.fixedBottom {
  // position: absolute;
  // bottom: 0;
  // left: 0;
  // right: 0;
  // width: 100%;
  // padding: 0 4px 12px;
  background-color: #fff;
  box-sizing: border-box;
  flex-shrink: 0;
  // border: 1px solid rgba(0, 0, 0, 0.1);
  // border-top: none;
  // border-bottom-left-radius: 8px;
  // border-bottom-right-radius: 8px;
}

.jdBeanCustomize {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 0 8px;

  .jdBeanInput {
    input {
      border: none;
      padding: 0;
    }

    .common-input {
      flex: auto;
    }
  }

  .btn {
    margin-right: 4px;
    padding-left: 12px;
    border-left: 1px solid rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    color: #1A1A1A;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
  }

  &Edit {
    border-bottom-color: #FF8595;

    .btn {
      color: #FF0F23;
      border-left-color: #FFEBEF;
    }
  }
}

.jdBeanItem {
  height: 36px;
  line-height: 36px;
  padding: 0 8px;
  color: #1A1A1A;
  font-size: 14px;
  font-weight: 400;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #F7F8FC;
  }

  &Active {
    color: #FF0F23;
  }
}
.jdBeanDropDownCont {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.jdBeanDropItem {
  flex: 1;
  overflow-y: auto;
}

.icon {
  position: relative;
  top: 1px;
  margin-left: 4px;
}

.jdBeanPromptText {
  display: flex;
  align-items: center;
}

.disabled {
  cursor: not-allowed;
}