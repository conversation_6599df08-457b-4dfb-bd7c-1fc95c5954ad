.purchase-limit {
  overflow-y: auto;
  max-height: 310px;
  .purchase-limit-item {
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.purchase-limit-item {
  display: flex;
  padding: 8px;
  border: 0.5px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  background: #F7F8FC;

  img {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    margin-right: 12px;
  }
}

.purchase-limit-item__inner {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  padding: 4px 0;

  &>div {
    width: 100%;
  }
}

.purchase-limit-item-name {
  color: #1A1A1A;
  font-size: 16px;
  line-height: 1;
}

.purchase-limit-item-description {
  color: #C47600;
  font-size: 14px;
  line-height: 1;
}

.purchase-limit-item-amount {
  display: flex;
  align-items: center;
  line-height: 1;
  font-family: JDZhengHeiVRegular2-1;
}


.purchase-limit-item-price {
  color: #FF0F23;
  font-size: 16px;
  font-weight: Bold;
}

.purchase-limit-item-origin {
  margin-left: 4px;
  color: #888B94;
  font-size: 14px;
  text-decoration: line-through;
}

.purchase-limit-item-count {
  margin-left: auto;
  color: #888B94;
  font-size: 14px;
}