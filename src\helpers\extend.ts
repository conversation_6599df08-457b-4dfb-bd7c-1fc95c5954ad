import isPlainObject from './isPlainObject'

function extend<T, U, V, W, X, Y, Z>(
  deep: true,
  target: T,
  object1: U,
  object2: V,
  object3: W,
  object4: X,
  object5: Y,
  object6: Z,
): T & U & V & W & X & Y & Z
function extend<T, U, V, W, X, Y>(deep: true, target: T, object1: U, object2: V, object3: W, object4: X, object5: Y): T & U & V & W & X & Y
function extend<T, U, V, W, X>(deep: true, target: T, object1: U, object2: V, object3: W, object4: X): T & U & V & W & X
function extend<T, U, V, W>(deep: true, target: T, object1: U, object2: V, object3: W): T & U & V & W
function extend<T, U, V>(deep: true, target: T, object1: U, object2: V): T & U & V
function extend<T, U>(deep: true, target: T, object1: U): T & U
function extend(deep: true, target: any, object1: any, ...objectN: any[]): any
function extend<T, U, V, W, X, Y, Z>(
  target: T,
  object1: U,
  object2: V,
  object3: W,
  object4: X,
  object5: Y,
  object6: Z,
): T & U & V & W & X & Y & Z
function extend<T, U, V, W, X, Y>(target: T, object1: U, object2: V, object3: W, object4: X, object5: Y): T & U & V & W & X & Y
function extend<T, U, V, W, X>(target: T, object1: U, object2: V, object3: W, object4: X): T & U & V & W & X
function extend<T, U, V, W>(target: T, object1: U, object2: V, object3: W): T & U & V & W
function extend<T, U, V>(target: T, object1: U, object2: V): T & U & V
function extend<T, U>(target: T, object1: U): T & U
function extend(target: any, object1: any, ...objectN: any[]): any
/**
 * extend 实现对象的扩展和拷贝功能
 */
function extend(this: Object) {
  var options,
    name,
    src,
    copy,
    copyIsArray,
    clone,
    target = arguments[0] || {},
    i = 1,
    length = arguments.length,
    deep = false

  // Handle a deep copy situation
  if (typeof target === 'boolean') {
    deep = target

    // Skip the boolean and the target
    target = arguments[i] || {}
    i++
  }

  // Handle case when target is a string or something (possible in deep copy)
  if (typeof target !== 'object' && typeof target !== 'function') {
    target = {}
  }

  // Extend itself if only one argument is passed
  if (i === length) {
    target = this
    i--
  }

  for (; i < length; i++) {
    // Only deal with non-null/undefined values
    if ((options = arguments[i]) != null) {
      // Extend the base object
      for (name in options) {
        copy = options[name]

        // Prevent Object.prototype pollution
        // Prevent never-ending loop
        if (name === '__proto__' || target === copy) {
          continue
        }

        // Recurse if we're merging plain objects or arrays
        if (deep && copy && (isPlainObject(copy) || (copyIsArray = Array.isArray(copy)))) {
          src = target[name]

          // Ensure proper type for the source value
          if (copyIsArray && !Array.isArray(src)) {
            clone = []
          } else if (!copyIsArray && !isPlainObject(src)) {
            clone = {}
          } else {
            clone = src
          }
          copyIsArray = false

          // Never move original objects, clone them
          target[name] = extend(deep, clone, copy)

          // Don't bring in undefined values
        } else if (copy !== undefined) {
          target[name] = copy
        }
      }
    }
  }

  // Return the modified object
  return target
}

export default extend
