export const ABTestsMAP = {
    // 3.4需求 写入全站缓存成功通知后发起回调通知 AB实验数据
    ab_cacheCallbackSendMsg_v1: {
        experiment: "ab_cacheCallbackSendMsg_v1",
    },
    // 自动获取粘贴板内容
    ab_getClipboardContent_V1: {
        experiment: "ab_getClipboardContent_V1"
    },
    // 地区选址联想地址传参经纬度优化 https://joyspace.jd.com/pages/TZqZBAFxImYWQtANXCfk
    ab_suggestlonlat_v1: {
        experiment: "ab_suggestlonlat_v1"
    },
    // 存量地址需求
    ab_quality_upgrade: {
        experiment: "ab_quality_upgrade"
    },
    // 门详超配可点需求
    ab_overhangCondition_v1: {
        experiment: 'ab_overhangCondition_v1'
    },
    // 列表、新建/编辑隐藏末级地址
    ab_hidefourth_address: {
        experiment: 'ab_hidefourth_address'
    },
    // 嵌套地址
    ab_nested_address: {
        experiment: 'ab_nested_address'
    },
    // 精细化推荐poi需求
    ab_poi_recommended: {
        experiment: 'ab_poi_recommended'
    },
    // lbs缓存封闭场域，校验供给
    ab_lbs_cache: {
        experiment: 'ab_lbs_cache'
    },
    // 从地图回来移除1-4级级联接口
    ab_remove_cascade: {
        experiment: 'ab_remove_cascade'
    },
    // 英文版模式下 国内地址支持海外手机号开关
    ab_mainland_support_overSeaMobile: {
        experiment: 'ab_mainland_support_overSeaMobile'
    },
    // 英文版降级开关
    ab_multi_language: {
        experiment: 'ab_multi_language'
    },
}

/**
 * 降级控制数组结果
 */
export interface IABResult {
    experimentId: string;
    experimentParamMap: ICacheCallbackSendMsg | GetClipboardContent | ISuggestlonlat | OverhangConditon | HidefourthAddress | NestedAddress | RecommondPoiAddress | LbsCache | RemoveCascade | MainlandSupportOverSeaMobile | MultiLanguage; // 后续增加类型 可以用 | 追加
    hit: boolean
}

// 3.4需求 写入全站缓存成功通知后发起回调通知 AB实验数据
export interface ICacheCallbackSendMsg {
    cacheCallbackSendMsg: boolean;
}

export interface GetClipboardContent {
    getClipboardContent: boolean;
}

// 地区选址联想地址传参经纬度优化
export interface ISuggestlonlat {
    suggestlonlat: boolean;
}

export interface GetAddressFloor {
    getAddressFloor: boolean;
}

// 门详超配可点需求

export interface OverhangConditon {
    overhangCondition: boolean;
}

// 924列表、新建/编辑隐藏末级地址需求+存量地址需求
export interface HidefourthAddress {
    hidefourthAddress: boolean
}

// 924嵌套地址需求
export interface NestedAddress {
    nestedAddress: boolean
}
// 1126 推荐poi
export interface RecommondPoiAddress {
    ab_poi_recommended: boolean
}

export interface LbsCache {
    ab_lbs_cache: Record<string, { supplyRuleType?: string }>
}

// 从地图回来移除1-4级级联接口
export interface RemoveCascade {
    ab_remove_cascade: boolean
}
// 英文版模式下 国内地址支持海外手机号开关
export interface MainlandSupportOverSeaMobile {
    mainlandSupportOverSeaMobile: boolean
}
// 英文版降级开关
export interface MultiLanguage {
    ab_multi_language: boolean
}
/**
 * abTestResult 匹配实验数据
 *
 * @param abResultArr
 * @param experiment - 当前实验
 * @returns 当前实验数据
 */

export const getABTestExperimentParamMap = (abResultArr: IABResult[] | undefined, experiment: string) => {
    if (!abResultArr || !abResultArr.length) {
        return null
    }

    const result = abResultArr.find(item => item.experimentId === experiment)

    if (result?.hit) {
        return result.experimentParamMap
    }

    return null
}
