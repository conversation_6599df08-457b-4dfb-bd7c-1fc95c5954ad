/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-02 18:10:08
 * @LastEditors: ext.wangchao120
 * @Description: 组合优惠券
 * @FilePath: /pc_settlement/src/components/VirtualAsset/Coupons/Combination/index.tsx
 */
import React, { useEffect, useState, useRef, FC } from 'react'
import { ExposeBuriedPoints } from '@app/utils/dataSet'
import { _batchExposeDom, domMap } from '../../batchExpose'
import styles from './index.module.scss'
import Checkbox from '@app/common/checkbox'
import Card from '../Card'
import useMasterData from '@app/hooks/useMasterData'
import type { CouponItem } from '@app/typings/virtualAsset'
import { api_getBestVertualCoupons } from '@app/services/api'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import useUpdateMasterData from '@app/hooks/useUpdateMasterData'
import showToast from '@app/components/payment'
import useRequest from 'ahooks/lib/useRequest'
import { useAtom } from 'jotai'
import { virtualAssetLoading } from '@app/components/VirtualAsset/atoms'
interface PropsType {
  onChange: (checked: boolean) => void
  checked: boolean
  combinationClickCount?: number
  tabsName?: string[]
  tabKey?: string
}
const Combination: FC<PropsType> = ({ onChange, checked, combinationClickCount = 0, tabKey }) => {
  const masterData = useMasterData()?.body
  const [couponList, setCouponList] = useState<CouponItem[]>([])
  const updateMasterData = useUpdateMasterData()
  const observer = useRef<ExposeBuriedPoints | null>(null)
  const [, setLoading] = useAtom(virtualAssetLoading)
  /**
   * 获取最优优惠券列表
   */
  const { run: getCouponList } = useRequest(api_getBestVertualCoupons, {
    retryCount: 3,
    manual: true,
    onBefore: () => {
      setLoading(true)
    },
    onSuccess: (res) => {
      if (res?.code !== '0') {
        showToast({ title: res?.message || '请稍后再试' })
      } else {
        if (res?.body?.couponList && res?.body?.couponList?.length > 0) {
          setCouponList(res?.body?.couponList)
          // 更新融合接口
          updateMasterData(
            {
              balanceCommonOrderForm: {
                useBestCoupon: true,
              },
            },
            'virtualAsset',
          )
        }
      }
    },
    onError: (error) => {
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.VirtualAsset,
        msg: {
          functionId: 'balance_getBestVertualCoupons_pc',
          error_type_txt: 'balance_getBestVertualCoupons_pc接口异常',
          error_msg: JSON.stringify(error),
        },
      })
    },
    onFinally: () => {
      setLoading(false)
    },
  })
  useEffect(() => {
    if (checked) {
      // 融合接口有下发最优推荐组合列表取融合接口
      if (masterData?.couponVOList && masterData?.couponVOList.length > 0) {
        setCouponList(masterData?.couponVOList)
        // 主动切换组合按钮才调用更新融合接口
        if (!combinationClickCount || combinationClickCount === 0) return

        // 更新融合接口
        updateMasterData(
          {
            balanceCommonOrderForm: {
              useBestCoupon: true,
            },
          },
          'virtualAsset',
        )
      } else {
        // 融合接口无最优推荐调最优组合优惠券列表
        getCouponList()
      }
    }
  }, [checked])

  const loadingDom = (id: string) => {
    domMap.set(id, id)
    // 当数据长度和domMap相等时，说明dom已经加载完毕
    if (couponList.length === domMap.size) {
      observer.current = _batchExposeDom('virtualasset_CouponExpo', `coupon`, 'couponid', { second_tab_name: ['优惠组合推荐'] })
    }
  }
  // 批量曝光
  useEffect(() => {
    // 清空map
    domMap.clear()
  }, [tabKey, checked])

  return (
    <>
      <div className={styles.combination}>
        <div className={styles.combinationTitle}>
          <Checkbox onChange={(e) => onChange(e?.target?.checked)} checked={checked} />
          <span>
            优惠组合推荐
            {checked && masterData?.balanceVirtualAssetsVO?.couponDiscount
              ? `，共抵扣${masterData?.balanceVirtualAssetsVO?.couponDiscount}元：使用优惠券${masterData?.couponVOList?.length}张`
              : null}
          </span>
        </div>
        {/* 优惠组合推荐列表 */}
        {checked ? (
          <div className={styles.combinationCard}>
            {couponList &&
              couponList?.map((item: CouponItem) => {
                return <Card item={item} key={item?.id} loadingDom={loadingDom} checked={checked} tabKey={tabKey}></Card>
              })}
          </div>
        ) : null}
      </div>
    </>
  )
}

export default Combination
