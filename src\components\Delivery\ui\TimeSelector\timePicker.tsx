import styles from './index.module.scss'
import { useEffect, useState } from 'react'
import { UiTimePicker } from './types'

export type timeState = {
  list: {
    weekName: string
    items: { text: string; type: string; targetData: any }[]
  }[]
}

export default ({
  onChange,
  calendarData,
  onClick,
}: {
  onChange: (n: any) => void
  calendarData: UiTimePicker.ListState<any>
  onClick?: (s: string) => void
}) => {
  const [state, setState] = useState<[null | number, number | null]>([null, null])

  useEffect(() => {
    setState([0, 0])
    calendarData.forEach((item, index) => {
      item.items.forEach((item2, index2) => {
        if (item2.targetData?.selected) {
          setState([index, index2])
          onChange(item2.targetData)
        }
      })
    })
  }, [calendarData])

  return (
    <div className={styles.time_picker}>
      {calendarData.map((item, index) => {
        const [i1, i2] = state
        return (
          <div key={index} className={styles.item}>
            <div className={`${styles.item2} ${styles.week}`}>{item.weekName} </div>
            {item.items.map((item2, index2) => {
              return (
                <div
                  onClick={() => {
                    onClick && onClick(item2.text)
                    setState([index, index2])
                    onChange(item2.targetData)
                  }}
                  key={index2}
                  className={`${styles.item2} ${i1 === index && i2 === index2 ? styles.item_selected : ''} `}
                >
                  {item2.text}
                </div>
              )
            })}
          </div>
        )
      })}
    </div>
  )
}
