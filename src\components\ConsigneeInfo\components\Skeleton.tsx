/*
 * @Author: ext.xuch<PERSON>26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息骨架屏
 */
import classNames from 'classnames'
import styles from './skeleton.module.scss'

function Skeleton() {
  return (
    <div className={classNames(`${styles['consignee']} pl-16 pr-10 mb-16`)}>
      <div className={`${styles['consignee-header']} is-flex justify-between items-center h-68`}>
        <p className="h-20 w-110 bg-ftx15 round-4 jd-skeleton"></p>
        <div className="is-flex items-center">
          <span className="h-14 w-28 bg-ftx15 round-4 jd-skeleton"></span>
          <span className={`h-12 ${styles['divider']} mx-12 jd-skeleton`}></span>
          <span className="h-14 w-28 mr-6 bg-ftx15 round-4 jd-skeleton"></span>
        </div>
      </div>
      <div className={`is-flex ${styles['consignee-items']}`}>
        {[...Array(3)].map((_, i) => {
          return (
            <div key={i} className={classNames(`${styles['consignee-item']} is-flex`)}>
              <span className={`${styles['consignee-item-icon']} w-20 h-20 bg-ftx15 round-4 jd-skeleton`}></span>
              <div className={`${styles['consignee-item-addressDetail']} ml-12`}>
                <div className="w-160 mb-8 bg-ftx15 h-14 round-4 jd-skeleton"></div>
                <div className="mb-16 bg-ftx15 h-16 w-full round-4 jd-skeleton"></div>
                <div className="w-130 h-16 bg-ftx15 round-4 jd-skeleton"></div>
              </div>
            </div>
          )
        })}
      </div>
      <div className={`h-62 is-flex items-center ${styles['consignee-expand']}`}>
        <span className="h-14 w-100 bg-ftx15 round-4 jd-skeleton"></span>
      </div>
    </div>
  )
}

export default Skeleton
