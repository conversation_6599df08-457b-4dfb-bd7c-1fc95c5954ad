.header {
  height: 48px;
  display: flex;
  gap: 44px;
  padding-left: 16px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.06);
  border-width: 0px 0px 1px 0px;
  .item{
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    color: #1A1A1A;
    justify-content: center;
    font-size: 16px;
    font-family: PingFang SC;
    font-weight: 600;
    letter-spacing: 0px;
    padding: 16px 0;
    flex-wrap: nowrap;
    white-space: nowrap;
  }
  .item_selected{
    color: #FF0F23;
    border-bottom: 2px solid #FF0F23;
  }
}

.header2 {
  height: 36px;
  display: inline-flex;
  gap: 12px;
  margin-top: 12px;

  border-style: solid;
  border-color: rgba(0, 0, 0, 0.06);
  border-width: 0px 0px 0px 0px;
  .item{
    cursor: pointer;
    width: 106px;
    height: 36px;
    //padding: 9px 32px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 4px;
    box-sizing: border-box;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    background: #F7F8FC;

    color: #1F1F1F;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 14px;
    letter-spacing: 0px;
    text-align: center;
  }
  .item_selected{
    color: #FF0F23;
    border: 1px solid #FF0F23;
    background: #FFF7F9;
  }
}


.button {
  //width: 496px;
  width: 100%;
  height: 72px;
  //padding: 16px 24px 24px;
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  align-items: center;
  justify-content: flex-end;
  gap: 11px;
  background: #FFFFFF;
  .cancel{
    cursor: pointer;
    width: 88px;
    height: 32px;
    padding: 9px 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 4px;
    box-sizing: border-box;
    border: 0.5px solid #C2C4CC;
    border-radius: 4px;
    background: #FFFFFF;
  }
  .sure{
    cursor: pointer;
    width: 88px;
    height: 32px;
    padding: 9px 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    justify-content: center;
    gap: 4px;
    border-radius: 4px;
    background: linear-gradient(90deg, rgba(255, 71, 93, 1) 0%, rgba(255, 15, 35, 1) 100%);
    color: #FFFFFF;
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 600;
    line-height: 14px;
    letter-spacing: 0px;
    text-align: center;
  }
}

.body_warp{
  //width: 496px;
  //height: 452px;
  //box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  background: #FFFFFF;
  box-shadow: 0px 12px 24px rgba(0, 0, 0, 0.1);
  padding: 8px 24px;
}

.skeleton_wrap {
  .skeleton-container {
    max-width: 500px;
    margin: 0 auto;
    background-color: white;
    border-radius: 8px;
    padding: 20px;
  }
  
  /* 骨架屏基础样式 */
  .skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
  }
  
  @keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
  
  /* 骨架屏元素尺寸 */
  .skeleton-header {
    height: 40px;
    width: 100%;
    margin-bottom: 15px;
  }
  
  .skeleton-header2 {
    height: 30px;
    width: 70%;
    margin-bottom: 15px;
  }

  .skeleton-text {
    height: 16px;
    width: 100%;
    margin-bottom: 10px;
  }
  
  .skeleton-text.small {
    width: 70%;
  }
  
  .skeleton-image {
    height: 190px;
    width: 100%;
    margin: 15px 0;
  }
  
  .skeleton-button {
    height: 40px;
    width: 120px;
  }
  
  .skeleton-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin: 15px 0;
  }
  
  .skeleton-grid-item {
    height: 80px;
  }
}