.el-input,.el-input__inner {
  width: 100%;
  display: inline-block
}

.el-color-picker__panel {
  position: absolute;
  z-index: 10;
  padding: 6px;
  background-color: #fff;
  border: 1px solid #d1dbe5;
  box-shadow: 0 2px 4px rgba(0,0,0,.12),0 0 6px rgba(0,0,0,.12)
}

.el-input {
  position: relative;
  font-size: 14px
}

.el-input.is-disabled .el-input__inner {
  background-color: #eef1f6;
  border-color: #d1dbe5;
  color: #bbb;
  cursor: not-allowed
}

.el-input.is-disabled .el-input__inner::placeholder {
  color: #C2C4CC
}

.el-input.is-active .el-input__inner {
  outline: 0;
  border-color: #FF8595;
}

.el-input__inner {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  color: #1A1A1A;
  font-size: inherit;
  height: 36px;
  line-height: 1;
  outline: 0;
  padding: 8px 12px;
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  caret-color: #FF0F23;
}

.el-button,.el-checkbox-button__inner {
  -webkit-appearance: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  outline: 0;
  text-align: center
}

.el-input__inner::placeholder {
  color: #C2C4CC
}

.el-input__inner:hover {
  border-color: #FF8595;
}

.el-input__inner:focus {
  outline: 0;
  border-color: #FF8595;
}

.el-input__icon {
  position: absolute;
  width: 35px;
  height: 100%;
  right: 0;
  top: 0;
  text-align: center;
  color: #bfcbd9;
  transition: all .3s
}

.el-input__icon:after {
  content: '';
  height: 100%;
  width: 0;
  display: inline-block;
  vertical-align: middle
}

.el-input__icon+.el-input__inner {
  padding-right: 35px
}

.el-input__icon.is-clickable:hover {
  cursor: pointer;
  color: #8391a5
}

.el-input__icon.is-clickable:hover+.el-input__inner {
  border-color: #8391a5
}

.el-input--large {
  font-size: 16px
}

.el-input--large .el-input__inner {
  height: 42px
}

.el-input--small {
  font-size: 13px
}

.el-input--small .el-input__inner {
  height: 30px
}

.el-input--mini {
  font-size: 12px
}

.el-input--mini .el-input__inner {
  height: 22px
}

.el-input-group {
  line-height: normal;
  display: inline-table;
  width: 100%;
  border-collapse: separate
}

.el-input-group>.el-input__inner {
  vertical-align: middle;
  display: table-cell;
}

.el-input-group__append,.el-input-group__prepend {
  background-color: #fbfdff;
  color: #97a8be;
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 0 10px;
  width: 1px;
  white-space: nowrap;
}

.el-input-group--prepend .el-input__inner,.el-input-group__append {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.el-input-group--append .el-input__inner,.el-input-group__prepend {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.el-input-group__append .el-button,.el-input-group__append .el-select,.el-input-group__prepend .el-button,.el-input-group__prepend .el-select {
  display: block;
  margin: -10px
}

.el-input-group__append button.el-button,.el-input-group__append div.el-select .el-input__inner,.el-input-group__append div.el-select:hover .el-input__inner,.el-input-group__prepend button.el-button,.el-input-group__prepend div.el-select .el-input__inner,.el-input-group__prepend div.el-select:hover .el-input__inner {
  border-color: transparent;
  background-color: transparent;
  color: inherit;
  border-top: 0;
  border-bottom: 0
}

.el-input-group__append .el-button,.el-input-group__append .el-input,.el-input-group__prepend .el-button,.el-input-group__prepend .el-input {
  font-size: inherit
}

.el-button,.el-textarea__inner {
  font-size: 14px;
  box-sizing: border-box
}

.el-input-group__prepend {
  border-right: 0
}

.el-input-group__append {
  border-left: 0
}

.el-textarea {
  display: inline-block;
  width: 100%;
  vertical-align: bottom
}

.el-textarea.is-disabled .el-textarea__inner {
  background-color: #eef1f6;
  border-color: #d1dbe5;
  color: #bbb;
  cursor: not-allowed
}

.el-textarea.is-disabled .el-textarea__inner::placeholder {
  color: #C2C4CC
}

.el-textarea__inner {
  display: block;
  resize: vertical;
  padding: 8px 12px;
  line-height: 18px;
  width: 100%;
  color: #1A1A1A;
  font-family: 'PingFang SC';
  background-color: #fff;
  background-image: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  transition: border-color .2s cubic-bezier(.645,.045,.355,1);
  caret-color: #FF0F23;
}

.el-textarea__inner::placeholder {
  color: #C2C4CC
}

.el-textarea__inner:hover {
  border-color: #FF8595;
}

.el-textarea__inner:focus {
  outline: 0;
  border-color: #FF8595;
}