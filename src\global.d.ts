declare module '*.png'
declare module '*.gif'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.svg'
declare module '*.css'
declare module '*.less'
declare module '*.scss'
declare module '*.sass'
declare module '*.styl'
declare module 'js-cookie'
declare module 'react-transition-group'
declare module 'jsonp'

declare module '*.config.js' {
  const config: any
  export default config
}

interface Window {
  ParamsSign?: any
  PSign: {
    sign(params: any): Promise<Record<string, any>>
  }
  SHA256(value: string): string
  getJsToken(callback: Function, value: number): void
  log(bizType: string, funcId: string, params: ReturnType<typeof JSON.stringify>): void
  expLogJSON(bizType: string, funcId: string, params: unknown): void
  dra?: any
  EventEmitterPcItem?: any
  searchMainConfig?: any
  pageConfig?: any
  itemEventBus?: any
  area?: (s: any, o: any) => void
  elevator?: (s: any) => void
  jQuery?: {
    ajax: (s: any) => void
  }
  $?: (s: any) => any
  clipboardData: any
  captchaLoadJS: any
  ParamsSign?: any
}

declare global {
  interface Window {
    area?: (s: any, o: any) => void
    elevator?: (s: any) => void
    jQuery?: {
      ajax: (s: any) => void
    }
    $?: (s: any) => any
  }
}

declare module '@app/common/legao'
declare module '@app/common/legao/Context'
declare module '@app/common/legao/libs'
declare module '@app/hooks/useClickOutside'
declare module '@app/hooks/useClassAndStyle' {
  export interface ClassAndStyleHelpers {
    className: (...args: any[]) => string
    classNames: (...args: any[]) => string
    style: (styles?: React.CSSProperties) => React.CSSProperties
  }

  const useClassAndStyle: (props: { className?: string; style?: React.CSSProperties }) => ClassAndStyleHelpers

  export default useClassAndStyle
}
