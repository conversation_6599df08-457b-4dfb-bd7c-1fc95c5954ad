/**
 * 美妆加赠
 */
import { ErrorBoundary } from 'react-error-boundary'
import { useMasterData } from '@app/context/masterContext'
import Goods from './Goods'
import scope from './index.module.scss'

const BeautyPlusGift: React.FC = () => {
  const { balanceAdditionalGiftVo } = useMasterData()?.body || {}

  // 不是数组不展示
  if (!balanceAdditionalGiftVo?.additionalGiftList?.length) {
    return null
  }

  return (
    <div className={scope['beauty-plus-gift']}>
      <div className={`${scope.toolbar} flex-center`}>
        <img
          src="https://img14.360buyimg.com/imagetools/jfs/t1/298680/3/13049/4078/6841415eF6bb57a08/592d5011a44a7e45.png"
          className={scope.icon}
        />
        <div className={scope.tip}>恭喜！本订单免费赠送以下赠品</div>
      </div>
      <div className={scope.list}>
        {balanceAdditionalGiftVo?.additionalGiftList?.map((sku, skuIndex) => (
          <div className={scope.item} key={skuIndex}>
            <Goods sku={sku} />
          </div>
        ))}
      </div>
    </div>
  )
}

const CatchError = () => {
  const fallbackRender = () => {
    console.warn('美妆加赠白屏')
    // 插入白屏DOM
    return <div id="beauty-plus-gift-empty" className={scope.none}></div>
  }

  return (
    <ErrorBoundary fallbackRender={fallbackRender}>
      <BeautyPlusGift />
    </ErrorBoundary>
  )
}

export default CatchError
