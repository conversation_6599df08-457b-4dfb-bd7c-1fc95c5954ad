/**
 * 通用价格
 */
import scope from '../index.module.scss'
import { BalanceSkuType } from '@app/typings/master_api_response'
import { Fragment } from 'react'

const GeneralPrice = ({ sku }: { sku: BalanceSkuType }) => {
  // 点击领取国补跳转
  const handleGetGov = () => {
    window.open(sku.unHitGovSubsidySkuJumpUrl)
  }

  return (
    <div className={scope.right}>
      {/* 到手价 */}
      <div className={`flex ${scope.price}`}>
        {/* 到手价存在 */}
        {sku.finalPrice && (
          <Fragment>
            <div className={`${scope['received-price']} ${scope[sku?.finalPriceClass || ''] || ''}`}>&yen;{sku.finalPrice}</div>
            {sku.finalPriceName && (
              <div className={`${scope.text} ${scope[sku?.finalPriceNameClass || ''] || ''}`}>{sku.finalPriceName}</div>
            )}
          </Fragment>
        )}

        {/* 到手价不存在 */}
        {!sku.finalPrice && <div className={`${scope['received-price']}`}>&yen;{sku.jdPrice}</div>}
      </div>

      {/* 京东价-到手价存在时展示 */}
      {sku.finalPrice && <div className={`${scope['jd-price']}`}>&yen;{sku.jdPrice}&nbsp;京东价</div>}

      {/* 国补领取提示 */}
      {sku.unHitGovSubsidySkuJumpUrl && sku.unHitGovSubsidySkuTips && (
        <div className={`flex-center ${scope['get-gov-tip']}`} onClick={handleGetGov} title={sku.unHitGovSubsidyTitleTips || ''}>
          <div>{sku.unHitGovSubsidySkuTips}</div>
          <div className={scope.arrow}></div>
        </div>
      )}

      {/* 重量 ｜ 购买数量 */}
      <div className={scope.quantity}>
        {sku.weightShowType === 2 && sku.weight && <Fragment>{sku.weight}kg</Fragment>}
        &times;{sku.buyNum}
      </div>
    </div>
  )
}

export default GeneralPrice
