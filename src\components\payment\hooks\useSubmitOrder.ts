/**
 * @file: useSubmitOrder.ts
 * @description: 提交订单逻辑
 */
import md5 from 'md5'
import <PERSON><PERSON> from 'js-cookie'
import { useCallback, useEffect, useRef } from 'react'
import { useStore } from 'jotai'
import { to } from '@app/helpers'
import showBlockModal from '../components/block'
import showToast from '../components/toast'
import { confirm, CommonConfirmDialogs } from '../components/confirm_dialog'
import { submitOrder, getSuccessUrl } from '@app/services/api'
import { setMasterApiParams } from '@app/services/parameters'
import { ORDER_CENTER_URL, SUCCESS_PAGE_URL, CART_URL, TW_HK_OVERSEA_ID, PAGE_QUERY_PARAMS, AUTHAUTH_PAGE_URL } from '@app/services/const'
import useMasterData from '@app/hooks/useMasterData'
import useUpdateMasterData from '@app/hooks/useUpdateMasterData'
import { showInvoiceModalAtom, invoiceInfoMessageAtom } from '@app/components/Invoice/atom/invoiceAtom'
import { venderRemarksAtom } from '@app/atoms/masterAtom'
import { presalePayTypeAtom } from '@app/atoms/presaleAtom'
import phoneNumberAtom from '../../Presale/atoms/phoneNumberAtom'
import type { PaymentPasswordRef } from '../PaymentPassword'
import type { PaymentAgreementRef } from '../PaymentAgreement'
import { reportClick, reportExpose } from '@app/utils/event_tracking'
import reportException from '../report_exception'
import { getBalanceSkus, buildTrackParams } from '../utils'

interface DoSubmit {
  (params: {
    agreementRef: React.RefObject<PaymentAgreementRef>
    passwordRef: React.RefObject<PaymentPasswordRef>
    enterPriseUser?: boolean
    agreeContinueSubmit?: boolean
    ignorePriceChange?: 1 | 0
  }): Promise<void>
}

function useSubmitOrder() {
  const store = useStore()
  const masterData = useMasterData()?.body
  const updateMasterData = useUpdateMasterData()
  const invokedRef = useRef(false)
  /** 曝光埋点 */
  useEffect(() => {
    if (invokedRef.current) return
    const skus = masterData?.mainSkuIdList
    if (skus && Array.isArray(skus)) {
      invokedRef.current = true
      reportExpose('paydetail', { skuid: skus })
    }
  }, [masterData])

  return useCallback<DoSubmit>(
    async function doSubmit(params) {
      reportClick('trade1', buildTrackParams(masterData))
      const { passwordRef, agreementRef, enterPriseUser, agreeContinueSubmit, ignorePriceChange } = params
      /** 实名弹窗 */
      const realName = masterData?.balanceUser?.realName
      const selectRedPacket = masterData?.redPacketResultVO?.selectRedPacket
      const provinceId = masterData?.balanceAddress?.provinceId
      if (realName === false && selectRedPacket === true && provinceId && TW_HK_OVERSEA_ID.includes(+provinceId)) {
        CommonConfirmDialogs.confirmRealName({
          onOk() {
            window.open(masterData?.pageLinkConfig?.realNameAuthentication ?? AUTHAUTH_PAGE_URL)
          },
        })
        return
      }
      /** 支付密码 */
      let payPassword
      if (passwordRef.current) {
        const [flag, value] = passwordRef.current.value
        if (!flag) {
          confirm({
            title: '请输入支付密码',
            okText: '我知道了',
            onOk: () => {
              passwordRef.current?.focus()
            },
          })
          return
        } else {
          payPassword = md5(value)
        }
      }
      /** 用户协议 */
      if (agreementRef.current) {
        const agreements = agreementRef.current.values
        for (let i = 0; i < agreements.length; i++) {
          const agreement = agreements[i]
          if (!agreement.checked) {
            CommonConfirmDialogs.confirmUserAgreement()
            return
          }
        }
      }
      /** 店铺留言 */
      let vendorRemarks,
        vendorRemarksValue = store.get(venderRemarksAtom)
      if (Array.isArray(vendorRemarksValue) && vendorRemarksValue.length) {
        vendorRemarks = vendorRemarksValue
      }
      /** 优惠券使用数量 */
      let couponNum
      const couponVOList = masterData?.couponVOList
      if (Array.isArray(couponVOList) && couponVOList.length) {
        couponNum = couponVOList.length + ''
      }
      /** 是否使用最优优惠券 */
      let isBestCoupon
      const useBestCoupon = masterData?.balanceVirtualAssetsVO?.useBestCoupon
      if (useBestCoupon != null) {
        isBestCoupon = +!!useBestCoupon
      }
      /** 预售 */
      const presale = masterData?.balancePresaleVO
      let presalePayType
      if (presale) {
        if (presale.payStepType == '1') {
          presalePayType = 1
        } else if (['3', '5'].includes(`${presale.payStepType}`)) {
          presalePayType = 2
        } else if (['2', '4'].includes(`${presale.payStepType}`)) {
          // 暂不支持该业务
        }
      }
      presalePayType ??= store.get(presalePayTypeAtom)

      let presaleMobile
      const mobile = store.get(phoneNumberAtom)
      if (presalePayType == 2 && mobile) {
        presaleMobile = mobile
      }

      /** 页面链接上的参数 */
      const { fq, qygId } = PAGE_QUERY_PARAMS as Record<string, string>

      const [err, data] = await to(
        /** @ts-ignore */
        submitOrder({
          ...setMasterApiParams({
            balanceCommonOrderForm: {
              agreeContinueSubmit,
            },
          }),
          source: 'pc-home',
          btSupport: '1',
          ignorePriceChange,
          payPassword,
          presalePayType,
          presaleMobile,
          vendorRemarks,
          couponNum,
          isBestCoupon,
          qygId,
          fq,
        }),
      )

      if (err) {
        showToast({ title: err.message || '提交订单失败' })
        reportException({ submitOrder: err, sence: 'submitOrder' })
        return
      }

      if (data == null) {
        showToast({ title: '提交订单失败' })
        reportException({ submitOrder: err, sence: 'submitOrder noData' })
        return
      }

      if (data.resultFlag) {
        reportExpose(
          'tradesucess',
          buildTrackParams(masterData, {
            orderId: data?.order?.orderId ?? '',
            pin: `${enterPriseUser ? '0' : '1'}-${Cookie.get('pin') ?? ''}`,
            is_invoice: `${masterData?.balanceInvoiceDesc?.useInvoiceStatus ?? ''}`,
            text: store.get(invoiceInfoMessageAtom)?.message ?? '',
          }),
        )
        // 收银台下发失败
        if (!data.order || data.order.orderId == null) {
          window.location.replace(ORDER_CENTER_URL)
          return
        }

        if (enterPriseUser) {
          window.location.replace(ORDER_CENTER_URL)
          return
        }

        const result = await getSuccessUrl()
        // 提单成功跳转支付页面
        if (result?.goPay) {
          window.location.replace(result?.payJumpUrl!)
        } else {
          window.location.replace(`${result?.notPayJumpUrl ?? SUCCESS_PAGE_URL}?orderId=${data.order.orderId}&rid=${Math.random()}`)
        }
        return
      }

      // 提单异常
      if (data.errorCode) {
        const { errorCode, errorReason } = data
        const action = {
          '720': () => {
            // 提单通用异常
            showToast({ title: errorReason || '提交过快，请稍后' })
          },
          '721': () => {
            // 超区
            const content = errorReason || '当前订单超出门店的配送范围，请修改地址或返回上一级页面选择其他门店'
            confirm({ title: '提示信息', okText: '知道了', content })
          },
          '723': () => {
            // 结算金额为空
            showToast({ title: '支付异常' })
          },
          '724': () => {
            // 不满足起送价
            const content = errorReason || '当前订单未达到起送价，请返回上一级页面修改商品'
            confirm({ title: '提示信息', okText: '知道了', content })
          },
          '726': () => {
            // 提单成功，但收银台下发失败,去订单列表
            window.location.replace(ORDER_CENTER_URL)
          },
          '728': () => {
            // 提单成功，但收银台下发失败,去订单详情
            window.location.replace(ORDER_CENTER_URL)
          },
        }[errorCode]

        if (action) {
          action()
        } else if (
          ['722', '7203', '7204', '7205', '7206', '7207', '7208', '727', '7209', '7210', '7211'].includes(errorCode) ||
          data?.submitOrderPromptVO
        ) {
          const destroy = showBlockModal({
            ...data,
            mainSkuIdList: masterData?.mainSkuIdList,
            // balanceVendorBundleList: masterData?.balanceVendorBundleList,
            balancePresale: masterData?.balancePresaleVO,
            balanceSkus: getBalanceSkus(masterData),
            balancePurchaseCasual: masterData?.balancePurchaseCasual,
            subsidyToast: data?.usedGovSubsidyToast || masterData?.balanceExt?.usedGovSubsidyToast,
            onClose: () => {
              destroy()
            },
            onResume: () => {
              doSubmit({ ...params, agreeContinueSubmit: true, ignorePriceChange: 1 })
            },
            onRefresh: (params?: Parameters<typeof updateMasterData>[0]) => {
              return updateMasterData({ ...params })
            },
            onOpenPanel: () => {
              // 打开发票弹窗
              store.set(showInvoiceModalAtom, true)
            },
            goBack: () => {
              if (document.referrer) {
                window.history.back()
              } else {
                window.location.replace(masterData?.pageLinkConfig?.backToCartLink ?? CART_URL)
              }
            },
          } as Parameters<typeof showBlockModal>[0])
        } else if (errorReason) {
          confirm({ title: '提示信息', okText: '知道了', content: errorReason })
        }
        return
      }
    },
    [masterData],
  )
}

export default useSubmitOrder
