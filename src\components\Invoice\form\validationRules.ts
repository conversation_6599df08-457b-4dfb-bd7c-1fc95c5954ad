import { invoiceValidationService } from '../services/invoice.validation.service'
import { formFieldsConfig } from './formFieldsConfig'

/**
 * 验证规则生成器 - 根据字段名和配置生成对应的验证规则
 */
export const generateValidationRules = (fieldName: string) => {
  // 获取字段配置
  const config = formFieldsConfig[fieldName] || { required: false, label: fieldName }
  const { required, label } = config

  // 通用验证器函数
  const createValidator = (validationFn: (value: string, required: boolean, label: string) => any) => {
    return (_rule: any, value: string, callback: (error?: Error) => void) => {
      if (!value && !required) {
        callback()
        return
      }
      if (!value && required) {
        callback(new Error(`${label}不能为空`))
        return
      }
      const result = validationFn(value, required, label)
      if (!result.isValid) {
        callback(new Error(result.message))
      } else {
        callback()
      }
    }
  }

  // 根据字段名返回不同的验证规则
  switch (true) {
    // 支持固定电话校验相关字段
    case /tel|regPhone/i.test(fieldName):
      return {
        validator: createValidator(invoiceValidationService.validateRegTel.bind(invoiceValidationService)),
        trigger: 'blur',
      }
    // 电话号码相关字段
    case /phone|mobile/i.test(fieldName):
      return {
        validator: createValidator(invoiceValidationService.validateMobile.bind(invoiceValidationService)),
        trigger: 'blur',
      }

    // 邮箱相关字段
    case /email/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateInvoiceEmail.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 纳税人识别号相关字段
    case /invoiceCode|code/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateVatCode.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 公司名称相关字段
    case /companyName|electroCompanyName/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateVatCompanyName.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 地址相关字段
    case /address|addr/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateRegAddress.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 银行名称相关字段
    case /bank/i.test(fieldName) && !/account/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateRegBank.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 银行账户相关字段
    case /account/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateRegAccount.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 个人名称相关字段
    case /personalName|consigneeName/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateVatConsigneeName.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 增值税发票银行账户相关字段
    case /regBankAccount|vatBankAccount/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateVatBankAccount.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 增值税发票注册地址相关字段
    case /regAddr|vatAddress/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateVatAddress.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 增值税发票注册电话相关字段
    case /regPhone|vatPhone/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateVatPhone.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 通用文本字段（使用新的通用验证方法）
    case /generalText|userInput/i.test(fieldName):
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
        {
          validator: createValidator(invoiceValidationService.validateGeneralText.bind(invoiceValidationService)),
          trigger: ['blur', 'change'],
        },
      ]

    // 默认规则
    default:
      return [
        {
          required: required,
          message: `请输入${label}`,
          trigger: ['blur', 'change'],
        },
      ]
  }
}

/**
 * 根据表单类型生成整套验证规则
 * @param formType electroInvoice(电子普通发票), normalInvoice(纸质普通发票), vat(专用发票), eleVat(电子专用发票)
 * @param step 专用发票的步骤（可选，仅对vat有效）1=第一步公司信息，2=第二步收票人信息
 * @returns 该表单类型对应的所有验证规则
 */
export const generateFormRules = (formType: string, step?: number) => {
  const rules: Record<string, any> = {}

  // 根据不同的表单类型，选择需要生成规则的字段
  switch (formType) {
    // 电子普通发票
    case 'electroInvoice':
      // 个人抬头 - 使用完整路径作为键名
      rules['electroInvoice:personalName'] = generateValidationRules('electroInvoice.personalName')
      rules['electroInvoice:phone'] = generateValidationRules('electroInvoice.phone')
      rules['electroInvoice:email'] = generateValidationRules('electroInvoice.email')

      // 单位抬头 - 使用完整路径作为键名
      rules['electroInvoice:electroCompanyName'] = generateValidationRules('electroInvoice.electroCompanyName')
      rules['electroInvoice:invoiceCode'] = generateValidationRules('electroInvoice.invoiceCode')
      rules['electroInvoice:regAddress'] = generateValidationRules('electroInvoice.regAddress')
      rules['electroInvoice:regTel'] = generateValidationRules('electroInvoice.regTel')
      rules['electroInvoice:regBank'] = generateValidationRules('electroInvoice.regBank')
      rules['electroInvoice:regAccount'] = generateValidationRules('electroInvoice.regAccount')
      break

    // 纸质普通发票
    case 'normalInvoice':
      // 单位抬头
      rules['normalInvoice:companyName'] = generateValidationRules('normalInvoice.companyName')
      // 个人抬头
      rules['normalInvoice:personalName'] = generateValidationRules('normalInvoice.personalName')
      rules['normalInvoice:invoiceCode'] = generateValidationRules('normalInvoice.invoiceCode')
      rules['normalInvoice:regTel'] = generateValidationRules('normalInvoice.regTel')
      rules['normalInvoice:regBank'] = generateValidationRules('normalInvoice.regBank')
      rules['normalInvoice:regAccount'] = generateValidationRules('normalInvoice.regAccount')
      rules['normalInvoice:regAddress'] = generateValidationRules('normalInvoice.regAddress')
      break

    // 专用发票
    case 'vat':
      // 第一步：公司信息验证规则
      if (!step || step === 1) {
        rules['vat:companyName'] = generateValidationRules('vat.companyName')
        rules['vat:code'] = generateValidationRules('vat.code')
        rules['vat:regAddr'] = generateValidationRules('vat.regAddr')
        rules['vat:regPhone'] = generateValidationRules('vat.regPhone')
        rules['vat:regBank'] = generateValidationRules('vat.regBank')
        rules['vat:regBankAccount'] = generateValidationRules('vat.regBankAccount')
        rules['vat:email'] = generateValidationRules('vat.email')
      }

      // 第二步：收票人信息验证规则
      if (!step || step === 2) {
        rules['vat:invoiceConsigneeEditVO:consigneeName'] = generateValidationRules('vat.invoiceConsigneeEditVO.consigneeName')
        rules['vat:invoiceConsigneeEditVO:phone'] = generateValidationRules('vat.invoiceConsigneeEditVO.phone')
        rules['vat:invoiceConsigneeEditVO:address'] = generateValidationRules('vat.invoiceConsigneeEditVO.address')
      }
      break

    // 电子专用发票
    case 'eleVat':
      rules['eleVat:companyName'] = generateValidationRules('eleVat.companyName')
      rules['eleVat:code'] = generateValidationRules('eleVat.code')
      rules['eleVat:regAddr'] = generateValidationRules('eleVat.regAddr')
      rules['eleVat:regPhone'] = generateValidationRules('eleVat.regPhone')
      rules['eleVat:regBank'] = generateValidationRules('eleVat.regBank')
      rules['eleVat:regBankAccount'] = generateValidationRules('eleVat.regBankAccount')
      rules['eleVat:email'] = generateValidationRules('eleVat.email')
      break
  }
  return rules
}
