/**
 * @file: hwjy.tsx
 * @description: 海外配送子组件，用于展示海外配送到店取货等逻辑
 */
import { useEffect, useState } from 'react'
import { Tips } from '../../../ui/Tips/tips'
import { api_balance_transportPickSiteListBundle_pc } from '@app/services/api'
import { UiAddrSelector } from '../../../ui/TimeSelector/types'
import AddrSelector from '../../../ui/TimeSelector/addrSelector'
import { DeliveryPickSiteInfo } from '@app/typings/delivery_api'
import { OverseasPickInfo } from '@app/typings/master_api_response'
import Checkbox from '@app/common/checkbox'
import toast from '@app/common/toast'

export default function Hwjy({
  bundleId,
  tips,
  transportCode,
  onChange,
  overseasPickInfo,
}: {
  overseasPickInfo?: OverseasPickInfo
  bundleId: string
  transportCode: string
  tips: string[]
  onChange: (addr: DeliveryPickSiteInfo) => void
}) {
  const [shopList, setShopList] = useState<UiAddrSelector.InitState<any, any>['addrList']>([])

  const [needShop, setNeedShop] = useState<boolean>(overseasPickInfo?.selected || false)

  const [isLoading, setIsLoading] = useState<boolean>(false)

  // console.log(overseasPickInfo, "overseasPickInfo");

  useEffect(() => {
    setNeedShop(overseasPickInfo?.selected || false)
  }, [overseasPickInfo])

  useEffect(() => {
    if (!needShop) {
      return
    }
    if (!overseasPickInfo?.pickId) {
      return
    }
    setIsLoading(true)
    api_balance_transportPickSiteListBundle_pc({ formatBundleUUID: bundleId, transportCode: transportCode, overseasTransport: true })
      .then((res) => {
        const list: UiAddrSelector.addInfo<any>[] = res?.body
          ?.map((item: DeliveryPickSiteInfo) => {
            const ret = {
              pickAddress: item.address,
              pickName: item.pickName,
              isSelected: false,
              pickPhone: item.telephone,
              targetData: item,
              distance: item.distance + '千米',
            }
            if (overseasPickInfo && item.pickId === overseasPickInfo.pickId && overseasPickInfo.selected) {
              ret.isSelected = true
            }
            if (!item.telephone) {
              return { ...ret, businessHours: item.specialRemark }
            }
            return ret
          })
          .sort((a, b) => {
            if (a.isSelected === b.isSelected) {
              return 0
            }
            return a.isSelected ? -1 : 1
          })
        setShopList(list || [])
      })
      .catch(() => {
        setShopList([])
        toast.error('服务繁忙，请稍后重试～')
      })
      .finally(() => {
        setIsLoading(false)
      })
  }, [bundleId, transportCode, overseasPickInfo, needShop])

  return (
    <>
      <Tips contentList={tips} maxWidth={'448px'} />
      {overseasPickInfo && overseasPickInfo.pickId && (
        <>
          <div
            style={{
              display: 'flex',
              padding: '0px 13px',
              height: '34px',
              alignItems: 'center',
              justifyContent: 'start',
              gap: '10px',
              cursor: 'pointer',
              marginTop: '12px',
            }}
            onClick={(e) => {
              e.stopPropagation()
              setNeedShop(!needShop)
              if (needShop) {
                onChange({} as DeliveryPickSiteInfo)
              }
            }}
          >
            <Checkbox style={{ pointerEvents: 'none' }} label={''} checked={needShop} onChange={() => null} />
            <span style={{ fontSize: '14px', fontWeight: '500' }} className={'text_14'}>
              需要到店取货
            </span>
          </div>
          {needShop && (
            <AddrSelector<string, string>
              onChange={(e) => onChange((e.addr || {}) as DeliveryPickSiteInfo)}
              initState={{ addrList: shopList, hasAddrInput: false, pickTimeList: undefined, inLoading: isLoading }}
            />
          )}
        </>
      )}
    </>
  )
}
