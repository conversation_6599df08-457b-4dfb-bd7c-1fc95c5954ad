/**
 * @file: master_context.tsx
 * @description: 结算页应用主数据上下文，包括获取通用配置文案、获取主数据、更新主数据方法声明
 */
import React, { createContext, useContext, useCallback } from 'react'
import Doging from '@app/common/doging'
import useRequest from 'ahooks/lib/useRequest'
import { useAtom } from 'jotai'
import { masterAtom } from '@app/atoms/masterAtom'
import { loadingAtom } from '@app/atoms/loadingAtom'
import { commonConfigAtom } from '@app/atoms/commonConfigAtom'
import { COMMON_CONFIG_TYPE } from '@app/services/const'
import { getMasterApiParams, setMasterApiParams, mergeParams } from '@app/services/parameters'
import { fetchCommonConfig, fetchMasterData, updateMasterData } from '@app/services/api'
import reportException from '@app/services/report_exception'
import { getBalanceSkus, buildTrackParams } from '@app/components/payment/utils'
import { reportExpose, commonPoints } from '@app/utils/event_tracking'
import type { MasterApiResponse } from '@app/typings/master_api_response'
import { confirm } from '@app/components/payment/components/confirm_dialog'

type UpdateMasterData = (params?: Parameters<typeof updateMasterData>[0], caller?: string) => ReturnType<typeof updateMasterData>

const MasterDataContext = createContext<MasterApiResponse | null>(null)
const UpdateMasterDataContext = createContext<UpdateMasterData | null>(null)

type MasterContextProviderProps = {
  children: React.ReactNode
}

/**
 * 主数据上下文：
 * 1.获取通用配置文案
 * 2.获取主数据
 * 3.更新主数据方法声明
 */
export const MasterContextProvider: React.FC<MasterContextProviderProps> = ({ children }) => {
  const [, setCommonConfig] = useAtom(commonConfigAtom)
  const [, setLoading] = useAtom(loadingAtom)
  const [masterData, setMasterData] = useAtom(masterAtom)
  /**
   * 获取通用配置文案
   */
  useRequest(fetchCommonConfig, {
    retryCount: 2,
    defaultParams: [{ configType: COMMON_CONFIG_TYPE }],
    onSuccess: (data) => {
      setCommonConfig(data?.body)
    },
    onError: (_error) => {
      reportException({ error: _error, sence: 'masterContext.fetchCommonConfig' })
    },
  })
  /**
   * 获取主数据
   */
  useRequest(fetchMasterData, {
    retryCount: 3,
    defaultParams: [getMasterApiParams(true)],
    onSuccess: (data) => {
      handleSuccess(data, setMasterData)
      /** 如有百补商品，则更改公参 */
      if (
        !getMasterApiParams(true)?.balanceCommonOrderForm?.supportTenBillion &&
        getBalanceSkus(data?.body).find((el) => !!el?.promotion?.subsidyType)
      ) {
        setMasterApiParams({
          balanceCommonOrderForm: {
            supportTenBillion: true,
          },
        })
      }
      const trackParams = buildTrackParams(data?.body)
      /** @ts-ignore */
      trackParams.skuinfo = trackParams.skuinfo.map(({ skuid, skunum, skutype }) => ({ skuid, skunum, skutype }))
      // 下发试金石
      commonPoints.setData('touchstone_expids', data?.body?.abDataResult?.tagsPool || [])
      reportExpose('paramauto', trackParams)
    },
    onError: (error) => {
      handleError(error, 'masterContext.fetchMasterData')
    },
    onFinally: () => {
      setLoading(false)
    },
  })

  const { loading, runAsync } = useRequest(updateMasterData, {
    manual: true,
    loadingDelay: 3e3,
    onSuccess: (data) => {
      handleSuccess(data, setMasterData)
    },
    onError: (error) => {
      handleError(error, 'masterContext.updateMasterData')
    },
  })
  /**
   * 更新主数据
   * @param params
   * @param caller 业务调用者
   */
  const update = useCallback((params: Parameters<typeof updateMasterData>[0], caller?: string) => {
    const _params = mergeParams(params)
    if (!['virtualAsset', 'select_payment'].includes(caller!)) {
      _params!.balanceCommonOrderForm!.useBestCoupon = true
    }
    return runAsync(_params)
  }, [])

  return (
    <UpdateMasterDataContext.Provider value={update}>
      <MasterDataContext.Provider value={masterData}>
        {children}
        {loading && <Doging />}
      </MasterDataContext.Provider>
    </UpdateMasterDataContext.Provider>
  )
}

MasterContextProvider.displayName = 'MasterContextProvider'

export const useMasterData = () => {
  return useContext(MasterDataContext)
}

export const useUpdateMasterData = () => {
  return useContext(UpdateMasterDataContext)!
}

function handleSuccess(data: MasterApiResponse, setMasterData: Function) {
  setMasterData(data)
  const tipsVOList = data?.body?.tipsVOList
  /** “国家补贴”提示文案 */
  let stateSubsidyTips
  if (Array.isArray(tipsVOList) && tipsVOList?.length && (stateSubsidyTips = tipsVOList.find((el) => el.type == 10 && el.tip))) {
    const { tip, url, extMap } = stateSubsidyTips
    /** [title, okText, cancelText] */
    const TEXTS =
      {
        1: ['完善实名信息', '去实名', '下次再说'],
        2: ['绑定银行卡', '去绑卡', '下次再说'],
        3: ['地址重置'],
        4: ['时效重置'],
        5: ['发票重置'],
      }[extMap?.type as string] || []

    confirm({
      title: TEXTS[0] || '提示',
      description: tip,
      cancelText: TEXTS[2] || '我知道了',
      okText: url ? TEXTS[1] || '去完善' : null,
      onOk: url ? () => window.open(url) : null,
    })
  }
}

function handleError(error: any, sence: string) {
  reportException({ error, sence })
}
