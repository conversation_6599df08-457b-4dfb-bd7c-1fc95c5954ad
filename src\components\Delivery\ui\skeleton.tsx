import Botton from './botton'
import styles from './index.module.scss'

export default ({ onClick }: { onClick: () => void }) => {
  return (
    <div className={styles.skeleton_wrap}>
      <div className={styles['skeleton-container']}>
        {/* 骨架屏内容 */}
        <div className={`${styles.skeleton} ${styles['skeleton-header']}`}></div>
        <div className={`${styles.skeleton} ${styles['skeleton-header2']}`}></div>
        {/* <div className={`${styles.skeleton} ${styles['skeleton-text']}`}></div>
            <div className={`${styles.skeleton} ${styles['skeleton-text']}`}></div>
            <div className={`${styles.skeleton} ${styles['skeleton-text']} ${styles.small}`}></div> */}

        <div className={`${styles.skeleton} ${styles['skeleton-image']}`}></div>

        {/* <div className={`${styles.skeleton} ${styles['skeleton-text']}`}></div>
            <div className={`${styles.skeleton} ${styles['skeleton-text']}`}></div> */}

        {/* <div className={styles['skeleton-grid']}>
                <div className={`${styles.skeleton} ${styles['skeleton-grid-item']}`}></div>
                <div className={`${styles.skeleton} ${styles['skeleton-grid-item']}`}></div>
                <div className={`${styles.skeleton} ${styles['skeleton-grid-item']}`}></div>
            </div>

            <div className={`${styles.skeleton} ${styles['skeleton-button']}`}></div> */}
      </div>
      <Botton
        onSure={() => {
          onClick()
        }}
        onCancel={() => onClick()}
      />
    </div>
  )
}
