/*
 * @Author: ext.xuchao26
 * @Date: 2025-05-27 12:55:06
 * @LastEditTime: 2025-07-02 22:10:08
 * @LastEditors: ext.xuchao26
 * @Description: 收货人信息表单
 */
import React, { createRef, RefObject } from 'react'
import { Input, Form, Select, Toast2, Modal } from '@app/common/legao'
import { JDTag, Area, GBAreaNestTip } from '../components'
import apis from '../api'
import { addAddress, modifyAddress } from '../utils/address_api'
import { debounce } from '@app/common/legao/libs/utils/debounce'
import { getMapKey, trimTxt, areaCodes, originRules, isOverSea, formData, full } from '../utils'

type postCodeDarkGrainType = Record<string, string>

export default class JDForm extends React.Component {
  formRef: RefObject<HTMLFormElement> = createRef()
  areaRef = createRef()
  addressDetailRef = createRef()
  mobileRef = createRef()
  phoneRef = createRef()
  constructor(props) {
    super(props)
    const {
      addressId,
      provinceId = 0,
      cityId = 0,
      countyId = 0,
      townId = 0,
      provinceName,
      cityName,
      countyName,
      townName,
      addressDetail,
      shortAddress,
      houseNumber,
      name,
      mobile,
      realMobile,
      email,
      phone,
      postCode,
      text,
      retTag,
      defaultAddress,
      tagSource,
      userDefinedTag,
      coordType,
      latitudeString,
      longitudeString,
    } = props.data
    const isOverSeaArea = isOverSea(provinceId)
    const areaCode = isOverSeaArea ? props.data.areaCode : '86'
    const areaName = areaCodes[areaCode] || cityName
    this.state = {
      form: {
        addressId,
        provinceId,
        cityId,
        countyId,
        townId,
        provinceName,
        cityName,
        countyName,
        townName,
        addressDetail,
        shortAddress,
        houseNumber,
        name,
        mobile,
        realMobile,
        phone,
        email,
        postCode,
        areaCode,
        areaName,
        text,
        retTag,
        defaultAddress: !!defaultAddress,
        tagSource,
        userDefinedTag,
        coordType: coordType || 5,
        latitudeString,
        longitudeString,
      },
      selectVisible: { mobile: false, phone: false },
      suggestAddr: [],
      loading: false,
      addAddressPageData: {},
      rules: {},
      originRules: originRules.call(this),
      areaCodeList: [],
      isAreaCode: '',
    }
    this.handleSubmit = this.handleSubmit.bind(this)
    this.handleReset = this.handleReset.bind(this)
    this.handleNameChange = this.onChange.bind(this, 'name')
    this.handleAreaCodeChange = this.handleAreaCodeChange.bind(this)
    this.handleMobileChange = this.onChange.bind(this, 'mobile')
    this.handlePhoneChange = this.onChange.bind(this, 'phone')
    this.handlePostCodeChange = this.onChange.bind(this, 'postCode')
    this.handleEmailChange = this.onChange.bind(this, 'email')
    this.handleTagChange = this.handleTagChange.bind(this)
    this.onSearch = this.onSearch.bind(this)
    this.onMobileVisibleChange = this.onVisibleChange.bind(this, 'mobile')
    this.onPhoneVisibleChange = this.onVisibleChange.bind(this, 'phone')
  }

  updateAddressForm = (val) => {
    const { name, addrMobile, addressDetail, provinceId = 0, shortAddress } = val
    const isOverSeaArea = isOverSea(provinceId)
    const areaCode = isOverSeaArea ? val.areaCode : '86'
    const areaName = areaCodes[areaCode] || cityName
    this.setState(
      (current) => {
        return {
          form: {
            ...current.form,
            ...val,
            name: name || current?.form?.name || '',
            mobile: addrMobile.length === 11 ? addrMobile : current?.form?.mobile || '',
            shortAddress: !isOverSeaArea ? shortAddress || addressDetail : '', // 短地址:地图模式+大陆,更新短地址
            addressDetail: addressDetail || (!isOverSeaArea ? shortAddress : ''), // 海外或者非poi更新详细地址
            areaName,
          },
        }
      },
      async () => {
        const { provinceId = 0, cityId = 0, countyId = 0, townId = 0, addressDetail } = this.state.form
        const areaId = [provinceId, cityId, countyId, townId].join('-')
        this.areaRef.current && this.areaRef.current.setValue(areaId)
      },
    )
  }

  onMouseDown(isAreaCode) {
    this.setState({ isAreaCode })
  }
  // select下拉框位置优化
  onVisibleChange(key, value) {
    this.setState(
      (prevState) => ({
        selectVisible: { ...(prevState.selectVisible || {}), [key]: value },
      }),
      () => {
        setTimeout(() => {
          let paddingTop = 0,
            popperJS = null
          const formDom = this.formRef.current?.formRef?.current
          if (this.state.isAreaCode == key && formDom) {
            const dialogRect = formDom?.getBoundingClientRect()
            const cssProps = window.getComputedStyle(formDom, null)
            if (key === 'mobile') {
              const mobileRootDom = this.mobileRef.current.wrappedRef.current.rootRef.current
              const mobilePopDom = this.mobileRef.current.wrappedRef.current.popperRef.current
              popperJS = this.mobileRef.current.wrappedRef.current.popperJS
              const mobileRootRect = mobileRootDom.getBoundingClientRect()
              const mobilePopRect = mobilePopDom?.getBoundingClientRect()
              const mobileOffset = mobileRootRect.top - dialogRect.top
              paddingTop = mobilePopRect.height - mobileOffset + 4
            } else if (key === 'phone') {
              const phoneRootDom = this.phoneRef.current.wrappedRef.current.rootRef.current
              const phonePopDom = this.phoneRef.current.wrappedRef.current.popperRef.current
              popperJS = this.phoneRef.current.wrappedRef.current.popperJS
              const phoneRootRect = phoneRootDom.getBoundingClientRect()
              const phonePopRect = phonePopDom?.getBoundingClientRect()
              const phoneOffset = phoneRootRect.top - dialogRect.top
              paddingTop = phonePopRect.height - phoneOffset + 4
            }
            if (value) {
              formDom.style.paddingTop = parseInt(cssProps?.getPropertyValue('padding-top')) + paddingTop + 'px'
            } else {
              formDom.style.paddingTop = 0
            }
            popperJS?.update?.()
          }
        }, 200)
      },
    )
  }

  onChange = (key, value) => {
    this.setState((prevState) => ({
      form: { ...prevState.form, [key]: value },
    }))
  }

  updatePostDarkGrain = () => {
    const { addAddressPageData, foreignPostCode, form } = this.state
    const { areaCode, provinceId } = form
    const postCodeDarkGrain: postCodeDarkGrainType = Object.entries(addAddressPageData?.postCodeDarkGrain || {}).reduce(
      (acc: postCodeDarkGrainType, [key, value]) => {
        acc[key] = value as string
        const newKey = key.replace(/^0+/, '')
        acc[newKey] = value as string
        return acc
      },
      {},
    )
    if (provinceId === 53283) {
      // 海外地址
      if (Object.keys(postCodeDarkGrain || {})?.includes(areaCode as string)) {
        this.setState((current) => ({
          addAddressPageData: {
            ...current.addAddressPageData,
            foreignAddressDarkGrain: {
              ...current.addAddressPageData.foreignAddressDarkGrain,
              postCode: postCodeDarkGrain[areaCode as string],
            },
          },
        }))
      } else {
        this.setState((current) => ({
          addAddressPageData: {
            ...current.addAddressPageData,
            foreignAddressDarkGrain: {
              ...current.addAddressPageData.foreignAddressDarkGrain,
              postCode: foreignPostCode as string,
            },
          },
        }))
      }
    }
  }

  handleAreaCodeChange(areaName) {
    const { areaNames } = this.state
    const { areaCode, parentId = 0, addrId = 0 } = areaNames?.[areaName] || {}
    this.setState(
      (prevState) => ({
        form: {
          ...prevState.form,
          areaCode,
          areaName,
          provinceId: parentId,
          cityId: addrId,
          countyId: 0,
          addressDetail: parentId === 53283 ? '' : prevState.form.addressDetail,
          latitudeString: '',
          longitudeString: '',
        },
        suggestAddr: [],
      }),
      () => {
        const { provinceId, cityId, countyId } = this.state.form
        const areaId = [provinceId, cityId, countyId].join('-')
        this.areaRef.current && this.areaRef.current.setValue(areaId)
        this.formRef.current?.validateField('provinceId')
        // this.formRef.current?.validate()
        this.updatePostDarkGrain()
      },
    )
  }

  handleTagChange = (value) => {
    this.setState((prevState) => ({
      form: { ...prevState.form, ...value },
    }))
  }

  handleAreaChange = (area, local) => {
    const { provinceId = 0, cityId = 0, countyId = 0, provinceName = '', cityName = '', countyName = '' } = local || {}
    const { areaNames } = this.state
    const { areaCode, name } = areaNames?.[cityName] || { areaCode: '86', name: '中国大陆' }
    this.setState(
      (prevState) => ({
        form: {
          ...prevState.form,
          provinceId,
          cityId,
          countyId,
          townId: undefined,
          provinceName,
          cityName,
          countyName,
          townName: undefined,
          areaCode,
          areaName: name,
          addressDetail: provinceId === 53283 ? '' : prevState.form.addressDetail,
          latitudeString: '',
          longitudeString: '',
        },
        suggestAddr: [],
      }),
      () => {
        this.updatePostDarkGrain()
      },
    )
  }

  handleAddressDetailChange = (addressDetail) => {
    const { suggestAddr, form } = this.state
    const { title, latitude, longitude, countyId } =
      suggestAddr?.find((m) => addressDetail?.includes(m.title) || m.title?.includes(addressDetail)) || {}
    if (!title) {
      this.setState(
        (prevState) => ({
          form: { ...prevState.form, addressDetail, latitudeString: '', longitudeString: '' },
        }),
        () =>
          form.provinceId === 53283 &&
          this.addressDetailRef.current?.wrappedRef.current?.state?.visible &&
          this.addressDetailRef.current?.wrappedRef.current?.handleClose(),
      )
    } else {
      this.setState((prevState) => ({
        form: {
          ...prevState.form,
          addressDetail,
          countyId: countyId || prevState.form.countyId,
          latitudeString: latitude,
          longitudeString: longitude,
        },
      }))
    }
  }

  handleTextChangeDelay = debounce((text) => {
    const { addAddressPageData } = this.state
    apis
      .getIntelligentText({
        text,
        usePoiAddress: !!addAddressPageData?.usePoiAddress,
        scene: '0',
      })
      .then((res) => {
        // 识别成功
        if (res && res.resultFlag) {
          const { provinceName, cityName, countyName, addressDetail, mobile, provinceId, cityId, countyId, complete } =
            res.addressInfo || {}
          const addrMobile = mobile || this.state.form.mobile || ''
          if (provinceId && cityId && provinceName && cityName) {
            if (countyId && countyName && addressDetail && addrMobile.length === 11) {
              console.warn('识别成功，请检查是否准确，如有错漏请及时修正。')
            }
          }

          // 文本识别返回poi地址
          if (res?.addressInfoVOList?.length) {
            this.updateAddressForm({ ...res.addressInfoVOList[0], addrMobile })
          } else if (complete && addressDetail) {
            // 1-4级地址全，并且有addressDetail
            this.updateAddressForm({ ...res?.addressInfo, addrMobile })
          } else {
            this.updateAddressForm({ ...res?.addressInfo, addrMobile })
          }
        } else {
          // 识别失败
          console.warn(res.message)
          // resultCode-：-1:无法识别或识别失败；0:识别不全；1：没有识别成功；2:所选地区暂未开通该功能；3:识别成功；
        }
      })
      .catch(() => console.error('抱歉，没有识别成功噢'))
  })

  handleTextChange = (text) => {
    this.setState((prevState) => ({
      form: { ...prevState.form, text },
    }))
    this.handleTextChangeDelay(text)
  }

  saveOrUpdateAddress = async (queryParams, type: string) => {
    const { onSelectAddress, dispatch } = this.props
    queryParams.editVersion = 2 // 给服务端标识是新编辑版本
    const apiFunc = type === 'add' ? addAddress : modifyAddress
    try {
      const res = await apiFunc(queryParams)
      const { resultCode } = res || {}
      // 如果保存成功
      if (res?.resultFlag) {
        Toast2.create({
          type: 'success',
          text: '保存成功',
        })
        typeof onSelectAddress === 'function' && onSelectAddress(res)
        return Promise.resolve(res)
      } else {
        const frameTips = res?.frameTips
        const {
          townName,
          countyName,
          cityName,
          provinceName,
          townId,
          countyId,
          cityId,
          provinceId,
          selectLeftMsg, // 弹窗左侧按钮文案
          selectRightMsg, // 弹窗右侧按钮文案
          titleMsg, // 弹窗提示语
          latitudeString,
          longitudeString,
          conflictLevel, // 嵌套
          reportCode, // 存量地址需求，type埋点上报类型7-开头，逻辑后移，下发的埋点数据类型，以后的type需求下发也逻辑后移
          addressDetail, // 详细地址
          shortAddress, // 短地址，推荐poi
          houseNumber, // 推荐poi
        } = res.addressCheckResultVO || {}

        if (frameTips) {
          // 新失败弹窗逻辑
          // 地址校验弹窗曝光

          try {
            // sgm.customReport({
            //   type: 6,
            //   code: sgmCode.NOPASS_FRAME_TIPS,
            //   msg: `命中frameTips:${sgmMsg}&frameTips=${JSON.stringify(frameTips)}`,
            // })
          } catch (error) {
            console.log(error)
          }
          const btnActionFn = (actionType: string) => {
            if (actionType === 'continueSave') {
              this.saveOrUpdateAddress(
                {
                  ...queryParams,
                  overhang: {
                    ...queryParams.overhang,
                    mustCheck: false, // 是否校验
                  },
                },
                type,
              )
            }
          }

          if (frameTips.resultType === 'defaultTwoButton') {
            const { leftButton, rightButton } = frameTips

            Modal.create({
              content: frameTips.content,
              confirmText: rightButton.text || '确定',
              cancelText: leftButton.text || '取消',
              onConfirm: () => {
                btnActionFn(rightButton.actionType)
              },
              onCancel: () => {
                dispatch({ isShow: false })
                btnActionFn(leftButton.actionType)
              },
            })
          }
          return Promise.reject(frameTips.content)
        }

        // showBoxNoAutoComp详细地址与四级地址不匹配（不在一个范围），展示弹窗
        // showBoxNoChoose四级地址末级地址选择暂不知道，根据详细地址匹配，接口返回相关信息，弹窗提示用户
        // showBox，根据详细地址匹配，接口返回相关信息，弹窗提示用户
        // showBoxNoChooseFail：识别不成功的时候
        // showBoxForceSave详细地址与四级地址不匹配（不在一个范围），点击修改，关闭弹窗,不用更新四级地址。点击取消，返回上一页
        // showBoxRecommendPoi 推荐地图返回来的地址加上门牌号的场景，精细化poi地址弹窗。
        if (
          resultCode === 'showBox' ||
          resultCode === 'showBoxNoChoose' ||
          resultCode === 'showBoxNoAutoComp' ||
          resultCode === 'showBoxNoChooseFail' ||
          resultCode === 'showBoxForceSave' ||
          resultCode === 'showBoxRecommendPoi'
        ) {
          // 弹窗曝光sgm上报
          // try {
          // const boxSgmCode = SHOWBOX_SGM_MAP[resultCode]
          // boxSgmCode &&
          //   sgm.customReport({
          //     type: 6,
          //     code: boxSgmCode,
          //     msg: `命中resultCode=${boxSgmCode}:${sgmMsg}`,
          //   })
          // } catch (error) {
          //   console.error(error)
          // }
          // 弹窗，使用addressCheckResultVO渲染弹窗

          if (titleMsg) {
            // showBoxNoChooseFail：识别不成功的时候
            if (res?.resultCode === 'showBoxNoChooseFail') {
              Modal.create({
                content: titleMsg,
                confirmText: selectRightMsg || '好的',
                onConfirm: () => {
                  //  展示地区选择弹窗,第三个参数传'0',接口不会再返回暂不选择选项
                  // props?.showAddrLayer(undefined, undefined, '0')
                },
              })
              return Promise.reject(titleMsg)
            }

            // 四级地址暂不选择时，如果使用接口下发地址进行替换，需传递该对象
            let addressFeedbackVO: AddressStandard.AddressFeedbackVO | undefined
            if (res?.resultCode === 'showBoxNoChoose') {
              addressFeedbackVO = {
                /* 原始地址信息 - 用户最初提供或系统采集的地址 */
                originProvinceCode: queryParams?.provinceId /* 原始地址的省份编码 */,
                originProvinceName: queryParams?.provinceName /* 原始地址的省份名称 */,
                originCityCode: queryParams?.cityId /* 原始地址的城市编码 */,
                originCityName: queryParams?.cityName /* 原始地址的城市名称 */,
                originCountyCode: queryParams?.countyId /* 原始地址的县/区编码 */,
                originCountyName: queryParams?.countyName /* 原始地址的县/区名称 */,
                originTownCode: queryParams?.townId /* 原始地址的镇/街道编码 */,
                originTownName: queryParams?.townName /* 原始地址的镇/街道名称 */,
                originLat: queryParams?.latitudeString /* 原始地址的纬度 */,
                originLng: queryParams?.longitudeString /* 原始地址的经度 */,
                originAddress: queryParams?.fullAddress /* 原始完整地址字符串 */,

                /* 匹配地址信息 - 系统处理后与原始地址匹配的结果 */
                matchProvinceCode: provinceId /* 匹配后的省份编码 */,
                matchProvinceName: provinceName /* 匹配后的省份名称 */,
                matchCityCode: cityId /* 匹配后的城市编码 */,
                matchCityName: cityName /* 匹配后的城市名称 */,
                matchCountyCode: countyId /* 匹配后的县/区编码 */,
                matchCountyName: countyName /* 匹配后的县/区名称 */,
                matchTownCode: townId /* 匹配后的镇/街道编码 */,
                matchTownName: townName /* 匹配后的镇/街道名称 */,
                matchAddress: full({
                  ...res.addressCheckResultVO,
                  addressDetail: res.addressCheckResultVO?.addressDetail
                    ? res.addressCheckResultVO?.addressDetail
                    : queryParams.addressDetail, // 接口下发的addressCheckResultVO没有详细地址，所以还使用之前的
                }) /* 匹配后的完整地址字符串 */,
                matchLat: latitudeString /* 匹配后地址的纬度 */,
                matchLng: longitudeString /* 匹配后地址的经度 */,

                /* 用户自报地址信息 - 用户主动提供的当前地址信息 */
                userProvinceCode: provinceId /* 用户报告的省份编码 */,
                userProvinceName: provinceName /* 用户报告的省份名称 */,
                userCityCode: cityId /* 用户报告的城市编码 */,
                userCityName: cityName /* 用户报告的城市名称 */,
                userCountyCode: countyId /* 用户报告的县/区编码 */,
                userCountyName: countyName /* 用户报告的县/区名称 */,
                userTownCode: townId /* 用户报告的镇/街道编码 */,
                userTownName: townName /* 用户报告的镇/街道名称 */,
                userAddress: full({
                  ...res.addressCheckResultVO,
                  addressDetail: res.addressCheckResultVO?.addressDetail
                    ? res.addressCheckResultVO?.addressDetail
                    : queryParams.addressDetail, // 接口下发的addressCheckResultVO没有详细地址，所以还使用之前的
                }) /* 用户报告的完整地址字符串 */,
                userLat: longitudeString /* 用户报告地址的纬度 */,
                userLng: longitudeString /* 用户报告地址的经度 */,
                treatment: 1, // 用户的操作行为 1：接收 2：拒绝(修改地址)
                /**
                 * 匹配操作的基准ID，可能用于关联特定的匹配规则或过程
                 * 列表携带过来的
                 */
                // baseId: addressEditOpt?.baseId,
              }
            }
            // 地图poi精细化地址弹窗
            if (res?.resultCode === 'showBoxRecommendPoi') {
              const recommondAddressDetail = `${shortAddress}${houseNumber}`
              if (queryParams.shortAddress && shortAddress) {
                Modal.create({
                  title: titleMsg,
                  contentArr: [
                    { name: '当前', value: queryParams.shortAddress },
                    { name: '推荐', value: shortAddress },
                  ],
                  confirmText: selectRightMsg || '推荐使用',
                  cancelText: selectLeftMsg || '保持当前',
                  onConfirm: () => {
                    // 用户点击确认后，更新保存入参并再次调用保存接口，传入isRecommendPoi: false ，此时接口不再校验
                    this.saveOrUpdateAddress(
                      {
                        ...queryParams,
                        townName,
                        countyName,
                        cityName,
                        provinceName,
                        townId,
                        countyId,
                        cityId,
                        provinceId,
                        longitudeString,
                        latitudeString,
                        shortAddress,
                        houseNumber,
                        addressDetail: recommondAddressDetail,
                        fullAddress: full({
                          ...res.addressCheckResultVO,
                          addressDetail: recommondAddressDetail,
                        }),
                        isRecommendPoi: false,
                      },
                      type,
                    )
                  },
                  onCancel: () => {
                    // 用户点击确认后，更新保存入参并再次调用保存接口，传入isRecommendPoi: false ，此时接口不再校验
                    this.saveOrUpdateAddress(
                      {
                        ...queryParams,
                        isRecommendPoi: false,
                      },
                      type,
                    )
                  },
                })
                return Promise.reject(titleMsg)
              }
              // sgm.customReport({
              //   type: 6,
              //   code: sgmCode.POI,
              //   msg: `精细化地址需求没有下发弹窗信息，current:${queryParams.addressDetail},recommend:${recommondAddressDetail}`,
              // })
              this.saveOrUpdateAddress(
                {
                  ...queryParams,
                  isRecommendPoi: false,
                },
                type,
              )
              return Promise.reject(titleMsg)
            }
            // 展示询问弹窗
            Modal.create({
              content: titleMsg,
              confirmText: selectRightMsg || '确定',
              cancelText: selectLeftMsg || '取消',
              onConfirm: () => {
                // 四级地址末级地址选择暂不知道:点击好的，提示地址更换成功，调用保存接口
                if (res?.resultCode === 'showBoxNoChoose') {
                  // 关闭弹窗
                  Toast2.create({
                    type: 'success',
                    text: '地址更换成功',
                  })
                }

                // 详细地址与四级地址不匹配（不在一个范围），点击修改，关闭弹窗,不用更新四级地址。点击取消，返回上一页
                // 详细地址范围过大，点击修改，弹框消失，停留在新建地址页；
                if (res?.resultCode === 'showBoxNoAutoComp' || resultCode === 'showBoxForceSave') {
                  return
                }

                // 用户点击确认后，更新保存入参并再次调用保存接口，传入validRegion: false ，此时接口不再校验
                this.saveOrUpdateAddress(
                  {
                    ...queryParams,
                    townId,
                    townName,
                    countyId,
                    countyName,
                    cityId,
                    cityName,
                    provinceId,
                    provinceName,
                    latitudeString,
                    longitudeString,
                    addressFeedbackVO,
                    validRegion: false,
                    addressDetail: addressDetail || queryParams.addressDetail,
                    fullAddress: full({
                      ...res.addressCheckResultVO,
                      addressDetail: res.addressCheckResultVO?.addressDetail
                        ? res.addressCheckResultVO?.addressDetail
                        : queryParams.addressDetail, // 接口下发的addressCheckResultVO没有详细地址，所以还使用之前的
                    }),
                  },
                  type,
                )
              },
              onCancel: () => {
                // 3：末级地址校验，弹窗左侧按钮为“不用”（不修改地址直接保存），右侧按钮为“好的”（按系统定位修改地址保存）；
                // 4：省市地址嵌套校验，弹窗左侧按钮为“保存”（不修改地址直接保存），右侧按钮为“修改”（弹窗消失，用户回到页面手动修改）；
                // 5：地址嵌套强校验，弹窗左侧按钮为“取消”（不保存返回上一页），右侧按钮为“修改”（弹窗消失，用户回到页面手动修改）。
                // conflictLevel 1：一级嵌套； 2：二级嵌套； 3：三级嵌套； 4：四级嵌套；

                // 详细地址与四级地址不匹配（不在一个范围），点击修改，关闭弹窗,不用更新四级地址。点击取消，返回上一页
                if (res?.resultCode === 'showBoxNoAutoComp') {
                  // 返回上一页
                  // router.back()
                  dispatch({ isShow: false })
                  return
                }
                // 四级地址末级地址选择暂不知道:点击否，提示请选择地址所在地区，展示地区选择弹窗
                if (res?.resultCode === 'showBoxNoChoose') {
                  // 关闭弹窗
                  Toast2.create({
                    type: 'warning',
                    text: '请选择地址所在地区',
                  })
                  setTimeout(() => {
                    //  展示地区选择弹窗,第三个参数传'0',接口不会再返回暂不选择选项
                    // props?.showAddrLayer(undefined, undefined, '0')
                  }, 1000)
                }
                // 在用户四级地址都选择的情况下，保存接口提示不匹配，点击取消后，再次调用保存接口
                if (res?.resultCode === 'showBox' || resultCode === 'showBoxForceSave') {
                  // 用户点击确认后再次调用保存接口，传入validRegion: false ，此时接口不再校验
                  this.saveOrUpdateAddress({ ...queryParams, validRegion: false }, type)
                }
              },
            })
          }

          return Promise.reject(titleMsg)
        }
        // 4级地址校验不通过，觉得地址无问题，点击反馈问题
        if (resultCode === '200002') {
          Modal.create({
            content: resultCode + ' 觉得地址无问题，点击反馈问题',
            confirmText: '反馈问题',
            cancelText: '取消',
            onConfirm: () => {
              window.open('https://wj-dongjian.jd.com/q/61961d8186206f00b9b1bae0')
              dispatch({ isShow: false })
            },
            onCancel: () => {
              dispatch({ isShow: false })
            },
          })
          // setRedTipsVal({
          //   content: '觉得地址无问题，点击反馈问题',
          //   icon: 'success',
          //   resultCode,
          // })
          // sgm.customReport({
          //   type: 6,
          //   code: sgmCode.FEEDBACKTIPS,
          //   msg: `觉得地址无问题，点击反馈问题:${sgmMsg}`,
          // })
        } else {
          // sgm.customReport({
          //   type: 6,
          //   code: sgmCode.ERRORTOAST,
          //   msg: `${res?.message}:${sgmMsg}`,
          // })
        }

        Toast2.create({
          type: 'fail',
          text: res?.message || '网络异常，请稍后重试~',
        })
        return Promise.reject(res?.message || '网络异常，请稍后重试~')
      }
    } catch (err) {
      const errMsg = err?.response?.data?.body?.errorReason || '网络异常，请稍后重试~'
      console.error(errMsg)
      return Promise.reject(errMsg)
    }
  }

  handleSubmit(e) {
    e?.preventDefault()
    return new Promise((resolve, reject) => {
      // console.log(this.state)
      this.formRef.current?.validate(async (valid: boolean) => {
        if (valid) {
          const { form } = this.state
          const { type } = this.props
          const { mobile, realMobile, addressDetail, provinceId, latitudeString, longitudeString } = form
          const isOverSeaArea = isOverSea(provinceId)
          const fullAddress = full({ ...form, addressDetail })
          const shortAddress = isOverSeaArea ? undefined : form.shortAddress
          const houseNumber = isOverSeaArea ? undefined : form.houseNumber
          let usePoiAddress = false
          if (latitudeString && longitudeString) {
            usePoiAddress = true
          }
          const queryParams = {
            ...form,
            shortAddress,
            houseNumber,
            fullAddress,
            usePoiAddress,
            mobile: type !== 'add' ? mobile || realMobile : mobile,
          }
          try {
            const res = await this.saveOrUpdateAddress(queryParams, type)
            resolve(res)
          } catch (error) {
            reject(error)
            console.error(error)
          }
        } else {
          reject(valid)
          return false
        }
      })
    })
  }

  handleReset(e) {
    e?.preventDefault()
    this.formRef.current.resetFields()
  }

  onSearch(keyword: string) {
    if (keyword) {
      this.setState({
        loading: true,
      })
      const {
        provinceId = 0,
        cityId = 0,
        countyId = 0,
        provinceName = '',
        cityName = '',
        countyName = '',
        addressDetail,
        latitudeString,
        longitudeString,
      } = this.state.form
      if (provinceId === 53283)
        return this.setState({
          suggestAddr: [],
          loading: false,
        })
      const region = [provinceName, cityName, countyName].join('|')
      apis
        .searchAddress({
          region,
          keyword,
          provinceId, // 一二三级地址
          cityId, // 一二三级地址
          countyId, // 一二三级地址
          latitudeString: '', // 经纬度
          longitudeString: '', // 经纬度
          supportNewParamEncode: true, // 是否加密，true加密
          layerFlag: true, // 是否是地址弹窗})
        })
        .then(({ addressSuggestionVOList }) => {
          const suggestAddr = addressSuggestionVOList || []
          const { title, latitude, longitude, countyId } =
            addressSuggestionVOList?.find((m) => keyword?.includes(m.title) || m.title?.includes(keyword)) || {}
          if (!title) {
            this.setState((prevState) => ({
              form: { ...prevState.form, addressDetail: keyword, latitudeString: '', longitudeString: '' },
              suggestAddr,
            }))
          } else {
            this.setState((prevState) => ({
              form: {
                ...prevState.form,
                addressDetail: keyword,
                countyId: countyId || prevState.form.countyId,
                latitudeString: latitude,
                longitudeString: longitude,
              },
              suggestAddr,
            }))
          }
        })
        .finally(() =>
          this.setState({
            loading: false,
          }),
        )
    } else {
      this.setState({
        suggestAddr: [],
      })
    }
  }

  filterMethod = (query, option) => {
    return option.label.toLowerCase().includes(query.toLowerCase()) || option.sortCode?.toLowerCase().includes(query.toLowerCase())
  }

  getAddressCheckObj() {
    const { form, addAddressPageData } = this.state

    return addAddressPageData[getMapKey(+form?.provinceId, 'Check')]
  }
  getAddressDarkGrain() {
    const { form, addAddressPageData } = this.state

    return addAddressPageData[getMapKey(+form?.provinceId, 'DarkGrain')]
  }

  updateRules() {
    const { originRules, form } = this.state
    const rules = JSON.parse(JSON.stringify(originRules))
    const checkObj = this.getAddressCheckObj() || {}
    const darkGrain = this.getAddressDarkGrain() || {}
    const rulesMap = new Map(Object.entries(originRules))
    const createValidator = (originValidator, checkConfig, key) => {
      const { provinceId, cityId } = form || {}
      // 根据城市（海外）判断
      if (provinceId === 53283 && cityId && checkConfig?.addressDetailRuleMap?.[cityId]) {
        checkConfig = checkConfig.addressDetailRuleMap[cityId]
      }
      const { limit, rule, message } = checkConfig || {}
      const regexLimit = limit ? new RegExp(`^.{${limit + 1},}$`) : null
      const regex = rule ? new RegExp(rule) : null
      return (_rule, value, callback) => {
        // 先调用原始验证器
        originValidator(_rule, value, (error) => {
          if (error) {
            return callback(error)
          }
          value = trimTxt(value)
          // 执行额外验证
          if (regex && value && !regex.test(value)) {
            callback(new Error(message))
          } else if (regexLimit && regexLimit.test(value)) {
            callback(`${formData[key] || '文本'}过长，请删减，不超过${limit}个字符`)
          } else {
            callback()
          }
        })
      }
    }
    rulesMap.forEach((_value, key) => {
      originRules[key].forEach((m, i) => {
        rules[key][i].required = checkObj?.[key]?.required
        if (checkObj[key] && m.validator) {
          if (key === 'addressDetail' && this.state.form.provinceId == 53283) {
            return (rules[key][i].validator = createValidator(m.validator, checkObj['poiDetailAddress'], key))
          }
          rules[key][i].validator = createValidator(m.validator, checkObj[key], key)
        } else if (!checkObj[key] && m.validator) {
          rules[key][i].validator = m.validator
        } else if (m.message) {
          rules[key][i].message = checkObj?.[key]?.message
        }
        // else if (!checkObj[key] && key === 'phone' && m.validator) {
        //   rules[key][i].validator = createValidator(m.validator, checkObj['mobile'])
        // }
      })
    })
    rules.provinceId = originRules.provinceId
    // console.log(99999999999, checkObj, darkGrain, rules, rulesMap, this.state)
    this.setState({ rules })
  }

  componentDidMount(): void {
    const { commonConfig, type } = this.props
    const { SETTLEMENT_ADDRESS_RULES } = commonConfig || {}
    // apis.addAddressPage({
    //   action: type === 'add' ? '1' : '2',
    //   usePoiAddress: false,
    //   editVersion: 2,
    // })
    this.setState(
      (current) => {
        return {
          addAddressPageData: { ...current.addAddressPageData, ...SETTLEMENT_ADDRESS_RULES },
          foreignPostCode: SETTLEMENT_ADDRESS_RULES?.foreignAddressDarkGrain?.postCode,
        }
      },
      () => {
        this.updateRules()
        this.updatePostDarkGrain()
      },
    )
    // const { addressDetail } = this.state.form
    // addressDetail && this.onSearch(addressDetail)
    apis.getAreaCodeList().then((res) => {
      const areaCodeList = [
        { key: '常用区号', value: res.commonAreaCodeList.map((m) => ({ ...m, sortCode: '常用区号' })) },
        ...res.wordCodeResult,
      ]
      const areaNames = {}
      // 遍历所有分组和地区，构建索引
      areaCodeList.forEach((group) => {
        group.value.forEach((region) => {
          areaNames[region.name] = region
        })
      })
      this.setState({
        areaCodeList,
        areaNames,
      })
    })
  }

  componentDidUpdate(prevProps: Readonly<{}>, prevState: Readonly<{}>, snapshot?: any): void {
    if (this.state.form?.addressDetail !== prevState.form?.addressDetail) {
      // this.state.form?.addressDetail && this.onSearch(this.state.form?.addressDetail)
    }
    if (
      (this.state.form.provinceId && this.state.form.provinceId !== prevState.form.provinceId) ||
      (this.state.form.areaName && this.state.form.areaName !== prevState.form.areaName)
    ) {
      this.updateRules()
    }
  }

  render() {
    const {
      retTag,
      defaultAddress,
      tagSource,
      userDefinedTag,
      provinceId,
      cityId,
      countyId,
      townId = 0,
      addressDetail,
      areaName,
      name,
      mobile,
      phone,
      postCode,
      email,
      text,
    } = this.state.form
    const { type, data, isAddressChunked } = this.props
    const initArea = [provinceId, cityId, countyId, townId].join('-')
    const checkObj = this.getAddressCheckObj() || {}
    const mobileLimit = () => {
      if (provinceId === 53283 && cityId && checkObj?.mobile?.addressDetailRuleMap?.[cityId]?.textLimit) {
        return checkObj.mobile.addressDetailRuleMap[cityId].textLimit
      }
      return checkObj?.mobile?.textLimit
    }
    const darkGrain = this.getAddressDarkGrain() || {}
    // console.log(darkGrain, 'darkGrain')
    return (
      <Form ref={this.formRef} model={this.state.form} rules={this.state.rules} labelWidth="96" className="consignee-item-form">
        {type === 'add' && !isOverSea(provinceId) && (
          <Form.Item label="地址粘贴板" prop="text">
            <Input
              className="clipboard"
              type="textarea"
              rows={4}
              value={text}
              onChange={this.handleTextChange}
              autoComplete="off"
              placeholder="试试粘贴收件人姓名、手机号、收货地址，可快速识别你的收货信息"
            />
          </Form.Item>
        )}
        <Form.Item label="所在地区" prop="provinceId">
          <Area ref={this.areaRef} value={initArea} scopeLevel={4} writeCookie onChange={this.handleAreaChange} />
        </Form.Item>
        <Form.Item label="详细地址" prop="addressDetail">
          {type === 'edit' && isAddressChunked && data.idx === 0 && <GBAreaNestTip />}
          <Select
            ref={this.addressDetailRef}
            className="w-full addressDetail"
            type="textarea"
            autosize={{ maxRows: 2 }}
            value={addressDetail}
            filterable
            reserveKeyword
            remote
            onChange={this.handleAddressDetailChange}
            remoteMethod={this.onSearch}
            autoComplete="off"
            loading={this.state.loading}
            maxLength={checkObj.addressDetail?.textLimit}
            placeholder={
              (isOverSea(provinceId) ? darkGrain.addressDetail : darkGrain.searchAddress) || '请输入道路、小区、单元楼、门牌号等详细信息'
            }
          >
            {this.state.suggestAddr?.map((el) => {
              return (
                <Select.Option key={el.id || el.title} label={el.title} value={el.title}>
                  <div>
                    <div className="is-ellipsis" style={{ maxWidth: 390 }} title={el.title}>
                      {el.title}
                    </div>
                    <div className="is-ellipsis" style={{ fontSize: 12, color: '#888B94', maxWidth: 390 }}>
                      {el.shortAddress || el.title}
                    </div>
                  </div>
                </Select.Option>
              )
            })}
          </Select>
        </Form.Item>
        <Form.Item label="收货人姓名" prop="name">
          <Input
            value={name}
            onChange={this.handleNameChange}
            autoComplete="off"
            maxLength={checkObj.name?.textLimit}
            placeholder={darkGrain.name || '请输入收货人姓名'}
          />
        </Form.Item>
        <Form.Item label="手机号码" required={true}>
          <div className="is-flex">
            <Form.Item prop="areaName" labelWidth="0px">
              <Select
                ref={this.mobileRef}
                className="w-168 mr-8"
                value={areaName}
                filterable
                placeholder="请选择电话区号"
                onChange={this.handleAreaCodeChange}
                onVisibleChange={this.onMobileVisibleChange}
                onMouseDown={() => this.onMouseDown('mobile')}
                filterMethod={this.filterMethod}
                autoComplete="off"
                title={areaName}
              >
                {this.state.areaCodeList.map((group) => {
                  return (
                    <Select.OptionGroup key={group.key} label={group.key}>
                      {group.value.map((el) => {
                        return (
                          <Select.Option
                            key={el.areaCode + el.name}
                            label={el.name.substring(0, 5) + '+' + el.areaCode}
                            sortCode={el.sortCode}
                            value={el.name}
                          >
                            <span className="is-ellipsis" style={{ float: 'left', maxWidth: 96 }} title={el.name}>
                              {el.name}
                            </span>
                            <span style={{ float: 'right' }}>+{el.areaCode}</span>
                          </Select.Option>
                        )
                      })}
                    </Select.OptionGroup>
                  )
                })}
              </Select>
            </Form.Item>
            <Form.Item prop="mobile" labelWidth="0px">
              <Input
                className="w-244"
                value={mobile}
                onChange={this.handleMobileChange}
                autoComplete="off"
                maxLength={mobileLimit()}
                placeholder={darkGrain.mobile || '请输入手机号码'}
              />
            </Form.Item>
          </div>
        </Form.Item>
        <Form.Item label="固定电话">
          <div className="is-flex">
            <Form.Item prop="areaName" labelWidth="0px">
              <Select
                ref={this.phoneRef}
                className="w-168 mr-8"
                value={areaName}
                filterable
                placeholder="请选择电话区号"
                onChange={this.handleAreaCodeChange}
                onVisibleChange={this.onPhoneVisibleChange}
                onMouseDown={() => this.onMouseDown('phone')}
                filterMethod={this.filterMethod}
                autoComplete="off"
                title={areaName}
              >
                {this.state.areaCodeList.map((group) => {
                  return (
                    <Select.OptionGroup key={group.key} label={group.key}>
                      {group.value.map((el) => {
                        return (
                          <Select.Option
                            key={el.areaCode + el.name}
                            label={el.name.substring(0, 5) + '+' + el.areaCode}
                            sortCode={el.sortCode}
                            value={el.name}
                          >
                            <span className="is-ellipsis" style={{ float: 'left', maxWidth: 96 }} title={el.name}>
                              {el.name}
                            </span>
                            <span style={{ float: 'right' }}>+{el.areaCode}</span>
                          </Select.Option>
                        )
                      })}
                    </Select.OptionGroup>
                  )
                })}
              </Select>
            </Form.Item>
            <Form.Item prop="phone" labelWidth="0px">
              <Input
                className="w-244"
                value={phone}
                onChange={this.handlePhoneChange}
                autoComplete="off"
                maxLength={checkObj.phone?.textLimit}
                placeholder={darkGrain.phone || '请输入固定电话'}
              />
            </Form.Item>
          </div>
        </Form.Item>
        {isOverSea(provinceId) && (
          <Form.Item label="邮政编码" prop="postCode">
            <Input
              value={postCode}
              onChange={this.handlePostCodeChange}
              autoComplete="off"
              maxLength={checkObj.postCode?.textLimit}
              placeholder={darkGrain.postCode}
            />
          </Form.Item>
        )}
        <Form.Item label="邮箱地址" prop="email">
          <Input
            value={email}
            onChange={this.handleEmailChange}
            autoComplete="off"
            maxLength={checkObj.email?.textLimit}
            placeholder={darkGrain.email || '选填'}
          />
        </Form.Item>
        <Form.Item label="添加标签" prop="tag" className="a-left mb-12">
          <JDTag value={{ retTag, defaultAddress, tagSource, userDefinedTag }} onChange={this.handleTagChange} />
        </Form.Item>
      </Form>
    )
  }
}
