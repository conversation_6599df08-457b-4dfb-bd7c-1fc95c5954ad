/** 
 * @file: const.ts
 * @description: 网络请求层相关的常量声明
 */
import queryString from 'query-string'
import { getUrlFilename } from '@app/helpers'

const PROTOCOL = location.protocol === 'https:' ? 'https:' : 'http:'

/** color网关域名 */
const _COLOR_API_ORIGIN = (() => {
  const ECFlag = (/[?&](ECFlag)=([^&#]*)/.exec(location.href) ?? [])[2] as `${number}` | undefined
  const origins = ['//api.m.jd.com/', '//api.jdtest.net/', '//beta-api.m.jd.com/'] as const
  return origins[ECFlag!] ?? origins[0]
})()

export const COLOR_API_ORIGIN = (PROTOCOL + _COLOR_API_ORIGIN) as `${typeof PROTOCOL}${typeof _COLOR_API_ORIGIN}`

export const GATEWAY = window.location.href.includes('ECFlag=')
  ? COLOR_API_ORIGIN
  : window.pageConfig?.product?.colorApiDomain
    ? window.pageConfig?.product?.colorApiDomain
    : '//api.m.jd.com/'

/** 当前页面参数 */
export const PAGE_QUERY_PARAMS = queryString.parse(location.search)

/** 当前页面文件名 */
export const PAGE_FILE_NAME = getUrlFilename(location.href, true)

/** 购物车地址 */
export const CART_URL = '//cart.jd.com/cart_index'

/** 订单中心地址 */
export const ORDER_CENTER_URL = '//order.jd.com/center/list.action'

/** 订单提交成功页面地址 */
export const SUCCESS_PAGE_URL = '//success.jd.com/success/success.action'

/** 实名认证页面地址 */
export const AUTHAUTH_PAGE_URL = '//authpay.jd.com/auth/toAuthPage?source=203'

/** 登陆页面 */
export const LOGIN_URL = `//passport.jd.com/new/login.aspx?ReturnUrl=${encodeURIComponent(window.location.href)}`

/** 港澳台、海外地区ID，对应provinceId */
export const TW_HK_OVERSEA = new Map([
  ['hongKongId', 52993],
  ['taiWanId', 32],
  ['overSeasId', 53283],
])

export const TW_HK_OVERSEA_ID = Array.from(TW_HK_OVERSEA.values())

/** 含有公共参数的接口 */
export const HAS_COMMON_PARAMS_APIS = ['balance_selectPayment_pc', 'balance_getBundleShipmentList_pc', 'balance_getPageCouponList_pc']

/** 页面来源 */
export const SOURCE = (() => {
  const referrer = document.referrer ?? ''
  if (/https?:\/\/item.jd.com/i.test(referrer)) {
    return 'item'
  }
  if (/https?:\/\/cart.jd.com/i.test(referrer)) {
    return 'cart'
  }
  return referrer
})()

/** 公共配置文案类型 */
export const COMMON_CONFIG_TYPE = ['SETTLEMENT_ADDRESS_RULES ', 'TAX_AND_FEE_BREAKDOWN_RULES']
