.el-tag {
  background-color: #F7F8FC;
  padding: 0 14px;
  height: 36px;
  line-height: 34px;
  font-size: 14px;
  color: #1A1A1A;
  border-radius: 4px;
  box-sizing: border-box;
  border: 0.5px solid rgba(0, 0, 0, 0.06);
  display: inline-block;
  text-align: center;
  + .el-tag {
    margin-left: 10px;
  }
  margin-bottom: 10px;
  &:hover {
    border-color: #FF0F23;
  }
}

.el-tag .el-icon-close {
  display: inline-block;
  border-radius: 50%;
  text-align: center;
  position: relative;
  cursor: pointer;
  transform: scale(.75,.75);
  height: 18px;
  width: 18px;
  line-height: 18px;
  vertical-align: middle;
  top: -1px;
  right: -2px
}

.el-tag .el-icon-close:hover {
  background-color: #fff;
  color: #8391a5
}

.el-tag--gray {
  background-color: #e4e8f1;
  border-color: #e4e8f1;
  color: #48576a
}

.el-tag--gray .el-tag__close:hover {
  background-color: #48576a;
  color: #fff
}

.el-tag--gray.is-hit {
  border-color: #48576a
}

.el-tag--primary {
  background-color: rgba(32,160,255,.1);
  border-color: rgba(32,160,255,.2);
  color: #20a0ff
}

.el-tag--primary .el-tag__close:hover {
  background-color: #20a0ff;
  color: #fff
}

.el-tag--primary.is-hit {
  border-color: #20a0ff
}

.el-tag--success {
  background-color: rgba(18,206,102,.1);
  border-color: rgba(18,206,102,.2);
  color: #13ce66
}

.el-tag--success .el-tag__close:hover {
  background-color: #13ce66;
  color: #fff
}

.el-tag--success.is-hit {
  border-color: #13ce66
}

.el-tag--warning {
  background-color: rgba(247,186,41,.1);
  border-color: rgba(247,186,41,.2);
  color: #f7ba2a
}

.el-tag--warning .el-tag__close:hover {
  background-color: #f7ba2a;
  color: #fff
}

.el-tag--warning.is-hit {
  border-color: #f7ba2a
}

.el-tag--danger {
  background-color: rgba(255,73,73,.1);
  border-color: rgba(255,73,73,.2);
  color: #ff4949
}

.el-tag--danger .el-tag__close:hover {
  background-color: #ff4949;
  color: #fff
}

.el-tag--danger.is-hit {
  border-color: #ff4949
}