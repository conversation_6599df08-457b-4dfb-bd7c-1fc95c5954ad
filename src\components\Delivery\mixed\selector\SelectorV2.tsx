/**
 * @file: SelectorV2.tsx
 * @description: 配送楼层主组件，用于展示配送楼层等逻辑
 */
import styles from '../index.module.scss'
import { selectDataProcess } from './initSelectData'
import { PromptText } from '../../ui/Tips/tips'
import { useDelivery } from '../../context'
import InstallModule from '../main/InstallModule'
import DingQiGouModule from '../main/DingQiGouModule'
import InputModule from '../main/InputModule'
import { useMemo } from 'react'

export default function SelectorV2({ input }: { input: boolean }) {
  const deliveryState = useDelivery()
  const bundle = deliveryState.state.wrapInitState.bundle
  // const [theTipText, setTipText] = useState('')

  const { installItem, inputItem, dingQiGouItem, tipText } = useMemo(() => selectDataProcess(bundle), [bundle])
  const isEmpty = !installItem && !(inputItem && input) && !dingQiGouItem

  return (
    !isEmpty && (
      <div style={{ marginLeft: '16px', marginBottom: '16px' }}>
        {tipText && (
          <div>
            <PromptText text={tipText} />
          </div>
        )}
        <div style={{ marginTop: '12px' }} className={styles.selector_wrap}>
          {/* <DeliveryModule /> */}
          <InstallModule />
          {/* <DingQiGouModule /> */}
          {input && <InputModule />}
        </div>
      </div>
    )
  )
}
