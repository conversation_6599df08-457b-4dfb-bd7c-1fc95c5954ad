/**
 * @file: SelectItemV2.tsx
 * @description: 配送楼层显示子组件，用于展示配送楼层等逻辑
 */
import React, { useRef, useEffect, useState } from 'react'
// import {useState, useEffect,} from "react";
import styles from '../index.module.scss'
import Item from '../../ui/Selector/Item'
import { UiSelector } from '../../ui/Selector/types'

export default ({
  children,
  canOpen,
  items,
  isOpen,
  onClick,
  noBorder,
  defalutAlignRight,
}: {
  children: React.ReactNode
  canOpen: boolean
  items: UiSelector.TextItem[]
  isOpen: boolean
  targeValue?: string
  onClick: () => void
  noBorder?: boolean
  defalutAlignRight?: boolean
}) => {
  const popupRef = useRef<HTMLDivElement>(null)
  const [alignRight, setAlignRight] = useState(defalutAlignRight || false)

  useEffect(() => {
    if (isOpen && popupRef.current) {
      const rect = popupRef.current.getBoundingClientRect()
      if (rect.right >= window.innerWidth || defalutAlignRight) {
        setAlignRight(true)
      } else {
        setAlignRight(false)
      }
    } else {
      setAlignRight(false)
    }
  }, [isOpen, defalutAlignRight])

  return (
    <>
      <div className={styles.selector} style={{ maxWidth: '100%' }}>
        <Item noBorder={!!noBorder} canOpen={canOpen} isOpen={isOpen} items={items} onClick={onClick} />
        {canOpen && (
          <div
            ref={popupRef}
            className={''}
            style={{
              zIndex: 99,
              position: 'absolute',
              top: '40px',
              display: isOpen ? 'block' : 'none',
              left: alignRight ? undefined : 0,
              right: alignRight ? 0 : undefined,
            }}
          >
            <div className={styles.body_warp} style={{}}>
              {children}
            </div>
          </div>
        )}
      </div>
    </>
  )
}
