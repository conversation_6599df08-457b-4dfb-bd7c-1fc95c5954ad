.container {
  box-sizing: border-box;
  display: inline-block;
  min-width: 340px;
  max-width: 496px;
  padding: 24px;
  background-color: #fff;
  border-radius: 12px;
  text-align: left;
  transform: translateY(-100px);
}

.header {
  display: flex;
  align-items: center;
  color: #1a1a1a;
  font-size: 18px;
  line-height: 1.1;

  i {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-image: url("//img13.360buyimg.com/ling/jfs/t1/280203/14/16455/1306/67f3799fF560685be/ce58b4c487168393.png");
    background-size: 24px 24px;
    background-repeat: no-repeat;
    margin-right: 12px;
    flex: none;
  }
}

.desc,
.tips {
  padding-left: 36px;
  margin-top: 10px;
  font-size: 14px;
  line-height: 20px;
}

.tips {
  background: rgba(255, 247, 217, 1);
  color: rgba(212, 110, 8, 1);
}

.body {
  padding: 12px 0;
  max-height: 310px;
  overflow: auto;

  &:empty {
    padding: 0;
  }

  .content {
    font-size: 28px;
    color: rgba(26, 26, 26, 1);
    margin: -12px 0;
  }

  .notice {
    font-size: 24px;
    color: rgba(250, 44, 25, 1);
    margin-top: 10px;
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .alert {
    background: rgba(251, 248, 217, 1);
    border-radius: 4px;
    font-size: 16px;
    line-height: 1.1;
    color: rgba(222, 106, 28, 1);
    padding: 11px 24px;
  }

  .row {
    display: flex;
    padding: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    border-radius: 8px;
    background: #F7F8FC;

    .pic {
      overflow: hidden;
      position: relative;
      width: 80px;
      height: 80px;
      border-radius: 4px;
      background: url("https://storage.360buyimg.com/static-common/daojia/list-default-img.png") center / contain no-repeat;
      margin-right: 12px;

      .image {
        width: 80px;
        height: 80px;
        border-radius: 4px;
      }

      .mask {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.02);
        border-radius: 8px;

        &::after {
          content: "无货";
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          left: 50%;
          top: 50%;
          width: 72px;
          height: 72px;
          transform: translate(-50%, -50%);
          background: rgba(0, 0, 0, 0.5);
          border-radius: 36px;
          font-size: 20px;
          color: rgba(255, 255, 255, 1);
        }
      }

      .mark,
      .light {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 32px;
        height: 32px;
        background: rgba(255, 0, 0, 0.7);
        font-size: 20px;
        color: rgba(255, 255, 255, 1);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .mark {
        background: rgba(0, 0, 0, 0.7);
      }
    }
  }

  // 服务楼层
  .service_row {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    font-size: 16px;

    .service_name {
      width: 330px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    .service_num {
      flex: 1;
      text-align: right;
    }
  }

  .info {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    padding: 4px 0;

    &>div {
      width: 100%;
    }

    .name {
      color: #1A1A1A;
      font-size: 16px;
      line-height: 1.1;
    }

    .block {
      color: #C47600;
      font-size: 14px;
      line-height: 1;
    }

    .line {
      display: flex;
      align-items: center;
      line-height: 1;
      font-family: JDZhengHeiVRegular2-1;

    }

    .count {
      margin-left: auto;
      color: #888B94;
      font-size: 14px;
    }

    .jdPrice {
      color: #FF0F23;
      font-size: 16px;
      font-weight: Bold;
    }

    .price {
      margin-left: 4px;
      color: #888B94;
      font-size: 14px;
      text-decoration: line-through;
    }

    .jump {
      display: flex;
      align-items: center;
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      margin-top: 12px;
      color: rgba(242, 39, 12, 1);

      &::after {
        content: "";
        width: 18px;
        height: 20px;
        margin-left: 4px;
        background: url("https://img20.360buyimg.com/img/jfs/t1/95843/29/29820/155/64ef0be2Fc1573480/bd60c7b6ef0e5b5e.png") center / contain no-repeat;
      }
    }

    .reason {
      display: flex;
      font-size: 24px;
      margin-top: 12px;

      .left {
        white-space: nowrap;
        color: rgba(140, 140, 140, 1);
      }

      .right {
        margin-left: 20px;
        flex: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        color: rgba(242, 39, 12, 1);
      }
    }
  }

  .total {
    margin-left: 24px;
    font-size: 28px;
    color: rgba(26, 26, 26, 1);
    white-space: nowrap;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;

  button+button {
    margin-left: 8px;
  }
}