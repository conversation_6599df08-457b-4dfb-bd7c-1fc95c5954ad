/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-02 18:13:21
 * @LastEditors: ext.wangchao120
 * @Description: 领货码-不可用列表
 * @FilePath: /pc_settlement/src/components/VirtualAsset/DeliveryCode/NotList/index.tsx
 */
import React, { useEffect, useRef } from 'react'
import styles from './index.module.scss'
import NoData from '../../components/NoData'
import useGift from '../../hooks/useGift'
import { _batchExposeDom, domMap } from '../../batchExpose'
import { ExposeBuriedPoints } from '@app/utils/dataSet'
import { GIFT_CODE_TABS } from '../../constants'
import Card from '../Card'

interface Props {
  tabKey?: string
  activeKey?: string
}
const NotList: React.FC<Props> = ({ tabKey, activeKey }) => {
  const { notGiftCardList, getGiftCardList } = useGift()
  const observer = useRef<ExposeBuriedPoints | null>(null)
  useEffect(() => {
    domMap.clear()
    if (tabKey === 'deliveryCode' && activeKey === 'unavailable') {
      getGiftCardList('accesskey', 'unavailable')
    }
  }, [tabKey, activeKey])

  // 批量曝光
  const loadingDom = (id: string) => {
    domMap.set(id, id)
    // 当tabkey为领货码且是不可用列表，domMap与数据长度相等，上报曝光
    if (tabKey === 'deliveryCode' && activeKey === 'unavailable' && domMap.size === notGiftCardList.length) {
      observer.current?.destory()
      observer.current = _batchExposeDom('virtualasset_RedeemcodeEXPO', 'unredeemcode', 'getstock_id', {
        second_tab_name: GIFT_CODE_TABS,
      })
    }
  }
  return (
    <>
      <div className={styles.list}>
        <NoData arr={notGiftCardList}>
          <div className={styles.cardWarp}>
            {notGiftCardList &&
              notGiftCardList.map((item: any) => {
                return <Card key={item.id} item={item} loadingDom={loadingDom} tabKey={tabKey} activeKey={activeKey} disabled />
              })}
          </div>
        </NoData>
      </div>
    </>
  )
}
export default NotList
