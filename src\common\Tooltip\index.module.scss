.custom {
  display: inline-block;
  box-sizing: border-box;
  position: relative;
}

.customCon {
  --translate-x: -50%;
  --translate-y: 0;
  --rotate: 0;
  position: absolute;
  z-index: 1000;
  display: inline-block;
  box-sizing: border-box;
  animation: slideIn 0.2s ease-out forwards;
}

.customConInner {
  background: #fff;
  border-radius: 4px;
  box-sizing: border-box;
  word-break: break-all;
}

.tooltipConArrow {
  box-sizing: border-box;
  position: absolute;
  left: 50%;
  transform: translateX(var(--translate-x)) translateY(var(--translate-y)) rotate(var(--rotate)) translateZ(0);
  width: 10px;
  height: 10px;

  &::before {
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    width: 10px;
    height: 5px;
    clip-path: polygon(0 0, 100% 0, 50% 100%);
    content: "";
  }
}

.tooltipConArrowBottom {
  bottom: 0;
  --translate-y: calc(100%);
  --rotate: 0;
}

.tooltipConArrowTop {
  top: 0;
  --rotate: 180deg;
  --translate-y: calc(-100%);

}


.tooltip {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 400;
  white-space: pre-line;
}


.popover {
  height: 100%;
}

.tooltipCon {
  .customConInner {
    border-radius: 8px;
  }
  
  .tooltipConArrow {
    &::before {
      background: rgba(0, 0, 0, 0.75);
    }
  }

}

.popoverCon {
  .customConInner {
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 8px;
  }
  .tooltipConArrow {
    &::before {
      background: #fff;
    }

  }
}

@keyframes slideIn {
  0% {
    transform: translateY(-2px);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}