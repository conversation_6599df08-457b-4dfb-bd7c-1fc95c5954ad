import { SaveInvoiceForm, InvoiceEditParam, UsualInvoiceItem, InvoiceEditVO, InvoiceType, InvoiceTitle } from '@app/typings/invoice.d'

export const invoiceDataService = {
  /**
   * @description 收集发票数据
   * @param {Object} state - 纯自营或纯pop状态对象
   * @returns {InvoiceEditParam} 格式化后的发票参数
   */
  collectInvoiceData(state: any, venderType: string): InvoiceEditParam {
    // 获取发票类型和状态信息
    const selectedInvoiceType = state?.invoiceTypes?.find((item: any) => item.selected)?.value
    const electroInvoice = state?.electroInvoice || {}
    const normalInvoice = state?.normalInvoice || {}
    const vatInvoice = state?.vat || {}
    const invoiceConsigneeEditVO = state?.invoiceConsigneeEditVO || {}

    // 创建发票参数基础对象
    const params: InvoiceEditParam = {
      //必填  发票类型 1，纸质发票，2、专用发票，3，电子普通发票 22、电子增值发票
      selectedInvoiceType: '',
      //条件必填 普票抬头：单位名称
      norCompanyName: '',
      //必填 普票抬头：个人名称 默认给个人
      norPersonalName: '',
      //非必填 纳税人标识号(普票)
      norCode: '',
      //非必填 注册地址（普票）
      norRegAddr: '',
      //非必填 注册电话（普票）
      norRegPhone: '',
      //非必填 注册银行（普票）
      norRegBank: '',
      //非必填 注册银行帐号（普票）
      norRegBankAccount: '',
      //条件必填 电子发票名称单位
      electroCompanyName: '',
      //必填|| 默认值个人|| 电子发票名称个人
      electroPersonalName: '',
      //非必填  纳税人标识号(电票)
      eleCode: '',
      //非必填  注册地址(电票)
      eleRegAddr: '',
      //非必填  注册电话(电票)
      eleRegPhone: '',
      //非必填  注册银行(电票)
      eleRegBank: '',
      //非必填  注册银行帐号(电票)
      eleRegBankAccount: '',
      //必填 增值发票单位名称
      vatCompanyName: '',
      //必填 纳税人标识号(增票)
      vatCode: '',
      //必填 注册地址(增票)
      vatRegAddr: '',
      //必填 注册电话(增票)
      vatRegPhone: '',
      //必填 注册银行(增票)
      vatRegBank: '',
      //必填 注册银行帐号(增票)
      vatRegBankAccount: '',
      //非必填 电子增票邮箱（选填）
      vatEmail: '',
      vatEncryptEmail: vatInvoice.encryptEmail || '',
      // 开票方式(随货开票、邮寄到其它地址、集中开票) TODO 待确认字段值
      invoicePutType: state?.invoicePutType,
      //必填 用户选择的发票类型：4个人，5单位
      selectInvoiceTitle: '',
      // 用户选择的图书发票内容
      selectBookInvoiceContent: '',
      // 用户选择的普通商品的发票内容
      selectNormalInvoiceContent: '',
      // 是否含有普通商品
      hasCommon: state?.hasCommon,
      // 是否含有图书
      hasBook: state?.hasBook,
      //必填 收票人的姓名
      consigneeName: '',
      //必填 联系人电话
      consigneePhone: '',
      //必填 联系人详细地址
      consigneeAddress: '',
      //必填 联系人所在省份
      consigneeProvince: '',
      //必填 联系人所在省份ID
      consigneeProvinceId: 0,
      //必填 联系人所在城市
      consigneeCity: '',
      //必填 联系人所在城市ID
      consigneeCityId: 0,
      //必填 联系人所在县(3级地址)
      consigneeCounty: '',
      //必填 联系人所在县(3级地址)ID
      consigneeCountyId: 0,
      //必填 联系人所在镇（四级地址）
      consigneeTown: '',
      //必填 联系人所在镇（四级地址）ID
      consigneeTownId: 0,
      //条件必填 是否单独寄送发票
      sendSeparate: state?.sendSeparate || false,
      // 电子发票抬头 4个人，5单位
      selectElectroTitle: state?.selectElectroTitle || 0,
      // 电子发票邮箱
      electroInvoiceEmail: '',
      electroEncryptEmail: electroInvoice.encryptEmail || '',
      //必填 电子发票手机 （收货人手机）
      electroInvoicePhone: '',
      // 普通发票给1 其他发票给0
      saveInvoiceFlag: state?.saveInvoiceFlag,
      // 票分离开关值0:全部关闭，1:礼品购流程开启，2:全部开启
      invoiceSeparateSwitch: state?.invoiceSeparateSwitch,
      // //后端值:Self ,"Pop"
      venderType: venderType,
      // 单位发票列表
      usualInvoiceList: [] as UsualInvoiceItem[],
      // 个人发票列表
      personalUsualInvoiceList: [] as UsualInvoiceItem[],
      // 常用发票ID
      usualInvoiceId: (state?.usualInvoiceList && state?.usualInvoiceList?.find((item: any) => item.selected)?.id) || '',
      // 0、自营，1、pop,2、pop自营混单
      selectedInvoiceStatus: state?.selectedInvoiceStatus,
      shoppingUuidList: state?.shoppingUuidList,
      // 是否默认
      defaultFlag: state?.defaultFlag,
      invokeInvoiceBasicService: state?.invokeInvoiceBasicService,
      type: 1,
      showInvoiceSeparate: state?.showInvoiceSeparate,
      hasHaoKa: state?.hasHaoKa,
      hasHyj: state?.hasHyj,
    }
    // 设置分类属性
    if (selectedInvoiceType === InvoiceType.NORMAL) {
      // 普通发票参数
      params.selectedInvoiceType = String(selectedInvoiceType)
      params.selectInvoiceTitle = String(normalInvoice.invoiceTitles.find((item: any) => item.selected)?.value)
      if (normalInvoice.hasBook) {
        params.selectBookInvoiceContent = normalInvoice.bookInvoiceContents.find((item: any) => item.selected)?.value || ''
      } else {
        params.selectNormalInvoiceContent = normalInvoice.normalInvoiceContents.find((item: any) => item.selected)?.value || ''
      }
      params.invoiceCode = normalInvoice.invoiceCode || ''
      params.defaultFlag = normalInvoice.defaultFlag || false
      if (normalInvoice?.invoiceTitles?.find((item: any) => item.selected)?.value === InvoiceTitle.COMPANY) {
        params.norCompanyName = normalInvoice.companyName || ''
        params.norCode = normalInvoice.invoiceCode || ''
        params.norRegAddr = normalInvoice.regAddress || ''
        params.norRegPhone = normalInvoice.regTel || ''
        params.norRegBank = normalInvoice.regBank || ''
        params.norRegBankAccount = normalInvoice.regAccount || ''
      } else {
        params.norPersonalName = normalInvoice.personalName || '个人'
      }
    } else if (selectedInvoiceType === InvoiceType.VAT || selectedInvoiceType === InvoiceType.E_VAT) {
      // 增值税发票参数
      params.selectedInvoiceType = String(selectedInvoiceType)
      if (vatInvoice.hasBook) {
        params.selectBookInvoiceContent = vatInvoice.bookInvoiceContents.find((item: any) => item.selected)?.value || ''
      } else {
        params.selectNormalInvoiceContent = vatInvoice.normalInvoiceContents.find((item: any) => item.selected)?.value || ''
      }
      params.vatCompanyName = vatInvoice.companyName || ''
      params.vatCode = vatInvoice.code || ''
      params.vatRegAddr = vatInvoice.regAddr || ''
      params.vatRegPhone = vatInvoice.regPhone || ''
      params.vatRegBank = vatInvoice.regBank || ''
      params.vatRegBankAccount = vatInvoice.regBankAccount || ''
      params.vatEmail = vatInvoice.email || ''
      params.invoiceCode = vatInvoice.code || ''
      params.defaultFlag = vatInvoice.defaultFlag || false
      params.vatEncryptEmail = vatInvoice.encryptEmail || ''
      params.invoicePutType = vatInvoice?.invoicePutTypes?.find((item: any) => item.selected)?.value || 0
      params.consigneeName = vatInvoice.invoiceConsigneeEditVO?.consigneeName || ''
      params.consigneePhone = vatInvoice.invoiceConsigneeEditVO?.phone || ''
      params.consigneeAddress = vatInvoice.invoiceConsigneeEditVO?.address || ''
      params.consigneeProvince = vatInvoice.invoiceConsigneeEditVO?.province || ''
      params.consigneeProvinceId = vatInvoice.invoiceConsigneeEditVO?.provinceId || 0
      params.consigneeCity = vatInvoice.invoiceConsigneeEditVO?.city || ''
      params.consigneeCityId = vatInvoice.invoiceConsigneeEditVO?.cityId || 0
      params.consigneeCounty = vatInvoice.invoiceConsigneeEditVO?.county || ''
      params.consigneeCountyId = vatInvoice.invoiceConsigneeEditVO?.countyId || 0
      params.consigneeTown = vatInvoice.invoiceConsigneeEditVO?.town || ''
      params.consigneeTownId = vatInvoice.invoiceConsigneeEditVO?.townId || 0
    } else if (selectedInvoiceType === InvoiceType.ELECTRONIC) {
      // 电子发票参数
      params.selectedInvoiceType = String(selectedInvoiceType)
      params.selectInvoiceTitle = String(electroInvoice.invoiceTitles.find((item: any) => item.selected)?.value)
      if (electroInvoice.hasBook) {
        params.selectBookInvoiceContent = electroInvoice.bookInvoiceContents.find((item: any) => item.selected)?.value || ''
      } else {
        params.selectNormalInvoiceContent = electroInvoice.normalInvoiceContents.find((item: any) => item.selected)?.value || ''
      }
      params.invoiceCode = electroInvoice.invoiceCode || ''
      params.electroInvoiceEmail = electroInvoice.email || ''
      params.electroEncryptEmail = electroInvoice.encryptEmail || ''
      params.electroInvoicePhone = electroInvoice.phone || ''
      params.defaultFlag = electroInvoice.defaultFlag || false
      params.invoicePutType = electroInvoice?.invoicePutTypes?.find((item: any) => item.selected)?.value || 0

      if (electroInvoice.invoiceTitles.find((item: any) => item.selected)?.value === InvoiceTitle.COMPANY) {
        params.electroCompanyName = electroInvoice.electroCompanyName || ''
        params.eleCode = electroInvoice.invoiceCode || ''
        params.eleRegAddr = electroInvoice.regAddress || ''
        params.eleRegPhone = electroInvoice.regTel || ''
        params.eleRegBank = electroInvoice.regBank || ''
        params.eleRegBankAccount = electroInvoice.regAccount || ''
      } else {
        params.electroPersonalName = electroInvoice.personalName || '个人'
      }
    }

    // 设置收票人信息（如果需要单独邮寄）
    if (params.sendSeparate) {
      params.consigneeName = invoiceConsigneeEditVO.consigneeName || ''
      params.consigneePhone = invoiceConsigneeEditVO.phone || ''
      params.consigneeAddress = invoiceConsigneeEditVO.address || ''
      params.consigneeProvince = invoiceConsigneeEditVO.province || ''
      params.consigneeProvinceId = invoiceConsigneeEditVO.provinceId || 0
      params.consigneeCity = invoiceConsigneeEditVO.city || ''
      params.consigneeCityId = invoiceConsigneeEditVO.cityId || 0
      params.consigneeCounty = invoiceConsigneeEditVO.county || ''
      params.consigneeCountyId = invoiceConsigneeEditVO.countyId || 0
      params.consigneeTown = invoiceConsigneeEditVO.town || ''
      params.consigneeTownId = invoiceConsigneeEditVO.townId || 0
    }

    // 常用发票ID
    params.usualInvoiceId = (state?.usualInvoiceList && state?.usualInvoiceList?.find((item: any) => item.selected)?.id) || ''
    params.usualInvoiceList = state?.usualInvoiceList || []
    params.personalUsualInvoiceList = state?.personalUsualInvoiceList || []
    // 添加发票代码
    if (state?.invoiceCode) {
      params.invoiceCode = state?.invoiceCode
    }

    // 添加保存标记
    if (state?.saveInvoiceFlag) {
      params.saveInvoiceFlag = state?.saveInvoiceFlag
    }

    // 添加商品UUID列表
    params.groupShoppingList = state?.groupShoppingList || []
    return params
  },

  /**
   * @description 创建保存发票请求的参数
   * @param {Object} state - 纯自营或纯pop
   * @param {Object} invoiceEditVo - 发票编辑对象
   * @returns {SaveInvoiceForm} 格式化后的发票请求参数
   */
  createInvoiceParams(state: any, venderType: string, invoiceEditVo?: InvoiceEditVO): any {
    const InvoiceParam = this.collectInvoiceData(state, venderType)

    // 基础参数
    const params: SaveInvoiceForm = {
      selectedInvoiceType: InvoiceParam.selectedInvoiceType || '',
      invoicePutType: InvoiceParam.invoicePutType,
      selectInvoiceTitle: String(InvoiceParam.selectInvoiceTitle),
      selectBookInvoiceContent: InvoiceParam.selectBookInvoiceContent || '',
      selectNormalInvoiceContent: InvoiceParam.selectNormalInvoiceContent || '',
      hasCommon: invoiceEditVo?.hasCommon || false,
      hasBook: invoiceEditVo?.hasBook || false,
      sendSeparate: InvoiceParam.sendSeparate,
      selectElectroTitle: InvoiceParam.selectElectroTitle,
      saveInvoiceFlag: InvoiceParam.saveInvoiceFlag,
      showInvoiceSeparate: InvoiceParam.showInvoiceSeparate,
      invoiceSeparateSwitch: InvoiceParam.invoiceSeparateSwitch,
      defaultFlag: InvoiceParam.defaultFlag || false,
      selectedInvoiceStatus: invoiceEditVo?.selectedInvoiceStatus || 0,
      invoiceEditParamList: [InvoiceParam],
      hasHaoKa: InvoiceParam.hasHaoKa,
      hasHyj: InvoiceParam.hasHyj,
    }

    return params
  },

  /**
   * @description 创建保存发票请求的参数 - 混合商家（自营+POP）
   * @param {Object} selfState - 自营商家状态对象
   * @param {Object[]} popStates - 非自营商家状态对象列表
   * @param {Object} invoiceEditVo - 发票编辑对象
   * @returns {SaveInvoiceForm} 格式化后的发票请求参数
   */
  createMixedInvoiceParams(selfState: any, popStates: any, invoiceEditVo?: InvoiceEditVO): SaveInvoiceForm {
    // 创建最终要发送给后端的SaveInvoiceForm对象
    const selfInvoiceParam = this.collectInvoiceData(selfState, 'Self')
    const popInvoiceParams = this.collectInvoiceData(popStates, 'POP')
    const params: SaveInvoiceForm = {
      selectedInvoiceType: selfInvoiceParam.selectedInvoiceType || '',
      invoicePutType: selfInvoiceParam.invoicePutType,
      selectInvoiceTitle: String(selfInvoiceParam.selectInvoiceTitle),
      selectBookInvoiceContent: selfInvoiceParam.selectBookInvoiceContent || '',
      selectNormalInvoiceContent: selfInvoiceParam.selectNormalInvoiceContent || '',
      hasCommon: invoiceEditVo?.hasCommon || false,
      hasBook: invoiceEditVo?.hasBook || false,
      sendSeparate: selfInvoiceParam.sendSeparate,
      selectElectroTitle: selfInvoiceParam.selectElectroTitle,
      saveInvoiceFlag: selfInvoiceParam.saveInvoiceFlag,
      showInvoiceSeparate: selfInvoiceParam.showInvoiceSeparate,
      invoiceSeparateSwitch: selfInvoiceParam.invoiceSeparateSwitch,
      defaultFlag: selfInvoiceParam.defaultFlag || false,
      selectedInvoiceStatus: invoiceEditVo?.selectedInvoiceStatus || 0,
      invoiceEditParamList: [selfInvoiceParam, popInvoiceParams],
      hasHaoKa: selfInvoiceParam.hasHaoKa,
      hasHyj: selfInvoiceParam.hasHyj,
    }
    return params
  },

  /**
   * @description 创建保存发票请求的参数 - 智能判断场景
   * @param {Object} selfState - 自营商家状态对象 (可选)
   * @param {Object[]} popStates - 非自营商家状态对象列表 (可选)
   * @param {Object} invoiceEditVo - 发票编辑对象
   * @returns {SaveInvoiceForm} 格式化后的发票请求参数
   */
  createSaveInvoiceParams(selfState?: any, popStates?: any, invoiceEditVo?: InvoiceEditVO): SaveInvoiceForm {
    // 根据传入参数判断场景类型
    if (selfState && popStates) {
      // 混合场景：自营+POP
      return this.createMixedInvoiceParams(selfState, popStates, invoiceEditVo)
    } else if (selfState) {
      // 仅自营场景
      return this.createInvoiceParams(selfState, 'Self', invoiceEditVo)
    } else {
      // 仅POP场景
      return this.createInvoiceParams(popStates, 'POP', invoiceEditVo)
    }
  },
}
