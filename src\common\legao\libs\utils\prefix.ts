import classNames from 'classnames'

export default function prefix(pre: string, className?: string | string[]): string {
  if (!pre || !className) {
    return ''
  }

  if (typeof className === 'string') {
    return `${pre}-${className}`
  }
  return classNames(className.filter((name) => !!name).map((name) => `${pre}-${name}`))
}

export const addPrefix = (name: string | string[], prefixCls: string) => {
  return prefix(prefixCls, name)
}
