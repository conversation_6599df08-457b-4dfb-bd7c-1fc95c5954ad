import { DeleteInvoiceFromUsualForm, InvoiceResult, UsualInvoiceItem } from '@app/typings/invoice'
import { deleteInvoiceFromUsual as deleteInvoiceFromUsualApi } from '@app/services/api'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'

interface UseDeleteInvoiceFromUsualParams {
  updateInvoiceList: (itemId: number) => void
}

/**
 * 删除历史发票抬头的Hook
 */
export const useDeleteInvoiceFromUsual = ({ updateInvoiceList }: UseDeleteInvoiceFromUsualParams) => {
  return async (item: UsualInvoiceItem) => {
    // 调用删除API
    const deleteParams: DeleteInvoiceFromUsualForm = {
      usualInvoiceId: item.id,
    }
    // console.log('item', item)
    // 记录返回数据
    let responseData = null
    try {
      // 0 后端自动生成的id
      if (!item.id && item.id !== 0) {
        return {
          success: false,
          message: '发票ID不存在，无法删除',
        }
      }

      const response = (await deleteInvoiceFromUsualApi(deleteParams)) as InvoiceResult<boolean>
      responseData = response
      // 检查API调用是否成功
      if (response && response.code === '0') {
        // 更新发票列表，移除该发票
        updateInvoiceList(item.id)

        return {
          success: true,
        }
      } else {
        const errorMsg = response?.message
        const errorCode = response?.code
        console.error('删除常用发票失败:', response)
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.Invoice,
          msg: {
            type: '发票',
            action: '删除常用发票',
            error: errorMsg,
            code: errorCode,
            traceId: response?.traceId,
            requestId: response?.requestId,
          },
        })
        return {
          success: false,
          message: errorMsg,
        }
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      console.error('删除常用发票异常:', error)
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.Invoice,
        msg: {
          type: '发票',
          action: '删除常用发票',
          requestParms: deleteParams,
          responseData: responseData,
          error: '删除常用发票异常:' + errorMsg,
        },
      })
      return {
        success: false,
        message: errorMsg,
      }
    }
  }
}

export default useDeleteInvoiceFromUsual
