import React, { useState, useMemo, useEffect } from 'react'
import Tooltip from '@app/common/Tooltip'
import Checkbox from '@app/common/checkbox'
import { confirm } from './components/confirm_dialog'
import { selectPayment } from '@app/services/api'
import useUpdateMasterData from '@app/hooks/useUpdateMasterData'
import type { BalancePayment } from '@app/typings/master_api_response'
import { reportClick } from '@app/utils/event_tracking'
import MixedPaymentDialog from './components/mixed_payment_dialog'

type PaymentMode = BalancePayment & {
  explanation?: string | TrustedHTML
  addition?: string
}

type Props = {
  paymentModeList?: PaymentMode[]
}

const PaymentMethod: React.FC<Props> = (props) => {
  const updateMasterData = useUpdateMasterData()
  const { paymentModeList } = props
  const [_paymentModeList, setPaymentModeList] = useState(() => {
    return Array.isArray(paymentModeList) ? paymentModeList : []
  })

  useEffect(() => {
    if (Array.isArray(paymentModeList)) {
      setPaymentModeList(paymentModeList)
    }
  }, [paymentModeList])

  const handleChange = useMemo(
    () => (paymentMode: PaymentMode) => {
      const { paymentId, subPaymentId, selected, combinedPayment } = paymentMode
      reportClick('payway', { paymentId })
      if (selected) return
      /** 混合支付 */
      if (paymentId == '1' && combinedPayment) {
        const { paymentName, mainSupportSkus, subSupportSkus } = paymentMode
        if (Array.isArray(mainSupportSkus) 
          && mainSupportSkus.length 
          && Array.isArray(subSupportSkus) 
          && subSupportSkus.length
        ) {
          confirm({
            title: '请确认支付方式',
            cancelText: '取消',
            onOk: update,
            content: (
              <MixedPaymentDialog
                {...{
                  paymentName,
                  mainSupportSkus,
                  subSupportSkus,
                }}
              />
            ),
          })
          return
        }
      }
      /** 切换支付方式 */
      update()
      function update() {
        setPaymentModeList((prev) => {
          const newList = prev.map((item) => {
            return {
              ...item,
              selected: item.paymentId === paymentId,
            }
          })
          return newList
        })
        selectPayment({ paymentId, subPaymentId }).then((_data) => {
          updateMasterData(undefined, 'select_payment')
        })
      }
    },
    [],
  )

  return (
    <div className="payment-summary-method">
      {_paymentModeList
        .sort((a, b) => {
          // 不可用支付方式UI沉底显示
          const _a = !a.isSupported || a.isLimit
          const _b = !b.isSupported || b.isLimit
          return _a === _b ? 0 : _a ? 1 : -1
        })
        .map((item) => {
          const disabledSettings = (() => {
            const { isSupported, isLimit } = item
            if (!isSupported || isLimit) {
              return {
                disabled: true,
                indeterminate: true,
              }
            }
            return {}
          })()

          const paymentName = (() => {
            const { paymentId, paymentName, subPaymentName, combinedPayment, selected} = item
            if (paymentId == '1' && combinedPayment && selected && subPaymentName) {
              return `${paymentName}+${subPaymentName}`
            }
            return paymentName
          })()

          return (
            <div key={item.paymentId} className="payment-method-item">
              <Checkbox {...disabledSettings} label={paymentName} checked={item.selected} onChange={() => handleChange(item)} />
              {item.explanation && typeof item.explanation === 'string' && (
                <Tooltip
                  content={<div className="payment-method-item__tip" dangerouslySetInnerHTML={{ __html: item.explanation }} />}
                  placement="top"
                  width={280}
                  padding="12px"
                  arrow
                >
                  <i className="icon-info" />
                </Tooltip>
              )}
              {item.addition && <span className="payment-method-item__disabled">{item.addition}</span>}
            </div>
          )
        })}
    </div>
  )
}
PaymentMethod.displayName = 'PaymentMethod'
export default PaymentMethod
