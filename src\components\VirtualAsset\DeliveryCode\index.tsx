/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-09 11:18:10
 * @LastEditTime: 2025-07-03 12:20:35
 * @LastEditors: ext.wangchao120
 * @Description: 领货码
 * @FilePath: /pc_settlement/src/components/VirtualAsset/DeliveryCode/index.tsx
 */
import React from 'react'
import Tabs from '../components/Tabs'
import List from './List'
import NotList from './NotList'
import RedeemCode from '../components/RedeemCode'
import useGift from '../hooks/useGift'
import { deliveryCodeTabKey } from '@app/components/VirtualAsset/atoms'
import { useAtom } from 'jotai'
interface Props {
  tabKey?: string
}
const DeliveryCode: React.FC<Props> = ({ tabKey }) => {
  const { handleSubmit, codeRef } = useGift()
  const [activeKey, seActiveKey] = useAtom(deliveryCodeTabKey)
  return (
    <>
      <Tabs type="button" activeKey={activeKey} onChange={(key: string) => seActiveKey(key)} maxHeight="300">
        <Tabs.Panel tabKey="available" tab="可用列表">
          <List tabKey={tabKey} activeKey={activeKey} />
        </Tabs.Panel>
        <Tabs.Panel tabKey="unavailable" tab="不可用列表">
          <NotList tabKey={tabKey} activeKey={activeKey} />
        </Tabs.Panel>
        <Tabs.Panel tabKey="add" tab="添加领货码">
          <RedeemCode ref={codeRef} onClick={(code) => handleSubmit(code, 'accesskey')}>
            添加并使用
          </RedeemCode>
        </Tabs.Panel>
      </Tabs>
    </>
  )
}

export default DeliveryCode
