import { atom } from 'jotai'
import {
  InvoiceConsigneeEditVO,
  VenderInvoiceEditVO,
  ShowSkuVO,
  InvoiceTypeVO,
  InvoiceEditVO,
  InvoiceType,
  InvoiceShowEditVO,
  InvoiceTitle,
} from '@app/typings/invoice.d'

// 是否显示发票弹窗
export const showInvoiceModalAtom = atom<boolean>(false)
// 发票信息文本 - 用于在发票楼层显示
export const invoiceInfoMessageAtom = atom<InvoiceShowEditVO | null>(null)
// 纳税人识别号文案
export const invoiceCodeDescAtom = atom<string>('')
// 是否是bpin true:是，false:否
export const isBpinAtom = atom<boolean>(false)
// 保存查询发票参数
export const invoiceEditVoAtom = atom<InvoiceEditVO>()
// 发票数据 -自营 -非自营
export const selfInvoiceAtom = atom<Partial<VenderInvoiceEditVO>>({})
export const popInvoiceAtom = atom<Partial<VenderInvoiceEditVO>>({})
// 判断单个场景是自营还是pop - 0:自营，1:POP 其他：无意义
export const typeAtom = atom<number>(0)
// 自营、非自营商品列表
export const selfSkuListAtom = atom<ShowSkuVO[]>([])
export const popSkuListAtom = atom<ShowSkuVO[]>([])
// 订单类型状态
export const selectedInvoiceStatusAtom = atom<number>(0) // 0=仅自营，1=仅POP，2=混合
// 收票人信息
export const invoiceConsigneeEditVOAtom = atom<Partial<InvoiceConsigneeEditVO>>({})
// 是否显示提示弹窗
export const showNoticeModalAtom = atom<boolean>(false)
// 自营药品是否已勾选
export const disclaimerCheckedAtom = atom<boolean>(false)
// 专用发票ui步骤
export const specialInvoiceStepAtom = atom<number>(1)
// 发票类型切换状态 - 0: 自营商品发票, 1: POP商品发票
export const activeInvoiceTabAtom = atom<number>(0)
// 其他状态
export const needEleVatInvoiceAtom = atom<boolean>(false)
// 表单是否修改
export const isFormModifiedAtom = atom<boolean>(false)

// 当前业务场景下的发票数据 - 派生状态
export const currentInvoiceAtom = atom((get) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const type = get(typeAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const selfInvoice = get(selfInvoiceAtom)
  const popInvoice = get(popInvoiceAtom)
  if (selectedInvoiceStatus === 2) {
    // 混合场景: 自营 + POP
    return activeInvoiceTab === 0 ? selfInvoice : popInvoice
  } else {
    // 非混合场景: 纯自营或纯POP
    return type === 0 ? selfInvoice : popInvoice
  }
})
// 当前业务场景下的发票类型 - 派生状态
export const currentInvoiceTypesAtom = atom((get) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const type = get(typeAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const selfInvoiceTypes = get(selfInvoiceAtom).invoiceTypes
  const popInvoiceTypes = get(popInvoiceAtom).invoiceTypes

  // 根据业务场景选择对应的发票类型
  if (selectedInvoiceStatus === 2) {
    // 混合场景: 自营 + POP
    return activeInvoiceTab === 0 ? selfInvoiceTypes : popInvoiceTypes
  } else {
    // 非混合场景: 纯自营或纯POP
    return type === 0 ? selfInvoiceTypes : popInvoiceTypes
  }
})
// 当前业务场景下的开票方式 - 派生状态
export const currentInvoicePutTypesAtom = atom((get) => {
  const currentInvoiceType = get(currentInvoiceTypesAtom)?.find((item) => item.selected)?.value
  if (currentInvoiceType === InvoiceType.VAT) {
    return get(currentInvoiceAtom).vat?.invoicePutTypes
  } else if (currentInvoiceType === InvoiceType.ELECTRONIC) {
    return get(currentInvoiceAtom).electroInvoice?.invoicePutTypes
  }
})
// 当前业务场景下的发票抬头 - 派生状态
export const currentInvoiceTitlesAtom = atom((get) => {
  const currentInvoiceType = get(currentInvoiceTypesAtom)?.find((item) => item.selected)?.value
  if (currentInvoiceType === InvoiceType.NORMAL) {
    return get(currentInvoiceAtom).normalInvoice?.invoiceTitles
  } else if (currentInvoiceType === InvoiceType.ELECTRONIC) {
    return get(currentInvoiceAtom).electroInvoice?.invoiceTitles
  }
})
// 当前业务场景下的发票内容 - 派生状态
export const currentNormalInvoiceContentsAtom = atom((get) => {
  const selectedInvoiceType = get(selectedInvoiceTypeAtom)
  // 根据当前选中的发票类型获取对应的发票内容
  if (selectedInvoiceType === 3) {
    // 电子普通发票
    return get(currentInvoiceAtom).electroInvoice?.normalInvoiceContents
  } else if (selectedInvoiceType === 1) {
    // 纸质普通发票
    return get(currentInvoiceAtom).normalInvoice?.normalInvoiceContents
  } else if (selectedInvoiceType === 2 || selectedInvoiceType === 22) {
    // 专用发票或电子专用发票
    return get(currentInvoiceAtom).vat?.normalInvoiceContents
  } else {
    return get(currentInvoiceAtom).normalInvoice?.normalInvoiceContents
  }
})
// 当前业务场景下的图书发票内容 - 派生状态
export const currentBookInvoiceContentsAtom = atom((get) => {
  const selectedInvoiceType = get(selectedInvoiceTypeAtom)

  // 根据当前选中的发票类型获取对应的图书发票内容
  if (selectedInvoiceType === 3) {
    // 电子普通发票
    return get(currentInvoiceAtom).electroInvoice?.bookInvoiceContents
  } else if (selectedInvoiceType === 1) {
    // 纸质普通发票
    return get(currentInvoiceAtom).normalInvoice?.bookInvoiceContents
  } else if (selectedInvoiceType === 2 || selectedInvoiceType === 22) {
    // 专用发票或电子专用发票
    return get(currentInvoiceAtom).vat?.bookInvoiceContents
  } else {
    return get(currentInvoiceAtom).normalInvoice?.bookInvoiceContents
  }
})
// 当前业务场景下的不支持电子专用发票的skuIds - 派生状态
export const currentUnsupportEleVatSkuIds = atom((get) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const type = get(typeAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const unsupportSelfEleVatSkuIdsAtom = get(selfInvoiceAtom).unsupportEleVatSkuIds
  const unsupportPopEleVatSkuIdsAtom = get(popInvoiceAtom).unsupportEleVatSkuIds

  // 根据业务场景选择对应的发票类型
  if (selectedInvoiceStatus === 2) {
    // 混合场景: 自营 + POP
    return activeInvoiceTab === 0 ? unsupportSelfEleVatSkuIdsAtom : unsupportPopEleVatSkuIdsAtom
  } else {
    // 非混合场景: 纯自营或纯POP
    return type === 0 ? unsupportSelfEleVatSkuIdsAtom : unsupportPopEleVatSkuIdsAtom
  }
})
// 当前业务场景下的发票类型支持状态 - 派生状态
export const currentInvoiceSupportAtom = atom((get) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const type = get(typeAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)

  // 发票类型的提示信息
  const supportSelfVatMessage = get(selfInvoiceAtom).supportVatMessage
  const supportSelfElectroMessage = get(selfInvoiceAtom).supportElectroMessage
  const supportSelfEleVatMessage = get(selfInvoiceAtom).supportEleVatMessage

  const supportPopVatMessage = get(popInvoiceAtom).supportVatMessage
  const supportPopElectroMessage = get(popInvoiceAtom).supportElectroMessage
  const supportPopEleVatMessage = get(popInvoiceAtom).supportEleVatMessage

  // 根据业务场景选择对应的发票类型
  if (selectedInvoiceStatus === 2) {
    // 混合场景: 自营 + POP
    if (activeInvoiceTab === 0) {
      return {
        supportVatMessage: supportSelfVatMessage,
        supportElectroMessage: supportSelfElectroMessage,
        supportEleVatMessage: supportSelfEleVatMessage,
      }
    } else {
      return {
        supportVatMessage: supportPopVatMessage,
        supportElectroMessage: supportPopElectroMessage,
        supportEleVatMessage: supportPopEleVatMessage,
      }
    }
  } else {
    // 非混合场景: 纯自营或纯POP
    if (type === 0) {
      return {
        supportVatMessage: supportSelfVatMessage,
        supportElectroMessage: supportSelfElectroMessage,
        supportEleVatMessage: supportSelfEleVatMessage,
      }
    } else {
      return {
        supportVatMessage: supportPopVatMessage,
        supportElectroMessage: supportPopElectroMessage,
        supportEleVatMessage: supportPopEleVatMessage,
      }
    }
  }
})
// 当前业务场景下的选中开票方式 - 派生状态
export const selectedInvoicePutTypeAtom = atom((get) => {
  const currentInvoicePutTypes = get(currentInvoicePutTypesAtom)
  return currentInvoicePutTypes?.find((item) => item.selected)?.value
})
// 当前业务场景下的选中发票类型 - 派生状态
export const selectedInvoiceTypeAtom = atom((get) => {
  const currentInvoiceTypes = get(currentInvoiceTypesAtom)
  return currentInvoiceTypes?.find((item) => item.selected)?.value
})
// 当前业务场景下的常用发票列表 - 派生状态
export const currentUsualInvoiceListAtom = atom((get) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const type = get(typeAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const selfUsualInvoiceList = get(selfInvoiceAtom)?.usualInvoiceList || []
  const popUsualInvoiceList = get(popInvoiceAtom)?.usualInvoiceList || []

  if (selectedInvoiceStatus === 2) {
    return activeInvoiceTab === 0 ? selfUsualInvoiceList : popUsualInvoiceList
  } else {
    return type === 0 ? selfUsualInvoiceList : popUsualInvoiceList
  }
})
// 当前业务场景下的个人常用发票列表 - 派生状态
export const currentPersonalUsualInvoiceListAtom = atom((get) => {
  const selectedInvoiceStatus = get(selectedInvoiceStatusAtom)
  const type = get(typeAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const selfPersonalUsualInvoiceList = get(selfInvoiceAtom)?.personalUsualInvoiceList || []
  const popPersonalUsualInvoiceList = get(popInvoiceAtom)?.personalUsualInvoiceList || []

  if (selectedInvoiceStatus === 2) {
    return activeInvoiceTab === 0 ? selfPersonalUsualInvoiceList : popPersonalUsualInvoiceList
  } else {
    return type === 0 ? selfPersonalUsualInvoiceList : popPersonalUsualInvoiceList
  }
})
// 当前业务场景下的选中发票抬头 - 派生状态
export const selectedInvoiceTitleAtom = atom((get) => {
  const currentInvoiceTitles = get(currentInvoiceTitlesAtom)
  return currentInvoiceTitles?.find((item: InvoiceTypeVO) => item.selected)?.value
})
// 当前选中的发票内容 - 派生状态
export const selectedNormalInvoiceContentAtom = atom((get) => {
  const currentNormalInvoiceContents = get(currentNormalInvoiceContentsAtom)
  return currentNormalInvoiceContents?.find((item) => item.selected)?.value
})
// 当前选中的图书发票内容 - 派生状态
export const selectedBookInvoiceContentAtom = atom((get) => {
  const currentBookInvoiceContents = get(currentBookInvoiceContentsAtom)
  return currentBookInvoiceContents?.find((item) => item.selected)?.value
})

// 判断当前业务场景下是否正确勾选自营药品
export const isSelfMedicineCheckedAtom = atom((get) => {
  const currentInvoice = get(currentInvoiceAtom)
  const activeInvoiceTab = get(activeInvoiceTabAtom)
  const selectedInvoiceTitle = get(selectedInvoiceTitleAtom)
  const disclaimerChecked = get(disclaimerCheckedAtom)
  // 选中自营商品且抬头为单位且需要合规提示且免责声明未勾选
  if (
    activeInvoiceTab === 0 &&
    selectedInvoiceTitle === InvoiceTitle.COMPANY &&
    currentInvoice.needConformanceTips === 1 &&
    !disclaimerChecked
  ) {
    return false
  } else {
    return true
  }
})
