/**
 * 发票类型枚举
 */
export enum InvoiceType {
  /** 普通发票类型 */
  NORMAL = 1,
  /** 增值税专用发票类型 */
  VAT = 2,
  /** 电子普通发票类型 */
  ELECTRONIC = 3,
  /** 电子增值税专用发票类型 */
  E_VAT = 22,
}

/**
 * 商家类型枚举
 */
export enum VenderType {
  /** 自营商家 */
  SELF = 0,
  /** POP商家 */
  POP = 1,
  /** 自营+POP */
  MIX = 2,
}

export enum InvoiceTitle {
  /** 个人发票抬头 */
  PERSONAL = 4,
  /** 单位发票抬头 */
  COMPANY = 5,
}
export interface InvoiceResult<T> {
  code?: string
  message?: string
  timestamp?: number
  body?: T
  traceId?: string
  requestId?: string
}

/**
 * 发票类型
 */
export interface VenderInvoiceEditVO {
  /**0-自营 1- pop null - 无意义*/
  type?: number
  /**是否需要合规提示的标示： 判断商品列表是否包含处方药或者非处方药，包含任一类则需要提示合规信息*/
  isNeedConformanceTips?: boolean
  /**用户常用发票列表。*/
  usualInvoiceList?: UsualInvoiceItem[]
  /**选中的常用发票id*/
  selectedUsualInvoiceId: number
  /**用户选择的发票类型：1 普通 2 增值税*/
  selectedInvoiceType: number
  /**支持的发票类型<普通发票|电子发票|增值税发票>*/
  supportInvoiceTypeList?: number[]
  /**supportInvoiceTypeList不包含专票时的原因
     2001-命中百亿补贴不开专票
     2002-命中北京政府消费券不开专票*/
  notVatCode?: string
  /**抬头不支持企业时具体的原因code码
    
     3001-命中百亿补贴补开企业抬头*/
  notCompanyTitleCode?: string
  /**用户选择的增值税发票*/
  vat?: VatEditVO
  /**用户选择的普通发票*/
  normalInvoice?: NormalInvoiceEditVO
  /**电子发票*/
  electroInvoice?: ElectroInvoiceEditVO
  /**混单类型:1都是自营商品；2都是pop商品；3既有自营又有pop商品;其中自营类包括（自营、FBP、FCS），其中loc商品不在处理范围之内。*/
  invoiceOrderType: number
  /**暂不确认*/
  ivcExtInfoMap?: Map<string, string>
  /**发票重置标记
     1,消费劵发票重置标记
     2,标识百补重置*/
  resetInvoiceTags?: string[]
  /**发票个人常用抬头列表*/
  personalUsualInvoiceList?: UsualInvoiceItem[]
  selectedUsualInvoiceTitle: number
  /**用户选择的电子增值税发票*/
  eleVat?: VatEditVO
  /**预售发票信息*/
  presaleInvoiceResultList?: PresaleInvoiceResult[]
  /**预售发票信息状态，如果loc+预售尾款商品，二级页应该展示跟全部预售尾款商品一样
     1表示除了预售尾款商品全部是不开发票的商品 2表示包含普通和不开发票商品*/
  presaleInvoceState?: number
  /**不支持专票sku*/
  unSupportVatSkuVO?: UnSupportVatSkuVO
  skuInfoList?: ShowSkuVO[]
  /**是否支持普通电子发票*/
  supportElectro: boolean
  /**不支持普通电子发票文案*/
  supportElectroMessage?: string
  /**是否支持增值发票*/
  supportVat: boolean
  /**不支持增票文案*/
  supportVatMessage?: string
  /**是否支持电子增值发票*/
  supportEleVat: boolean
  /**不支持电子增票文案*/
  supportEleVatMessage?: string
  /**是否显示不支持专用发票文案 1：支持 0 ：不支持 2:兜底*/
  supportEleVatTip: number
  /**不支持专用发票文案*/
  unsupportEleVatText?: string
  /**是否显示不支持专用发票文案*/
  unsupportEleVatSkuIds?: string
  /**零售普通发票校验 1:需要校验 0：无需校验*/
  needConformanceTips: number

  invoiceTypes?: InvoiceTypeVO[]

  hasKaPop: boolean
}
//发票查询接口响应信息
export interface InvoiceInfoEditVO {
  /**待确认*/
  hasKa?: boolean
  /**发票信息*/
  invoiceEditVO?: InvoiceEditVO
  /**是否走发票基础服务*/
  invokeBasicInvoiceService?: boolean
  /**待确认*/
  hasHaoKa?: boolean
  /**是否有合约机*/
  hasHyj?: boolean
  /**是否开启发票联想*/
  downAutoInvoice?: boolean
  /**color切量*/
  useColorApi?: boolean
}

//增票
export interface VatEditVO {
  invoicePutTypes?: InvoiceTypeVO[]
  invoicePutFlag?: boolean
  companyName?: string
  /**纳税人识别号*/
  code?: string

  regAddr?: string

  regPhone?: string

  regBank?: string
  /**注册银行帐号*/
  regBankAccount?: string

  normalInvoiceContents?: InvoiceTypeVO[]

  bookInvoiceContents?: InvoiceTypeVO[]

  disabled?: boolean

  showVatPrompt?: boolean

  invoiceConsigneeEditVO?: InvoiceConsigneeEditVO

  hasBookSku?: boolean

  hasNotBookSku?: boolean

  userLevel?: number

  centralizedInvoice?: number

  invoicePutType?: number

  configOpen?: boolean

  email?: string
  /**是否是默认发票*/
  defaultFlag?: boolean
  /**加密邮箱*/
  encryptEmail?: string
}

export interface DonatedCouponVO {
  couponType: number

  couponQuota: number

  couponAd?: string

  key?: string

  limitCateGoryCoupon: boolean
}

export interface PromotionVO {
  promoId: number
  promoType: number
  price?: string
  promotionDiscount?: string
  cashBack?: string
  donatedCouponList?: DonatedCouponVO[]
  giftScore: number
  manfanMoney?: string
  addMoney?: string
  needMoney?: string
  needNum: number
  deliverNum: number
  jdBeanNum: number
  maxNumLimit: number
  discountLimit?: string
  moneyDeliverNum: number
  beginTime: number
  endTime: number
  extProp?: Map<string, string>
  promoTags?: number[]
  redPacket?: string
  priceReset: boolean
  promotionTags?: number[]
  multiPromoTags?: number[]

  promoPriceTypeList?: number[]

  scalePromoInfo?: Map<number, number>

  needMoneyRule?: string

  discountRule?: string

  fullReductionPromotionType?: string

  subsidy?: string

  subsidyType?: string

  skuTotalNormalJdPriceRange?: string

  normalJdPriceRange?: string

  plus95?: boolean

  pyby?: boolean

  subType?: string
}

// 商品信息
export interface ShowSkuVO {
  skuId: number

  skuName?: string

  partsItem?: boolean

  skuImgUrl?: string

  venderId: number

  venderName?: string

  skuNum: number

  bigItem: boolean

  supportInstall: boolean

  factoryShip: boolean

  noZy?: string

  yanbao: boolean

  serviceItemId: number

  uuid?: string

  sxType: number

  venderColType: number

  venderType: number

  skuMark: number

  presaleBalanceFlag?: boolean

  mainSku?: boolean

  promotion?: PromotionVO

  cartUuid?: string

  useUuid?: boolean

  storeId?: string

  consigneeName?: string

  presentFlag: boolean

  gift?: boolean

  regularBuy?: boolean

  totalPromoPrice?: string

  customGiftAttrId?: string

  customServiceType?: string

  productTags?: string[]

  specialService?: string

  specialServiceMock?: string

  mark?: Map<string, any>

  qualificationStatus?: number

  bundleUUID?: string

  venderNae?: string

  book: boolean
}

//不支持专票sku
export interface UnSupportVatSkuVO {
  allNotVat?: boolean

  notOpenVatRemind?: boolean

  codeMap?: Map<string, string>

  unSupportVatSkus?: ShowSkuVO[]
}

// 发票类型
export interface InvoiceTypeVO {
  value: number
  content?: string
  selected: boolean
  disabled?: boolean
  hidden?: boolean
  descLabel?: string
}

// 预售发票结果
export interface PresaleInvoiceResult {
  showSkuVOList?: ShowSkuVO[]

  uuid?: string

  invoiceType: number

  invoiceTitle: number

  companyName?: string

  invoiceCode?: string

  invoiceContent?: number

  invoicePutType?: number
}
//普通发票
export interface NormalInvoiceEditVO {
  invoicePutTypes?: InvoiceTypeVO[]
  invoicePutFlag?: boolean
  /**发票抬头*/
  invoiceTitles?: InvoiceTypeVO[]
  /**公司名称*/
  companyName?: string
  /**个人名称*/
  personalName?: string
  /**非图书发票内容*/
  normalInvoiceContents?: InvoiceTypeVO[]
  /**图书发票内容*/
  bookInvoiceContents?: InvoiceTypeVO[]
  /**收票人地址信息*/
  invoiceConsigneeEditVO?: InvoiceConsigneeEditVO
  /**是否含有图书*/
  hasBookSku?: boolean
  /**是否含有非图书商品*/
  hasNotBookSku?: boolean
  /**用户级别*/
  userLevel?: number
  /**0 不集中开发票;1 集中开发票*/
  centralizedInvoice?: number
  /**开票方式(随货开票、邮寄到其它地址、集中开票)*/
  invoicePutType?: number
  /**是否是自营四类*/
  allJDsku?: boolean
  /**开关是否打开*/
  configOpen?: boolean
  /**纳税人识别号*/
  invoiceCode?: string
  /**公司注册电话*/
  regTel?: string
  /**公司注册银行*/
  regBank?: string
  /**公司注册银行账号*/
  regAccount?: string
  /**公司注册地址*/
  regAddress?: string
  /**选中的常用发票title 4 个人 5 单位*/
  selectedInvoiceTitle?: number
  /**是否是默认发票*/
  defaultFlag?: boolean
  /**是否有更多选填项*/
  hasMoreInfo?: boolean
}

// 获取发票信息
export interface GetInvoiceForm {
  /**是否含有号卡*/
  hasHaoKa: boolean
  /**是否含有合约机*/
  hasHyj: boolean
  /**企业采购通过采购清单下单时，source=qyg,需要sendepay第500位打标*/
  hasSendPay?: string
  /**请求来源*/
  source?: string
  /**取消或选中余额*/
  use: boolean
  /**刷新页面接口
     refreshPage=true， 调用doGetCurrentOrderWithNewRule*/
  refreshPage: boolean
  /**User-Agent*/
  agent?: string
  /**配送一级地址*/
  provinceId?: string
  /**配送二级地址*/
  cityId?: string
  /**配送三级地址*/
  areaId?: string
  /**配送四级地址, 例如：1-72-2799*/
  locationId?: string
  /**客户端Ip*/
  ip?: string
}

//发票信息
export interface InvoiceEditVO {
  /**常用发票列表*/
  usualInvoiceList?: UsualInvoiceItem[]
  /**是否打开电子增票*/
  needEleVatInvoice: boolean
  /**发票类型*/
  invoiceTypes?: InvoiceTypeVO[]
  /**纳税人识别号文案*/
  invoiceCodeDesc: string
  /**普通发票*/
  normalInvoice?: NormalInvoiceEditVO
  /**增票*/
  vat?: VatEditVO
  /**电子增票*/
  eleVat?: VatEditVO
  /**电子发票*/
  electroInvoice?: ElectroInvoiceEditVO
  /**是否含有图书*/
  hasBook: boolean
  /**是否含有图书商品*/
  hasCommon: boolean
  /**是否显示一个虚拟的增值税发票选项 仅仅起到提示的作用*/
  showVirtualVatRadio: boolean
  /**是否调用发票的基础服务*/
  invokeBasicInvoiceService: boolean
  /**混单类型:1都是自营商品；2都是pop商品；3既有自营又有pop商品;其中自营类包括（自营、FBP、FCS），其中loc商品不在处理范围之内。（V1.3版本新增）*/
  invoiceOrderType: number
  /**常用发票列表（个人）*/
  personalUsualInvoiceList?: UsualInvoiceItem[]
  /**选中的常用发票title 4 个人 5 单位*/
  selectedInvoiceTitle: number
  /**是否支持普通电子发票*/
  supportElectro: boolean
  /**不支持普通电子发票文案*/
  supportElectroMessage?: string
  /**是否支持增值发票*/
  supportVat: boolean
  /**不支持增票文案*/
  supportVatMessage?: string
  /**是否支持电子增值发票*/
  supportEleVat: boolean
  /**不支持电子增票文案*/
  supportEleVatMessage?: string
  /**是否显示不支持专用发票文案 1：支持 0 ：不支持 2:兜底*/
  supportEleVatTip: number
  /**是否显示不支持专用发票文案*/
  unsupportEleVatSkuIds?: string
  /**不支持专用发票文案*/
  unsupportEleVatText?: string
  /**零售普通发票校验 1:需要校验 0：无需校验*/
  needConformanceTips: number
  /**选中的发票状态 0-仅自营 1-仅pop 2- pop + 自营混单*/
  selectedInvoiceStatus: number
  /**发票升级后保存发票需要传入当前的商品uuid集合，当前的uuid不包含预售尾款*/
  shoppingUuidList?: string[]

  bPin: boolean

  venderInvoiceVOList?: VenderInvoiceEditVO[]
  groupTag?: Map<string, string>
}

//BalanceClientInfo class
export interface ClientInfoQuery {
  /**全链路测试标识*/
  forcebot?: string
  /**流量终端*/
  ua?: number
  /**客户端类型*/
  client?: string
  /**客户端版本*/
  clientVersion?: string
  /**客户端小版本号*/
  build?: number
  /**客户端操作系统版本*/
  osVersion?: string
  /**客户端设备号*/
  uuid?: string
  /**用户pin*/
  pin?: string
  /**用户会员级别*/
  umg?: string
  /**用户风险级别*/
  urg?: string
  /**plus级别*/
  upg?: string
  /**合作伙伴*/
  partner?: string
  /**客户端屏幕尺寸*/
  screen?: string
  /**设备品牌*/
  dBrand?: string
  /**设备型号*/
  dModel?: string
  /**请求网络类型*/
  networkType?: string
  /**用户ip*/
  ip?: string
  /**服务器ip*/
  serverIp?: string
  /**访问京东时用户ip的原始端口*/
  port?: number
  /**根据用户ip计算出的附加信息*/
  location?: string
  /**四级地址*/
  area?: string
  /**前端请求携带的HTTP Referer*/
  referer?: string
  /**前端请求携带的User-Agent*/
  agent?: string
  /**前端请求携带的cookie*/
  cookie?: string
  /**红包所需服务名称*/
  serverName?: string
  /**红包所需用户IP*/
  userIP?: string
  /**红包所需countKey*/
  countKey: number
  /**红包所需netBuySourceType*/
  netBuySourceType: number
  /**调用发票基础服务invokeInvoiceBasicService*/
  invokeInvoiceBasicService: boolean
  /**客户端浏览器名称*/
  browserName?: string
  /**客户端浏览器版本号*/
  browserVersion?: string
  /**操作系统版本号*/
  osName?: string
}

//收票人地址信息
export interface InvoiceConsigneeEditVO {
  consigneeName?: string

  provinceId: number

  cityId: number

  countyId: number

  townId: number

  provinceName?: string
  cityName?: string
  countyName?: string
  townName?: string

  phone?: string

  address?: string

  sendSeparate: boolean
}

// 常用发票
export interface UsualInvoiceItem {
  id: number

  content?: string

  selected: boolean

  invoiceCode?: string

  regTel?: string

  regBank?: string

  regAccount?: string

  regAddress?: string
}

//电子发票
export interface ElectroInvoiceEditVO {
  invoicePutTypes?: InvoiceTypeVO[]
  invoicePutFlag?: boolean
  invoiceTitles?: InvoiceTypeVO[]

  electroCompanyName?: string

  personalName?: string

  bookInvoiceContents?: InvoiceTypeVO[]

  normalInvoiceContents?: InvoiceTypeVO[]

  phone?: string

  email?: string

  encryptEmail?: string

  hasBookSku?: boolean

  hasNotBookSku?: boolean

  userLevel?: number

  centralizedInvoice?: number

  invoiceCode?: string

  hasPopNonLoc?: boolean

  invoicePutType?: number

  regTel?: string

  regBank?: string

  regAccount?: string

  regAddress?: string
  /**选中的常用发票title 4 个人 5 单位*/
  selectedInvoiceTitle?: number
  /**是否是默认发票*/
  defaultFlag?: boolean
  /**是否有更多选填项*/
  hasMoreInfo?: boolean
}

//发票信息
export interface InvoiceShowEditVO {
  // 发票说明
  message?: string
  // icon发票说明
  iconMessage?: string
  // 发票说明icon
  imageLink?: string
  // 发票抬头
  invoiceTitle?: string
  // 是否显示icon
  iconFlag?: string
}

// 保存发票请求参数
export interface SaveInvoiceForm {
  /**发票类型 0-纯自营 1-纯pop null - 无意义*/
  selectInvoiceTitle: string
  /**常用发票id TODO*/
  usualInvoiceId?: string
  /**是否含有图书 TODO*/
  hasBook: boolean
  /**是否含有普通商品 TODO*/
  hasCommon: boolean
  /**是否单独寄送发票 TODO*/
  sendSeparate: boolean
  /**开票方式(随货开票、邮寄到其它地址、集中开票)*/
  invoicePutType: number
  /**是否是默认发票*/
  defaultFlag: boolean
  /**是否展示货票分离.<br>
      礼品购二期.<br>
      条件：自营、FBP、LBP、厂直*/
  showInvoiceSeparate: boolean
  /**发票类型 0-纯自营 1-纯pop null - 无意义*/
  selectElectroTitle: number
  /**货票分离开关值.<br> TODO
      0:全部关闭，1：礼品购流程开启，2：全部开启*/
  invoiceSeparateSwitch: number
  /**图书发票内容*/
  selectBookInvoiceContent: string
  /**普通发票内容*/
  selectNormalInvoiceContent: string
  /**纳税人识别号*/
  invoiceCode?: string
  /**保存普通发票标示 TODO*/
  saveInvoiceFlag?: string
  /**是否含有5g号卡*/
  hasHaoKa: boolean
  /**是否含有合约机*/
  hasHyj: boolean
  /**key：VenderType（商家类型）value：Self/POP（自营/POP*/
  groupTag?: Map<string, string>
  /**商品得uuid*/
  groupShoppingList?: string[]
  /**发票信息*/
  invoiceEditParamList?: InvoiceEditParam[]
  /**选中的发票状态 0-仅自营 1-仅pop 2- pop + 自营混单*/
  selectedInvoiceStatus: number
  /**选中的发票类型 0-纯自营 1-纯pop null - 无意义*/
  selectedInvoiceType: string
  /**TODO*/
  venderType?: string
  /**常用发票列表*/
  usualInvoiceList?: UsualInvoiceItem[]
  /**常用发票列表（个人）*/
  personalUsualInvoiceList?: UsualInvoiceItem[]
  /**User-Agent*/
  agent?: string
  /**配送一级地址*/
  provinceId?: string
  /**配送二级地址*/
  cityId?: string
  /**配送三级地址*/
  areaId?: string
  /**配送四级地址, 例如：1-72-2799*/
  locationId?: string
  /**客户端Ip*/
  ip?: string
  /**cookieMapStr*/
  cookieMapStr?: string
  /**headerUserAgent*/
  headerUserAgent?: string
  /**color参数：根据用户ip计算出的附加信息*/
  location?: string
  /**orderFrom*/
  orderFrom?: number
  /**android/ios/pc*/
  client?: string
  /**白条、京东支付前置类型：mzwx：m站微信环境，mzsq：m站手q环境*/
  payChannelType?: string
  /**广告参数（m站区分广告来源，--暂无功能使用，用于大数据上报统计）*/
  utmMedium?: string
  /**进入系统时间*/
  startTime?: number
  /**color应用app id*/
  appid?: string
  /**设备指纹信息*/
  eid?: string
  /**风控 fp*/
  fp?: string
  /**百亿补贴屏蔽入参标识*/
  bbtf?: string
  /**来源：主要和商详保持一致，能区分后续来源*/
  sourceType?: string
  /**业务类型*/
  businessType?: string
  /**刷新结算页的时候，判断是否不需要重置默认地址（大家电换区、轻松购不需要重置）*/
  // notResetDefaultAddress: boolean
  /**进入结算的整单uuid*/
  orderUuid?: string
  /**对应的子单uuid*/
  combinationBundleUuid?: string
  /**中台地址全站化使用，由soa传入，只在首次进结算使用，作为进结算时的默认地址*/
  addressId?: number

  // needIdentity: number
  /**店铺ID*/
  venderId?: number
  /**用户pin*/
  pin?: string
  /**用户Ip*/
  UserIp?: string
  /**uuid*/
  uuid?: string
  /**设备ID*/
  deviceUUID?: string
  /**应用ID*/
  appId?: string
  /**插件ID，应用类型为2（插件）时，必填*/
  pluginId?: string
  /**鉴权token，加密生成。*/
  token?: string
  /**请求来源*/
  source?: string
  /**全链路压测标识*/
  forcebot?: string
  /**登陆模式*/
  loginType?: string
  /**扩展参数（可选，按需传值）*/
  extParam?: any
}

// 发票编辑参数
export interface InvoiceEditParam {
  /**发票类型 0-纯自营 1-纯pop null - 无意义*/
  type: number
  electroEncryptEmail?: string
  vatEncryptEmail?: string

  usualInvoiceId?: string

  selectedInvoiceType?: string

  selectInvoiceTitle?: string

  selectNormalInvoiceContent?: string

  selectBookInvoiceContent?: string

  hasBook: boolean

  hasCommon: boolean

  norCompanyName?: string

  norPersonalName?: string

  norCode?: string

  norRegAddr?: string

  norRegPhone?: string

  norRegBank?: string

  norRegBankAccount?: string

  consigneeName?: string

  consigneePhone?: string

  consigneeAddress?: string

  consigneeProvinceId: number

  consigneeProvince?: string

  consigneeCityId: number

  consigneeCity?: string

  consigneeCountyId: number

  consigneeCounty?: string

  consigneeTownId: number

  consigneeTown?: string

  sendSeparate: boolean

  selectElectroTitle: number

  electroInvoiceEmail?: string

  electroInvoicePhone?: string

  electroCompanyName?: string

  electroPersonalName?: string

  eleCode?: string

  eleRegAddr?: string

  eleRegPhone?: string

  eleRegBank?: string

  eleRegBankAccount?: string

  vatCompanyName?: string

  vatCode?: string

  vatRegAddr?: string

  vatRegPhone?: string

  vatRegBank?: string

  vatRegBankAccount?: string

  vatEmail?: string

  showInvoiceSeparate: boolean

  invoiceSeparateSwitch: number

  invoicePutType: number

  invoiceCode?: string

  saveInvoiceFlag?: string

  hasHaoKa: boolean

  hasHyj: boolean
  /**是否是默认发票*/
  defaultFlag?: boolean
  /**商品得uuid*/
  groupShoppingList?: string[]

  venderType?: string

  groupShoppingStr?: string
  /**选中的发票状态 0-仅自营 1-仅pop 2- pop + 自营混单*/
  selectedInvoiceStatus: number
  /**常用发票列表*/
  usualInvoiceList?: UsualInvoiceItem[]
  /**常用发票列表（个人）*/
  personalUsualInvoiceList?: UsualInvoiceItem[]

  /**商品得uuid*/
  shoppingUuidList?: string[]

  invokeInvoiceBasicService?: boolean
}
// 添加发票到常用发票请求参数
export interface InvoiceToUsualForm {
  /**用户选择的发票类型：个人，单位*/
  selectInvoiceTitle?: string
  /**普票单位名称*/
  norCompanyName?: string
  /**普票个人名称 */
  norPersonalName?: string
  /**纳税人识别号 -编辑用*/
  norCode?: string
  /***纳税人识别号 - 保存用 */
  invoiceCode?: string
  /**注册地址*/
  norRegAddr?: string
  /**注册电话*/
  norRegPhone?: string
  /**注册银行*/
  norRegBank?: string
  /**注册银行帐号*/
  norRegBankAccount?: string
  /**选中的图书发票内容 */
  selectBookInvoiceContent?: string
  /**选中的普票发票内容 */
  selectNormalInvoiceContent?: string
  /**常用发票id */
  usualInvoiceId?: string
}

//发票保存接口响应信息
export interface InvoiceInfoSaveVO {
  /**是否是礼品购流程*/
  giftbuy: boolean
  /**发票信息*/
  invoiceInfo?: InvoiceShowEditVO
  /**常用发票ID*/
  usualInvoiceId: number
  /**发票修改状态*/
  saveDefaultInvoice?: boolean
}
// 删除发票请求参数
export interface DeleteInvoiceFromUsualForm {
  /**常用发票ID*/
  usualInvoiceId: number
}
// 发票联想返回信息
export interface CompanyInvoiceInfoDO {
  /**匹配名字（带高亮）*/
  entName?: string
  /**匹配名字*/
  originalName?: string
  /**发票税号*/
  creditCode?: string
}
// 发票联想请求参数
export interface SearchCreditParam {
  queryString?: string
}
