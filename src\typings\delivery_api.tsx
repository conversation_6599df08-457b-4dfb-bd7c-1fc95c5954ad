export interface DeliveryStoreItem {
    venderId: number;
    storeId: number;
    warehouseId: number;
    name: string;
    businessHours: string;
    longitude: number;
    latitude: number;
    storeMark: number;
    recentlyMark: number;
    stockStatus: number;
    distance: string;
    storeAddress: string;
    idProvince: number;
    idCity: number;
    idArea: number;
    idTown: number;
    vendSource: string;
    venderStoreStockTab: number;
}

export interface DeliveryStoreResponse {
    code: string;
    message: string;
    timestamp: number;
    body: DeliveryStoreItem[];
}

export interface DeliveryPickSiteInfo {
    pickId: number;
    pickName: string;
    selected: boolean;
    address: string;
    branchId: number;
    message: string;
    canSelected: boolean;
    siteType: number;
    helpMessage: string;
    cabinetAvailable: boolean;
    limitKeyword: number;
    specialRemark: string;
    isCod: number;
    distance: number;
    telephone: string;
    recommended: number;
    nearestSite: boolean;
    used: boolean;
}

export interface DeliveryPickSiteResponse {
    code: string;
    message: string;
    timestamp: number;
    body: DeliveryPickSiteInfo[];
}