/**
 * @file: DeliveryTips.tsx
 * @description: 配送提示组件，用于显示配送提示信息
 */
import React from 'react'
import { ComShipmentType } from '@app/typings/api_getBundleShipmentList_resp'
import { DeliveryEnum } from '../../../types'
import Tooltip from '@app/common/Tooltip'
import { Tips } from '../../../../ui/Tips/tips'
import { BundleType } from '@app/typings/master_api_response'

interface DeliveryTipsProps {
  shipmentInfo: ComShipmentType | null
  bundle: BundleType
}

export const DeliveryTips: React.FC<DeliveryTipsProps> = ({ shipmentInfo, bundle }) => {
  if (!shipmentInfo) return null

  const tips: (string | React.ReactElement)[] = []
  // 京尊达服务费提示
  if (shipmentInfo.shipmentType === DeliveryEnum.ShipmentTypeEnum.JZD && shipmentInfo.honorFreight) {
    tips.push(
      <div style={{ display: 'flex', fontSize: '12px', alignItems: 'center', gap: '5px' }}>
        <span>京尊达服务费 </span>
        <Tooltip
          content={
            <div
              className="payment-method-item__tip"
              dangerouslySetInnerHTML={{
                __html:
                  '京尊达是京东物流推出的高端服务产品。只要您在京东商场提交订单时选择"京尊达"服务，着正装、戴白手套的尊享使者驾驶新能源车到达指定地点，手捧精美礼盒，传达亲切、暖心服务，创造极具仪式感的交付现场，让收货人感受到尊贵般礼遇。无论是传达浓情蜜意，还是礼品馈赠，京尊达服务是您馈赠亲友、关心爱人、又或犒劳自己的不二选择。京尊达，为情而生。目前该服务开放城市为北京、上海、广州、深圳、成都、武汉、西安、沈阳、杭州。',
              }}
            />
          }
          placement="top"
          width={380}
          padding="12px"
          arrow
        >
          <span style={{ height: '20px', display: 'inline-flex', fontSize: '12px', alignItems: 'center', gap: '5px' }}>
            <i className="icon-info" />
          </span>
        </Tooltip>
        <span>：{shipmentInfo.honorFreight}元</span>
      </div>,
    )
  }

  // 京东快递提示
  if (shipmentInfo.shipmentType === DeliveryEnum.ShipmentTypeEnum.JDKD) {
    tips.push(<span>若社区村镇无人员出入管控，京东快递可送货上门</span>)
  }
  if (bundle.hasNoSupportGbPromise && bundle.hasNoSupportGbPromiseTxt) {
    tips.push(<span>{bundle.hasNoSupportGbPromiseTxt}</span>)
  }

  if (shipmentInfo.shipmentType === DeliveryEnum.ShipmentTypeEnum.MDZQ) {
    tips.push(<span>提货码将在支付完成后发送至您收货地址的手机号 请注意查收~</span>)
    tips.push(<span>京东将根据您的收货地址显示起范围内的门店，请确保您的收货地址正确填写</span>)
  }

  return (
    <>
      {tips.length > 0 && (
        <>
          <div style={{ marginTop: '15px' }}></div>
          <Tips contentList={tips} />
        </>
      )}
    </>
  )
}
