/** 
 * @file: index.tsx
 * @description: 付款详情模块入口
 */
import { ErrorBoundary } from 'react-error-boundary'
import Payment from './payment'
import Skeleton from './skeleton'
import { useAtomValue } from 'jotai'
import { loadingAtom } from '@app/atoms/loadingAtom'
import useMasterData from '@app/hooks/useMasterData'
import reportException from './report_exception'

const Index: React.FC = () => {
  const loading = useAtomValue(loadingAtom)
  const masterData = useMasterData()?.body

  return (
    <ErrorBoundary 
      FallbackComponent={Skeleton} 
      onError={(error, info) => reportException({ error, info, sence: 'ErrorBoundary' })}
    >
      {loading || !masterData ? <Skeleton /> : <Payment />}
    </ErrorBoundary>
  )
}

Index.displayName = 'PaymentModule'

export default Index
