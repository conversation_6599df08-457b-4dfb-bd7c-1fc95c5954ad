// 骨架屏基础变量
$spacing-base: 8px;
$border-radius: 4px;


.invoice-dialog-wrapper {
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
}

.invoice-dialog-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 998;
  background-color: rgba(0, 0, 0, 0.5);
}

// 工具类
.is-flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.items-center {
  align-items: center;
}

.mr-8 {
  margin-right: $spacing-base;
}

.mr-12 {
  margin-right: $spacing-base * 1.5;
}

.mr-16 {
  margin-right: $spacing-base * 2;
}

.mb-8 {
  margin-bottom: $spacing-base;
}

.mb-12 {
  margin-bottom: $spacing-base * 1.5;
}

.mb-16 {
  margin-bottom: $spacing-base * 2;
}

// 骨架屏主体样式
.invoice-skeleton {
  .invoice-skeleton-header {
    padding: 0 24px;
    height: 58px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    
    .invoice-skeleton-title {
      width: 120px;
      height: 18px;
    }
  }

  .invoice-skeleton-tabs {
    padding: 20px 24px 10px;
    
    .invoice-tab-item {
      width: 80px;
      height: 16px;
    }
  }

  .invoice-skeleton-content {
    padding: 0 24px;
  }

  .invoice-skeleton-options {
    margin-bottom: 20px;
    gap: 16px;
    
    .invoice-skeleton-option {
      .option-radio {
        width: 16px;
        height: 16px;
        border-radius: 50%;
      }
      
      .option-text {
        width: 120px;
        height: 16px;
      }
    }
  }

  .invoice-skeleton-form {
    margin-bottom: 24px;
    .invoice-skeleton-form-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 5px 30px;
    }
    
    .form-label {
      width: 130px;
      height: 24px;
      border-radius: 4px;
    }
    
    .form-input {
      width: 100%;
      height: 34px;
      border-radius: 4px;
    }

    .form-radio {
      width: 80px;
      height: 34px;
      border-radius: 4px;
      background-color: #f0f0f0;
    }
  }

  .invoice-skeleton-products {
    .products-title {
      width: 140px;
      height: 15px;
    }
    
    .product-item {
      justify-content: space-between;
      
      .product-name {
        width: 200px;
        height: 16px;
      }
      
      .product-price {
        width: 80px;
        height: 16px;
      }
    }
  }

  .invoice-skeleton-footer {
    padding: 16px 24px 24px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
    
    .cancel-btn {
      width: 80px;
      height: 36px;
      border-radius: 4px;
    }
    
    .confirm-btn {
      width: 80px;
      height: 36px;
      border-radius: 4px;
    }
  }
}
