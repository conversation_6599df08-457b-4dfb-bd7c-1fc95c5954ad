/* 基础样式 */
.btn {
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.65);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    line-height: 1.5715;
    position: relative;
    text-align: center;
    text-decoration: none;
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
  }
  
  /* Primary 按钮样式 */
  .btn-primary {
    color: #fff;
    background: linear-gradient(90deg, #ff475d, #ff0f23);
    border-color: #ff0f23;
  }
  
  .btn-primary:hover {
    background: linear-gradient(90deg, #ff475d, #ff0f23);
    border-color: #ff0f23;
  }
  
  /* Default 按钮样式 */
  .btn-default {
    color: #1A1A1A;
    background-color: #fff;
    border-color: #C2C4CC;
  }
  
  .btn-default:hover {
    background-color: #fff;
    color: #FF0F23;
    border-color: #FF0F23;
  }
  
  /* Dashed 按钮样式 */
  .btn-dashed {
    color: #1A1A1A;
    background-color: #fff;
    border-color: #C2C4CC;
    border-style: dashed;
  }
  
  .btn-dashed:hover {
    background-color: #fff;
    color: #FF0F23;
    border-color: #FF0F23;  }
  
  /* Text 按钮样式 */
  .btn-text {
    color: #FF0F23;
    background-color: transparent;
    border-color: transparent;
  }
  
  .btn-text:hover {
    // background-color: #fff;
    border-color: transparent;
  }
  
  /* Link 按钮样式 */
  .btn-link {
    color: #FF0F23;
    background-color: transparent;
    border-color: transparent;
    padding: 0;
    font-size: inherit;
    height: auto;
  }
  
  .btn-link:hover {
    background-color: transparent;
    border-color: transparent;
  }
  
  /* 大小样式 */
  .btn-large {
    height: 48px;
    padding: 0 20px;
    font-size: 16px;
  }
  
  .btn-small {
    height: 24px;
    padding: 0 8px;
    font-size: 12px;
  }
  
  /* 禁用样式 */
  .btn-disabled {
    // opacity: 0.5;
    pointer-events: none;
    cursor: not-allowed;
    background: none;
    background-color: #C2C4CC;
    border-color: transparent;
    color: rgba(255, 255, 255, 0.5);
  }