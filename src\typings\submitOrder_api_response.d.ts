export type SubmitOrderApiResponse = Partial<{
  /** 错误码 */
  code: `${number}`
  /** 错误信息 */
  message: string
  /** 服务器时间戳 */
  timestamp: number
  /** 响应数据 */
  body: Body
  /**pfinder的tradeId*/
  traceId?: string
  /**color的requestId*/
  requestId?: string
}>

export interface Body {
  usedGovSubsidyToast: unknown
  /**业务错误码*/
  errorCode?: string
  /**错误原因*/
  errorReason?: string
  /**提单结果*/
  resultFlag?: boolean
  /**上游返回的交易码*/
  tradeResultCode?: string
  /**场景
      0:小程序
      1：视频号*/
  scene?: string
  /**订单对象*/
  order?: OrderVO
  /**收银台业务对象
      调用收银台接口成功时，返回该对象*/
  cashier?: CashierResultVO
  /**提单卡单后的提示信息
   主要针对弹层提示进行数据*/
  submitOrderPromptVO?: SubmitOrderPromptVO
  /**无货商品列表*/
  noStockSkuList?: BalanceSkuVO[]
  /**统一供销商无货*/
  commonStockSkuList?: BalanceSkuVO[]
  /**促销限pin商品集合-需要返回购物车操作*/
  scaleSkuInfoList?: BalanceSkuVO[]
  /**不支持销售卡单商品集合*/
  noAvailableSkuList?: BalanceSkuVO[]
  /**价格升高卡单商品集合-需要返回购物车操作
      balanceSkuVO.jdPrice 当前价格
      balanceSkuVO.priceDisparities 价格升高差值*/
  priceChangeList?: BalanceSkuVO[]
  /**商品限购*/
  purchaseList?: BalancePurchaseVO[]
  /**不支持的服务家商品集合*/
  noSupportHomeServiceSkuList?: BalanceSkuVO[]
  /**药品疫情登记链接*/
  jumpUrl?: string
  /**药品疫情未登记sku*/
  medicineSkuList?: BalanceSkuVO[]
  /**appId,用户客户端支付(待确认其他功能)*/
  appId?: string
  /**缺失包装服务*/
  packagingFeeServiceVos?: PackagingFeeServiceVO[]
  /**收银台信息*/
  balanceCashierDesk?: BalanceCashierDeskVO
  /**全球购实名信息*/
  weChatInternationalAuthInfo?: WeChatInternationalAuthInfoVO
  /**描述：门店快送同步微信订单对象*/
  submitWechatOrderVO?: SubmitWechatOrderVO
  /**购买完成页URI*/
  buyFinishUri?: string
  /**微信先用后付提单返回数据*/
  weChatCreditPay?: WeChatCreditPayVO
  /**以旧换新资质失效商品集合*/
  qualificationExpireSkuList?: BalanceSkuVO[]
}

export interface BalanceSkuPriceIconVO {
  /**priceIcon
      形如：//img30.360buyimg.com/jdmonitor/jfs/t1/182848/13/10920/3014/60d459d0E8abc7725/df9aa11880e72ed7.png*/
  priceIcon?: string
  /**priceColor*/
  priceColor?: string
  /**priceContentMsg*/
  priceContentMsg?: string[]
  /**priceResourceCode*/
  priceResourceCode?: string
  /**priceDec*/
  priceDec?: string
}

//卡单促销信息
export interface BlockSkuVO {
  /** 商品id */
  id: number
  /** 购物车Uuid */
  cartUuid: string
  imgUrl: string
  /** 名称 */
  name: string
  buyNum: number
  promotion?: Promotion
  skuMark: string
  useUuid: boolean
  uuid: string
  venderId: number
  /** 当前价格 */
  jdPrice: string
  /** 价格升高差值 */
  priceDisparities: string
  /** 原始价格 */
  pricePrevious: string
  /** 限购文案信息 */
  limitReason: string
  /** 当前商品可用文案 */
  showText: string
  /** 当前商品数量文案 */
  pastText: string
  /** 购买历史订单 */
  historyOrderList?: Array<BlockHistoryOrderVO>
  /** 最大购买数量 */
  canMaxBuyNum?: number
  /** 商品卡单信息 */
  blockExtMsg?: string
  /** 商品跳转信息 */
  linkVO?: {
    /** 跳转类型 10001：小程序商详 2:M商详 */
    linkType?: string
    /** 跳转链接 */
    linkUrl?: string
    /** 跳转文案 */
    linkText?: string
  }
  /** 类型 */
  type?: number
  /** 是否包含不支持的服务：true-包含 */
  containsNoSupportService?: boolean
  /** 不支持的服务列表 */
  noSupportServiceList?: Array<NoSupportService>
}

/** 不支持的服务列表 */
export interface NoSupportService extends BlockSkuVO {
  /** 价格和数量拼接串 */
  priceAndNumText?: string
}

export interface CashierResultVO {
  /**0为正常*/
  code?: string
  /**（老收银台接口）
      移动支付单*/
  payId?: string
  /**（老收银台接口）
      收银台首页地址*/
  url?: string
  /**跳转收银台router协议透传参数， 透传给前端*/
  routerParamMap?: string
  /**收银台错误码*/
  errorCode?: string
}

//微信先用后付提单返回数据
export interface WeChatCreditPayVO {
  /**订单标识，便于区分特殊订单提单后相关交互判断*/
  orderFlag?: number
  /**微信支付 payId*/
  payId?: string
  /**微信唤起确认参数*/
  extraData?: Map<string, string>
}

//BalanceSkuVO class
export interface BalanceSkuVO {
  /**商品id*/
  id?: number
  /**venderId*/
  venderId?: number
  /**商品类型*/
  type?: number
  /**uuid*/
  uuid?: string
  /**imgUrl*/
  imgUrl?: string
  /**商品图片底部浮层*/
  skuMark?: string
  /**图标*/
  titleIcon?: string
  /**非主商品下发标签 [赠品]  高级项：[粉丝专享赠品][品牌会员赠品][PLUS专享赠品]*/
  titleTag?: string
  /**name*/
  name?: string
  /**重量*/
  weight?: number
  /**color*/
  color?: string
  /**size*/
  size?: string
  /**红字价*/
  jdPrice?: string
  /**预售到手价*/
  deliveryPrice?: string
  /**预售到手价文案*/
  deliveryPriceText?: string
  /**价格升高前的价格*/
  pricePrevious?: string
  /**价格差*/
  priceDisparities?: string
  /**当前选中落地配服务单件的价格*/
  deliveryServicePrice?: string
  /**buyNum*/
  buyNum?: number
  /**商品库存状态 1-有货，0-无货。*/
  stockState?: number
  /**购物车Uuid*/
  cartUuid?: string
  /**0：非生鲜 1：生鲜*/
  fresh?: number
  /**主商品的附属vo type区分 [赠品]*/
  affiliatedList?: BalanceSkuVO[]
  /**买一赠一赠品楼层*/
  oneByOneVo?: BalanceSkuVO
  /**titleb*/
  subTitleFlag?: string
  /**买一赠一 赠品
      JSON*/
  giftStr?: BalanceOneByOne[]
  /**送装服务*/
  deliveryInstallationServiceList?: BalanceSkuVO[]
  /**主商品的附属vo [延保]，[服务]，[红盒子] [门店]*/
  serviceList?: BalanceSkuVO[]
  /**为赠品时，赠品无货，是否可单独购买主品*/
  canBuyWithoutGift?: boolean
  /**商品打标*/
  skuIconList?: BalanceSkuIconVO[]
  /**是否是京东国际商品*/
  isOverseaPurchase?: boolean
  /**全球购Code*/
  overseaPurchaseCode?: string
  /**FBP商品*/
  isFBP?: boolean
  /**FCS商品*/
  isFCS?: boolean
  /**BalancePromotionVO class*/
  promotion?: BalancePromotionVO
  /**促销限购最大数量*/
  maxBuyNum?: number
  /**促销限购最小数量*/
  minBuyNum?: number
  /**促销id*/
  promoId?: number
  /**设置是否loc*/
  loc?: boolean
  /**loc类型*/
  locType?: number
  /**到店店铺id*/
  locShopId?: string
  /**字段仅给无货剔除用-是否用uuid*/
  useUuid?: boolean
  /**一小时达*/
  anHour?: boolean
  /**厂家直送*/
  factoryDirect?: boolean
  /**服务id（目前：送装）*/
  serviceItemId?: string
  /**服务模板id（目前：送装）*/
  templateId?: number
  /**价格标签*/
  priceIcon?: string
  /**一级品类id*/
  firstCategoryId?: number
  /**二级品类id*/
  secondCategoryId?: number
  /**三级品类id*/
  thirdCategoryId?: number
  /**优惠金额*/
  shareCoupon?: string
  /**extMap*/
  extMap?: Map<string, string>
  /**如果是购买了5g号卡，则显示选中的号卡*/
  selectCard?: string
  /**是否是预售尾款商品*/
  presaleBalanceFlag?: boolean
  /**已付定金*/
  presaleDeposit?: string
  /**应付尾款*/
  endPaymentPresale?: string
  /**预售定金膨胀*/
  depositExpandDiscount?: string
  /**预售尾款立减*/
  endPaymentPresaleDiscount?: string
  /**价保文案*/
  priceProtectMsg?: string
  /**是否隐藏尾款信息*/
  isHideRealPrice?: boolean
  /**换购、选赠、加价购*/
  optionalGiftsTag?: string
  /**是否是赠品，与 optionalGiftsTag 字段关联
      1:为赠品*/
  giftTag?: string
  /**体积*/
  volume?: number
  /**sku是否包含落地配服务标识*/
  isFreeload?: string
  /**xx class*/
  balanceSkuPriceIcon?: BalanceSkuPriceIconVO
  /**疫情登记标识*/
  xxdjsp?: string
  /**是否otc*/
  isOtc?: boolean
  /**是否是处方药*/
  isPrescriptCat?: boolean
  /**中台返回的原生venderId*/
  originVenderId?: string
  /**店铺核心类型*/
  venderColType?: number
  /**店铺类型*/
  venderType?: number
  /**是否用京券*/
  canUseJQ?: boolean
  /**是否用东券*/
  canUseDQ?: boolean
  /**山姆商品*/
  samGoods?: boolean
  /**shopid*/
  shopId?: string
  /**用来判断95折*/
  parallelDiscount?: string
  /**spuId*/
  spuId?: string
  /**判断是否是pop*/
  checkPop?: boolean
  /**门店id--提单卡单用*/
  storeId?: string
  /**知识虚拟服务类型：1-直充和2-非直充*/
  xnywms?: string
  /**商家经营主体类型 0为企业工商户、1为个体工商户、2为自然人， 6为海外主体*/
  companyType?: number
  /**是否京喜自营商品
      0或null：否；
      1：是*/
  isJxzy?: number
  /**商品合作类型*/
  colType?: string
  /**业务身份*/
  venderBizId?: string
  /**是否是到店商品*/
  sameCityToShop?: boolean
  /**同城到店直充是否显示手机号*/
  supportPhysicalStoreMobile?: boolean
  /**是否补贴价*/
  isSubsidyPrice?: boolean
  /**是否屏蔽修改数量按钮*/
  skuNumFixed?: boolean
  /**短链专属--是否支持修改*/
  isEdited?: boolean
  /**备件库标识*/
  backupStorage?: boolean
  /**结算链接下发对象  主要 实名认证  忘记密码及后续需结算页链接下发*/
  linkVO?: LinkVO
  /**通用标识，2是备件库*/
  usedSPLX?: string
  /**综合所有子品行的平均促销价格（所有拆行商品的price-discount之和/总数量）*/
  calculateAvgPrice?: string
  /**以旧换新*/
  balanceOldToNewVO?: BalanceOldToNewVO
  /**sku维度卡单扩展信息*/
  blockExtMsg?: string
  /**标识新京喜亮证*/
  gxlysplx?: string
  /**productTags判断前置仓标识*/
  productTags?: string[]
  /**虚拟-京东服务+
      jdfwj=2*/
  jdfwj?: string
  /**SKU维度超市卡返卡需求——主品下返卡相关信息*/
  skuOtherList?: BalanceSkuVO[]
  /**主品包含不支持履约服务信息*/
  containsNoSupportService?: boolean
  /**不支持服务sku列表*/
  noSupportServiceList?: BalanceSkuItem[]
  /**轻餐饮副标题，灰色字段*/
  subQcyTitle?: string
  /**是否是轻餐饮商品*/
  isQcy?: boolean
  /**是否到店*/
  deliveryToStore?: boolean
  /**产业带商品*/
  subSkuVOList?: BalanceSubSkuVO[]
  /**是否是plus会籍卡商品*/
  plusXnk?: boolean
  /**是否当前sku是否享受综合国补（以旧换新国补、购新立减国补、国补消费券）*/
  hasMixGovSubsidy?: boolean
  /**当前sku是否已经选中不支持国补的时效*/
  selectedInvalidGovSubsidyDate?: boolean
  /**是否满足超级18促销*/
  superEighteen?: boolean
  /**展示扩展属性*/
  ShowExtMap?: Map<string, string>
  /**是否一品多行的子品*/
  hotSubItem?: boolean
  /**存在一品多行拆行情况下，对应主品行的商品数量*/
  hotMainItemBuyNum?: number
  /**以旧换新类型，透传结算信息*/
  oldToNewUniteType?: number
  /**是否选中以旧换新*/
  oldToNewSelected?: boolean
}

//BalancePurchaseVO class
export interface BalancePurchaseVO {
  /**商品限购*/
  purchaseSkuList?: BlockSkuVO[]
  /**限购原因*/
  errorMsg?: string
}

//BalanceDonatedCouponVO class
export interface BalanceDonatedCouponVO {
  /**优惠券类型：0-京卷 1-东卷*/
  couponType?: number
  /**赠券的额度*/
  couponQuota?: number
  /**赠券广告语*/
  couponAd?: string
  /**限品类优惠券key*/
  key?: string
  /**是否是限品类优惠券*/
  limitCateGoryCoupon: boolean
}

//全球购实名信息
export interface WeChatInternationalAuthInfoVO {
  /**是否认证通过*/
  authFlag?: boolean
  /**标题*/
  title?: string
  /**内容*/
  content?: string[]
  /**按钮文案*/
  confirmText?: string
  /**tips*/
  tipTxt?: string
}

//描述：包装服务
export interface PackagingFeeServiceVO {
  /**店铺id*/
  venderId?: string
  /**唯一标识*/
  uuid?: string
}

//BalanceSkuItem class
export interface BalanceSkuItem {
  /**skuId*/
  skuId?: string
  /**商品名*/
  name?: string
  /**主品的sku uuid
      对应融合是 mainSkuUuid*/
  skuUuid?: string
  /**虚拟组套虚商品id*/
  vskuId?: string
  /**套装id*/
  suitPromoId?: string
  /**总价促销套装id*/
  manSuitPromoId?: string
  /**商品类型 1*/
  itemType?: number
  /**数量*/
  num?: number
  /**定制*/
  customAttrId?: string
  /**号卡手机号--如果是合约机，字段则对应phoneNum*/
  cartPn?: string
  /**业务流水号--对应深圳ptOrderId*/
  cartPbn?: string
  /**京品式商品标识*/
  cartJps?: string
  /**合约机签名校验--sign*/
  cartPag?: string
  /**号卡或者套餐skuid*/
  treatySkuId?: string
  /**业务流水号*/
  ptOrderId?: string
  /**延保sku列表*/
  ybItemsList?: BalanceSkuItem[]
  /**服务+sku列表*/
  jdHomeServicesList?: BalanceSkuItem[]
  /**3c原厂服务sku列表*/
  factoryServicesList?: BalanceSkuItem[]
  /**京东零售服务sku列表*/
  retailServicesList?: BalanceSkuItem[]
  /**不支持服务sku列表*/
  noSupportServiceList?: BalanceSkuItem[]
  /**所选赠品sku列表*/
  giftList?: BalanceSkuItem[]
  /**落地配服务id和汽车增值服务id*/
  deliveryItemId?: string
  /**loc门店id*/
  storeId?: string
  /**预售订单号*/
  psoOrderId?: string
  /**预售商品uuid*/
  psoUuid?: string
  /**无货移除cartUuid*/
  cartUuid?: string
  /**赠品skuid(前端不方便使用giftList，嵌套层级太复杂，这里把无货移除赠品平铺开，服务端循环主品列表判断赠品)*/
  giftSkuId?: string
  /**订单返现标识*/
  orderCashBack?: boolean
  /**购物车参数扩展点*/
  extFlag?: BalanceSkuItemExtFlag
  /**主品服务地址校验，当前配送地址是否包含不支持服务*/
  containsNoSupportService?: boolean
  /**商品价格 * 数量*/
  priceAndNumText?: string
  /**养车助手loc门店id*/
  locShopId?: string
  /**养车助手车型id*/
  carType?: string
  /**养车助手服务市场服务ID*/
  carServiceId?: string
  /**京保养sku选择的门店ID*/
  carShopid?: string
  /**养车助手关系ID*/
  carRelationId?: string
  /**养车助手专属渠道促销 ID （exclusiveBusiness）*/
  carPromotid?: string
}

//订单对象
export interface OrderVO {
  /**orderId*/
  orderId?: number
  /**订单应付金额*/
  orderPrice?: string
  /**订单类型*/
  orderType?: number
  /**订单类型编码*/
  orderTypeCode?: number
  /**预售定金阶段-定金*/
  factEarnest?: string
  /**预售定金阶段-尾款*/
  factEndPayment?: string
  /**当前订单得主支付方式*/
  paymentId?: number
  /**当前订单得子支付方式*/
  subPaymentId?: number
}

//BalancePromotionVO class
export interface BalancePromotionVO {
  /**促销Id*/
  promoId?: number
  /**促销类型*/
  promoType?: number
  /**京东价,优惠前*/
  price?: string
  /**优惠金额*/
  promotionDiscount?: string
  /**返现*/
  cashBack?: string
  /**赠送优惠券列表*/
  donatedCouponList?: BalanceDonatedCouponVO[]
  /**赠送积分*/
  giftScore?: number
  /**满返满减返现 （满返==满减）*/
  manfanMoney?: string
  /**满赠套装加价购金额*/
  addMoney?: string
  /**满赠套装需满金额*/
  needMoney?: string
  /**满减返现需要的商品数量*/
  needNum?: number
  /**免的商品数量*/
  deliverNum?: number
  /**京豆优惠购商品所需要支付的京豆数量*/
  jdBeanNum?: number
  /**最大数限制，注：用于超过最大数时只计算最大数的优惠，超过最大数的数量商品为原价(mnl)*/
  maxNumLimit?: number
  /**最大数限制时：直降优惠金额，当超过最大购买数时有这个属性值(dl)*/
  discountLimit?: string
  /**跨店铺满减需求，已购满多少件*/
  moneyDeliverNum?: number
  /**促销开始时间*/
  beginTime?: number
  /**促销结束时间*/
  endTime?: number
  /**extProp*/
  extProp?: Map<string, string>
  /**promoTags*/
  promoTags?: number[]
  /**redPacket*/
  redPacket?: string
  /**promotionTags*/
  promotionTags?: number[]
  /**multiPromoTags*/
  multiPromoTags?: number[]
  /**promoPriceTypeList*/
  promoPriceTypeList?: number[]
  /**百补促销金额：补贴优惠金额*/
  subsidy?: string
  /**百补促销标示：百亿补贴促销类型：0或null-无百亿补贴；1-包含官方补贴；2-非官方补贴*/
  subsidyType?: string
}

//  买一赠一赠品
export interface BalanceOneByOne {
  /**商品id*/
  skuId?: number
  /**buyNum*/
  num?: number
}

//描述：门店快送同步微信订单对象
export interface SubmitWechatOrderVO {
  /**requireOrder*/
  requireOrder?: number
  /**requiredFundType*/
  requiredFundType?: number
  /**跟踪id*/
  traceId?: string
  /**最终结果*/
  result?: boolean
  /**提示信息*/
  message?: string
}

//卡单历史卡单 结算中台下发数据使用
export interface BlockHistoryOrderVO {
  /**订单列表*/
  skuList?: BlockSkuVO[]
  /**订单id*/
  orderId?: string
  /**下单时间*/
  orderTime?: number
}

//结算链接下发对象  主要 实名认证  忘记密码及后续需结算页链接下发
export interface LinkVO {
  /**链接类型*/
  linkType?: string
  /**链接url*/
  linkUrl?: string
  /**跳转文案*/
  linkText?: string
  /**备份url*/
  backUrl?: string
}

// 商品打标
export interface BalanceSkuIconVO {
  /**描述文案*/
  desc?: string
  /**url url 不为空展示url*/
  url?: string
  /**标签标识*/
  id?: string
}

//收银台信息
export interface BalanceCashierDeskVO {
  /**支付页面地址*/
  url?: string
  /**支付信息id*/
  payId?: string
  /**扩展信息*/
  extMap?: Map<string, any>
}

//以旧换新
export interface BalanceOldToNewVO {
  /**资质Id*/
  qualiId?: string
  /**是否支持以旧换新*/
  isOldToNew?: boolean
  /**是否可选*/
  isOldToNewFlag?: boolean
  /**是否选中*/
  isOldToNewSelected?: boolean
  /**换新方式*/
  renewType?: string
  /**交易方式*/
  tradeType?: string
  /**用户期望上门时间*/
  userExpectedDeliveryTime?: string
  /**折扣信息*/
  oldToNewDiscount?: string
  /**标签*/
  tags?: string[]
  /**标题*/
  title?: string
  /**以旧换新H5跳转链接*/
  oldToNewJumpUrl?: string
}

export interface BalanceSubSkuVO {
  /**数量*/
  buyNum: number
  /**skuUuid*/
  id: number
  /**skuUuid*/
  uuid?: string
  /**门店*/
  cartUuid?: string
}

export interface SubmitOrderPromptVO {
  /**浮层类型  1.商品移除 2.促销移除 3.商品限购  4.疫情登记 5.数据展示（skuList） 6.送装服务 7.浮层展示 8.通用逻辑引导*/
  promptType?: number
  /**头部信息*/
  title?: string
  /**描述*/
  context?: string
  /**提示内容*/
  content?: string
  /**确认按钮文字*/
  confirmText?: string
  /**取消按钮文字*/
  cancelText?: string
  /**"back" || "close"*/
  cancelType?: string
  /**引导url*/
  jumpUrl?: string
  /**展示商品记录*/
  skuList?: BalanceSkuVO[]
  /**缺失包装服务*/
  packagingFeeServiceVos?: PackagingFeeServiceVO[]
  /**商品限购*/
  purchaseList?: BalancePurchaseVO[]
  /**埋点类型 曝光*/
  buried?: string
  /**埋点类型 确认点击*/
  buriedConfirm?: string
  /**埋点类型  取消点击*/
  buriedCancel?: string
  /**卡单弹层中提示文案*/
  tips?: string
}

//购物车参数扩展点
export interface BalanceSkuItemExtFlag {
  /**deliveryMode  1=支持履约可选优先门店 2=支持履约可选优先大仓*/
  deliveryMode?: string
}
