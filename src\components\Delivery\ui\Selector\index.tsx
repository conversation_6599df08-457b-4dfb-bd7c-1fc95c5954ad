// import Item, {ItemProps} from "@app/components/Delivery/ui/Selector/Item";
// import {useEffect, useReducer, useRef} from "react";
// import styles from './index.module.scss';
// import Main from "@app/components/Delivery/mixed/SelectBody";

// type Action = {
//     type: string,
//     payload?: any,
// }

// type State = {
//     delivery: ItemProps,
//     install: ItemProps,
//     input: ItemProps
// }

// const initialState: State = {
//         delivery: {
//             isOpen: false,
//             items: [
//                 {text: '配送', type: 'none', data: {}},
//                 {text: '京东快递3月25日 [周五]', type: 'none', data: {}},
//                 {text: '09:00-15:01', type: 'none', data: {}},
//             ]
//         },
//         install:{
//             isOpen: false,
//             items: [
//                 {text: '安装', type: 'none', data: {}},
//                 {text: '京东快递3月25日 [周五]', type: 'none', data: {}},
//                 {text: '09:00-15:01', type: 'none', data: {}},
//             ]
//         },
//         input: {
//             isOpen: false,
//             items: [
//                 {text: '留言', type: 'none', data: {}},
//                 {text: '建议先与商家沟通确认', type: 'gray', data: {}},
//             ]
//         }
// }

// const reducer = (state: State, action: Action) => {
//     const newState = {...state}
//     switch (action.type) {
//         case 'click':
//             const type = action.payload
//             newState[type].isOpen = !newState[type].isOpen
//             if (newState[type].isOpen) {
//                 for (const key in newState) {
//                     if (newState.hasOwnProperty(key)) { // 只遍历自身属性，不包含继承属性
//                         if (key !== type) {
//                             newState[key].isOpen = false
//                         }
//                     }
//                 }
//             }
//             return newState
//         case 'cancel':
//             for (const key in newState) {
//                 if (newState.hasOwnProperty(key)) { // 只遍历自身属性，不包含继承属性
//                     newState[key].isOpen = false
//                 }
//             }
//             return newState
//         case 'select': {
//             const {type, value} = action.payload
//             if (type !== 'input') {
//                 newState[type].items[1].text = value
//                 delete newState[type].items[2]
//             }else {
//                 newState[type].items[1].text = value
//             }
//             return newState
//         }
//         default:
//             return newState
//     }
// }

// type Props = {
//     delivery?: boolean,
//     install?: boolean,
//     input?: boolean
// }

// export default (props: Props) => {

//     const [state, dispatch] = useReducer<typeof reducer, State>(reducer, initialState)
//     const getProps = (type) => {
//         return {onCancel: ()=>dispatch({type: 'cancel', payload: type}), onSure: (v) => dispatch({type: 'select', payload: {type, value: v}})}
//     }
//     const wrapperRef = useRef<HTMLDivElement | null>(null);

//     useEffect(() => {
//         const handleClickOutside = (event: MouseEvent) => {
//             if (wrapperRef.current &&!wrapperRef.current.contains(event.target as Node)) {
//                 dispatch({ type: 'cancel' });
//             }
//         };
//         document.addEventListener('mousedown', handleClickOutside);
//         return () => {
//             document.removeEventListener('mousedown', handleClickOutside);
//         };
//     }, [dispatch]);

//     return (
//         <div className={styles.selector_wrap} ref={wrapperRef}>
//             {
//                 Object.keys(state).map((key) => {
//                     if (props[key]) {
//                         const item = state[key];
//                         return <div className={styles.item} key={key}>
//                             <Item isOpen={item.isOpen} items={item.items} onClick={() => dispatch({type: 'click', payload: key})} />
//                             <div className={""} style={{zIndex:1, position: "absolute", top : "40px",display: item.isOpen ? "block" : "none"}}>
//                                 <Main state={state} type={key as 'delivery'} {...getProps(key)} isShow={item.isOpen} />
//                             </div>
//                         </div>
//                     }
//                 })
//             }
//         </div>

//     )
// }
