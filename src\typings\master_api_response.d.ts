/**
 * 根据开发迭代进度对类型声明内容新增、移除或修改
 */
export type MasterApiResponse = Partial<{
  /** 错误码 */
  code: `${number}`
  /** 错误信息 */
  message: string
  /** 服务器时间戳 */
  timestamp: number
  /** 响应数据 */
  body: Body
  traceId: string
  requestId: string
}>

export type Body = Partial<{
  /** 试金石 */
  abDataResult?: AbDataResult
  /** 提示信息 */
  tipsVOList: TipsVolist[]
  /** 结算信息 */
  balanceTotal: BalanceTotal
  balanceAddress: BalanceAddressDo
  matchAddress: number
  combinationPaymentVO: CombinationPaymentVo
  /** 支付方式 */
  paymentVOList: BalancePayment[]
  /** 协议列表 */
  licenseList: LicenseList[]
  /** 扩展信息 */
  balanceExt: BalanceExt
  /** 主商品列表 */
  mainSkuIdList: Array<number>
  /** 运费详情信息 */
  balanceVenderFreightVoList: BalanceVenderFreight[]
  balanceVirtualAssetsVO: BalanceVirtualAssetsVO
  couponVOList: CouponVolist[]
  /** 店铺包裹列表 */
  balanceVendorBundleList: BalanceVendorBundleListType
  balanceSkuPriceDesc?: BalanceSkuPriceDescType
  balanceInvoiceDesc: BalanceInvoiceDesc
  pageLinkConfig?: PageLinkConfigType
  balanceUser: BalanceUser
  jdBeanVO: JdBeanVo
  redPacketResultVO: RedPacketResultVo
  virtualPropertyVO: VirtualPropertyVO
  newRemarkVOMap: NewRemarkVOMap
  balanceShopInfos?: BalanceShopInfosType
  balanceAdditionalGiftVo: BalanceAdditionalGiftVoType
  /** 共计几件商品 */
  sumBuyNum: number
  /** 规则文案 */
  rules: Record<string, string[]>
  /** 预售定金信息 */
  balancePresaleVO?: BalancePresale
  /** 预售尾款类型（0：无预售尾款，1：全部是预售尾款，2：混单） */
  presaleBalanceStatus?: number
  balancePurchaseCasual: unknown
  areaIdFromCookie?: string
  balanceSkuMdVO?: { skuId: number; skuNum: number; skuType: number }[]
}>

interface AbDataResult {
  /** 试金石 */
  tagsPool?: string[]
}

// 页面提示信息-后台配置下发的字段
export type BalanceSkuPriceDescType = {
  /** 价格说明 */
  bodyInformation: string
  /** 客服信息 */
  contactInformation: string
}

// 页面跳转地址-后台配置下发的字段
export type PageLinkConfigType = {
  addressManageLink: string
  /** 返回购物车 */
  backToCartLink: string
  jingDouDescLink: string
  realNameAuthentication: string
}

export type BalanceAdditionalGiftVoType = {
  additionalGiftList?: AdditionalGiftListType
  type: string
}

export type AdditionalGiftListType = BalanceSkuType[]

export type BalanceShopInfosType = Record<string, BalanceShopType> // 门店信息

export type BalanceShopType = {
  /** 店铺ID */
  id: number
  /** 店铺名 */
  name: string
}

export interface NewRemarkVOMap {
  [key: string]: NewRemarkVOMapItem
}

export interface NewRemarkVOMapItem {
  venderId: string
  idType: number
  maxLength: number
  show: boolean
}

/**
 * TipsVolist
 * @description 提示信息
 */
export interface TipsVolist {
  /** 地址楼层1; 发票楼层2; 循环包装(全部商品支持)31; 循环包装(部分商品支持)32；顶部提示4；发票楼层文案提示5；腰部提示6； 密码提示9； 国补相关提示10*/
  type: 1 | 2 | 31 | 32 | 4 | 5 | 6 | 9 | 10
  tip?: string
  url?: string
  /** 扩展字段 */
  extMap?: Record<string, string>
}

/**
 * BalanceTotalVO
 * @description 合计
 */
export interface BalanceTotal {
  /** 总重量 */
  allWeight?: string
  /** 商品总价 */
  itemTotalPrice?: string
  /** 实际下单金额 */
  factPrice?: string
  /** 预售商品信息 */
  presaleSkuVOList?: Array<BalancePresaleSku>
  /** 礼金明细 */
  giftCashDetailList?: Array<GiftCashDetail>
  /** 付款详情明细 */
  priceInfoList: PriceInfoList[]
  /** 优惠总金额 */
  virtualDiscount: string
  /** 优惠浪费金额 */
  allWasteAmount?: `${number}`
  govPayTitle?: string
  govPayDesc?: string
  /** 国补提示*/
  govSubsidTips?: string
  /** 未领取国补资格跳转链接*/
  unHitGovSubsidySkuJumpUrl?: string
}

/**
 * PriceInfoList
 * @description 付款详情明细
 */
export interface PriceInfoList {
  /** 价格名称 */
  name: string
  /** 价格值 */
  value: string
  /** 是否标红展示 */
  mark?: boolean
  /** 提示信息 */
  info?: string
  /** 展开提示信息 */
  summary?: string
  /** 展开商品信息 */
  supportSkus?: {
    id: number
    name: string
    imgUrl: string
  }[]
  /**  */
  type: number
  /** 礼金 */
  contentList?: string[]
}

export interface BalanceInvoiceDesc {
  /** 是否包含京东国际商品(维护但弃用) */
  hasInternationalSku: boolean
  /** 京东国际商品文案提示(维护但弃用) */
  internationalInformation: string
  /** 是否支持开发票 */
  supportInvoice: boolean
  /** 发票楼层文案 */
  bodyInformation: string
  /** 是否展示不支持开发票文案 */
  showUnInvoiceTop: boolean
  /** 不支持开发票文案 */
  unInvoiceTop: string
  /** 是否显示发票楼层 */
  show: boolean
  /** 小i弹出提示 */
  promptInformation: string
  /** 是否展示纳税人识别号未填提示 */
  isShowInvoiceCodeTip: boolean
  /** 是否展示默认发票 */
  defaultSelected: boolean
  /** 默认发票链接 */
  defaultSelectedLink: string
  /** 默认发票标题 */
  selectedInvoiceTitle: string
  /** 默认发票内容 */
  selectedInvoiceContent: string
  /** 发票状态 */
  useInvoiceStatus: number
}

export interface BalancePresaleSku {
  /** 预售定金 */
  deposit: string
  /** 预售尾款 */
  endPayment: string
  /** 预售总价 */
  price: string
  /** 优惠金额 */
  discount: string
  /** skuId */
  skuId: number
  /** 商品名称 */
  skuName: string
  /** 商品数量 */
  skuNum: number
  /** 商品图片路径 */
  skuImgUrl: string
  /** 商家id */
  venderId: string
  /** 店铺name */
  venderName: string
}

export interface GiftCashDetail {
  /** 礼金分类标识 */
  giftCashCode: string
  /** 礼金名称 */
  giftCashName: string
  /** 礼金金额 */
  giftCashDisCount: number
}

/**
 * BalancePayment
 * @description 支付方式
 */
export interface BalancePayment {
  /** 支付类型Id 1:货到付款 4:在线支付 5:公司转账 20:先用后付 */
  paymentId: '1' | '4' | '5' | '20'
  /** 支付方式名称 */
  paymentName: string
  /** 子支付ID：1:信用试，2:微信先用后付 */
  subPaymentId?: '1' | '2'
  /** 微信支付工具id(1.微信支付 2.云闪付) */
  wxPayToolsId?: '1' | '2'
  /** 是否选中 */
  selected: boolean
  /** 支持此支付方式的skuId */
  supSkuIds?: Array<string>
  /** 是否支持此种支付方式 */
  isSupported: boolean
  /** 是否限制使用 */
  isLimit: boolean
  /** 排序 */
  sortNum: number
  subPaymentName?: string
  /** 是否支持合并支付 */
  combinedPayment: boolean
  /** 支持此支付方式的sku */
  mainSupportSkus?: ShipmentSku[]
  /** 不支持此支付方式的sku */
  subSupportSkus?: ShipmentSku[]
}

/** 用户协议列表 */
export interface LicenseList {
  /** 链接后文本 */
  after: string
  /** 链接文本 */
  link: string
  /** 协议文本 */
  text: string
  /** 协议地址 */
  url: string
  /** 是否显示复选框,默认为true */
  showCheckbox: boolean
  /** 协议的唯一标识 */
  id: string
  /** 默认选中 */
  checked?: boolean
  /** 是否必须 */
  required?: boolean
  /** 统一文案链接前 */
  frontSummaryText?: string
  /** 统一文案链接后 */
  afterSummaryText?: string
}

export interface BalanceExt {
  exchangeInfo?: string
  usedGovSubsidyToast: unknown
}
export interface BalancePresale {
  /** 预售定金 */
  earnest: string
  /** 预售尾款 */
  finalPay: string
  /** 预售全款 */
  balancePay: string
  /**
   * 预售总金额
   * 预售全款+膨胀金/尾款立减金(客户端使用这个字段展示)
   */
  presaleTotalAmount: string
  /** 用于接收预售手机号 */
  userMobile: string
  /** 用于接收预售手机号-密文 */
  realMobile: string
  /**
   * 预售分阶段支付类型，
   * 1-一阶梯全款（仅全款支付）
   * 2-一阶梯可选（全款和定金选择支付）
   * 3-三阶梯定金（仅定金支付））
   * 5-定金阶段
   */
  payStepType: string
  /** 预售可退定金金额
   * refundDeposit = "0" 预售活动定金不可退
   * refundDeposit = "1" 预售活动定金可退
   * 若refundDeposit字段为null，按refundDeposit = "0" 处理
   */
  refundDeposit: string
  /** 尾款支付开始时间（时间戳） */
  endPaymentStartTime: number
  /** 尾款支付结束时间（时间戳） */
  endPaymentEndTime: number
  /**
   * 实际使用分阶段支付类型
   * 1、全款
   * 2、定金
   * */
  factPayType: number
  /**
   * 预售优惠类型：
   * 1定金膨胀
   * 2尾款立减
   * 客户端要根据这个字段预售优惠类型，膨胀金和立减不能同时存在
   */
  presaleDiscountType: number
  /** 扩展字段 */
  extMap?: Record<string, string>
  /** 定金膨胀金额 */
  amountDeposit: string
  /** 定金膨胀文案 */
  amountDepositMsg: string
  /** 是否隐藏价格(用于无定价预售) */
  isHideRealPrice: boolean
  /** 尾款立减 */
  expAmount: string
  /** 尾款立减文案 */
  expAmountMsg: string
  /**
   * 定金是否含税费、运费标识
   * 1：含运费
   * 2：含税费
   * 3：含运费、税费
   * */
  isContainsFee?: 1 | 2 | 3
  /** 定金是否可退 */
  presaleCanRefund?: boolean
  /**
   * 其他费用
   */
  preSaleTaxFees?: string
}

export interface BalanceFreightDetail {
  /** 运费明细类型 */
  freightType: number
  /** 运费明细名称 */
  freightName: string
  /** 运费金额 */
  freight: number
  /** 续重运费超重信息 */
  otherOverWeightMsg: string
  /** 续重运费超重信息 */
  freshOverWeightMsg: string
  /** 计费重量 */
  weight: string
}

export interface ShipmentSku {
  id: number
  name: string
  imgUrl: string
}

export interface BalanceVenderFreight {
  /** 店铺id */
  venderId: string
  /** 店铺名称 */
  venderName: string
  /** 店铺总运费金额 */
  venderFreight: number
  /** 店铺运费明细列表 */
  freightDetailVoList?: Array<BalanceFreightDetail>
  /** 商品列表 */
  shipmentSkuVoList?: Array<ShipmentSku>
}

// 店铺包裹
export type BalanceVendorBundleType = {
  isSelfVendor: boolean // 是否自营
  venderName?: string // 店铺名称
  venderId: number // 店铺ID
  bundleList: BundleListType // 包裹列表
  jdcombineStoreId?: number
  storeId?: number
}

interface ComShipmentTypeVO {
  shipmentTypeName: string
  shipmentType: number
  selected: true
  locShipmentMsg?: string
  honorFreight?: number
}

interface DeliveryInfoVO {
  promiseList: PromiseItem[]
  shopClosing: boolean
  secondDelivery: boolean
}

interface PromiseItem {
  promiseTypeName: string //配送类型
  promiseType: number
  promiseDate: string
  promiseMsg: string //配送时效描述
  deliveryFloorTypeName: string
  promiseTip: string
  weekString?: string
  timeOffset?: number
  venderSelfDeliveryStoreInfo?: VenderSelfDeliveryStoreInfo
  pickId?: number
  pickName?: string
  pickAddress?: string
}
interface VenderSelfDeliveryStoreInfo {
  businessHours: string
  distance: string
  idArea: number
  idCity: number
  idProvince: number
  idTown: number
  latitude: number
  longitude: number
  name: string
  recentlyMark: number
  stockStatus: number
  storeAddress: string
  storeId: number
  storeMark: number
  vendSource: string
  venderId: number
  venderStoreStockTab: number
  warehouseId: number
}

export type BalanceVendorBundleListType = BalanceVendorBundleType[] // 店铺包裹列表

export interface OverseasPickInfo {
  shipmentType: string
  batchId: number
  selected: boolean
  pickId: number
  pickName: string
  pickAddress: string
  siteType: number
  longitude: number
  latitude: number
  businessHoursStart: string
  businessHoursEnd: string
  telephone: string
  promiseDistance: number
  distance: number
  specialRemark: string
  siteAddress: string
  nearestSite: boolean
  usedBefore: boolean
  selfpickSiteAvailable: boolean
  selfpickCounterAvailable: boolean
  selfPickSiteRecommended: boolean
}

interface OverseasShipmentTypeVO {
  shipmentTypeName: string
  shipmentType: string
  selected: boolean
  venderUuidMap: string
  overseasPickInfo: OverseasPickInfo
  freight: number
  selfPickFreight: number
  freightInner: number
  transportMsg: string
  promiseType: string
  promiseMsg: string
  promiseUuid: string
  shipmentOptionalNum: number
  disabled: boolean
}

// 包裹
export type BundleType = {
  productionList: ProductionListType // 商品列表
  deliveryInfoVO: DeliveryInfoVO //配送信息
  comShipmentTypeVO: ComShipmentTypeVO //配送方式
  overseasShipmentTypeVO: OverseasShipmentTypeVO //海外配送方式
  bundleId: string // ID
  regularBuy?: boolean
  regularBuyPlan?: string
  regularBuyCalendarList?: RegularBuyCalendarItem[]
  regularBuyContentVO: RegularBuyContentVO
  venderUuidMap?: string
  hasNoSupportGbPromise?: boolean
  hasNoSupportGbPromiseTxt?: string
}
export type RegularBuyCalendarItem = {
  offset: number
  date: string
  fullDate: string
  selected: boolean
  midPromiseFormatType: number
  midPromiseType: number
  midShipmentTimeOffset: number
}
export type RegularBuyContentVO = {
  buyNum: number
  regularBuyActivityId: string
  sectionNum: number
  sendCycle: number
  totalSectionNum: number
}

export type BundleListType = BundleType[] // 包裹列表

// 商品信息
export type BalanceSkuType = {
  affiliatedList?: AffiliatedListType // 赠品列表
  skuIconList?: SkuIconListType // 服务标签
  serviceList?: ServiceListType // 服务列表
  customServiceList?: ServiceListType // 定制服务列表
  CustomServiceListType
  jdPrice: string // 京东价
  imgUrl: string // 商品图
  name: string // 商品名称
  size: string // 尺码
  color: string // 颜色
  buyNum: number // 购买数量
  id: number // skuid
  skuTitleIconList?: SkuTitleIconListType // 标题标签
  weight?: number // 重量
  weightShowType?: number // 重量展示类型
  stockStateText?: string // 库存提示文案
  unit?: string // 套装单位
  loc?: boolean // 是否为loc商品
  locShopId?: string // loc店铺ID
  titleTag?: string // 标题前tag文字
  primitivePrice?: string // 美妆加赠 原始价格
  selectCard?: string // 号卡号码
  finalPrice?: string // 到手价
  finalPriceName?: string // 到手价标题
  finalPriceClass?: string // 到手价css类名
  finalPriceNameClass?: string // 到手价标题 css 类名
  unHitGovSubsidySkuJumpUrl?: string // 领国补地址
  unHitGovSubsidySkuTips?: string // 领国补标题
  unHitGovSubsidyTitleTips?: string // 划过领国补提示
}

// 商品标签
export type SkuTitleIconType = {
  id: string // ID
  url: string // 标签图片
}

export type SkuTitleIconListType = SkuTitleIconType[] // 商品标签列表

export type BalanceSkuListType = BalanceSkuType[] // SKU列表

/**
 * 套装类型
 * 1：一般单品
 * 4：一般套装
 * 21、22：套装(满返、满赠)
 * 23：虚拟组套
 * 16：选赠品（说明：一般单品，满返满赠套装和选赠品没有title，一般套装和虚拟组套有title）
 */
export type ProductionItemType = 1 | 4 | 21 | 22 | 23 | 16

// 商品列表
export type ProductionType = {
  type: ProductionItemType
  name?: string // 套装名称
  num?: number // 套装数量
  titleName?: string // 标题前提示
  price?: string // 价格
  balanceSkuList?: BalanceSkuListType // SKU列表
  promotionDesc?: string // 促销描述
  promotionIcon?: string // 促销标
  unitPrice?: string // 套装价
}
export type ProductionListType = ProductionType[] // 商品列表

// 商品-服务
export type ServiceType = {
  buyNum: number // 购买数量
  name: string // 服务名称
  jdPrice: string // 服务价格
  type: number // 服务类型
  titleTag?: string // 标题前提示
}
export type ServiceListType = ServiceType[]

// 定制服务
export type CustomServiceType = {
  buyNum: number // 购买数量
  name: string // 名称
  jdPrice: string // 价格
  type: number // 类型
  titleTag?: string // 标题前提示
}
export type CustomServiceListType = CustomServiceType[] // 定制服务列表

// 服务标签
export type SkuIconType = {
  desc: string // 名称
  id: string // ID
  url?: string // 跳转地址
}

export type SkuIconListType = SkuIconType[] // 标签列表

// 赠品
export type AffiliatedType = {
  buyNum: number // 数量
  name: string // 名称
  id: number // ID
}
export type AffiliatedListType = AffiliatedType[] // 赠品列表

export interface BalanceVirtualAssetsVO {
  /** 虚拟资产-礼品卡可用数量 (不为空获取礼品卡列表) */
  availableGiftNum: number
  /** 优惠券共抵扣金额 */
  couponDiscount: string
  /** 优惠券总数量 */
  totalCouponNum: number
  /** 可用优惠券数量 */
  availableCouponNum: number
  /** 已选优惠券数量 */
  selectedCouponNum: number
  /** 礼品卡总数量 */
  totalGiftNum: number
  /** 已选礼品卡数量 */
  selectedGiftCardNum: number
  /** 用户已选礼品卡优惠金额 */
  giftCardDiscount: string
  /** 领货礼品卡总数量 */
  totalConsignmentGiftNum: number
  /** 可用领货礼品卡数量 */
  availableConsignmentGiftNum: number
  /** 已选领货卡数量 */
  selectedConsignmentGiftNum: number
  /** 取货码抵扣优惠金额 */
  consignmentCardDiscount: string
  /** 红包总额度 */
  redPacketTotalBalance: string
  /** 本单可用红包余额 */
  redPacketTotalUsableBalance: string
  /** 本单不可使用的红包额度 */
  redPacketTotalUnavailableBalance: string
  /** 用户已选红包优惠金额 */
  orderRedPacketUsedBalance: string
  /** 红包屏蔽不可用文案 */
  redUnavailableReason: string
  /** 总京豆数 */
  totalJdBeanCount: number
  /** 可用京豆数 */
  canUseJdBeanCount: number
  /** 用户使用京豆数 */
  usedJdBeanCount: number
  /** 用户已选京豆优惠金额 */
  currentUsedJdBean: string
  /** 最少使用京豆数 */
  lowerLimitAvailablePoints: number
  /** 最大使用京豆数 */
  upperLimitAvailablePoints: number
  /** 整单用豆的应付金额门槛 */
  minPayableAmount: string
  /** 抵扣比例 */
  pointsDeductionRatio: string
  /** 用户的余额 */
  userRemainderBanlance: string
  /** 用户是否使用了余额 */
  useBalance: boolean
  /** 使用的余额 */
  currentUsedBalance: string
  /** 余额提醒文案 */
  balanceRemindMsg: string
  /** 余额弹窗文案 */
  balancePopUpWindowMsg: string
  /** 余额二级页面开关 */
  balanceOpenSecondPage: boolean
  /** 跨店满减 */
  crossShopFullReductionPrice: string
  /** 返回金额 */
  rePrice: string
  /** 可用福粒卡总数量 */
  totalWelfareCardAvailableNum: number
  /** 不可用福粒卡总数量 */
  totalWelfareCardUnavailableNum: number
  /** 使用福粒卡总数量 */
  useWelfareCardNum: number
  /** 福粒卡抵扣的总金额 */
  totalWelfareCardDeductionAmount: string
  /** 福利卡可用的总金额 */
  totalWelfareCardCanUseAmount: string
  /** 礼品卡抵扣金额 */
  welfareCardDiscount: string
  /** 以旧换新楼层折扣信息 */
  oldToNewDisCountDesc: string
  /** 以旧换新楼层扣减金额 */
  oldToNewDisCountAmount: string
  /** 屏蔽礼品卡楼层 */
  disableGiftCardShow: boolean
  /** 京豆开关 */
  jdBean: boolean
  /** 是否显示京豆 */
  disableJdBeanShow: boolean
  /** 是否显示红包 */
  forbidRedPacket: boolean
  /** 是否显示优惠券 */
  disableCouponShow: boolean
  /** 是否领货码 */
  forbidPickUpCode: boolean
  /** 是否显示最优优惠券 */
  useBestCoupon: boolean
  /** */
  virtualAssetsConfigVO: VirtualAssetsConfigVO
  /** 是否显示余额 */
  balanceShow: boolean
  /** 是否显示余额红点 */
  balanceRedShow: boolean
}
/**
 * 虚拟资产-红包
 */
export interface RedPacketResultVo {
  /**
   * 红包总可用余额
   */
  redPacketTotalUsableBalance: number
  /**
   * 红包总额度
   */
  redPacketTotalBalance: number
  redPacketTotalUnavailableBalance: number
  /**
   * 结算页是否勾选红包值
   */
  selectRedPacket: boolean
  canUseBalance: number
  unavailableReason: string
  /**
   * 红包列表
   */
  availableRedPacketList: AvailableRedPacketList[]
}

/**
 * 虚拟资产-余额
 */
export interface VirtualPropertyVO {
  /** 用户的余额   */
  userRemainderBanlance: string
  /** 用户可用余额*/
  canUseBalance: string
  /** 是否使用余额 */
  useBalance: boolean
  /** 使用的余额 */
  usedBalance: string
  /** 不可使用商品sku 列表 */
  unSupportSkuImgList: string[]
}

export interface CouponVolist {
  /** 优惠券id */
  id: string
  /** 优惠卷key */
  key: string
  /** 限店铺券店铺id，如果为非限店铺券，则venderId=0 */
  venderId: number
  /** 优惠券类型，京券=0 东券=1 运费券=2（couponType=2 and couponStyle=2 才是运费券） */
  couponType: number
  /** 优惠券名称 */
  couponTypeStr: string
  /** 优惠券样式-----couponType0:电子券;1: 实体券 (已废弃)2:免运费券 */
  couponStyle: number
  /** 优惠券面值/限额，如果是京券，该值=0，如果是东券，该值=使用该券的最低金额 */
  quota: string
  /** 优惠额度,优惠金额，面值   这里需要显示的金额 */
  discount: string
  /** 优惠券发放时间 */
  beginTime: string
  /** 优惠劵过期时间 */
  endTime: string
  /** 是否选中 */
  selected: boolean
  /** 优惠券限制信息 */
  limitDesc: string
  /** 该张券可使用的sku列表 */
  supportSkus: ShowSkuVO[]
  /** 东券叠加描述 */
  dongOverlapStr: string[]
  /** 折扣券的最大优惠金额 */
  highDiscount: string
  /** 折扣信息模板 */
  discountInfos: DiscountItem[]
  /** 差X元可用该券 */
  lessQuota: string
  /** 不可用原因描述码,券为可用券时,该字段为空 */
  descCode: string
  /** 不可用原因描述 */
  desc: string
  /** 消费劵标识 */
  couponIconClass: {
    /** 样式key */
    iconClass: string
    /** 文案key */
    desc: string
  }
  /** 是否氛围卷  true显示氛围卷背景 */
  coupon200: boolean
  /** 皮肤背景 */
  backgroundImg: string
  /** 是否可叠加 true只读，false可选择 */
  readOnly: boolean
}

export interface VirtualAssetsConfigVO {
  /** 礼品卡/领货码 */
  gbCardTip: string
  /** 红包 */
  gbRedPayTip: string
  /** 京豆 */
  gbJdbeanPayTip
}
/**
 * 可用sku列表
 */
export interface ShowSkuVO {
  /**
   * 商品id
   */
  skuId: number
  /**
   * 商品名称
   */
  skuName: string
  /**
   * 商品图片
   */
  imgUrl: string
}

/**
 * 京豆
 */
export interface JdBeanVo {
  /** 最大使用京豆数 */
  upperLimitAvailablePoints: number
  /**
   * 总金豆数量
   */
  totalJdBeanCount: number
  /** 整单用豆的应付金额门槛 */
  minPayableAmount: number
  /**
   * 京豆折扣比例（rate=1元抵扣的京豆数）
   */
  rate: number
  /** 最少使用京豆数 */
  lowerLimitAvailablePoints: number
  /** 可用京豆数 */
  canUseJdBeanCount: number
  /**
   * 京豆列表
   */
  jdBeanMaps: jdBean[]
}

export interface jdBean {
  /** 价格 */
  price?: string
  /** 京豆数量 */
  num?: number
  /** 描述 */
  text?: string
  /** 类型 1-一般整数抵扣 ｜ 2:最大抵扣 ｜3:暂不使用京豆 ｜ 4-自定义数量 */
  pointSelectFlag?: string
}

/**
 * 可用红包列表明细
 */
export interface AvailableRedPacketList {
  /** 红包活动编号 */
  activityId: string
  /** 红包活动名称 */
  activityName: string
  /** 红包类型 */
  type: string
  /** 红包编号 */
  id: string
  /**  红包开始时间 */
  beginTime: string
  /** 红包结束时间 */
  endTime: string
  /** 红包面值 */
  faceValue: number
  /** 红包余额 */
  balance: number
  /**  红包本次使用额度 */
  discountCurrentUsed: number
  /** 红包可使用金额 */
  canUseAmount: number
  /** 领取渠道 */
  channel: string
  /** 扩展Json */
  extJson: any
}
export interface BalanceUser {
  /**是否开启支付密码 true为开启 */
  fundsPwd: boolean
  /** 本单是否使用了虚拟资产（是否需要支付密码） */
  isNeed: boolean
  isShortPwdActive: boolean
  /** 是否实名用户 true为实名*/
  realName: boolean
  /** 用户等级 */
  userLevel: number
  /** 京享值 */
  userScore: number
}
export interface BalanceAddressDo {
  id: number
  addressDefault: boolean
  tag: string
  provinceId: number
  provinceName: string
  cityId: number
  cityName: string
  countyId: number
  countyName: string
  townId: number
  townName: string
  provinceCityCounty: string
  addressDetail: string
  addressType: number
  postCode: string
  fullAddress: string
  name: string
  mobile: string
  realMobile: string
  idCard: string
  phone: string
  realPhone: string
  email: string
  readOnly: boolean
  overseas: number
  areaCode: string
  nameCode: string
  selected: boolean
  gcLat: number
  gcLng: number
  latitude: number
  longitude: number
  showChangeJingAddressFlag: boolean
  isAreaGAT: boolean
  isAu: boolean
  needToUploadJicc: boolean
  supportAreaSeas: boolean
  fixed: boolean
  fixedMessage: string
  areaIdFromCookie: string
  addressChunked: boolean
}

export interface CombinationPaymentVo {
  mainPaymentTypeDO: MainPaymentTypeDo
  subPaymentTypeDO: SubPaymentTypeDo
  selected: boolean
}

export interface MainPaymentTypeDo {
  paymentId: number
  paymentName: string
  subPaymentId: string
  supportedSkus: SupportedSku[]
  unSupportedSkus: UnSupportedSku[]
  supported: boolean
  selected: boolean
  selectedCreditPaymentType: string
  errorCode: string
  errorMessage: string
  limit: boolean
}

export interface SupportedSku {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface UnSupportedSku {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface SubPaymentTypeDo {
  paymentId: number
  paymentName: string
  subPaymentId: string
  supportedSkus: SupportedSku2[]
  unSupportedSkus: UnSupportedSku2[]
  supported: boolean
  selected: boolean
  selectedCreditPaymentType: string
  errorCode: string
  errorMessage: string
  limit: boolean
}

export interface SupportedSku2 {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface UnSupportedSku2 {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface PersonNotifyInfo {
  yLhQQeKebWMzjry: string
}

export interface InvoiceConsigneeDo3 {
  consigneeName: string
  provinceId: number
  province: string
  cityId: number
  city: string
  countyId: number
  county: string
  townId: number
  town: string
  phone: string
  address: string
  sendSeparate: boolean
  updateTime: string
}

export interface Freight {
  remoteSkuFreight: RemoteSkuFreight
  venderFreightDetailMap: VenderFreightDetailMap
  venderSkuMap: VenderSkuMap
  xuZhongFreight: string
  xuZhongFreightSX: string
  xuZhongFreightWalMart: string
  basicFreight: string
  weightFreight: string
  distanceFreight: string
  periodFreight: string
  reduceFreight: string
  sxFreight: string
  allXuZhongWeight: string
  selfXuZhongWeight: number
  selfXuZhongWeightSX: number
  jzdAmount: string
  jsdAmount: string
  djzdAmount: string
  hasSx: boolean
  jzdTitle: string
  jzdTitle0: string
  djzdTitle: string
  sxjzdTitle: string
  sxdjzdTitle: string
  jsdTitle: string
  sxjsdTitle: string
  fuelFreight: string
  crossSku: CrossSku[]
  crossRegionalFee: CrossRegionalFee
  dcName: string
  overseaOriginalFreight: string
  overseaPromotionFreight: string
  overseaOriginalBookFreight: string
  overseaPromotionBookFreight: string
  overseaOriginalTotalFreight: string
  overseaPromotionTotalFreight: string
  overseaBookFreight: string
  overseaNoBookFreight: string
  siteFareList: SiteFareList[]
  actualFavour: string
  beforeActualFavour: string
  freightDiscount: string
  orderFreightBeforeDiscount: string
  totalGlobalVolumeWeight: string
  qcyPackageFeeMap: QcyPackageFeeMap
  allStockNetGoods: boolean
}

export interface RemoteSkuFreight {
  ARFlciBfkfnaFi: string
}

export interface VenderFreightDetailMap {
  GemGFGpDtXGHnnQjw: GemGfgpDtXghnnQjw
}

export interface GemGfgpDtXghnnQjw {
  stockNetVenderFreightVO: StockNetVenderFreightVo
  addressSortId: string
  storeId: string
  freightDetailVOMap: FreightDetailVomap2
  multipleVenderId: string
  venderId: string
  isShieldSelfVender: boolean
  venderName: string
  factoryPbself: boolean
  subsidyShield: boolean
  venderType: string
  consigneeName: string
  presentFlag: boolean
  shopIdList: string[]
  venderFreight: string
  allXuZhongWeightShow: string
  overXuZhongWeight: string
  allXuZhongWeight: number
  allXuZhongWeightSXShow: string
  overXuZhongWeightSX: string
  allXuZhongWeightSX: number
  globalVolumeWeight: string
  txFreightDiscount: string
}

export interface StockNetVenderFreightVo {
  stockNetVenderFreightVO: StockNetVenderFreightVo2
  addressSortId: string
  storeId: string
  freightDetailVOMap: FreightDetailVomap
  multipleVenderId: string
  venderId: string
  isShieldSelfVender: boolean
  venderName: string
  factoryPbself: boolean
  subsidyShield: boolean
  venderType: string
  consigneeName: string
  presentFlag: boolean
  shopIdList: string[]
  venderFreight: string
  allXuZhongWeightShow: string
  overXuZhongWeight: string
  allXuZhongWeight: number
  allXuZhongWeightSXShow: string
  overXuZhongWeightSX: string
  allXuZhongWeightSX: number
  globalVolumeWeight: string
  txFreightDiscount: string
}

export interface StockNetVenderFreightVo2 {}

export interface FreightDetailVomap {
  '0': N0
}

export interface N0 {
  freight: string
  skuList: SkuList[]
}

export interface SkuList {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface FreightDetailVomap2 {
  '0': N02
}

export interface N02 {
  freight: string
  skuList: SkuList2[]
}

export interface SkuList2 {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface VenderSkuMap {
  YApjAbDNrJnaYoWcja: YapjAbDnrJnaYoWcja[]
}

export interface YapjAbDnrJnaYoWcja {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface CrossSku {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface CrossRegionalFee {
  UeVbaT: string
}

export interface SiteFareList {
  venderId: number
  JDCombineStoreId: number
  siteBaseFreight: string
  sitePeriodFreight: string
  siteDistanceFreight: string
  siteWeightFreight: string
  siteActualFare: string
  storeActualFavour: string
  beforeStoreActualFavour: string
  skuList: SkuList3[]
  storeReduceFreight: string
  venderDaoJiaVip: boolean
  showSubOrderVos: ShowSubOrderVo[]
  venderType: number
  extraInfoMap: ExtraInfoMap
}

export interface SkuList3 {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface ShowSubOrderVo {
  name: string
  nameColor: string
  msgColor: string
  venderId: number
  JDCombineStoreId: number
  siteBaseFreight: string
  sitePeriodFreight: string
  siteDistanceFreight: string
  siteWeightFreight: string
  siteActualFare: string
  yunfeiMessages: string
  storeActualFavour: string
  beforeStoreActualFavour: string
  showSku: ShowSku[]
  storeReduceFreight: string
  venderDaoJiaVip: boolean
}

export interface ShowSku {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface ExtraInfoMap {
  KgBy: string
}

export interface QcyPackageFeeMap {
  aGMWJHfmXDMxquri: AGmwjhfmXdmxquri
}

export interface AGmwjhfmXdmxquri {
  storeId: string
  storePackageFee: string
  showSkuVOList: ShowSkuVolist[]
}

export interface ShowSkuVolist {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}

export interface ShowSkuVo {
  skuId: number
  skuName: string
  skuImgUrl: string
  presaleBalanceFlag: boolean
  presentFlag: boolean
  consigneeName: string
  venderId: number
  skuNum: number
  bigItem: boolean
  supportInstall: boolean
  factoryShip: boolean
  yanbao: boolean
  serviceItemId: number
  uuid: string
  sxType: number
  venderColType: number
  venderType: number
  isBook: boolean
  skuMark: number
  JDCombineStoreId: number
}
