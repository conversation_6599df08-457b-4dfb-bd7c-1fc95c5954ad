import env from './env'
import sgm from './sgm'
import { sgmCode } from '../constant'

const appNetwork = ({ functionId, body }) => {
  return new Promise<any>((resolve, reject) => {
    // const params = router.params();
    const params = { token: 'yhDAHweDVCaZsYh+sU9/WwUgZI9wr8d1' }

    // 根据network值，控制不同参数
    const data = () => {
      // @ts-ignore
      switch (params?.network) {
        case '2':
          return {
            headerType: '1',
            param: {},
          }
        case '3':
          return {
            headerType: '0',
            param: {
              appid: env.appid,
            },
          }
        case '4':
          return {
            headerType: '0',
            param: {},
          }
        default:
          return {
            headerType: '1',
            param: {
              appid: env.appid,
            },
          }
      }
    }

    const interfaceParameters = {
      url: `${env.baseURL}client.action`,
      functionId,
      body,
      headerType: data().headerType, // 默认为“0”； "1": 使用H5的cookie和ua  "0":使用原生的cookie和ua
      param: {
        ...data().param,
      },
    }
    const currentTimeMillis = Date.now()
    const radom = Math.random().toString().substring(3)
    const callBackMethod = `${functionId}${currentTimeMillis}${radom}`
    console.log('原生网络库入参', interfaceParameters)
    // sgm?.customReport({ type: 3, code: sgmCode.APP_NETWORK_PARAMS, msg: JSON.stringify(interfaceParameters) })

    window['XWebView'] &&
      window['XWebView'].callNative('ColorQueryPlugin', 'colorRequest', JSON.stringify(interfaceParameters), callBackMethod, '1') // 后两个参数可选

    // 设置固定时间终止网络请求
    const timeoutId = setTimeout(() => {
      reject({
        message: '网络请求超时取消',
      })
      sgm?.customReport({ type: 3, code: sgmCode.APP_NETWORK_TIMEOUT, msg: '网络请求超时取消' })
    }, 3000)

    console.log('原生网络库', timeoutId)
    window[callBackMethod] = function (result) {
      clearTimeout(timeoutId)

      const response = JSON.parse(result)
      const { status } = response
      if (status === '0') {
        if (response.data?.body === undefined) {
          sgm?.customReport({ type: 3, code: sgmCode.APP_NETWORK_FAIL, msg: 'empty response body' })
          reject({
            message: 'empty response body',
            response,
          })
          return
        }
        if (response.data?.code !== '0') {
          sgm?.customReport({ type: 3, code: sgmCode.APP_NETWORK_FAIL, msg: `response fail ${response.data.code}` })
          reject({
            message: `response fail ${response.data.code}`,
            response,
          })
          return
        }
        if (response.data?.body?.errorCode) {
          sgm?.customReport({
            type: 3,
            code: sgmCode.APP_NETWORK_FAIL,
            msg: `response error code:${response.data.body.errorCode} reason:${response.data.body.errorReason}`,
          })
          reject({
            message: `response error code:${response.data.body.errorCode} reason:${response.data.body.errorReason}`,
            response,
          })
          return
        }
        resolve(response)
        // sgm?.customReport({ type: 3, code: sgmCode.APP_NETWORK_SUCCESS, msg: JSON.stringify(response) })
      } else {
        reject({
          response,
        })
        sgm?.customReport({ type: 3, code: sgmCode.APP_NETWORK_FAIL, msg: JSON.stringify(response) })
      }
    }
  })
}
export default appNetwork
