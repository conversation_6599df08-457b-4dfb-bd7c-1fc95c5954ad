/**
 * 划过提示组件
 */
import React, { useState, useRef, useEffect } from 'react'
import './index.scss'

type TriggerType = 'click' | 'hover'
type PlacementType = 'top' | 'right' | 'bottom' | 'left'

interface PopoverProps {
  content: React.ReactNode
  children: React.ReactNode
  trigger: TriggerType
  placement: PlacementType
}

const Popover: React.FC<PopoverProps> = ({ content, children, trigger = 'click', placement = 'bottom' }) => {
  const [isOpen, setIsOpen] = useState(false)
  const ref = useRef<HTMLDivElement>(null)
  const targetRef = useRef<HTMLDivElement>(null)

  const handleToggle = () => {
    setIsOpen(!isOpen)
  }

  const handleMouseEnter = () => {
    if (trigger === 'hover') {
      setIsOpen(true)
    }
  }

  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      setIsOpen(false)
    }
  }

  const handleClickOutside = (event: MouseEvent) => {
    if (
      ref.current &&
      targetRef.current &&
      !ref.current.contains(event.target as Node) &&
      !targetRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false)
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const getArrowClass = () => {
    switch (placement) {
      case 'top':
        return 'arrow-top'
      case 'right':
        return 'arrow-right'
      case 'bottom':
        return 'arrow-bottom'
      case 'left':
        return 'arrow-left'
      default:
        return ''
    }
  }

  const getPopoverClass = () => {
    switch (placement) {
      case 'top':
        return 'popover-top'
      case 'right':
        return 'popover-right'
      case 'bottom':
        return 'popover-bottom'
      case 'left':
        return 'popover-left'
      default:
        return ''
    }
  }

  return (
    <div
      ref={targetRef}
      className="popover-trigger"
      onClick={trigger === 'click' ? handleToggle : undefined}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div>{children}</div>
      {isOpen && (
        <div ref={ref} className={`popover ${getPopoverClass()}`}>
          <div className={`arrow ${getArrowClass()}`} />
          {content}
        </div>
      )}
    </div>
  )
}

export default Popover
