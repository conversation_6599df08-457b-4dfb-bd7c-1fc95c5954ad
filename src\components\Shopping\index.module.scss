.shopping {
  margin: 16px 0;
  line-height: 1;
  box-sizing: border-box;
  padding: 20px 16px;
  background: #fff;
  border-radius: 8px;

  & * {
    box-sizing: border-box;
  }

  .list {
    margin-top: 16px;

    & > div:last-child {
      .bundle-list {
        & > div:last-child {
          margin-bottom: 0;
        }
      }
    }

    .bundle-item {
      border: 1px solid rgba(0, 0, 0, 0.06);
      border-radius: 8px;
      margin-bottom: 16px;
    }

    .production-list {
      padding: 0 16px;
    }
  }

  :global {
    .flex {
      display: flex;
    }

    .flex-column-center {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .flex-column-center2 {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .flex-end {
      display: flex;
      align-items: flex-end;
    }

    .flex-center {
      display: flex;
      align-items: center;
    }

    .flex-between {
      display: flex;
      justify-content: space-between;
    }

    .flex-around {
      display: flex;
      justify-content: space-around;
    }

    .flex-content-center {
      display: flex;
      justify-content: center;
    }

    .flex-center2 {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .flex-center-between {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .text-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .text-ellipsis-2 {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .flex-1 {
      flex: 1;
    }
  }
}
