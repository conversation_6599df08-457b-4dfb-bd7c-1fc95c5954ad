// Variables
$primary-color: #ff0f23;
$secondary-color: #f23030;
$accent-color: #fd4046;
$text-primary: #1a1a1a;
$text-secondary: #666;
$text-muted: #999;
$text-light: #c2c4cc;
$border-color: #e5e6eb;
$background-light: #f7f8fc;
$background-primary: #ffebf1;
$background-warning: #fff5e8;
$background-disabled: #f5f5f5;
$border-active: #ff8595;
$font-family: 'PingFang SC';
$disabled-color: #f7f8fc;
$disabled-text-color: #c2c4cc;

.invoice-model {
  a {
    color: #1890ff;
    text-decoration: none;
  }
}

.radio-group {
  display: flex;
  gap: 10px;
  align-items: center;

  &.disabled {
    label {
      cursor: not-allowed;
      color: $disabled-text-color;
      background-color: $disabled-color;
    }
  }
}

.radio-button {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  box-sizing: border-box;
  padding: 0 14px;
  height: 36px;
  border-width: 0.5px;
  border-radius: 4px;
  color: rgb(26, 26, 26);
  background-color: rgb(247, 248, 252);
  border: 0.5px solid rgba(0, 0, 0, 0.06);
  cursor: pointer;

  &.active {
    background-color: rgb(255, 235, 241);
    border: 0.5px solid #ff8595;
    color: rgb(255, 15, 35);
  }

  &.disabled {
    background-color: rgb(247, 248, 252);
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    color: rgb(194, 196, 204);
  }
}

/* 步骤指示器样式 */
.step-indicator {
  display: flex;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;
  position: relative;
  margin-left: 11%;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;

  .step {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2;
    margin-right: 10px;
    color: $text-muted;
    font-size: 14px;

    &.active {
      color: $secondary-color;

      .step-text {
        color: $text-primary;
      }
    }

    &.completed {
      color: $text-primary;
    }

    &:first-child .step-number {
      background: $background-primary;
      color: $primary-color;
    }

    &.completed .step-number::before {
      content: '';
      width: 16px;
      height: 16px;
      background: url(https://img14.360buyimg.com/imagetools/jfs/t1/279305/27/9656/1485/67e364d2Fdb97b218/0ff12e7e2178d138.png) center /
        12px auto no-repeat;
      border-radius: 50%;
      position: absolute;
      transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: center;
    }
  }

  .step-line {
    height: 1px;
    background-color: #c2c4cc;
    margin: 0 8px;
    width: 40px;
    height: 1px;
    stroke: #c2c4cc;
    stroke-width: 1px;

    &.active {
      background-color: $secondary-color;
    }
  }
}

.step-text {
  font-size: 14px;
  font-weight: 600;
  font-family: $font-family;
  line-height: 14px;
}

.step-number {
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  position: relative;
  z-index: 2;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  margin-right: 8px;
  background: $background-primary;
}

.National-subsidy {
  height: auto;
  padding: 10px 5px;
  background-color: $background-warning;
  border-radius: 8px;
  font-size: 12px;
  color: #ff791a;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 12px;

  a {
    color: #1890ff;
    text-decoration: none;
  }
}

/* 修改B端用户发票tab栏样式，使用新类名避免冲突 */
.invoice-header-tabs {
  display: flex;
  flex: 1;
  padding-right: 20px;
  align-items: center;
  gap: 10px;
  height: 100%;
}

.invoice-header-tab {
  position: relative;
  padding: 8px 20px;
  font-size: 16px;
  color: $text-secondary;
  cursor: pointer;
  text-align: center;
  font-family: $font-family;
  line-height: 16px;
  font-weight: 400;

  &:hover {
    color: $primary-color;
    transition: all 0.3s ease;
  }

  &.active {
    color: $primary-color;
    font-family: $font-family;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0px;
  }
}

.header-tab-indicator {
  position: absolute;
  bottom: -14px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: $primary-color;
  transition: all 0.3s ease;
}

i.arrow-up {
  transform: rotate(180deg);
}

.disabled-hint {
  font-size: 12px;
  color: $text-muted;
  margin-left: 5px;
}

/* 当前发票类型显示 */
.current-type-display {
  font-size: 14px;
  color: #333;
  padding: 8px 0;
  font-weight: 500;
}

/* 地址选择器样式 */
.address-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.address-select {
  flex: 1;
  min-width: 120px;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;

  &:focus {
    border-color: #409eff;
  }

  &:disabled {
    background-color: #f5f7fa;
    cursor: not-allowed;
  }

  option {
    padding: 6px 0;
  }
}

.address-preview {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

/* 表单操作按钮 */
.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
}

.btn-secondary {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  margin-right: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #c6e2ff;
    color: #409eff;
  }
}

.invoice-form-section {
  padding: 0 30px;
}
