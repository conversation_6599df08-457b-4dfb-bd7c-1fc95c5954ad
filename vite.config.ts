import react from '@vitejs/plugin-react'
// import autoprefixer from 'autoprefixer'
import fs from 'fs'
import path from 'path'
import postcssPresetEnv from 'postcss-preset-env'
import { defineConfig } from 'vite'
import type { ConfigEnv } from 'vite'
// import dts from "vite-plugin-dts";
import { createHtmlPlugin } from 'vite-plugin-html'
import requireTransform from 'vite-plugin-require-transform'
import { createHash } from 'crypto'
import legacy from '@vitejs/plugin-legacy'
import del from 'rollup-plugin-delete'
const packageJsonPath = path.resolve(__dirname, './package.json')
const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf-8')
const packageJson = JSON.parse(packageJsonContent)
const version = packageJson.version
const hash = createHash('md5').update(String(new Date().getTime())).digest('hex').slice(0, 8)

/**
 * 开发环境使用本地静态资源，其他环境使用头尾系统的推送内容；
 * @param env
 * @returns {string}
 */
function get_footer_content(env: ConfigEnv) {
  const { command } = env
  if (command === 'build') {
    return `<!--# include file="/fragment/includeFooter2025" -->`
  }
  return fs.readFileSync(path.resolve(__dirname, './public/footer.html'), 'utf-8')
}

export default defineConfig((env) => {
  return {
    server: {
      https: {
        key: fs.readFileSync('./key.pem'),
        cert: fs.readFileSync('./cert.pem'),
      },
      host: 'dev.jd.com',
      open: true,
      proxy: {},
    },
    base: `//storage.360buyimg.com/retail-mall/pc_settlement/pro/${version}/`,
    resolve: {
      extensions: ['.ts', '.tsx', '.js', '.json', '.css', '.scss'],
      alias: {
        '@app': path.resolve(__dirname, './src'),
      },
    },

    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'js',
      cssTarget: 'chrome61',
      rollupOptions: {
        // external: ["react", "react-dom"],

        output: {
          entryFileNames: `js/[name].${hash}.js`,
          chunkFileNames: `js/[name].${hash}.js`,
          // assetFileNames: `assets/[name][hash:10].[ext]`,
          // globals: {
          //   react: "react",
          //   "react-dom": "react-dom",
          // },
          // manualChunks: () => {
          //   return "abc";
          // },
          assetFileNames(assetInfo: any) {
            // if (assetInfo.name.endsWith('.css')) {
            //   return 'css/[name].[hash:6].css'
            // }
            // if (assetInfo.name.endsWith('.js')) {
            //   return 'js/[name].[hash:6].js'
            // }
            // const imgExts = ['.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg', '.ico']
            // if (imgExts.some((ext) => assetInfo.name.endsWith(ext))) {
            //   return 'imgs/[name].[hash:6].[ext]'
            // }
            // return 'assets/[name].[hash:6].[ext]'
            if (assetInfo.name.endsWith('.css')) {
              return `css/[name].${hash}.css` // CSS 文件
            } else {
              return 'assets/[name].[hash].[extname]' // 其他资源文件
            }
          },
        },
        plugins: [
          del({
            targets: ['dist/footer.html'],
            hook: 'closeBundle',
          }),
        ],
      },
    },
    plugins: [
      react(),
      requireTransform({
        // fileRegex: /.js$|.tsx$|.png$|.ts$|.jpg$/,
        fileRegex: /^(?!.*node_modules).*(.js$|.tsx$|.png$|.ts$|.jpg$)/,
      }),
      createHtmlPlugin({
        inject: {
          data: {
            footer: get_footer_content(env),
          },
        },
      }),
      legacy({
        targets: ['last 20 versions and not dead, > 0.01%, Firefox ESR', 'chrome <= 60', 'safari <=10', 'edge <=13', 'not ie <= 8'],
      }),
    ],
    css: {
      modules: {
        generateScopedName: '[local]-[hash:6]',
      },
      postcss: {
        plugins: [postcssPresetEnv()],
      },
    },
  }
})
