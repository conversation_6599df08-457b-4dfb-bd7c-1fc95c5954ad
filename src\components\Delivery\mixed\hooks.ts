import { useEffect, useRef } from 'react'
import { useDelivery } from '../context'

export const useFirstRenderLogger = (isInstall: boolean = false) => {
  const { deliveryLogger } = useDelivery()

  const isFirstRender = useRef(true)

  useEffect(() => {
    if (isFirstRender.current && !isInstall) {
      deliveryLogger.shipmentlayerExpo()
      isFirstRender.current = false
    }
  }, [deliveryLogger, isInstall])

  return deliveryLogger
}

/**
 * 始终返回最新的值（常用于函数、对象等）。
 * 返回的 ref 对象 identity 不变，适用于 useEffect/useCallback 等闭包场景。
 *
 * ⚠️ 注意：在 effect 里使用 ref.current 时，无需将 ref 加入依赖数组。
 * 这是社区公认的最佳实践，详见：https://react.dev/reference/react/useRef
 *
 * @example
 * const latestFn = useLatest(fn)
 * useEffect(() => {
 *   latestFn.current()
 *   // eslint-disable-next-line react-hooks/exhaustive-deps
 * }, [])
 *
 * EN:
 * Returns a ref object whose .current is always the latest value.
 * The ref identity is stable and does not need to be added to effect deps.
 * See: https://react.dev/reference/react/useRef
 */
// eslint-disable-next-line react-hooks/exhaustive-deps
export function useLatest<T>(value: T) {
  const ref = useRef(value)
  useEffect(() => {
    ref.current = value
  }, [value])
  return ref
}
