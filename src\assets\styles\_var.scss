@mixin fontSize {
  font-size: var(--fontSize);
}
@mixin sfontSize {
  font-size: var(--sFontSize);
}
@mixin lfontSize {
  font-size: var(--lFontSize);
}
@mixin xlfontSize {
  font-size: var(--xlFontSize);
}
@mixin iconSize {
  width: var(--iconSize);
  height: var(--iconSize);
}
@mixin fontWeight {
  font-weight: var(--fontWeight);
}
@mixin clamp {
  -webkit-line-clamp: var(--clamp);
}
@mixin lineHeight {
  line-height: var(--lineHeight);
}
@mixin iconSizeJd {
  width: var(--iconSizeW);
  height: var(--iconSize);
}
@mixin block {
  display: var(--block);
}
@mixin paddingLeft {
  padding-left: var(--paddingLeft);
}
