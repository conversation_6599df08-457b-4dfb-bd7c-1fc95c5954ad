.popover-trigger {
  position: relative;

  .popover {
    width: 238px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.75);
    z-index: 10;
    position: absolute;

    & > div:nth-child(2) {
      max-width: 232px;
      padding: 12px;
      color: #fff;
      font-size: 14px;

      line-height: 20px;
    }
  }

  .popover-top {
    left: 50%;
    transform: translateX(-50%);
    bottom: 100%;
    margin-bottom: 8px;
  }

  .popover-right {
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 8px;
  }

  .popover-bottom {
    left: 50%;
    transform: translateX(-50%);
    top: 100%;
    margin-top: 8px;
  }

  .popover-left {
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    margin-right: 8px;
  }

  .arrow {
    width: 0;
    height: 0;
    border-style: solid;
    position: absolute;
    color: rgba(0, 0, 0, 0.75);
  }

  .arrow-top {
    border-bottom-color: rgba(0, 0, 0, 0.75);
    border-top-width: 8px;
    border-left-width: 8px;
    border-right-width: 8px;
    border-left-color: transparent;
    border-right-color: transparent;
    border-bottom-width: 0;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
  }

  .arrow-right {
    border-left-color: rgba(0, 0, 0, 0.75);
    border-right-width: 8px;
    border-top-width: 8px;
    border-bottom-width: 8px;
    border-left-width: 0;
    border-top-color: transparent;
    border-bottom-color: transparent;
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
  }

  .arrow-bottom {
    border-top-color: rgba(0, 0, 0, 0.75);

    border-bottom-width: 8px;
    border-left-width: 8px;
    border-right-width: 8px;
    border-left-color: transparent;
    border-right-color: transparent;
    border-top-width: 0;

    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
  }

  .arrow-left {
    border-right-color: rgba(0, 0, 0, 0.75);
    border-left-width: 8px;
    border-top-width: 8px;
    border-bottom-width: 8px;
    border-right-width: 0;
    border-top-color: transparent;
    border-bottom-color: transparent;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
  }
}
