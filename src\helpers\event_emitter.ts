interface Listener {
  (...args: any[]): void
  fn?: Listener
}

class EventEmitter {
  private _callbacks: Map<string, Listener[]>

  constructor() {
    this._callbacks = new Map()
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 事件监听器
   */
  on(event: string, listener: Listener): EventEmitter {
    const listeners = this._callbacks.get(event) ?? []
    listeners.push(listener)
    this._callbacks.set(event, listeners)
    return this
  }
  /**
   * 添加一次性事件监听器
   * @param event 事件名称
   * @param listener 事件监听器
   * @returns
   */
  once(event: string, listener: Listener): EventEmitter {
    const wrappedListener = (...args: any[]) => {
      this.off(event, wrappedListener)
      listener.apply(this, args)
    }
    wrappedListener.fn = listener
    this.on(event, wrappedListener)
    return this
  }
  /**
   * 移除指定事件监听器
   * @param event 事件名称
   * @param listener 事件监听器
   */
  off(event: string, listener: Listener): EventEmitter
  /**
   * 移除某事件的所有监听器
   * @param event 事件名称
   */
  off(event: string): EventEmitter
  /**
   * 移除所有事件监听器
   */
  off(): EventEmitter
  off(event?: string, listener?: Listener) {
    if (event === undefined && listener === undefined) {
      this._callbacks.clear()
      return this
    }

    if (listener === undefined) {
      this._callbacks.delete(event!)
      return this
    }

    const callbacks = this._callbacks.get(event!)
    if (callbacks) {
      for (let index = 0; index < callbacks.length; index++) {
        const callback = callbacks[index];
        if (callback === listener || callback.fn === listener) {
          callbacks.splice(index, 1)
          break
        }
      }

      if (callbacks.length === 0) {
        this._callbacks.delete(event!)
      } else {
        this._callbacks.set(event!, callbacks)
      }
    }

    return this
  }

  /**
   * 触发事件并通知所有监听器
   * @param event 事件名称
   * @param args 参数
   * @returns
   */
  emit(event: string, ...args: any[]): EventEmitter {
    const callbacks = this._callbacks.get(event)
    if (callbacks) {
      const callbacksCopy = [...callbacks]
      for (const callback of callbacksCopy) {
        callback.apply(this, args)
      }
    }
    return this
  }

  /**
   * 获取监听器列表
   * @param event 事件名称
   * @returns
   */
  listeners(event: string): Array<Listener> {
    return this._callbacks.get(event) ?? []
  }
  
  /**
   * 获取指定事件的监听器数量
   * @param event 事件名称
   * @returns
   */
  listenerCount(event: string): number
  /**
   * 获取所有事件的监听器数量
   */
  listenerCount(): number
  listenerCount(event?: string) {
    if (event) {
      return this.listeners(event).length
    }

    let totalCount = 0
    /* @ts-ignore */
    for (const callbacks of this._callbacks.values()) {
      totalCount += callbacks.length
    }

    return totalCount
  }

  /**
   * 判断某事件是否有监听器
   * @param event 事件名称
   * @returns
   */
  hasListeners(event: string): boolean {
    return this.listenerCount(event) > 0
  }
}

export const GlobalEventEmitter = new EventEmitter()
export default EventEmitter
