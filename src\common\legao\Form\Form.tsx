import React, { createRef } from 'react'
import { Component, PropTypes } from '../libs'

// 创建Form上下文
import FormContext from '@app/common/legao/Context'

type State = {
  fields: Array<Component>
}

export default class Form extends Component {
  state: State

  constructor(props: object) {
    super(props)
    this.formRef = createRef()
    this.state = {
      fields: [],
    }
  }

  addField(field: Component): void {
    this.state.fields.push(field)
  }

  removeField(field: Component): void {
    if (field.props.prop) {
      this.state.fields.splice(this.state.fields.indexOf(field), 1)
    }
  }

  resetFields(): void {
    this.state.fields.forEach((field) => {
      field.resetField()
    })
  }

  validate(callback: Function): void {
    let valid = true
    let count = 0

    // 如果需要验证的fields为空，调用验证时立刻返回callback
    if (this.state.fields.length === 0 && callback) {
      callback(true)
    }

    this.state.fields.forEach((field) => {
      field.validate('', (errors) => {
        if (errors) {
          valid = false
        }
        if (typeof callback === 'function' && ++count === this.state.fields.length) {
          callback(valid)
        }
      })
    })
  }

  validateField(prop: string, cb: Function): void {
    const field = this.state.fields.filter((field) => field.props.prop === prop)[0]

    if (!field) {
      throw new Error('must call validateField with valid prop string!')
    }

    field.validate('', cb)
  }

  render(): React.DOM {
    return (
      <FormContext.Provider value={{ component: this }}>
        <form
          ref={this.formRef}
          style={this.style()}
          className={this.className('el-form', this.props.labelPosition && `el-form--label-${this.props.labelPosition}`, {
            'el-form--inline': this.props.inline,
          })}
          onSubmit={this.props.onSubmit}
        >
          {this.props.children}
        </form>
      </FormContext.Provider>
    )
  }
}

Form.propTypes = {
  model: PropTypes.object,
  rules: PropTypes.object,
  labelPosition: PropTypes.oneOf(['right', 'left', 'top']),
  labelWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  labelSuffix: PropTypes.string,
  inline: PropTypes.bool,
  onSubmit: PropTypes.func,
}

Form.defaultProps = {
  labelPosition: 'right',
  labelSuffix: '',
}
