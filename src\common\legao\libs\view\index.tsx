import React, { ReactElement, forwardRef } from 'react'

interface ViewProps {
  show?: boolean
  className?: string
  children: ReactElement
}

type ViewComponent = React.FC<ViewProps> & { _typeName: string }

const View: ViewComponent = forwardRef(({ show = true, className = '', children }, ref) => {
  // 确保只有一个子元素
  const child = React.Children.only(children)

  // 合并样式
  const mergedStyle = {
    ...child.props.style,
    ...(!show && { display: 'none' }),
  }

  // 合并类名
  const mergedClassName = [child.props.className, className].filter(Boolean).join(' ')

  // 克隆子元素并合并属性
  return React.cloneElement(child, {
    className: mergedClassName,
    style: mergedStyle,
  })
})

// 添加静态类型标识
View._typeName = 'View'

export default View
