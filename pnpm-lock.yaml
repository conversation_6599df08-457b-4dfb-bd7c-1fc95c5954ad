lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@plato/rsa':
        specifier: ^0.0.1
        version: 0.0.1
      ahooks:
        specifier: ^3.8.4
        version: 3.8.5(react@18.3.1)
      axios:
        specifier: ^1.8.1
        version: 1.9.0
      buffer:
        specifier: ^6.0.3
        version: 6.0.3
      des.js:
        specifier: ^1.1.0
        version: 1.1.0
      hoist-non-react-statics:
        specifier: 3.3.2
        version: 3.3.2
      jotai:
        specifier: ^2.11.0
        version: 2.12.5(@types/react@18.3.23)(react@18.3.1)
      js-cookie:
        specifier: ^3.0.5
        version: 3.0.5
      jsonp:
        specifier: ^0.2.1
        version: 0.2.1
      md5:
        specifier: ^2.3.0
        version: 2.3.0
      prop-types:
        specifier: 15.8.1
        version: 15.8.1
      query-string:
        specifier: ^9.1.1
        version: 9.2.0
      react:
        specifier: ^18.3.1
        version: 18.3.1
      react-dom:
        specifier: ^18.3.1
        version: 18.3.1(react@18.3.1)
      react-error-boundary:
        specifier: ^5.0.0
        version: 5.0.0(react@18.3.1)
    devDependencies:
      '@babel/core':
        specifier: ^7.26.9
        version: 7.27.4
      '@babel/plugin-transform-runtime':
        specifier: ^7.26.9
        version: 7.27.4(@babel/core@7.27.4)
      '@babel/preset-env':
        specifier: ^7.26.9
        version: 7.27.2(@babel/core@7.27.4)
      '@babel/preset-react':
        specifier: ^7.26.3
        version: 7.27.1(@babel/core@7.27.4)
      '@commitlint/cli':
        specifier: ^19.7.1
        version: 19.8.1(@types/node@22.15.31)(typescript@5.7.3)
      '@commitlint/config-conventional':
        specifier: ^19.7.1
        version: 19.8.1
      '@eslint/js':
        specifier: ^9.19.0
        version: 9.28.0
      '@jd/upload-oss-tools':
        specifier: 1.1.23
        version: 1.1.23
      '@types/md5':
        specifier: ^2.3.5
        version: 2.3.5
      '@types/node':
        specifier: ^22.13.1
        version: 22.15.31
      '@types/react':
        specifier: ^18.3.3
        version: 18.3.23
      '@types/react-dom':
        specifier: ^18.3.0
        version: 18.3.7(@types/react@18.3.23)
      '@vitejs/plugin-legacy':
        specifier: ^6.0.2
        version: 6.1.1(terser@5.41.0)(vite@6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0))
      '@vitejs/plugin-react':
        specifier: ^4.3.4
        version: 4.5.2(vite@6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0))
      autoprefixer:
        specifier: ^10.4.20
        version: 10.4.21(postcss@8.5.4)
      babel-loader:
        specifier: ^10.0.0
        version: 10.0.0(@babel/core@7.27.4)(webpack@5.99.9)
      classnames:
        specifier: ^2.5.1
        version: 2.5.1
      core-js:
        specifier: ^3.40.0
        version: 3.43.0
      eslint:
        specifier: ^9.19.0
        version: 9.28.0(jiti@2.4.2)
      eslint-config-prettier:
        specifier: ^10.0.1
        version: 10.1.5(eslint@9.28.0(jiti@2.4.2))
      eslint-plugin-prettier:
        specifier: ^5.2.3
        version: 5.4.1(@types/eslint@9.6.1)(eslint-config-prettier@10.1.5(eslint@9.28.0(jiti@2.4.2)))(eslint@9.28.0(jiti@2.4.2))(prettier@3.5.3)
      eslint-plugin-react-hooks:
        specifier: ^5.0.0
        version: 5.2.0(eslint@9.28.0(jiti@2.4.2))
      eslint-plugin-react-refresh:
        specifier: ^0.4.18
        version: 0.4.20(eslint@9.28.0(jiti@2.4.2))
      eslint-plugin-simple-import-sort:
        specifier: ^12.1.1
        version: 12.1.1(eslint@9.28.0(jiti@2.4.2))
      globals:
        specifier: ^15.14.0
        version: 15.15.0
      husky:
        specifier: ^9.1.7
        version: 9.1.7
      postcss:
        specifier: ^8.5.2
        version: 8.5.4
      postcss-loader:
        specifier: ^8.1.1
        version: 8.1.1(@rspack/core@1.3.15(@swc/helpers@0.5.17))(postcss@8.5.4)(typescript@5.7.3)(webpack@5.99.9)
      postcss-preset-env:
        specifier: ^10.1.3
        version: 10.2.2(postcss@8.5.4)
      prettier:
        specifier: ^3.5.0
        version: 3.5.3
      rollup-plugin-delete:
        specifier: ^3.0.1
        version: 3.0.1(rollup@4.42.0)
      sass-embedded:
        specifier: ^1.86.1
        version: 1.89.0
      terser:
        specifier: ^5.36.0
        version: 5.41.0
      typescript:
        specifier: ~5.7.2
        version: 5.7.3
      typescript-eslint:
        specifier: ^8.22.0
        version: 8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)
      vite:
        specifier: ^6.1.0
        version: 6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0)
      vite-plugin-html:
        specifier: ^3.2.2
        version: 3.2.2(vite@6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0))
      vite-plugin-require-transform:
        specifier: ^1.0.21
        version: 1.0.21

packages:

  '@adobe/css-tools@4.3.3':
    resolution: {integrity: sha1-kHSb3ouJzUF2QiT1qsKc1BOPdf8=, tarball: http://registry.m.jd.com/@adobe/css-tools/download/@adobe/css-tools-4.3.3.tgz}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=, tarball: http://registry.m.jd.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha1-IA9xXmbVKiOyIalDVTSpHME61b4=, tarball: http://registry.m.jd.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.27.5':
    resolution: {integrity: sha1-fQZY7BqEIPyGbR3xsDvqDnmTTII=, tarball: http://registry.m.jd.com/@babel/compat-data/download/@babel/compat-data-7.27.5.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.27.4':
    resolution: {integrity: sha1-zB/FXQzhQKGCjR3Souuiha2/s84=, tarball: http://registry.m.jd.com/@babel/core/download/@babel/core-7.27.4.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.5':
    resolution: {integrity: sha1-PrAYZrNFuiYbBJEQIMviLdS+jIw=, tarball: http://registry.m.jd.com/@babel/generator/download/@babel/generator-7.27.5.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.27.3':
    resolution: {integrity: sha1-8x/Ya5FfxNrx86xpdsWb5whO2cU=, tarball: http://registry.m.jd.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.27.3.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha1-RqD276uAjVHSnOloWN0Qzocycz0=, tarball: http://registry.m.jd.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.27.1':
    resolution: {integrity: sha1-W+5CYqbqXdyFLQgGGZ6xfKPekoE=, tarball: http://registry.m.jd.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.27.1':
    resolution: {integrity: sha1-BbCILZe6HU0DUZ5LzmFdcK+hjFM=, tarball: http://registry.m.jd.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.4':
    resolution: {integrity: sha1-Feh0Y2i/pnF4X1km/3SzBkwpH6s=, tarball: http://registry.m.jd.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.4.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-member-expression-to-functions@7.27.1':
    resolution: {integrity: sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=, tarball: http://registry.m.jd.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=, tarball: http://registry.m.jd.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.3':
    resolution: {integrity: sha1-2wu8+6WAL573hwcFp++HiFCO3gI=, tarball: http://registry.m.jd.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.3.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.27.1':
    resolution: {integrity: sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=, tarball: http://registry.m.jd.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=, tarball: http://registry.m.jd.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.27.1':
    resolution: {integrity: sha1-RgHVx84usq6lgyjUNyVSP802LOY=, tarball: http://registry.m.jd.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.27.1':
    resolution: {integrity: sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=, tarball: http://registry.m.jd.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    resolution: {integrity: sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=, tarball: http://registry.m.jd.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=, tarball: http://registry.m.jd.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=, tarball: http://registry.m.jd.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=, tarball: http://registry.m.jd.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.27.1':
    resolution: {integrity: sha1-uIKFAJwxQnrzGNT+N2Uc1ioUJAk=, tarball: http://registry.m.jd.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.27.1.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.6':
    resolution: {integrity: sha1-ZFb+0VsstmnS0fq+hLZrNJkdgSw=, tarball: http://registry.m.jd.com/@babel/helpers/download/@babel/helpers-7.27.6.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.5':
    resolution: {integrity: sha1-7SL4cfEQqihab9k0oO/tYh0RiCY=, tarball: http://registry.m.jd.com/@babel/parser/download/@babel/parser-7.27.5.tgz}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1':
    resolution: {integrity: sha1-Yd2KjmH361aCaNG18SnaPu42S/k=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-firefox-class-in-computed-class-key/download/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1':
    resolution: {integrity: sha1-Q/cKbX79UjcO7731WuA9kbKThW0=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-safari-class-field-initializer-scope/download/@babel/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1':
    resolution: {integrity: sha1-vrYjvVc7i28wR70EwyUGrcPlinI=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1':
    resolution: {integrity: sha1-4TSlR56yupwCcU6MHr8eyQdhJP0=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1':
    resolution: {integrity: sha1-uxwlrzTXURXOIpod5/pEv4+VVnA=, tarball: http://registry.m.jd.com/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/download/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha1-eET5KJVG76n+usLeTP41igUL1wM=, tarball: http://registry.m.jd.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.27.1':
    resolution: {integrity: sha1-iIlK79KwO17mrRVip8jhWHSWrs0=, tarball: http://registry.m.jd.com/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.27.1':
    resolution: {integrity: sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=, tarball: http://registry.m.jd.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.27.1':
    resolution: {integrity: sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=, tarball: http://registry.m.jd.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=, tarball: http://registry.m.jd.com/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.27.1':
    resolution: {integrity: sha1-biBhBnujqwJm2DSp+UgRGW8qupo=, tarball: http://registry.m.jd.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.27.1':
    resolution: {integrity: sha1-ykM9+YPWjhN1OY58pxvypPb9idc=, tarball: http://registry.m.jd.com/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.27.1':
    resolution: {integrity: sha1-mpOJO5N5s5Rmx0R09VrwPeeMZuc=, tarball: http://registry.m.jd.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.27.1':
    resolution: {integrity: sha1-VYqdbiTPcoAt07YqS1Hg1iwPV/k=, tarball: http://registry.m.jd.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.27.5':
    resolution: {integrity: sha1-mMN0hdgVUzYj2ZL9FJrz57MUAVc=, tarball: http://registry.m.jd.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.27.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.27.1':
    resolution: {integrity: sha1-3UCmo3Df1J0yNiriBt2vK7CCqSU=, tarball: http://registry.m.jd.com/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.27.1':
    resolution: {integrity: sha1-fpINViWyW7zNMGGu+8wFgF7VbOQ=, tarball: http://registry.m.jd.com/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.27.1':
    resolution: {integrity: sha1-A7sEvqLHsvcR8NtzBKjaRqhcztQ=, tarball: http://registry.m.jd.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.27.1':
    resolution: {integrity: sha1-gWYueL9ec0qXmCwrfwp5MojvPKo=, tarball: http://registry.m.jd.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.27.3':
    resolution: {integrity: sha1-PMgpnteY2akJ+NZt3rQISewy47A=, tarball: http://registry.m.jd.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.27.3.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.27.1':
    resolution: {integrity: sha1-qmgh3oZMUosf7PKG8KF0446Cb00=, tarball: http://registry.m.jd.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.27.1':
    resolution: {integrity: sha1-8fv2KOzhjhLnsysXWUDmg1j1RtE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1':
    resolution: {integrity: sha1-UEOFTKYgqUFJNy5pAw/4y2qesOw=, tarball: http://registry.m.jd.com/@babel/plugin-transform-duplicate-named-capturing-groups-regex/download/@babel/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-dynamic-import@7.27.1':
    resolution: {integrity: sha1-THjzVVKsDgaqH248Vz1naV6K9aQ=, tarball: http://registry.m.jd.com/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.27.1':
    resolution: {integrity: sha1-/El7EtgnflWXR/Wj7YaN2AZPg+E=, tarball: http://registry.m.jd.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.27.1':
    resolution: {integrity: sha1-ccpp00ce3W2qcRz038NABBXfnCM=, tarball: http://registry.m.jd.com/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.27.1':
    resolution: {integrity: sha1-vCT3CA6f9yG2OnCseyVkyhW2xAo=, tarball: http://registry.m.jd.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.27.1':
    resolution: {integrity: sha1-TQvzB3IOTc5tfDD8sf1sp3ves6c=, tarball: http://registry.m.jd.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.27.1':
    resolution: {integrity: sha1-ouDObvJWN2vVJ/KQ2gI5g1J6T0w=, tarball: http://registry.m.jd.com/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.27.1':
    resolution: {integrity: sha1-uq76TRCh1CBvnc3aUNfVgnu3CyQ=, tarball: http://registry.m.jd.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.27.1':
    resolution: {integrity: sha1-iQyyDgJw4OW+vj8CW0NIQcMtW6o=, tarball: http://registry.m.jd.com/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.27.1':
    resolution: {integrity: sha1-N7iLpZTYUkGOmVNvVhL3lfI66vk=, tarball: http://registry.m.jd.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.27.1':
    resolution: {integrity: sha1-pBRfnYfCKR/i0F+ZS2XbpOPnGW8=, tarball: http://registry.m.jd.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.27.1':
    resolution: {integrity: sha1-jkTtN8J4fswjvcNn9Jl3R2YU6DI=, tarball: http://registry.m.jd.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.27.1':
    resolution: {integrity: sha1-AOBbYYYwcNDzKSoAEmwWwOAkxO0=, tarball: http://registry.m.jd.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.27.1':
    resolution: {integrity: sha1-Y/LPT23BXevBL2lORHFIY9NM0zQ=, tarball: http://registry.m.jd.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1':
    resolution: {integrity: sha1-8yuPeBjY/AzEbuIKjvdfBxr5duE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.27.1':
    resolution: {integrity: sha1-JZxDk5coytFwasFzUbfmp76hq+s=, tarball: http://registry.m.jd.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1':
    resolution: {integrity: sha1-T50xU79ngtc91CeFqdItAxl7yR0=, tarball: http://registry.m.jd.com/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.27.1':
    resolution: {integrity: sha1-YU4LFcyADlmX2t2b1upSTtbIGcY=, tarball: http://registry.m.jd.com/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.27.3':
    resolution: {integrity: sha1-zhMKpz/vgovD4+g1+bxhRL4+scA=, tarball: http://registry.m.jd.com/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.27.3.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.27.1':
    resolution: {integrity: sha1-HJMs0nvzh0xDpcrE9D6/lwyYcbU=, tarball: http://registry.m.jd.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.27.1':
    resolution: {integrity: sha1-hMc0Hr3jXM02sTfp5FhmglByoww=, tarball: http://registry.m.jd.com/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.27.1':
    resolution: {integrity: sha1-h0zjxPBrd4BZLpRgJut2oygwRU8=, tarball: http://registry.m.jd.com/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.27.1':
    resolution: {integrity: sha1-gDNLVLmxrFJEFVoMgwShh6YY1ac=, tarball: http://registry.m.jd.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.27.1':
    resolution: {integrity: sha1-/ay6scXtgexw39u4shPWXaFItq8=, tarball: http://registry.m.jd.com/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.27.1':
    resolution: {integrity: sha1-TbvvKDtbLwGiHoHimfduNfkA+xE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.27.1':
    resolution: {integrity: sha1-B+r9YYgAWR6IBzoK8blA2aQsZCQ=, tarball: http://registry.m.jd.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-display-name@7.27.1':
    resolution: {integrity: sha1-Q68xNi1x94SM+sDLwhKIKxoW6A8=, tarball: http://registry.m.jd.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-development@7.27.1':
    resolution: {integrity: sha1-R/+VlA4go6cOaK09T8tle2R/bJg=, tarball: http://registry.m.jd.com/@babel/plugin-transform-react-jsx-development/download/@babel/plugin-transform-react-jsx-development-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-self@7.27.1':
    resolution: {integrity: sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=, tarball: http://registry.m.jd.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-source@7.27.1':
    resolution: {integrity: sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=, tarball: http://registry.m.jd.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx@7.27.1':
    resolution: {integrity: sha1-ECO8lLeLCi1oyCtelq7Vc7z7nbA=, tarball: http://registry.m.jd.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-pure-annotations@7.27.1':
    resolution: {integrity: sha1-M58c41Xq4kLgZJ8jKxxokHwC6Hk=, tarball: http://registry.m.jd.com/@babel/plugin-transform-react-pure-annotations/download/@babel/plugin-transform-react-pure-annotations-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.27.5':
    resolution: {integrity: sha1-DAH04OTM7RX2juFLnHbayYE4UMc=, tarball: http://registry.m.jd.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.27.5.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regexp-modifiers@7.27.1':
    resolution: {integrity: sha1-35ulV3yXTj8USYiLcLdhaZmKbQk=, tarball: http://registry.m.jd.com/@babel/plugin-transform-regexp-modifiers/download/@babel/plugin-transform-regexp-modifiers-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-reserved-words@7.27.1':
    resolution: {integrity: sha1-QPukh4zL0cVmBaRHmjqJGsAnS7Q=, tarball: http://registry.m.jd.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-runtime@7.27.4':
    resolution: {integrity: sha1-3uXF22VDMT0a4bSx7BIv8edzUrk=, tarball: http://registry.m.jd.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.27.4.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.27.1':
    resolution: {integrity: sha1-Uyq9rN7Ie/7h4O+OL83uVD/jK5A=, tarball: http://registry.m.jd.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.27.1':
    resolution: {integrity: sha1-GiZNX8EnUJGPUOP+PiTkNxeKuwg=, tarball: http://registry.m.jd.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.27.1':
    resolution: {integrity: sha1-GJhJNdnSKWhDpJHXigFJOffc0oA=, tarball: http://registry.m.jd.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.27.1':
    resolution: {integrity: sha1-Gg6zXYuz5u/AbJ/UDrC871SDKLg=, tarball: http://registry.m.jd.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.27.1':
    resolution: {integrity: sha1-cOlmu0kuA1Cc836vptzDBR+EQ2k=, tarball: http://registry.m.jd.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.27.1':
    resolution: {integrity: sha1-PjFD+EOK74Qt4ogW7OWHgBkM+AY=, tarball: http://registry.m.jd.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.27.1':
    resolution: {integrity: sha1-vf4tMXDHjFaRo8O+k0yMAIdSWVY=, tarball: http://registry.m.jd.com/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.27.1':
    resolution: {integrity: sha1-JZSPXDldsV9gkCjjcGZ+2Lrpr5c=, tarball: http://registry.m.jd.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.27.1':
    resolution: {integrity: sha1-arcG0Q+AG1xy2ouyVIVh+gQZPNE=, tarball: http://registry.m.jd.com/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.27.2':
    resolution: {integrity: sha1-EG5r+tkrWRsfb3b9TPE7dyWnv5o=, tarball: http://registry.m.jd.com/@babel/preset-env/download/@babel/preset-env-7.27.2.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=, tarball: http://registry.m.jd.com/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/preset-react@7.27.1':
    resolution: {integrity: sha1-huoKXKOYRmP3RL4v0my2dHw/0Ow=, tarball: http://registry.m.jd.com/@babel/preset-react/download/@babel/preset-react-7.27.1.tgz}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.27.6':
    resolution: {integrity: sha1-7EBwoE12uujduxB3C6VXFKQXt8Y=, tarball: http://registry.m.jd.com/@babel/runtime/download/@babel/runtime-7.27.6.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=, tarball: http://registry.m.jd.com/@babel/template/download/@babel/template-7.27.2.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.4':
    resolution: {integrity: sha1-sARaxwI8hHLD017/18yevWONpuo=, tarball: http://registry.m.jd.com/@babel/traverse/download/@babel/traverse-7.27.4.tgz}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.6':
    resolution: {integrity: sha1-pDTKet1RTU5kbID3N1wKor78VTU=, tarball: http://registry.m.jd.com/@babel/types/download/@babel/types-7.27.6.tgz}
    engines: {node: '>=6.9.0'}

  '@bufbuild/protobuf@2.4.0':
    resolution: {integrity: sha1-72v9L8A3SpWkkEzcX9jgLMmZz3M=, tarball: http://registry.m.jd.com/@bufbuild/protobuf/download/@bufbuild/protobuf-2.4.0.tgz}

  '@commitlint/cli@19.8.1':
    resolution: {integrity: sha1-hffZ8zE0Th8KK52LJP02lUZuEVg=, tarball: http://registry.m.jd.com/@commitlint/cli/download/@commitlint/cli-19.8.1.tgz}
    engines: {node: '>=v18'}
    hasBin: true

  '@commitlint/config-conventional@19.8.1':
    resolution: {integrity: sha1-6rQt9YzaRPGEEK4MvWeF7OAPIUs=, tarball: http://registry.m.jd.com/@commitlint/config-conventional/download/@commitlint/config-conventional-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/config-validator@19.8.1':
    resolution: {integrity: sha1-Kem7E2D6QblDmyPY4l3qrwlzBrU=, tarball: http://registry.m.jd.com/@commitlint/config-validator/download/@commitlint/config-validator-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/ensure@19.8.1':
    resolution: {integrity: sha1-k4xU1vWGvaYAtcjo6ELtsoFUbhQ=, tarball: http://registry.m.jd.com/@commitlint/ensure/download/@commitlint/ensure-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/execute-rule@19.8.1':
    resolution: {integrity: sha1-UwADY7c3dz4tJel8IPFeqnh0IGc=, tarball: http://registry.m.jd.com/@commitlint/execute-rule/download/@commitlint/execute-rule-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/format@19.8.1':
    resolution: {integrity: sha1-PgmxKRs+KQkteobwr7vPwNmdOtQ=, tarball: http://registry.m.jd.com/@commitlint/format/download/@commitlint/format-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/is-ignored@19.8.1':
    resolution: {integrity: sha1-/tCFE2DqLSF5nq+Oye9tmMFVNuM=, tarball: http://registry.m.jd.com/@commitlint/is-ignored/download/@commitlint/is-ignored-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/lint@19.8.1':
    resolution: {integrity: sha1-whv5AAylTkHFsBOcmKrxJHPAO7A=, tarball: http://registry.m.jd.com/@commitlint/lint/download/@commitlint/lint-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/load@19.8.1':
    resolution: {integrity: sha1-uZex9lqWG/CkcYnxX23IeGzrRXY=, tarball: http://registry.m.jd.com/@commitlint/load/download/@commitlint/load-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/message@19.8.1':
    resolution: {integrity: sha1-1dDYeDdIPZ+bRVn/oG4aqibSZtY=, tarball: http://registry.m.jd.com/@commitlint/message/download/@commitlint/message-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/parse@19.8.1':
    resolution: {integrity: sha1-cxJdBPB/EUd89WPL/gzJ9tyFp0c=, tarball: http://registry.m.jd.com/@commitlint/parse/download/@commitlint/parse-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/read@19.8.1':
    resolution: {integrity: sha1-gSkw/Q9hbnluEidRy5gzRuVFTsg=, tarball: http://registry.m.jd.com/@commitlint/read/download/@commitlint/read-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/resolve-extends@19.8.1':
    resolution: {integrity: sha1-pEu0wi4+fUB8yaN1j89Y9cNgtpQ=, tarball: http://registry.m.jd.com/@commitlint/resolve-extends/download/@commitlint/resolve-extends-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/rules@19.8.1':
    resolution: {integrity: sha1-HOpT1b+XDOVtwQXh2l5mVaL+el8=, tarball: http://registry.m.jd.com/@commitlint/rules/download/@commitlint/rules-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/to-lines@19.8.1':
    resolution: {integrity: sha1-waKKhFQse6MhwcEReLg64CQle0c=, tarball: http://registry.m.jd.com/@commitlint/to-lines/download/@commitlint/to-lines-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/top-level@19.8.1':
    resolution: {integrity: sha1-LJQhidg6KbIf97pukWBzAe/fWRY=, tarball: http://registry.m.jd.com/@commitlint/top-level/download/@commitlint/top-level-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@commitlint/types@19.8.1':
    resolution: {integrity: sha1-eXH71WsM+zFpKk4ZQbdKyCF8ROU=, tarball: http://registry.m.jd.com/@commitlint/types/download/@commitlint/types-19.8.1.tgz}
    engines: {node: '>=v18'}

  '@csstools/cascade-layer-name-parser@2.0.5':
    resolution: {integrity: sha1-Q/livr6tAFKp/tGi3usR+F78vHI=, tarball: http://registry.m.jd.com/@csstools/cascade-layer-name-parser/download/@csstools/cascade-layer-name-parser-2.0.5.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.5
      '@csstools/css-tokenizer': ^3.0.4

  '@csstools/color-helpers@5.0.2':
    resolution: {integrity: sha1-glksmnwrg8KT2RYYlOKmRx/rl7g=, tarball: http://registry.m.jd.com/@csstools/color-helpers/download/@csstools/color-helpers-5.0.2.tgz}
    engines: {node: '>=18'}

  '@csstools/css-calc@2.1.4':
    resolution: {integrity: sha1-hHP2Pi/NbkWYON1BJAHVlI8iTGU=, tarball: http://registry.m.jd.com/@csstools/css-calc/download/@csstools/css-calc-2.1.4.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.5
      '@csstools/css-tokenizer': ^3.0.4

  '@csstools/css-color-parser@3.0.10':
    resolution: {integrity: sha1-efxohk3UPDtngtKzgovA+p0IXBA=, tarball: http://registry.m.jd.com/@csstools/css-color-parser/download/@csstools/css-color-parser-3.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.5
      '@csstools/css-tokenizer': ^3.0.4

  '@csstools/css-parser-algorithms@3.0.5':
    resolution: {integrity: sha1-V1U3Cpopq67FUVtDyLPyz5wuMHY=, tarball: http://registry.m.jd.com/@csstools/css-parser-algorithms/download/@csstools/css-parser-algorithms-3.0.5.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-tokenizer': ^3.0.4

  '@csstools/css-tokenizer@3.0.4':
    resolution: {integrity: sha1-Mz/tq8P9Go5dAQABNzHPGeaoxdM=, tarball: http://registry.m.jd.com/@csstools/css-tokenizer/download/@csstools/css-tokenizer-3.0.4.tgz}
    engines: {node: '>=18'}

  '@csstools/media-query-list-parser@4.0.3':
    resolution: {integrity: sha1-eux3vLicLagO8gfnP0dO+eGzzfE=, tarball: http://registry.m.jd.com/@csstools/media-query-list-parser/download/@csstools/media-query-list-parser-4.0.3.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.5
      '@csstools/css-tokenizer': ^3.0.4

  '@csstools/postcss-cascade-layers@5.0.1':
    resolution: {integrity: sha1-lkAxPmS145Ez3n44pap/QNwllZc=, tarball: http://registry.m.jd.com/@csstools/postcss-cascade-layers/download/@csstools/postcss-cascade-layers-5.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-color-function@4.0.10':
    resolution: {integrity: sha1-Ea1Dpm7yzHlKuCagffi1+p+0ejo=, tarball: http://registry.m.jd.com/@csstools/postcss-color-function/download/@csstools/postcss-color-function-4.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-color-mix-function@3.0.10':
    resolution: {integrity: sha1-jJ0Mz65cRamHDdhIB+oplcejpRQ=, tarball: http://registry.m.jd.com/@csstools/postcss-color-mix-function/download/@csstools/postcss-color-mix-function-3.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-color-mix-variadic-function-arguments@1.0.0':
    resolution: {integrity: sha1-CynLm0Yw1+1oVJ2yZWYtQVVKF+0=, tarball: http://registry.m.jd.com/@csstools/postcss-color-mix-variadic-function-arguments/download/@csstools/postcss-color-mix-variadic-function-arguments-1.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-content-alt-text@2.0.6':
    resolution: {integrity: sha1-VIhiIm6sVLqw7l8b86mYE5OrIEs=, tarball: http://registry.m.jd.com/@csstools/postcss-content-alt-text/download/@csstools/postcss-content-alt-text-2.0.6.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-exponential-functions@2.0.9':
    resolution: {integrity: sha1-/APRJyiIy3fmTMGn2KMwFuTwXGk=, tarball: http://registry.m.jd.com/@csstools/postcss-exponential-functions/download/@csstools/postcss-exponential-functions-2.0.9.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-font-format-keywords@4.0.0':
    resolution: {integrity: sha1-ZzCDbrAVP/TzhAQWzCMi8SnAhuY=, tarball: http://registry.m.jd.com/@csstools/postcss-font-format-keywords/download/@csstools/postcss-font-format-keywords-4.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-gamut-mapping@2.0.10':
    resolution: {integrity: sha1-9RjZQSMdch2+z1tB48RBiF/y8os=, tarball: http://registry.m.jd.com/@csstools/postcss-gamut-mapping/download/@csstools/postcss-gamut-mapping-2.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-gradients-interpolation-method@5.0.10':
    resolution: {integrity: sha1-MUbaNSwxFCpyH9ugYqw6bRHbvsM=, tarball: http://registry.m.jd.com/@csstools/postcss-gradients-interpolation-method/download/@csstools/postcss-gradients-interpolation-method-5.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-hwb-function@4.0.10':
    resolution: {integrity: sha1-+T88RX5kQKw375uQj+tdkBtBfVA=, tarball: http://registry.m.jd.com/@csstools/postcss-hwb-function/download/@csstools/postcss-hwb-function-4.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-ic-unit@4.0.2':
    resolution: {integrity: sha1-dWHgnbZfrIMEzuq53T5cbkNBRYc=, tarball: http://registry.m.jd.com/@csstools/postcss-ic-unit/download/@csstools/postcss-ic-unit-4.0.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-initial@2.0.1':
    resolution: {integrity: sha1-w4W9nYrTGtFZ7deZIGnpfO6k0Jo=, tarball: http://registry.m.jd.com/@csstools/postcss-initial/download/@csstools/postcss-initial-2.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-is-pseudo-class@5.0.2':
    resolution: {integrity: sha1-AGR/2X29z734+EABc42aWsfNoEk=, tarball: http://registry.m.jd.com/@csstools/postcss-is-pseudo-class/download/@csstools/postcss-is-pseudo-class-5.0.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-light-dark-function@2.0.9':
    resolution: {integrity: sha1-n7CAGIkHU5c0qdUxHSocuCUx7zg=, tarball: http://registry.m.jd.com/@csstools/postcss-light-dark-function/download/@csstools/postcss-light-dark-function-2.0.9.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-float-and-clear@3.0.0':
    resolution: {integrity: sha1-YmF1ZBgs+Gq11OdIVDOtkeTFhXE=, tarball: http://registry.m.jd.com/@csstools/postcss-logical-float-and-clear/download/@csstools/postcss-logical-float-and-clear-3.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-overflow@2.0.0':
    resolution: {integrity: sha1-xt58XwTj1CM3MahH9sYoGby8+h0=, tarball: http://registry.m.jd.com/@csstools/postcss-logical-overflow/download/@csstools/postcss-logical-overflow-2.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-overscroll-behavior@2.0.0':
    resolution: {integrity: sha1-Q8A+rs3zQFXvU7+raR223JelPTc=, tarball: http://registry.m.jd.com/@csstools/postcss-logical-overscroll-behavior/download/@csstools/postcss-logical-overscroll-behavior-2.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-resize@3.0.0':
    resolution: {integrity: sha1-TfDusaYde9hTleVqXM41C12/3KY=, tarball: http://registry.m.jd.com/@csstools/postcss-logical-resize/download/@csstools/postcss-logical-resize-3.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-logical-viewport-units@3.0.4':
    resolution: {integrity: sha1-AW2YqLe1+WnljrhBNEfrgBrdFvw=, tarball: http://registry.m.jd.com/@csstools/postcss-logical-viewport-units/download/@csstools/postcss-logical-viewport-units-3.0.4.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-media-minmax@2.0.9':
    resolution: {integrity: sha1-GEJS1bkxVa5SZokyiva98/wROYc=, tarball: http://registry.m.jd.com/@csstools/postcss-media-minmax/download/@csstools/postcss-media-minmax-2.0.9.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-media-queries-aspect-ratio-number-values@3.0.5':
    resolution: {integrity: sha1-9IXDHsE9aw+1xSijR0M0pA7/XxE=, tarball: http://registry.m.jd.com/@csstools/postcss-media-queries-aspect-ratio-number-values/download/@csstools/postcss-media-queries-aspect-ratio-number-values-3.0.5.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-nested-calc@4.0.0':
    resolution: {integrity: sha1-dU4Q7caVjWZMEc3pF/RLoUQUHGI=, tarball: http://registry.m.jd.com/@csstools/postcss-nested-calc/download/@csstools/postcss-nested-calc-4.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-normalize-display-values@4.0.0':
    resolution: {integrity: sha1-7N3i2vThkuXaDG/ZM7bYr/MvKjY=, tarball: http://registry.m.jd.com/@csstools/postcss-normalize-display-values/download/@csstools/postcss-normalize-display-values-4.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-oklab-function@4.0.10':
    resolution: {integrity: sha1-1MI8Ud0L5F5t7d4iQy19AANxB4A=, tarball: http://registry.m.jd.com/@csstools/postcss-oklab-function/download/@csstools/postcss-oklab-function-4.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-progressive-custom-properties@4.1.0':
    resolution: {integrity: sha1-cMjUG1d/QCNjO343kWBOC383dbw=, tarball: http://registry.m.jd.com/@csstools/postcss-progressive-custom-properties/download/@csstools/postcss-progressive-custom-properties-4.1.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-random-function@2.0.1':
    resolution: {integrity: sha1-MZHzL+cpNuNh2t99v7VaAgniaR4=, tarball: http://registry.m.jd.com/@csstools/postcss-random-function/download/@csstools/postcss-random-function-2.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-relative-color-syntax@3.0.10':
    resolution: {integrity: sha1-2qhAWDlpRh4eBrEunFkeUqeQ7IY=, tarball: http://registry.m.jd.com/@csstools/postcss-relative-color-syntax/download/@csstools/postcss-relative-color-syntax-3.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-scope-pseudo-class@4.0.1':
    resolution: {integrity: sha1-n+YOnW2R1Y+1/Gx2ikD25H6JojU=, tarball: http://registry.m.jd.com/@csstools/postcss-scope-pseudo-class/download/@csstools/postcss-scope-pseudo-class-4.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-sign-functions@1.1.4':
    resolution: {integrity: sha1-qaxWlUAUrkxRNHWz8bPjQkoeDBI=, tarball: http://registry.m.jd.com/@csstools/postcss-sign-functions/download/@csstools/postcss-sign-functions-1.1.4.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-stepped-value-functions@4.0.9':
    resolution: {integrity: sha1-NgNvGg5eXuIwjnLzyctDNWfDh7k=, tarball: http://registry.m.jd.com/@csstools/postcss-stepped-value-functions/download/@csstools/postcss-stepped-value-functions-4.0.9.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-text-decoration-shorthand@4.0.2':
    resolution: {integrity: sha1-o7z4BJLm3aNkd1OKuOiUOQjJ+Ao=, tarball: http://registry.m.jd.com/@csstools/postcss-text-decoration-shorthand/download/@csstools/postcss-text-decoration-shorthand-4.0.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-trigonometric-functions@4.0.9':
    resolution: {integrity: sha1-P5TtLjGbV/LFlyC2Tk0Kim+4w7I=, tarball: http://registry.m.jd.com/@csstools/postcss-trigonometric-functions/download/@csstools/postcss-trigonometric-functions-4.0.9.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/postcss-unset-value@4.0.0':
    resolution: {integrity: sha1-fKqYGjQZbQanN3VIZLr3fWTeS7o=, tarball: http://registry.m.jd.com/@csstools/postcss-unset-value/download/@csstools/postcss-unset-value-4.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@csstools/selector-resolve-nested@3.1.0':
    resolution: {integrity: sha1-hIxvRMtl43M+R4MZuTQreqQ2+sc=, tarball: http://registry.m.jd.com/@csstools/selector-resolve-nested/download/@csstools/selector-resolve-nested-3.1.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss-selector-parser: ^7.0.0

  '@csstools/selector-specificity@5.0.0':
    resolution: {integrity: sha1-A3gXtXQmITTKvWj8TsGkVPFoQHs=, tarball: http://registry.m.jd.com/@csstools/selector-specificity/download/@csstools/selector-specificity-5.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss-selector-parser: ^7.0.0

  '@csstools/utilities@2.0.0':
    resolution: {integrity: sha1-9/8P7jjJ/7VkbUe2kG4LyIaL3mA=, tarball: http://registry.m.jd.com/@csstools/utilities/download/@csstools/utilities-2.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  '@esbuild/aix-ppc64@0.25.5':
    resolution: {integrity: sha1-Tg+Rd2wrNA51VY9gVSGV9vrQnxg=, tarball: http://registry.m.jd.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/android-arm64@0.25.5':
    resolution: {integrity: sha1-vHZkB/FxiSP2uAecjGG/hqw6ak8=, tarball: http://registry.m.jd.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/android-arm@0.25.5':
    resolution: {integrity: sha1-QpDW00B7rjiDrSze0QgaI0RzziY=, tarball: http://registry.m.jd.com/@esbuild/android-arm/download/@esbuild/android-arm-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/android-x64@0.25.5':
    resolution: {integrity: sha1-QMEdnLyk8kBlSMipiV0yG8OzXv8=, tarball: http://registry.m.jd.com/@esbuild/android-x64/download/@esbuild/android-x64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/darwin-arm64@0.25.5':
    resolution: {integrity: sha1-Sdi/ix35X3WayB6x0HNgGABtfjQ=, tarball: http://registry.m.jd.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/darwin-x64@0.25.5':
    resolution: {integrity: sha1-4npdkqFIhu8dSS/VD8YaLU2H5Bg=, tarball: http://registry.m.jd.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/freebsd-arm64@0.25.5':
    resolution: {integrity: sha1-l87eWdY4hAyhBOYFzbnxsRi6Cxw=, tarball: http://registry.m.jd.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/freebsd-x64@0.25.5':
    resolution: {integrity: sha1-ccd4EgQqGoGQw9WB4UDRW4drnG8=, tarball: http://registry.m.jd.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-arm64@0.25.5':
    resolution: {integrity: sha1-97fI+X7/j/0uR/bGfrXJdl8hgbg=, tarball: http://registry.m.jd.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-arm@0.25.5':
    resolution: {integrity: sha1-KgvnG2zYIB+lWa6kVZjf+rwF2RE=, tarball: http://registry.m.jd.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-ia32@0.25.5':
    resolution: {integrity: sha1-djQURjzZ6m+h+WVV0nYvn4TGF4M=, tarball: http://registry.m.jd.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-loong64@0.25.5':
    resolution: {integrity: sha1-QozyIT/3hqUCpSyWzynR/PHrhQY=, tarball: http://registry.m.jd.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-mips64el@0.25.5':
    resolution: {integrity: sha1-XLzH/YQbTNUzWK/TNSfNOU4yXZY=, tarball: http://registry.m.jd.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-ppc64@0.25.5':
    resolution: {integrity: sha1-DZVKs5zk9eUPAMT4xP04+XbBOtk=, tarball: http://registry.m.jd.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-riscv64@0.25.5':
    resolution: {integrity: sha1-Dn3TBzBQWr2AiDIehJfpS1R7+x4=, tarball: http://registry.m.jd.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-s390x@0.25.5':
    resolution: {integrity: sha1-VmmvgTJ6OYozbX5A4yC1u9bm5y0=, tarball: http://registry.m.jd.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/linux-x64@0.25.5':
    resolution: {integrity: sha1-sjV90VOqSQOJZ93B/9kMaKnSoNQ=, tarball: http://registry.m.jd.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/netbsd-arm64@0.25.5':
    resolution: {integrity: sha1-U7TfuP4c7pN3fJ42aJO9Paprpj0=, tarball: http://registry.m.jd.com/@esbuild/netbsd-arm64/download/@esbuild/netbsd-arm64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/netbsd-x64@0.25.5':
    resolution: {integrity: sha1-oCBvYxTOfchxO3cycD0PWN4dHnk=, tarball: http://registry.m.jd.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/openbsd-arm64@0.25.5':
    resolution: {integrity: sha1-Knlsh8ROjeeAAdgIx32UiiHsIv0=, tarball: http://registry.m.jd.com/@esbuild/openbsd-arm64/download/@esbuild/openbsd-arm64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/openbsd-x64@0.25.5':
    resolution: {integrity: sha1-KNDNiQm3+jlTr5mPKy7TT1dnKPA=, tarball: http://registry.m.jd.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/sunos-x64@0.25.5':
    resolution: {integrity: sha1-ooFk9bmX6CR9QH42yQ0/1d2+DcU=, tarball: http://registry.m.jd.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/win32-arm64@0.25.5':
    resolution: {integrity: sha1-bq2+rTjovRL2M6UZDkXv+A4kAH4=, tarball: http://registry.m.jd.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/win32-ia32@0.25.5':
    resolution: {integrity: sha1-urYogAVIL57Srbne1+iOuppizA0=, tarball: http://registry.m.jd.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.25.5.tgz}
    engines: {node: '>=18'}

  '@esbuild/win32-x64@0.25.5':
    resolution: {integrity: sha1-f8EUr19lY/GfczJLXV/zbs4IA9E=, tarball: http://registry.m.jd.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.25.5.tgz}
    engines: {node: '>=18'}

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha1-YHCEYwxsAzmSoILebm+8GotSF1o=, tarball: http://registry.m.jd.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=, tarball: http://registry.m.jd.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.20.0':
    resolution: {integrity: sha1-ehIy6CN2cS0zQAEqL1YaJ2TRmI8=, tarball: http://registry.m.jd.com/@eslint/config-array/download/@eslint/config-array-0.20.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.2.2':
    resolution: {integrity: sha1-N3n3a4lN46jsR2O3lmDm1U1bEBA=, tarball: http://registry.m.jd.com/@eslint/config-helpers/download/@eslint/config-helpers-0.2.2.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.14.0':
    resolution: {integrity: sha1-MmKJOAlo6vfpbzZOHkz4863y0AM=, tarball: http://registry.m.jd.com/@eslint/core/download/@eslint/core-0.14.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=, tarball: http://registry.m.jd.com/@eslint/eslintrc/download/@eslint/eslintrc-3.3.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.28.0':
    resolution: {integrity: sha1-eCLMwvjK58PNT5Ajd9Ug6a4D+EQ=, tarball: http://registry.m.jd.com/@eslint/js/download/@eslint/js-9.28.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=, tarball: http://registry.m.jd.com/@eslint/object-schema/download/@eslint/object-schema-2.1.6.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.3.1':
    resolution: {integrity: sha1-txsDey1NaDlt8EqMNaSUgeVZMGc=, tarball: http://registry.m.jd.com/@eslint/plugin-kit/download/@eslint/plugin-kit-0.3.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=, tarball: http://registry.m.jd.com/@humanfs/core/download/@humanfs/core-0.19.1.tgz}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=, tarball: http://registry.m.jd.com/@humanfs/node/download/@humanfs/node-0.16.6.tgz}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=, tarball: http://registry.m.jd.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=, tarball: http://registry.m.jd.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.3.1.tgz}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.3':
    resolution: {integrity: sha1-wrnS43TuYsWG062+qHGZsdenpro=, tarball: http://registry.m.jd.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.4.3.tgz}
    engines: {node: '>=18.18'}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=, tarball: http://registry.m.jd.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz}
    engines: {node: '>=12'}

  '@jd/jmfe-node-jss@1.2.14':
    resolution: {integrity: sha1-aEg7LODQI+mlSH3Dd/Dobz+Ki/Y=, tarball: http://registry.m.jd.com/@jd/jmfe-node-jss/download/@jd/jmfe-node-jss-1.2.14.tgz}

  '@jd/upload-oss-tools@1.1.23':
    resolution: {integrity: sha1-1nhL54L8UD473OSUDsz3bLTlCag=, tarball: http://registry.m.jd.com/@jd/upload-oss-tools/download/@jd/upload-oss-tools-1.1.23.tgz}
    hasBin: true

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=, tarball: http://registry.m.jd.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=, tarball: http://registry.m.jd.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=, tarball: http://registry.m.jd.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.6':
    resolution: {integrity: sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=, tarball: http://registry.m.jd.com/@jridgewell/source-map/download/@jridgewell/source-map-0.3.6.tgz}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=, tarball: http://registry.m.jd.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=, tarball: http://registry.m.jd.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz}

  '@module-federation/error-codes@0.14.3':
    resolution: {integrity: sha1-5rXDgCQPVlC89noZBrIicbiR0sU=, tarball: http://registry.m.jd.com/@module-federation/error-codes/download/@module-federation/error-codes-0.14.3.tgz}

  '@module-federation/runtime-core@0.14.3':
    resolution: {integrity: sha1-Q0AlwTBCeOMLvAJKrqsIbYD54ZY=, tarball: http://registry.m.jd.com/@module-federation/runtime-core/download/@module-federation/runtime-core-0.14.3.tgz}

  '@module-federation/runtime-tools@0.14.3':
    resolution: {integrity: sha1-+hQUtEnL5ftty95O0CyF4M3MdYs=, tarball: http://registry.m.jd.com/@module-federation/runtime-tools/download/@module-federation/runtime-tools-0.14.3.tgz}

  '@module-federation/runtime@0.14.3':
    resolution: {integrity: sha1-/JFCwJMAHGeg/KyvU9TrV0npu9Y=, tarball: http://registry.m.jd.com/@module-federation/runtime/download/@module-federation/runtime-0.14.3.tgz}

  '@module-federation/sdk@0.14.3':
    resolution: {integrity: sha1-oD438csBgoNULPxmqH56N+Oc/ho=, tarball: http://registry.m.jd.com/@module-federation/sdk/download/@module-federation/sdk-0.14.3.tgz}

  '@module-federation/webpack-bundler-runtime@0.14.3':
    resolution: {integrity: sha1-LWv2PpP2JqL15Gm8V/tbzAmP7jc=, tarball: http://registry.m.jd.com/@module-federation/webpack-bundler-runtime/download/@module-federation/webpack-bundler-runtime-0.14.3.tgz}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=, tarball: http://registry.m.jd.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=, tarball: http://registry.m.jd.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=, tarball: http://registry.m.jd.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz}
    engines: {node: '>= 8'}

  '@parcel/watcher-android-arm64@2.5.1':
    resolution: {integrity: sha1-UH+DbX4gQveYx9B60Zw1RvmEisE=, tarball: http://registry.m.jd.com/@parcel/watcher-android-arm64/download/@parcel/watcher-android-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-darwin-arm64@2.5.1':
    resolution: {integrity: sha1-PSbc443mWQ73nEfsLFV5PAatT2c=, tarball: http://registry.m.jd.com/@parcel/watcher-darwin-arm64/download/@parcel/watcher-darwin-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-darwin-x64@2.5.1':
    resolution: {integrity: sha1-mfOvOGkGnM93Tk3fzPfmT9IxHvg=, tarball: http://registry.m.jd.com/@parcel/watcher-darwin-x64/download/@parcel/watcher-darwin-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-freebsd-x64@2.5.1':
    resolution: {integrity: sha1-FNaFd0Gp9R3+UdWwi3yK/bxzrZs=, tarball: http://registry.m.jd.com/@parcel/watcher-freebsd-x64/download/@parcel/watcher-freebsd-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    resolution: {integrity: sha1-Q8MkbWiSOB20c7tPZjIprSC2CaE=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-arm-glibc/download/@parcel/watcher-linux-arm-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-arm-musl@2.5.1':
    resolution: {integrity: sha1-ZjdQ9wkLtieNIhDeZD64o/eA0I4=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-arm-musl/download/@parcel/watcher-linux-arm-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    resolution: {integrity: sha1-umDh9Wl39+R81+Ma1l0V/cvQfjA=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-arm64-glibc/download/@parcel/watcher-linux-arm64-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    resolution: {integrity: sha1-9/vN/y8ExSb5bqwB+XQZpqmYVdI=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-arm64-musl/download/@parcel/watcher-linux-arm64-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    resolution: {integrity: sha1-TS6g9jPrGRfYPUgzks5hgbapLk4=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-x64-glibc/download/@parcel/watcher-linux-x64-glibc-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-linux-x64-musl@2.5.1':
    resolution: {integrity: sha1-J3s0awXbVPVWVzAd13vfmdY2Bu4=, tarball: http://registry.m.jd.com/@parcel/watcher-linux-x64-musl/download/@parcel/watcher-linux-x64-musl-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-win32-arm64@2.5.1':
    resolution: {integrity: sha1-fp4ComeE1HUD3h0Q6Oq2zOtSQkM=, tarball: http://registry.m.jd.com/@parcel/watcher-win32-arm64/download/@parcel/watcher-win32-arm64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-win32-ia32@2.5.1':
    resolution: {integrity: sha1-LQ+U+lmoc83FhL9/ax3GKN35duY=, tarball: http://registry.m.jd.com/@parcel/watcher-win32-ia32/download/@parcel/watcher-win32-ia32-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher-win32-x64@2.5.1':
    resolution: {integrity: sha1-rlJpMllmS6byIo+mHX7kS2TqCUc=, tarball: http://registry.m.jd.com/@parcel/watcher-win32-x64/download/@parcel/watcher-win32-x64-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@parcel/watcher@2.5.1':
    resolution: {integrity: sha1-NCUHqc+q8XJHmogjCd7x6ZH7EgA=, tarball: http://registry.m.jd.com/@parcel/watcher/download/@parcel/watcher-2.5.1.tgz}
    engines: {node: '>= 10.0.0'}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=, tarball: http://registry.m.jd.com/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz}
    engines: {node: '>=14'}

  '@pkgr/core@0.2.7':
    resolution: {integrity: sha1-61AU39CwPn87ou7v9Qbu2JsCgFg=, tarball: http://registry.m.jd.com/@pkgr/core/download/@pkgr/core-0.2.7.tgz}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}

  '@plato/rsa@0.0.1':
    resolution: {integrity: sha1-8Vx/lYx3SjseaA8dUsqn/IYrZvQ=, tarball: http://registry.m.jd.com/@plato/rsa/download/@plato/rsa-0.0.1.tgz}

  '@rolldown/pluginutils@1.0.0-beta.11':
    resolution: {integrity: sha1-Hj6ARN0FPD36S7uzhh9uGA7ng0M=, tarball: http://registry.m.jd.com/@rolldown/pluginutils/download/@rolldown/pluginutils-1.0.0-beta.11.tgz}

  '@rollup/pluginutils@4.2.1':
    resolution: {integrity: sha1-5sbDq6B0Ttzj+yB0ki03dsCvKm0=, tarball: http://registry.m.jd.com/@rollup/pluginutils/download/@rollup/pluginutils-4.2.1.tgz}
    engines: {node: '>= 8.0.0'}

  '@rollup/rollup-android-arm-eabi@4.42.0':
    resolution: {integrity: sha1-i6rhWmon8Yt8W+Qg4AqwjH091vQ=, tarball: http://registry.m.jd.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.42.0.tgz}

  '@rollup/rollup-android-arm64@4.42.0':
    resolution: {integrity: sha1-Z5g5QkHRsm+LRNK72N6cEuud1uY=, tarball: http://registry.m.jd.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.42.0.tgz}

  '@rollup/rollup-darwin-arm64@4.42.0':
    resolution: {integrity: sha1-hkJIKsLSHndHp5scwyk9VxH+/qM=, tarball: http://registry.m.jd.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.42.0.tgz}

  '@rollup/rollup-darwin-x64@4.42.0':
    resolution: {integrity: sha1-4VVosv6k/cUm6GQkFQ357FEfuq8=, tarball: http://registry.m.jd.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.42.0.tgz}

  '@rollup/rollup-freebsd-arm64@4.42.0':
    resolution: {integrity: sha1-Dr2ztHDM9qzw6sroF3804nR3VZ8=, tarball: http://registry.m.jd.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.42.0.tgz}

  '@rollup/rollup-freebsd-x64@4.42.0':
    resolution: {integrity: sha1-Z1gIv0/nx/xFQyZRCrO+CFdibUE=, tarball: http://registry.m.jd.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.42.0.tgz}

  '@rollup/rollup-linux-arm-gnueabihf@4.42.0':
    resolution: {integrity: sha1-BeiBzGn1lBX+jBrxNVTGDHxJ0RQ=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.42.0.tgz}

  '@rollup/rollup-linux-arm-musleabihf@4.42.0':
    resolution: {integrity: sha1-65kL98PDd0nD1a/tNOat7BySeWM=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.42.0.tgz}

  '@rollup/rollup-linux-arm64-gnu@4.42.0':
    resolution: {integrity: sha1-Pe6s/1iefzcKylzvKdaNTI+gAzw=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.42.0.tgz}

  '@rollup/rollup-linux-arm64-musl@4.42.0':
    resolution: {integrity: sha1-bbgasGXvJ4+vg9h1x3/5zdUavP0=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.42.0.tgz}

  '@rollup/rollup-linux-loongarch64-gnu@4.42.0':
    resolution: {integrity: sha1-kNNTNq1MvzGGSOQbDnzjkgwo68k=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-loongarch64-gnu/download/@rollup/rollup-linux-loongarch64-gnu-4.42.0.tgz}

  '@rollup/rollup-linux-powerpc64le-gnu@4.42.0':
    resolution: {integrity: sha1-bSGg8YJiZI7BgfyTJrjwrAKqdE0=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-powerpc64le-gnu/download/@rollup/rollup-linux-powerpc64le-gnu-4.42.0.tgz}

  '@rollup/rollup-linux-riscv64-gnu@4.42.0':
    resolution: {integrity: sha1-5G4tESWVdpS/tSIuzWPdbJvWloI=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.42.0.tgz}

  '@rollup/rollup-linux-riscv64-musl@4.42.0':
    resolution: {integrity: sha1-R4oj8PoNgyoKb6hYqfPS6yAdRN4=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-riscv64-musl/download/@rollup/rollup-linux-riscv64-musl-4.42.0.tgz}

  '@rollup/rollup-linux-s390x-gnu@4.42.0':
    resolution: {integrity: sha1-QmHHFM11Dj+2haMw38p7uPVxFGk=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.42.0.tgz}

  '@rollup/rollup-linux-x64-gnu@4.42.0':
    resolution: {integrity: sha1-Rap1G98FrGltpBejf9/RP2B+H6s=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.42.0.tgz}

  '@rollup/rollup-linux-x64-musl@4.42.0':
    resolution: {integrity: sha1-mg+Gkd7eU9FyDrsq7vcuSDz2kiA=, tarball: http://registry.m.jd.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.42.0.tgz}

  '@rollup/rollup-win32-arm64-msvc@4.42.0':
    resolution: {integrity: sha1-OVrYtrY3KjiI0ulr9sRTkr6BX00=, tarball: http://registry.m.jd.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.42.0.tgz}

  '@rollup/rollup-win32-ia32-msvc@4.42.0':
    resolution: {integrity: sha1-DYAwWhT/83LqXpDNNcY8a4770UM=, tarball: http://registry.m.jd.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.42.0.tgz}

  '@rollup/rollup-win32-x64-msvc@4.42.0':
    resolution: {integrity: sha1-UWxncLoV/mrvNp0hepdHSSwB6Lc=, tarball: http://registry.m.jd.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.42.0.tgz}

  '@rspack/binding@1.3.15':
    resolution: {integrity: sha1-U1+h8U0XP7cqLYu33xCQaCfncYU=, tarball: http://registry.m.jd.com/@rspack/binding/download/@rspack/binding-1.3.15.tgz}

  '@rspack/core@1.3.15':
    resolution: {integrity: sha1-IrzpWao4bD84Ahr27mqUM5iW/9I=, tarball: http://registry.m.jd.com/@rspack/core/download/@rspack/core-1.3.15.tgz}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@swc/helpers': '>=0.5.1'

  '@rspack/lite-tapable@1.0.1':
    resolution: {integrity: sha1-1FQKXSi9YXcWS8C6C+5L3sBFhZE=, tarball: http://registry.m.jd.com/@rspack/lite-tapable/download/@rspack/lite-tapable-1.0.1.tgz}
    engines: {node: '>=16.0.0'}

  '@sindresorhus/merge-streams@2.3.0':
    resolution: {integrity: sha1-cZ33+0F2a8FDNp6qDdVtjch8mVg=, tarball: http://registry.m.jd.com/@sindresorhus/merge-streams/download/@sindresorhus/merge-streams-2.3.0.tgz}
    engines: {node: '>=18'}

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha1-WnvpWsDwvxhufm6JDnpvbNps6XE=, tarball: http://registry.m.jd.com/@swc/helpers/download/@swc/helpers-0.5.17.tgz}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=, tarball: http://registry.m.jd.com/@types/babel__core/download/@types/babel__core-7.20.5.tgz}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=, tarball: http://registry.m.jd.com/@types/babel__generator/download/@types/babel__generator-7.27.0.tgz}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=, tarball: http://registry.m.jd.com/@types/babel__template/download/@types/babel__template-7.4.4.tgz}

  '@types/babel__traverse@7.20.7':
    resolution: {integrity: sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=, tarball: http://registry.m.jd.com/@types/babel__traverse/download/@types/babel__traverse-7.20.7.tgz}

  '@types/bluebird@3.5.42':
    resolution: {integrity: sha1-fsBfHOmYbZIDE8E3elZisbVj02Y=, tarball: http://registry.m.jd.com/@types/bluebird/download/@types/bluebird-3.5.42.tgz}

  '@types/caseless@0.12.5':
    resolution: {integrity: sha1-25RoyxsbWpJbjzSCLxZp3wxUcvU=, tarball: http://registry.m.jd.com/@types/caseless/download/@types/caseless-0.12.5.tgz}

  '@types/conventional-commits-parser@5.0.1':
    resolution: {integrity: sha1-jLgc8XCFNJbLxQGjsy3PXkb/tho=, tarball: http://registry.m.jd.com/@types/conventional-commits-parser/download/@types/conventional-commits-parser-5.0.1.tgz}

  '@types/eslint-scope@3.7.7':
    resolution: {integrity: sha1-MQi9XxiwzbJ3yGez3UScntcHmsU=, tarball: http://registry.m.jd.com/@types/eslint-scope/download/@types/eslint-scope-3.7.7.tgz}

  '@types/eslint@9.6.1':
    resolution: {integrity: sha1-1Xla1zLOgXFfJ/ddqRMASlZ1FYQ=, tarball: http://registry.m.jd.com/@types/eslint/download/@types/eslint-9.6.1.tgz}

  '@types/estree@1.0.7':
    resolution: {integrity: sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=, tarball: http://registry.m.jd.com/@types/estree/download/@types/estree-1.0.7.tgz}

  '@types/estree@1.0.8':
    resolution: {integrity: sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=, tarball: http://registry.m.jd.com/@types/estree/download/@types/estree-1.0.8.tgz}

  '@types/glob@7.2.0':
    resolution: {integrity: sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=, tarball: http://registry.m.jd.com/@types/glob/download/@types/glob-7.2.0.tgz}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=, tarball: http://registry.m.jd.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz}

  '@types/md5@2.3.5':
    resolution: {integrity: sha1-SBzvColuOl3L/FqKiwLAWVivSKU=, tarball: http://registry.m.jd.com/@types/md5/download/@types/md5-2.3.5.tgz}

  '@types/minimatch@5.1.2':
    resolution: {integrity: sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=, tarball: http://registry.m.jd.com/@types/minimatch/download/@types/minimatch-5.1.2.tgz}

  '@types/node@14.18.63':
    resolution: {integrity: sha1-F4j6jag427X56plLg0J4IF22yis=, tarball: http://registry.m.jd.com/@types/node/download/@types/node-14.18.63.tgz}

  '@types/node@22.15.31':
    resolution: {integrity: sha1-RU8R4gYRUBNcg1PX87Oxgj/Knz8=, tarball: http://registry.m.jd.com/@types/node/download/@types/node-22.15.31.tgz}

  '@types/prop-types@15.7.15':
    resolution: {integrity: sha1-5uWobWAr6spxzlFj+t9fldcJMcc=, tarball: http://registry.m.jd.com/@types/prop-types/download/@types/prop-types-15.7.15.tgz}

  '@types/react-dom@18.3.7':
    resolution: {integrity: sha1-uJ3fLNg7T+r8xOLqQa/fuVoNGU8=, tarball: http://registry.m.jd.com/@types/react-dom/download/@types/react-dom-18.3.7.tgz}
    peerDependencies:
      '@types/react': ^18.0.0

  '@types/react@18.3.23':
    resolution: {integrity: sha1-hq5va5WkjEGP7NrMyAaeD7tjaWo=, tarball: http://registry.m.jd.com/@types/react/download/@types/react-18.3.23.tgz}

  '@types/request-promise@4.1.51':
    resolution: {integrity: sha1-prtDKJVp3oQFUHN1fTeo/WFGv+M=, tarball: http://registry.m.jd.com/@types/request-promise/download/@types/request-promise-4.1.51.tgz}

  '@types/request@2.48.12':
    resolution: {integrity: sha1-D1kPYVoQ+H2hjpeQrJTCnsTF7zA=, tarball: http://registry.m.jd.com/@types/request/download/@types/request-2.48.12.tgz}

  '@types/tough-cookie@4.0.5':
    resolution: {integrity: sha1-y24qaRtwyxd8bjrpwdLosuqM0wQ=, tarball: http://registry.m.jd.com/@types/tough-cookie/download/@types/tough-cookie-4.0.5.tgz}

  '@typescript-eslint/eslint-plugin@8.34.0':
    resolution: {integrity: sha1-lsn4GHgv4kzViDpdUXyhgm0/qcI=, tarball: http://registry.m.jd.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.34.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/parser@8.34.0':
    resolution: {integrity: sha1-cDJwQmrFKTBK5piEgvSHyFbZwT8=, tarball: http://registry.m.jd.com/@typescript-eslint/parser/download/@typescript-eslint/parser-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/project-service@8.34.0':
    resolution: {integrity: sha1-RJEZty/p+uGFATpr268f+/7mvK8=, tarball: http://registry.m.jd.com/@typescript-eslint/project-service/download/@typescript-eslint/project-service-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/scope-manager@8.34.0':
    resolution: {integrity: sha1-n+2uwCNwz3nAGKZWq0AusA3Gnmc=, tarball: http://registry.m.jd.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/tsconfig-utils@8.34.0':
    resolution: {integrity: sha1-l9CiTomjVekwjOvI4j8lVmm/CXk=, tarball: http://registry.m.jd.com/@typescript-eslint/tsconfig-utils/download/@typescript-eslint/tsconfig-utils-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/type-utils@8.34.0':
    resolution: {integrity: sha1-A+frN3YSnf11G6HKwMbqSw+rXsY=, tarball: http://registry.m.jd.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/types@8.34.0':
    resolution: {integrity: sha1-GAAPIFxZya/383H8VCa3ZM8okPs=, tarball: http://registry.m.jd.com/@typescript-eslint/types/download/@typescript-eslint/types-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.34.0':
    resolution: {integrity: sha1-yfP+7FETOe9k6eSIRRbD5VjxsEg=, tarball: http://registry.m.jd.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/utils@8.34.0':
    resolution: {integrity: sha1-eES+68EVO00+w0E1wtpTqR4Hb40=, tarball: http://registry.m.jd.com/@typescript-eslint/utils/download/@typescript-eslint/utils-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/visitor-keys@8.34.0':
    resolution: {integrity: sha1-x6FJQHvjHXVdunGYBhfWOKQKwJk=, tarball: http://registry.m.jd.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@vitejs/plugin-legacy@6.1.1':
    resolution: {integrity: sha1-yLZSOZR/FYqnGoWjWDJCTp4d5/w=, tarball: http://registry.m.jd.com/@vitejs/plugin-legacy/download/@vitejs/plugin-legacy-6.1.1.tgz}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    peerDependencies:
      terser: ^5.16.0
      vite: ^6.0.0

  '@vitejs/plugin-react@4.5.2':
    resolution: {integrity: sha1-i5io+87/9KpMlGlm++xWDcZtK9k=, tarball: http://registry.m.jd.com/@vitejs/plugin-react/download/@vitejs/plugin-react-4.5.2.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0

  '@webassemblyjs/ast@1.14.1':
    resolution: {integrity: sha1-qfagfysDyVyNOMRTah/ftSH/VbY=, tarball: http://registry.m.jd.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.14.1.tgz}

  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    resolution: {integrity: sha1-/Moe7dscxOe27tT8eVbWgTshufs=, tarball: http://registry.m.jd.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.13.2.tgz}

  '@webassemblyjs/helper-api-error@1.13.2':
    resolution: {integrity: sha1-4KFhUiSLw42u523X4h8Vxe86sec=, tarball: http://registry.m.jd.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.13.2.tgz}

  '@webassemblyjs/helper-buffer@1.14.1':
    resolution: {integrity: sha1-giqbxgMWZTH31d+E5ntb+ZtyuWs=, tarball: http://registry.m.jd.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.14.1.tgz}

  '@webassemblyjs/helper-numbers@1.13.2':
    resolution: {integrity: sha1-29kyVI5xGfS4p4d/1ajSDmNJCy0=, tarball: http://registry.m.jd.com/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.13.2.tgz}

  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    resolution: {integrity: sha1-5VYQh1j0SKroTIUOWTzhig6zHgs=, tarball: http://registry.m.jd.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.13.2.tgz}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    resolution: {integrity: sha1-lindqcRDDqtUtZEFPW3G87oFA0g=, tarball: http://registry.m.jd.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.14.1.tgz}

  '@webassemblyjs/ieee754@1.13.2':
    resolution: {integrity: sha1-HF6qzh1gatosf9cEXqk1bFnuDbo=, tarball: http://registry.m.jd.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.13.2.tgz}

  '@webassemblyjs/leb128@1.13.2':
    resolution: {integrity: sha1-V8XD3rAQXQLOJfo/109OvJ/Qu7A=, tarball: http://registry.m.jd.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.13.2.tgz}

  '@webassemblyjs/utf8@1.13.2':
    resolution: {integrity: sha1-kXog6T9xrVYClmwtaFrgxsIfYPE=, tarball: http://registry.m.jd.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.13.2.tgz}

  '@webassemblyjs/wasm-edit@1.14.1':
    resolution: {integrity: sha1-rGaJ9QIhm1kZjd7ELc1JaxAE1Zc=, tarball: http://registry.m.jd.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.14.1.tgz}

  '@webassemblyjs/wasm-gen@1.14.1':
    resolution: {integrity: sha1-mR5/DAkMsLtiu6yIIHbj0hnalXA=, tarball: http://registry.m.jd.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.14.1.tgz}

  '@webassemblyjs/wasm-opt@1.14.1':
    resolution: {integrity: sha1-5vce18yuRngcIGAX08FMUO+oEGs=, tarball: http://registry.m.jd.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.14.1.tgz}

  '@webassemblyjs/wasm-parser@1.14.1':
    resolution: {integrity: sha1-s+E/GJNgXKeLUsaOVM9qhl+Qufs=, tarball: http://registry.m.jd.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.14.1.tgz}

  '@webassemblyjs/wast-printer@1.14.1':
    resolution: {integrity: sha1-O7PpY4qK5f2vlhDnoGtNn5qm/gc=, tarball: http://registry.m.jd.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.14.1.tgz}

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=, tarball: http://registry.m.jd.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=, tarball: http://registry.m.jd.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz}

  JSONStream@1.3.5:
    resolution: {integrity: sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=, tarball: http://registry.m.jd.com/JSONStream/download/JSONStream-1.3.5.tgz}
    hasBin: true

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=, tarball: http://registry.m.jd.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.15.0:
    resolution: {integrity: sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=, tarball: http://registry.m.jd.com/acorn/download/acorn-8.15.0.tgz}
    engines: {node: '>=0.4.0'}
    hasBin: true

  ahooks@3.8.5:
    resolution: {integrity: sha1-D+LprnnRHVXO0NB7lTWjbL/7ZHQ=, tarball: http://registry.m.jd.com/ahooks/download/ahooks-3.8.5.tgz}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  ajv-formats@2.1.1:
    resolution: {integrity: sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=, tarball: http://registry.m.jd.com/ajv-formats/download/ajv-formats-2.1.1.tgz}
    peerDependencies:
      ajv: ^8.0.0

  ajv-keywords@5.1.0:
    resolution: {integrity: sha1-adTThaRzPNvqtElkoRcKiPh/DhY=, tarball: http://registry.m.jd.com/ajv-keywords/download/ajv-keywords-5.1.0.tgz}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=, tarball: http://registry.m.jd.com/ajv/download/ajv-6.12.6.tgz}

  ajv@8.17.1:
    resolution: {integrity: sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=, tarball: http://registry.m.jd.com/ajv/download/ajv-8.17.1.tgz}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=, tarball: http://registry.m.jd.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz}
    engines: {node: '>=8'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=, tarball: http://registry.m.jd.com/ansi-regex/download/ansi-regex-5.0.1.tgz}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=, tarball: http://registry.m.jd.com/ansi-regex/download/ansi-regex-6.1.0.tgz}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=, tarball: http://registry.m.jd.com/ansi-styles/download/ansi-styles-3.2.1.tgz}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=, tarball: http://registry.m.jd.com/ansi-styles/download/ansi-styles-4.3.0.tgz}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=, tarball: http://registry.m.jd.com/ansi-styles/download/ansi-styles-6.2.1.tgz}
    engines: {node: '>=12'}

  argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=, tarball: http://registry.m.jd.com/argparse/download/argparse-2.0.1.tgz}

  array-ify@1.0.0:
    resolution: {integrity: sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=, tarball: http://registry.m.jd.com/array-ify/download/array-ify-1.0.0.tgz}

  asn1@0.2.6:
    resolution: {integrity: sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=, tarball: http://registry.m.jd.com/asn1/download/asn1-0.2.6.tgz}

  assert-plus@1.0.0:
    resolution: {integrity: sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=, tarball: http://registry.m.jd.com/assert-plus/download/assert-plus-1.0.0.tgz}
    engines: {node: '>=0.8'}

  async@3.2.6:
    resolution: {integrity: sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=, tarball: http://registry.m.jd.com/async/download/async-3.2.6.tgz}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=, tarball: http://registry.m.jd.com/asynckit/download/asynckit-0.4.0.tgz}

  at-least-node@1.0.0:
    resolution: {integrity: sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=, tarball: http://registry.m.jd.com/at-least-node/download/at-least-node-1.0.0.tgz}
    engines: {node: '>= 4.0.0'}

  autoprefixer@10.4.21:
    resolution: {integrity: sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=, tarball: http://registry.m.jd.com/autoprefixer/download/autoprefixer-10.4.21.tgz}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  aws-sign2@0.7.0:
    resolution: {integrity: sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=, tarball: http://registry.m.jd.com/aws-sign2/download/aws-sign2-0.7.0.tgz}

  aws4@1.13.2:
    resolution: {integrity: sha1-CqFnIWllrJR0zPqDiSz7az4eUu8=, tarball: http://registry.m.jd.com/aws4/download/aws4-1.13.2.tgz}

  axios@1.9.0:
    resolution: {integrity: sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=, tarball: http://registry.m.jd.com/axios/download/axios-1.9.0.tgz}

  babel-loader@10.0.0:
    resolution: {integrity: sha1-uXQ3FMDh4ISz5K3vPNX67jMImXc=, tarball: http://registry.m.jd.com/babel-loader/download/babel-loader-10.0.0.tgz}
    engines: {node: ^18.20.0 || ^20.10.0 || >=22.0.0}
    peerDependencies:
      '@babel/core': ^7.12.0
      webpack: '>=5.61.0'

  babel-plugin-polyfill-corejs2@0.4.13:
    resolution: {integrity: sha1-fURfDgYH68j7awHX6PsCBpuR3Ys=, tarball: http://registry.m.jd.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.13.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.11.1:
    resolution: {integrity: sha1-Tk4YLxuzfHumLir4HY3QnfMTRPY=, tarball: http://registry.m.jd.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.11.1.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.4:
    resolution: {integrity: sha1-QoxhXTwXcpKiK0+T7ZnjWNeQaps=, tarball: http://registry.m.jd.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.4.tgz}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=, tarball: http://registry.m.jd.com/balanced-match/download/balanced-match-1.0.2.tgz}

  base64-js@1.5.1:
    resolution: {integrity: sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=, tarball: http://registry.m.jd.com/base64-js/download/base64-js-1.5.1.tgz}

  bcrypt-pbkdf@1.0.2:
    resolution: {integrity: sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=, tarball: http://registry.m.jd.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz}

  bluebird@3.7.2:
    resolution: {integrity: sha1-nyKcFb4nJFT/qXOs4NvueaGww28=, tarball: http://registry.m.jd.com/bluebird/download/bluebird-3.7.2.tgz}

  boolbase@1.0.0:
    resolution: {integrity: sha1-aN/1++YMUes3cl6p4+0xDcwed24=, tarball: http://registry.m.jd.com/boolbase/download/boolbase-1.0.0.tgz}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=, tarball: http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz}

  brace-expansion@2.0.1:
    resolution: {integrity: sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=, tarball: http://registry.m.jd.com/brace-expansion/download/brace-expansion-2.0.1.tgz}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=, tarball: http://registry.m.jd.com/braces/download/braces-3.0.3.tgz}
    engines: {node: '>=8'}

  browserslist-to-esbuild@2.1.1:
    resolution: {integrity: sha1-UNxMVaaIm6IsexvYIAMvgbgi+vA=, tarball: http://registry.m.jd.com/browserslist-to-esbuild/download/browserslist-to-esbuild-2.1.1.tgz}
    engines: {node: '>=18'}
    hasBin: true
    peerDependencies:
      browserslist: '*'

  browserslist@4.25.0:
    resolution: {integrity: sha1-mGqpxth5FohdorUNjrV3rI0TOyw=, tarball: http://registry.m.jd.com/browserslist/download/browserslist-4.25.0.tgz}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer-builder@0.2.0:
    resolution: {integrity: sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=, tarball: http://registry.m.jd.com/buffer-builder/download/buffer-builder-0.2.0.tgz}

  buffer-from@1.1.2:
    resolution: {integrity: sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=, tarball: http://registry.m.jd.com/buffer-from/download/buffer-from-1.1.2.tgz}

  buffer@6.0.3:
    resolution: {integrity: sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=, tarball: http://registry.m.jd.com/buffer/download/buffer-6.0.3.tgz}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=, tarball: http://registry.m.jd.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=, tarball: http://registry.m.jd.com/callsites/download/callsites-3.1.0.tgz}
    engines: {node: '>=6'}

  camel-case@4.1.2:
    resolution: {integrity: sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=, tarball: http://registry.m.jd.com/camel-case/download/camel-case-4.1.2.tgz}

  caniuse-lite@1.0.30001721:
    resolution: {integrity: sha1-NrkM2WkB+MmN1mmL9civfUxoctc=, tarball: http://registry.m.jd.com/caniuse-lite/download/caniuse-lite-1.0.30001721.tgz}

  caseless@0.12.0:
    resolution: {integrity: sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=, tarball: http://registry.m.jd.com/caseless/download/caseless-0.12.0.tgz}

  chalk@2.4.2:
    resolution: {integrity: sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=, tarball: http://registry.m.jd.com/chalk/download/chalk-2.4.2.tgz}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=, tarball: http://registry.m.jd.com/chalk/download/chalk-4.1.2.tgz}
    engines: {node: '>=10'}

  chalk@5.4.1:
    resolution: {integrity: sha1-G0i/CWPsFY3OKqz2nAk64t0gktg=, tarball: http://registry.m.jd.com/chalk/download/chalk-5.4.1.tgz}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  chardet@0.7.0:
    resolution: {integrity: sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=, tarball: http://registry.m.jd.com/chardet/download/chardet-0.7.0.tgz}

  charenc@0.0.2:
    resolution: {integrity: sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=, tarball: http://registry.m.jd.com/charenc/download/charenc-0.0.2.tgz}

  chokidar@4.0.3:
    resolution: {integrity: sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=, tarball: http://registry.m.jd.com/chokidar/download/chokidar-4.0.3.tgz}
    engines: {node: '>= 14.16.0'}

  chrome-trace-event@1.0.4:
    resolution: {integrity: sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=, tarball: http://registry.m.jd.com/chrome-trace-event/download/chrome-trace-event-1.0.4.tgz}
    engines: {node: '>=6.0'}

  classnames@2.5.1:
    resolution: {integrity: sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=, tarball: http://registry.m.jd.com/classnames/download/classnames-2.5.1.tgz}

  clean-css@5.3.3:
    resolution: {integrity: sha1-szBlPNO9a3UAnMJccUyue5M1HM0=, tarball: http://registry.m.jd.com/clean-css/download/clean-css-5.3.3.tgz}
    engines: {node: '>= 10.0'}

  cli-cursor@3.1.0:
    resolution: {integrity: sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=, tarball: http://registry.m.jd.com/cli-cursor/download/cli-cursor-3.1.0.tgz}
    engines: {node: '>=8'}

  cli-width@3.0.0:
    resolution: {integrity: sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=, tarball: http://registry.m.jd.com/cli-width/download/cli-width-3.0.0.tgz}
    engines: {node: '>= 10'}

  cliui@8.0.1:
    resolution: {integrity: sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=, tarball: http://registry.m.jd.com/cliui/download/cliui-8.0.1.tgz}
    engines: {node: '>=12'}

  color-convert@1.9.3:
    resolution: {integrity: sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=, tarball: http://registry.m.jd.com/color-convert/download/color-convert-1.9.3.tgz}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=, tarball: http://registry.m.jd.com/color-convert/download/color-convert-2.0.1.tgz}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=, tarball: http://registry.m.jd.com/color-name/download/color-name-1.1.3.tgz}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=, tarball: http://registry.m.jd.com/color-name/download/color-name-1.1.4.tgz}

  colorette@2.0.20:
    resolution: {integrity: sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=, tarball: http://registry.m.jd.com/colorette/download/colorette-2.0.20.tgz}

  colorjs.io@0.5.2:
    resolution: {integrity: sha1-Y7IBObAHWR68M1mTK++EYo6z/O8=, tarball: http://registry.m.jd.com/colorjs.io/download/colorjs.io-0.5.2.tgz}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=, tarball: http://registry.m.jd.com/combined-stream/download/combined-stream-1.0.8.tgz}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=, tarball: http://registry.m.jd.com/commander/download/commander-2.20.3.tgz}

  commander@8.3.0:
    resolution: {integrity: sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=, tarball: http://registry.m.jd.com/commander/download/commander-8.3.0.tgz}
    engines: {node: '>= 12'}

  compare-func@2.0.0:
    resolution: {integrity: sha1-+2XnXtvd/S5WhVTotbBf/3pR/LM=, tarball: http://registry.m.jd.com/compare-func/download/compare-func-2.0.0.tgz}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=, tarball: http://registry.m.jd.com/concat-map/download/concat-map-0.0.1.tgz}

  connect-history-api-fallback@1.6.0:
    resolution: {integrity: sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=, tarball: http://registry.m.jd.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz}
    engines: {node: '>=0.8'}

  consola@2.15.3:
    resolution: {integrity: sha1-LhH5jWpL5x/3LgvfB70j4Sy2FVA=, tarball: http://registry.m.jd.com/consola/download/consola-2.15.3.tgz}

  conventional-changelog-angular@7.0.0:
    resolution: {integrity: sha1-XuyO2/8VqpsWgKjc+9U+LX6yuno=, tarball: http://registry.m.jd.com/conventional-changelog-angular/download/conventional-changelog-angular-7.0.0.tgz}
    engines: {node: '>=16'}

  conventional-changelog-conventionalcommits@7.0.2:
    resolution: {integrity: sha1-ql2g8bJUMJSInoz3YW6+Go9ccNU=, tarball: http://registry.m.jd.com/conventional-changelog-conventionalcommits/download/conventional-changelog-conventionalcommits-7.0.2.tgz}
    engines: {node: '>=16'}

  conventional-commits-parser@5.0.0:
    resolution: {integrity: sha1-V/NZS4GtVNQMG0KA8EVU3yhifZo=, tarball: http://registry.m.jd.com/conventional-commits-parser/download/conventional-commits-parser-5.0.0.tgz}
    engines: {node: '>=16'}
    hasBin: true

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=, tarball: http://registry.m.jd.com/convert-source-map/download/convert-source-map-2.0.0.tgz}

  copy-anything@2.0.6:
    resolution: {integrity: sha1-CSRU6pWEp7etVXMGKyqH9ZAPxIA=, tarball: http://registry.m.jd.com/copy-anything/download/copy-anything-2.0.6.tgz}

  core-js-compat@3.42.0:
    resolution: {integrity: sha1-zhnClwbuWAbibTyzxULUz8DtUbs=, tarball: http://registry.m.jd.com/core-js-compat/download/core-js-compat-3.42.0.tgz}

  core-js@3.43.0:
    resolution: {integrity: sha1-9yWLFWUjIIFn3zXeoM/WtuzU7og=, tarball: http://registry.m.jd.com/core-js/download/core-js-3.43.0.tgz}

  core-util-is@1.0.2:
    resolution: {integrity: sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=, tarball: http://registry.m.jd.com/core-util-is/download/core-util-is-1.0.2.tgz}

  cosmiconfig-typescript-loader@6.1.0:
    resolution: {integrity: sha1-f2RFA+HCv/kK7S0ppjcAjyeWRrs=, tarball: http://registry.m.jd.com/cosmiconfig-typescript-loader/download/cosmiconfig-typescript-loader-6.1.0.tgz}
    engines: {node: '>=v18'}
    peerDependencies:
      '@types/node': '*'
      cosmiconfig: '>=9'
      typescript: '>=5'

  cosmiconfig@9.0.0:
    resolution: {integrity: sha1-NMP8WCh7kV866QWrbcPeJYtVrZ0=, tarball: http://registry.m.jd.com/cosmiconfig/download/cosmiconfig-9.0.0.tgz}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  cross-spawn@7.0.6:
    resolution: {integrity: sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=, tarball: http://registry.m.jd.com/cross-spawn/download/cross-spawn-7.0.6.tgz}
    engines: {node: '>= 8'}

  crypt@0.0.2:
    resolution: {integrity: sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=, tarball: http://registry.m.jd.com/crypt/download/crypt-0.0.2.tgz}

  css-blank-pseudo@7.0.1:
    resolution: {integrity: sha1-MgIL/yCiCaU61xuGdYUrSejVfkY=, tarball: http://registry.m.jd.com/css-blank-pseudo/download/css-blank-pseudo-7.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  css-has-pseudo@7.0.2:
    resolution: {integrity: sha1-+0Lo3nNx8olpYeH2MI8TwscBm3I=, tarball: http://registry.m.jd.com/css-has-pseudo/download/css-has-pseudo-7.0.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  css-prefers-color-scheme@10.0.0:
    resolution: {integrity: sha1-ugAbmbgQW4iWyib8ODCd2yJ4vTw=, tarball: http://registry.m.jd.com/css-prefers-color-scheme/download/css-prefers-color-scheme-10.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  css-select@4.3.0:
    resolution: {integrity: sha1-23EpsoRmYv2GKM/ElquytZ5BUps=, tarball: http://registry.m.jd.com/css-select/download/css-select-4.3.0.tgz}

  css-what@6.1.0:
    resolution: {integrity: sha1-+17/z3bx3eosgb36pN5E55uscPQ=, tarball: http://registry.m.jd.com/css-what/download/css-what-6.1.0.tgz}
    engines: {node: '>= 6'}

  cssdb@8.3.0:
    resolution: {integrity: sha1-lAvsrUl7hQmtgioo+wz+VMlpzP4=, tarball: http://registry.m.jd.com/cssdb/download/cssdb-8.3.0.tgz}

  cssesc@3.0.0:
    resolution: {integrity: sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=, tarball: http://registry.m.jd.com/cssesc/download/cssesc-3.0.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  csstype@3.1.3:
    resolution: {integrity: sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=, tarball: http://registry.m.jd.com/csstype/download/csstype-3.1.3.tgz}

  dargs@8.1.0:
    resolution: {integrity: sha1-o0hZ6lCcvORUheWqNW/vcL/McnI=, tarball: http://registry.m.jd.com/dargs/download/dargs-8.1.0.tgz}
    engines: {node: '>=12'}

  dashdash@1.14.1:
    resolution: {integrity: sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=, tarball: http://registry.m.jd.com/dashdash/download/dashdash-1.14.1.tgz}
    engines: {node: '>=0.10'}

  dayjs@1.11.13:
    resolution: {integrity: sha1-kkMLATkFXD67YBUKoT6GCktaNmw=, tarball: http://registry.m.jd.com/dayjs/download/dayjs-1.11.13.tgz}

  debug@2.6.9:
    resolution: {integrity: sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=, tarball: http://registry.m.jd.com/debug/download/debug-2.6.9.tgz}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=, tarball: http://registry.m.jd.com/debug/download/debug-4.4.1.tgz}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decode-uri-component@0.4.1:
    resolution: {integrity: sha1-KsSFlmPHBL4iv323YKFJSkmrLMU=, tarball: http://registry.m.jd.com/decode-uri-component/download/decode-uri-component-0.4.1.tgz}
    engines: {node: '>=14.16'}

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=, tarball: http://registry.m.jd.com/deep-is/download/deep-is-0.1.4.tgz}

  del@8.0.0:
    resolution: {integrity: sha1-8zOlZzz+ty5GCEAxcUp8MFFegKo=, tarball: http://registry.m.jd.com/del/download/del-8.0.0.tgz}
    engines: {node: '>=18'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=, tarball: http://registry.m.jd.com/delayed-stream/download/delayed-stream-1.0.0.tgz}
    engines: {node: '>=0.4.0'}

  des.js@1.1.0:
    resolution: {integrity: sha1-HTf1dm87v/Tuljjocah2jBc7gdo=, tarball: http://registry.m.jd.com/des.js/download/des.js-1.1.0.tgz}

  detect-libc@1.0.3:
    resolution: {integrity: sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=, tarball: http://registry.m.jd.com/detect-libc/download/detect-libc-1.0.3.tgz}
    engines: {node: '>=0.10'}
    hasBin: true

  detect-libc@2.0.4:
    resolution: {integrity: sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=, tarball: http://registry.m.jd.com/detect-libc/download/detect-libc-2.0.4.tgz}
    engines: {node: '>=8'}

  dom-serializer@1.4.1:
    resolution: {integrity: sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=, tarball: http://registry.m.jd.com/dom-serializer/download/dom-serializer-1.4.1.tgz}

  domelementtype@2.3.0:
    resolution: {integrity: sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=, tarball: http://registry.m.jd.com/domelementtype/download/domelementtype-2.3.0.tgz}

  domhandler@4.3.1:
    resolution: {integrity: sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=, tarball: http://registry.m.jd.com/domhandler/download/domhandler-4.3.1.tgz}
    engines: {node: '>= 4'}

  domutils@2.8.0:
    resolution: {integrity: sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=, tarball: http://registry.m.jd.com/domutils/download/domutils-2.8.0.tgz}

  dot-case@3.0.4:
    resolution: {integrity: sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=, tarball: http://registry.m.jd.com/dot-case/download/dot-case-3.0.4.tgz}

  dot-prop@5.3.0:
    resolution: {integrity: sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=, tarball: http://registry.m.jd.com/dot-prop/download/dot-prop-5.3.0.tgz}
    engines: {node: '>=8'}

  dotenv-expand@8.0.3:
    resolution: {integrity: sha1-KQFnV0VbzHSEacg6GbNqryuD3W4=, tarball: http://registry.m.jd.com/dotenv-expand/download/dotenv-expand-8.0.3.tgz}
    engines: {node: '>=12'}

  dotenv@16.5.0:
    resolution: {integrity: sha1-CStJ8l+AjwIAUAUdH/JY5ATHhpI=, tarball: http://registry.m.jd.com/dotenv/download/dotenv-16.5.0.tgz}
    engines: {node: '>=12'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=, tarball: http://registry.m.jd.com/dunder-proto/download/dunder-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=, tarball: http://registry.m.jd.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz}

  ecc-jsbn@0.1.2:
    resolution: {integrity: sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=, tarball: http://registry.m.jd.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz}

  ejs@3.1.10:
    resolution: {integrity: sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=, tarball: http://registry.m.jd.com/ejs/download/ejs-3.1.10.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  electron-to-chromium@1.5.165:
    resolution: {integrity: sha1-R3sJV+QvBxkFqG98kFqYSPldK9s=, tarball: http://registry.m.jd.com/electron-to-chromium/download/electron-to-chromium-1.5.165.tgz}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=, tarball: http://registry.m.jd.com/emoji-regex/download/emoji-regex-8.0.0.tgz}

  emoji-regex@9.2.2:
    resolution: {integrity: sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=, tarball: http://registry.m.jd.com/emoji-regex/download/emoji-regex-9.2.2.tgz}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha1-coqwgvi3toNt5R8WN6q107lWj68=, tarball: http://registry.m.jd.com/enhanced-resolve/download/enhanced-resolve-5.18.1.tgz}
    engines: {node: '>=10.13.0'}

  entities@2.2.0:
    resolution: {integrity: sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=, tarball: http://registry.m.jd.com/entities/download/entities-2.2.0.tgz}

  env-paths@2.2.1:
    resolution: {integrity: sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=, tarball: http://registry.m.jd.com/env-paths/download/env-paths-2.2.1.tgz}
    engines: {node: '>=6'}

  errno@0.1.8:
    resolution: {integrity: sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=, tarball: http://registry.m.jd.com/errno/download/errno-0.1.8.tgz}
    hasBin: true

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=, tarball: http://registry.m.jd.com/error-ex/download/error-ex-1.3.2.tgz}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=, tarball: http://registry.m.jd.com/es-define-property/download/es-define-property-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=, tarball: http://registry.m.jd.com/es-errors/download/es-errors-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.7.0:
    resolution: {integrity: sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=, tarball: http://registry.m.jd.com/es-module-lexer/download/es-module-lexer-1.7.0.tgz}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=, tarball: http://registry.m.jd.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=, tarball: http://registry.m.jd.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz}
    engines: {node: '>= 0.4'}

  esbuild@0.25.5:
    resolution: {integrity: sha1-cQdQVJk/3652xmWG+bnB+Nft1DA=, tarball: http://registry.m.jd.com/esbuild/download/esbuild-0.25.5.tgz}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=, tarball: http://registry.m.jd.com/escalade/download/escalade-3.2.0.tgz}
    engines: {node: '>=6'}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=, tarball: http://registry.m.jd.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=, tarball: http://registry.m.jd.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz}
    engines: {node: '>=10'}

  eslint-config-prettier@10.1.5:
    resolution: {integrity: sha1-AMGNciUEO2+85qZlaXN3mY1FN4I=, tarball: http://registry.m.jd.com/eslint-config-prettier/download/eslint-config-prettier-10.1.5.tgz}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-prettier@5.4.1:
    resolution: {integrity: sha1-mbVdfdcAR4hrIiL92FNmXxgLNq8=, tarball: http://registry.m.jd.com/eslint-plugin-prettier/download/eslint-plugin-prettier-5.4.1.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '>= 7.0.0 <10.0.0 || >=10.1.0'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true

  eslint-plugin-react-hooks@5.2.0:
    resolution: {integrity: sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=, tarball: http://registry.m.jd.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-5.2.0.tgz}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

  eslint-plugin-react-refresh@0.4.20:
    resolution: {integrity: sha1-O7+1yGN+KNGc40Q2hkReUC7NGLo=, tarball: http://registry.m.jd.com/eslint-plugin-react-refresh/download/eslint-plugin-react-refresh-0.4.20.tgz}
    peerDependencies:
      eslint: '>=8.40'

  eslint-plugin-simple-import-sort@12.1.1:
    resolution: {integrity: sha1-5kv9r5HFuYophhmqY0qfeqQ7cJ4=, tarball: http://registry.m.jd.com/eslint-plugin-simple-import-sort/download/eslint-plugin-simple-import-sort-12.1.1.tgz}
    peerDependencies:
      eslint: '>=5.0.0'

  eslint-scope@5.1.1:
    resolution: {integrity: sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=, tarball: http://registry.m.jd.com/eslint-scope/download/eslint-scope-5.1.1.tgz}
    engines: {node: '>=8.0.0'}

  eslint-scope@8.4.0:
    resolution: {integrity: sha1-iOZGogf61hQ2/6OetQUUcgBlXII=, tarball: http://registry.m.jd.com/eslint-scope/download/eslint-scope-8.4.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=, tarball: http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.1:
    resolution: {integrity: sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=, tarball: http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.1.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.28.0:
    resolution: {integrity: sha1-sLy+gqFpRaQJBpJL6nXotJgM7X0=, tarball: http://registry.m.jd.com/eslint/download/eslint-9.28.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.4.0:
    resolution: {integrity: sha1-1U9JSdRikAWh+haNk3w/8ffiqDc=, tarball: http://registry.m.jd.com/espree/download/espree-10.4.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=, tarball: http://registry.m.jd.com/esquery/download/esquery-1.6.0.tgz}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=, tarball: http://registry.m.jd.com/esrecurse/download/esrecurse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=, tarball: http://registry.m.jd.com/estraverse/download/estraverse-4.3.0.tgz}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=, tarball: http://registry.m.jd.com/estraverse/download/estraverse-5.3.0.tgz}
    engines: {node: '>=4.0'}

  estree-walker@2.0.2:
    resolution: {integrity: sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=, tarball: http://registry.m.jd.com/estree-walker/download/estree-walker-2.0.2.tgz}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=, tarball: http://registry.m.jd.com/esutils/download/esutils-2.0.3.tgz}
    engines: {node: '>=0.10.0'}

  events@3.3.0:
    resolution: {integrity: sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=, tarball: http://registry.m.jd.com/events/download/events-3.3.0.tgz}
    engines: {node: '>=0.8.x'}

  extend@3.0.2:
    resolution: {integrity: sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=, tarball: http://registry.m.jd.com/extend/download/extend-3.0.2.tgz}

  external-editor@3.1.0:
    resolution: {integrity: sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=, tarball: http://registry.m.jd.com/external-editor/download/external-editor-3.1.0.tgz}
    engines: {node: '>=4'}

  extsprintf@1.3.0:
    resolution: {integrity: sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=, tarball: http://registry.m.jd.com/extsprintf/download/extsprintf-1.3.0.tgz}
    engines: {'0': node >=0.6.0}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=, tarball: http://registry.m.jd.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz}

  fast-diff@1.3.0:
    resolution: {integrity: sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=, tarball: http://registry.m.jd.com/fast-diff/download/fast-diff-1.3.0.tgz}

  fast-glob@3.3.3:
    resolution: {integrity: sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=, tarball: http://registry.m.jd.com/fast-glob/download/fast-glob-3.3.3.tgz}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=, tarball: http://registry.m.jd.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=, tarball: http://registry.m.jd.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz}

  fast-uri@3.0.6:
    resolution: {integrity: sha1-iPEwt3z66iN41Wv5cN6iElemh0g=, tarball: http://registry.m.jd.com/fast-uri/download/fast-uri-3.0.6.tgz}

  fastq@1.19.1:
    resolution: {integrity: sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=, tarball: http://registry.m.jd.com/fastq/download/fastq-1.19.1.tgz}

  fdir@6.4.6:
    resolution: {integrity: sha1-KyaMAjJpcGMRG78/ZIEKKnQbooE=, tarball: http://registry.m.jd.com/fdir/download/fdir-6.4.6.tgz}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  figures@2.0.0:
    resolution: {integrity: sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=, tarball: http://registry.m.jd.com/figures/download/figures-2.0.0.tgz}
    engines: {node: '>=4'}

  figures@3.2.0:
    resolution: {integrity: sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=, tarball: http://registry.m.jd.com/figures/download/figures-3.2.0.tgz}
    engines: {node: '>=8'}

  file-entry-cache@8.0.0:
    resolution: {integrity: sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=, tarball: http://registry.m.jd.com/file-entry-cache/download/file-entry-cache-8.0.0.tgz}
    engines: {node: '>=16.0.0'}

  filelist@1.0.4:
    resolution: {integrity: sha1-94l4oelEd1/55i50RCTyFeWDUrU=, tarball: http://registry.m.jd.com/filelist/download/filelist-1.0.4.tgz}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=, tarball: http://registry.m.jd.com/fill-range/download/fill-range-7.1.1.tgz}
    engines: {node: '>=8'}

  filter-obj@5.1.0:
    resolution: {integrity: sha1-W9iWdgAKcT19suGX9mAnRCjlJO0=, tarball: http://registry.m.jd.com/filter-obj/download/filter-obj-5.1.0.tgz}
    engines: {node: '>=14.16'}

  find-up@2.1.0:
    resolution: {integrity: sha1-RdG35QbHF93UgndaK3eSCjwMV6c=, tarball: http://registry.m.jd.com/find-up/download/find-up-2.1.0.tgz}
    engines: {node: '>=4'}

  find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=, tarball: http://registry.m.jd.com/find-up/download/find-up-5.0.0.tgz}
    engines: {node: '>=10'}

  find-up@7.0.0:
    resolution: {integrity: sha1-6N7BRV90942IitZb98oT3StOZvs=, tarball: http://registry.m.jd.com/find-up/download/find-up-7.0.0.tgz}
    engines: {node: '>=18'}

  flat-cache@4.0.1:
    resolution: {integrity: sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=, tarball: http://registry.m.jd.com/flat-cache/download/flat-cache-4.0.1.tgz}
    engines: {node: '>=16'}

  flatted@3.3.3:
    resolution: {integrity: sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=, tarball: http://registry.m.jd.com/flatted/download/flatted-3.3.3.tgz}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=, tarball: http://registry.m.jd.com/follow-redirects/download/follow-redirects-1.15.9.tgz}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  foreground-child@3.3.1:
    resolution: {integrity: sha1-Mujp7Rtoo0l777msK2rfkqY4V28=, tarball: http://registry.m.jd.com/foreground-child/download/foreground-child-3.3.1.tgz}
    engines: {node: '>=14'}

  forever-agent@0.6.1:
    resolution: {integrity: sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=, tarball: http://registry.m.jd.com/forever-agent/download/forever-agent-0.6.1.tgz}

  form-data@2.3.3:
    resolution: {integrity: sha1-3M5SwF9kTymManq5Nr1yTO/786Y=, tarball: http://registry.m.jd.com/form-data/download/form-data-2.3.3.tgz}
    engines: {node: '>= 0.12'}

  form-data@2.5.3:
    resolution: {integrity: sha1-+bz4dBjOdIUTwMNJS7SOwnDJesw=, tarball: http://registry.m.jd.com/form-data/download/form-data-2.5.3.tgz}
    engines: {node: '>= 0.12'}

  form-data@4.0.3:
    resolution: {integrity: sha1-YIsbPz4ovg/M9ZAfyF+zZB5c8K4=, tarball: http://registry.m.jd.com/form-data/download/form-data-4.0.3.tgz}
    engines: {node: '>= 6'}

  fraction.js@4.3.7:
    resolution: {integrity: sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=, tarball: http://registry.m.jd.com/fraction.js/download/fraction.js-4.3.7.tgz}

  fs-extra@10.1.0:
    resolution: {integrity: sha1-Aoc8+8QITd4SfqpfmQXu8jJdGr8=, tarball: http://registry.m.jd.com/fs-extra/download/fs-extra-10.1.0.tgz}
    engines: {node: '>=12'}

  fs-extra@8.1.0:
    resolution: {integrity: sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=, tarball: http://registry.m.jd.com/fs-extra/download/fs-extra-8.1.0.tgz}
    engines: {node: '>=6 <7 || >=8'}

  fs-extra@9.1.0:
    resolution: {integrity: sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=, tarball: http://registry.m.jd.com/fs-extra/download/fs-extra-9.1.0.tgz}
    engines: {node: '>=10'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha1-FQStJSMVjKpA20onh8sBQRmU6k8=, tarball: http://registry.m.jd.com/fs.realpath/download/fs.realpath-1.0.0.tgz}

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=, tarball: http://registry.m.jd.com/fsevents/download/fsevents-2.3.3.tgz}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=, tarball: http://registry.m.jd.com/function-bind/download/function-bind-1.1.2.tgz}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=, tarball: http://registry.m.jd.com/gensync/download/gensync-1.0.0-beta.2.tgz}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=, tarball: http://registry.m.jd.com/get-caller-file/download/get-caller-file-2.0.5.tgz}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=, tarball: http://registry.m.jd.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=, tarball: http://registry.m.jd.com/get-proto/download/get-proto-1.0.1.tgz}
    engines: {node: '>= 0.4'}

  get-tsconfig@4.10.1:
    resolution: {integrity: sha1-00wcAfR9ZaYGw3qnoXe8PlarSy4=, tarball: http://registry.m.jd.com/get-tsconfig/download/get-tsconfig-4.10.1.tgz}

  getpass@0.1.7:
    resolution: {integrity: sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=, tarball: http://registry.m.jd.com/getpass/download/getpass-0.1.7.tgz}

  git-raw-commits@4.0.0:
    resolution: {integrity: sha1-shL9K/+XJtJ8EoOhFX6ClJBZMoU=, tarball: http://registry.m.jd.com/git-raw-commits/download/git-raw-commits-4.0.0.tgz}
    engines: {node: '>=16'}
    hasBin: true

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=, tarball: http://registry.m.jd.com/glob-parent/download/glob-parent-5.1.2.tgz}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=, tarball: http://registry.m.jd.com/glob-parent/download/glob-parent-6.0.2.tgz}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=, tarball: http://registry.m.jd.com/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz}

  glob@10.4.5:
    resolution: {integrity: sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=, tarball: http://registry.m.jd.com/glob/download/glob-10.4.5.tgz}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=, tarball: http://registry.m.jd.com/glob/download/glob-7.2.3.tgz}
    deprecated: Glob versions prior to v9 are no longer supported

  global-directory@4.0.1:
    resolution: {integrity: sha1-TXrHz9LLc/MExTuIEIkXSN9eNh4=, tarball: http://registry.m.jd.com/global-directory/download/global-directory-4.0.1.tgz}
    engines: {node: '>=18'}

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=, tarball: http://registry.m.jd.com/globals/download/globals-11.12.0.tgz}
    engines: {node: '>=4'}

  globals@14.0.0:
    resolution: {integrity: sha1-iY10E8Kbq89rr+Vvyt3thYrack4=, tarball: http://registry.m.jd.com/globals/download/globals-14.0.0.tgz}
    engines: {node: '>=18'}

  globals@15.15.0:
    resolution: {integrity: sha1-fEdhKZ1BwysHVxWkzh7eeJf/cqg=, tarball: http://registry.m.jd.com/globals/download/globals-15.15.0.tgz}
    engines: {node: '>=18'}

  globby@14.1.0:
    resolution: {integrity: sha1-E4t453z1qNeU4yexXc6Avx+wpz4=, tarball: http://registry.m.jd.com/globby/download/globby-14.1.0.tgz}
    engines: {node: '>=18'}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=, tarball: http://registry.m.jd.com/gopd/download/gopd-1.2.0.tgz}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=, tarball: http://registry.m.jd.com/graceful-fs/download/graceful-fs-4.2.11.tgz}

  graphemer@1.4.0:
    resolution: {integrity: sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=, tarball: http://registry.m.jd.com/graphemer/download/graphemer-1.4.0.tgz}

  har-schema@2.0.0:
    resolution: {integrity: sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=, tarball: http://registry.m.jd.com/har-schema/download/har-schema-2.0.0.tgz}
    engines: {node: '>=4'}

  har-validator@5.1.5:
    resolution: {integrity: sha1-HwgDufjLIMD6E4It8ezds2veHv0=, tarball: http://registry.m.jd.com/har-validator/download/har-validator-5.1.5.tgz}
    engines: {node: '>=6'}
    deprecated: this library is no longer supported

  has-flag@3.0.0:
    resolution: {integrity: sha1-tdRU3CGZriJWmfNGfloH87lVuv0=, tarball: http://registry.m.jd.com/has-flag/download/has-flag-3.0.0.tgz}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=, tarball: http://registry.m.jd.com/has-flag/download/has-flag-4.0.0.tgz}
    engines: {node: '>=8'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=, tarball: http://registry.m.jd.com/has-symbols/download/has-symbols-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=, tarball: http://registry.m.jd.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=, tarball: http://registry.m.jd.com/hasown/download/hasown-2.0.2.tgz}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha1-hK5l+n6vsWX922FWauFLrwVmTw8=, tarball: http://registry.m.jd.com/he/download/he-1.2.0.tgz}
    hasBin: true

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=, tarball: http://registry.m.jd.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz}

  html-minifier-terser@6.1.0:
    resolution: {integrity: sha1-v8gYk0zAeRj2s2afV3Ts39SPMqs=, tarball: http://registry.m.jd.com/html-minifier-terser/download/html-minifier-terser-6.1.0.tgz}
    engines: {node: '>=12'}
    hasBin: true

  http-signature@1.2.0:
    resolution: {integrity: sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=, tarball: http://registry.m.jd.com/http-signature/download/http-signature-1.2.0.tgz}
    engines: {node: '>=0.8', npm: '>=1.3.7'}

  husky@9.1.7:
    resolution: {integrity: sha1-1Go4A10QG0anBFaoUP9CATRMCy0=, tarball: http://registry.m.jd.com/husky/download/husky-9.1.7.tgz}
    engines: {node: '>=18'}
    hasBin: true

  iconv-lite@0.4.24:
    resolution: {integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=, tarball: http://registry.m.jd.com/iconv-lite/download/iconv-lite-0.4.24.tgz}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=, tarball: http://registry.m.jd.com/iconv-lite/download/iconv-lite-0.6.3.tgz}
    engines: {node: '>=0.10.0'}

  ieee754@1.2.1:
    resolution: {integrity: sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=, tarball: http://registry.m.jd.com/ieee754/download/ieee754-1.2.1.tgz}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=, tarball: http://registry.m.jd.com/ignore/download/ignore-5.3.2.tgz}
    engines: {node: '>= 4'}

  ignore@7.0.5:
    resolution: {integrity: sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=, tarball: http://registry.m.jd.com/ignore/download/ignore-7.0.5.tgz}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=, tarball: http://registry.m.jd.com/image-size/download/image-size-0.5.5.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  immutable@5.1.2:
    resolution: {integrity: sha1-6BaUdkFFBeWk+mUBB7ZeEifRbUs=, tarball: http://registry.m.jd.com/immutable/download/immutable-5.1.2.tgz}

  import-fresh@3.3.1:
    resolution: {integrity: sha1-nOy1ZQPAraHydB271lRuSxO1fM8=, tarball: http://registry.m.jd.com/import-fresh/download/import-fresh-3.3.1.tgz}
    engines: {node: '>=6'}

  import-meta-resolve@4.1.0:
    resolution: {integrity: sha1-+duL6tn6+mGtuBHbd6K/IsU5lwY=, tarball: http://registry.m.jd.com/import-meta-resolve/download/import-meta-resolve-4.1.0.tgz}

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=, tarball: http://registry.m.jd.com/imurmurhash/download/imurmurhash-0.1.4.tgz}
    engines: {node: '>=0.8.19'}

  inflight@1.0.6:
    resolution: {integrity: sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=, tarball: http://registry.m.jd.com/inflight/download/inflight-1.0.6.tgz}

  inherits@2.0.4:
    resolution: {integrity: sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=, tarball: http://registry.m.jd.com/inherits/download/inherits-2.0.4.tgz}

  ini@4.1.1:
    resolution: {integrity: sha1-2Vs9hDsekG5W1nR9VEeQT/UM56E=, tarball: http://registry.m.jd.com/ini/download/ini-4.1.1.tgz}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  inquirer@7.3.3:
    resolution: {integrity: sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=, tarball: http://registry.m.jd.com/inquirer/download/inquirer-7.3.3.tgz}
    engines: {node: '>=8.0.0'}

  intersection-observer@0.12.2:
    resolution: {integrity: sha1-SkU0nMDNkZFmgrH0TCjX7HN9w3U=, tarball: http://registry.m.jd.com/intersection-observer/download/intersection-observer-0.12.2.tgz}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=, tarball: http://registry.m.jd.com/is-arrayish/download/is-arrayish-0.2.1.tgz}

  is-buffer@1.1.6:
    resolution: {integrity: sha1-76ouqdqg16suoTqXsritUf776L4=, tarball: http://registry.m.jd.com/is-buffer/download/is-buffer-1.1.6.tgz}

  is-core-module@2.16.1:
    resolution: {integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=, tarball: http://registry.m.jd.com/is-core-module/download/is-core-module-2.16.1.tgz}
    engines: {node: '>= 0.4'}

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=, tarball: http://registry.m.jd.com/is-extglob/download/is-extglob-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=, tarball: http://registry.m.jd.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz}
    engines: {node: '>=8'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=, tarball: http://registry.m.jd.com/is-glob/download/is-glob-4.0.3.tgz}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=, tarball: http://registry.m.jd.com/is-number/download/is-number-7.0.0.tgz}
    engines: {node: '>=0.12.0'}

  is-obj@2.0.0:
    resolution: {integrity: sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=, tarball: http://registry.m.jd.com/is-obj/download/is-obj-2.0.0.tgz}
    engines: {node: '>=8'}

  is-path-cwd@3.0.0:
    resolution: {integrity: sha1-iJtB5VyFiLHrKpamHQV0CmdFIcc=, tarball: http://registry.m.jd.com/is-path-cwd/download/is-path-cwd-3.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-path-inside@4.0.0:
    resolution: {integrity: sha1-gFrrYsR8GxL8P9E7+z7R50MAcds=, tarball: http://registry.m.jd.com/is-path-inside/download/is-path-inside-4.0.0.tgz}
    engines: {node: '>=12'}

  is-text-path@2.0.0:
    resolution: {integrity: sha1-skhOK3IKYz/rLoW2fcGT/3LHVjY=, tarball: http://registry.m.jd.com/is-text-path/download/is-text-path-2.0.0.tgz}
    engines: {node: '>=8'}

  is-typedarray@1.0.0:
    resolution: {integrity: sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=, tarball: http://registry.m.jd.com/is-typedarray/download/is-typedarray-1.0.0.tgz}

  is-what@3.14.1:
    resolution: {integrity: sha1-4SIvRt3ahd6tD9HJ3xMXYOd3VcE=, tarball: http://registry.m.jd.com/is-what/download/is-what-3.14.1.tgz}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=, tarball: http://registry.m.jd.com/isexe/download/isexe-2.0.0.tgz}

  isstream@0.1.2:
    resolution: {integrity: sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=, tarball: http://registry.m.jd.com/isstream/download/isstream-0.1.2.tgz}

  jackspeak@3.4.3:
    resolution: {integrity: sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=, tarball: http://registry.m.jd.com/jackspeak/download/jackspeak-3.4.3.tgz}

  jake@10.9.2:
    resolution: {integrity: sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=, tarball: http://registry.m.jd.com/jake/download/jake-10.9.2.tgz}
    engines: {node: '>=10'}
    hasBin: true

  jest-worker@27.5.1:
    resolution: {integrity: sha1-jRRvCQDolzsQa29zzB6ajLhvjbA=, tarball: http://registry.m.jd.com/jest-worker/download/jest-worker-27.5.1.tgz}
    engines: {node: '>= 10.13.0'}

  jiti@1.21.7:
    resolution: {integrity: sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=, tarball: http://registry.m.jd.com/jiti/download/jiti-1.21.7.tgz}
    hasBin: true

  jiti@2.4.2:
    resolution: {integrity: sha1-0Zt3Muu2EWsG4gONp0pVNm+u9WA=, tarball: http://registry.m.jd.com/jiti/download/jiti-2.4.2.tgz}
    hasBin: true

  jotai@2.12.5:
    resolution: {integrity: sha1-LezfQd/K1eSvxyeNqBxFZG1K6K0=, tarball: http://registry.m.jd.com/jotai/download/jotai-2.12.5.tgz}
    engines: {node: '>=12.20.0'}
    peerDependencies:
      '@types/react': '>=17.0.0'
      react: '>=17.0.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      react:
        optional: true

  js-cookie@3.0.5:
    resolution: {integrity: sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=, tarball: http://registry.m.jd.com/js-cookie/download/js-cookie-3.0.5.tgz}
    engines: {node: '>=14'}

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=, tarball: http://registry.m.jd.com/js-tokens/download/js-tokens-4.0.0.tgz}

  js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=, tarball: http://registry.m.jd.com/js-yaml/download/js-yaml-4.1.0.tgz}
    hasBin: true

  jsbn@0.1.1:
    resolution: {integrity: sha1-peZUwuWi3rXyAdls77yoDA7y9RM=, tarball: http://registry.m.jd.com/jsbn/download/jsbn-0.1.1.tgz}

  jsesc@3.0.2:
    resolution: {integrity: sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=, tarball: http://registry.m.jd.com/jsesc/download/jsesc-3.0.2.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=, tarball: http://registry.m.jd.com/jsesc/download/jsesc-3.1.0.tgz}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=, tarball: http://registry.m.jd.com/json-buffer/download/json-buffer-3.0.1.tgz}

  json-parse-better-errors@1.0.2:
    resolution: {integrity: sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=, tarball: http://registry.m.jd.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=, tarball: http://registry.m.jd.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=, tarball: http://registry.m.jd.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=, tarball: http://registry.m.jd.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz}

  json-schema@0.4.0:
    resolution: {integrity: sha1-995M9u+rg4666zI2R0y7paGTCrU=, tarball: http://registry.m.jd.com/json-schema/download/json-schema-0.4.0.tgz}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=, tarball: http://registry.m.jd.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz}

  json-stringify-safe@5.0.1:
    resolution: {integrity: sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=, tarball: http://registry.m.jd.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz}

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=, tarball: http://registry.m.jd.com/json5/download/json5-2.2.3.tgz}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@4.0.0:
    resolution: {integrity: sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=, tarball: http://registry.m.jd.com/jsonfile/download/jsonfile-4.0.0.tgz}

  jsonfile@6.1.0:
    resolution: {integrity: sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=, tarball: http://registry.m.jd.com/jsonfile/download/jsonfile-6.1.0.tgz}

  jsonp@0.2.1:
    resolution: {integrity: sha1-pltPoPEL2nGaBUQep7lMVfPhW64=, tarball: http://registry.m.jd.com/jsonp/download/jsonp-0.2.1.tgz}

  jsonparse@1.3.1:
    resolution: {integrity: sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=, tarball: http://registry.m.jd.com/jsonparse/download/jsonparse-1.3.1.tgz}
    engines: {'0': node >= 0.2.0}

  jsprim@1.4.2:
    resolution: {integrity: sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=, tarball: http://registry.m.jd.com/jsprim/download/jsprim-1.4.2.tgz}
    engines: {node: '>=0.6.0'}

  keyv@4.5.4:
    resolution: {integrity: sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=, tarball: http://registry.m.jd.com/keyv/download/keyv-4.5.4.tgz}

  less@4.3.0:
    resolution: {integrity: sha1-7wz8JgqcqAee2NDjUSvaihLILyo=, tarball: http://registry.m.jd.com/less/download/less-4.3.0.tgz}
    engines: {node: '>=14'}
    hasBin: true

  levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=, tarball: http://registry.m.jd.com/levn/download/levn-0.4.1.tgz}
    engines: {node: '>= 0.8.0'}

  lightningcss-darwin-arm64@1.30.1:
    resolution: {integrity: sha1-PUfOXiIblWfHA5UO3yUpyko3AK4=, tarball: http://registry.m.jd.com/lightningcss-darwin-arm64/download/lightningcss-darwin-arm64-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-darwin-x64@1.30.1:
    resolution: {integrity: sha1-6BEF0/1jMIYMFf6GD2TTnP9fvSI=, tarball: http://registry.m.jd.com/lightningcss-darwin-x64/download/lightningcss-darwin-x64-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-freebsd-x64@1.30.1:
    resolution: {integrity: sha1-oOcyAxCD/51iXF2wIdCesIWvi+Q=, tarball: http://registry.m.jd.com/lightningcss-freebsd-x64/download/lightningcss-freebsd-x64-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-linux-arm-gnueabihf@1.30.1:
    resolution: {integrity: sha1-H17MpglVKN22SfkwS6JWDHJHSQg=, tarball: http://registry.m.jd.com/lightningcss-linux-arm-gnueabihf/download/lightningcss-linux-arm-gnueabihf-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-linux-arm64-gnu@1.30.1:
    resolution: {integrity: sha1-7ud5lyYQO///HoiZPfcm9pEewAk=, tarball: http://registry.m.jd.com/lightningcss-linux-arm64-gnu/download/lightningcss-linux-arm64-gnu-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-linux-arm64-musl@1.30.1:
    resolution: {integrity: sha1-8uS1P0KJL+7vj2IMu4iffAZKff4=, tarball: http://registry.m.jd.com/lightningcss-linux-arm64-musl/download/lightningcss-linux-arm64-musl-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-linux-x64-gnu@1.30.1:
    resolution: {integrity: sha1-L8cJYiS8AA67l+6pSuokjFsOsVc=, tarball: http://registry.m.jd.com/lightningcss-linux-x64-gnu/download/lightningcss-linux-x64-gnu-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-linux-x64-musl@1.30.1:
    resolution: {integrity: sha1-ZtyisVn9gZ6oMsRIldB+WzHXXyY=, tarball: http://registry.m.jd.com/lightningcss-linux-x64-musl/download/lightningcss-linux-x64-musl-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-win32-arm64-msvc@1.30.1:
    resolution: {integrity: sha1-fYEQoZ18LSK/3y8ruL5o59G2kDk=, tarball: http://registry.m.jd.com/lightningcss-win32-arm64-msvc/download/lightningcss-win32-arm64-msvc-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss-win32-x64-msvc@1.30.1:
    resolution: {integrity: sha1-/X3QCOqYSUuF0ktL6gFnk/Lg41I=, tarball: http://registry.m.jd.com/lightningcss-win32-x64-msvc/download/lightningcss-win32-x64-msvc-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lightningcss@1.30.1:
    resolution: {integrity: sha1-eOl5wtWVv8uQ0qjA62Mv5sW/7V0=, tarball: http://registry.m.jd.com/lightningcss/download/lightningcss-1.30.1.tgz}
    engines: {node: '>= 12.0.0'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=, tarball: http://registry.m.jd.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz}

  load-json-file@4.0.0:
    resolution: {integrity: sha1-L19Fq5HjMhYjT9U62rZo607AmTs=, tarball: http://registry.m.jd.com/load-json-file/download/load-json-file-4.0.0.tgz}
    engines: {node: '>=4'}

  loader-runner@4.3.0:
    resolution: {integrity: sha1-wbShY7mfYUgwNTsWdV5xSawjFOE=, tarball: http://registry.m.jd.com/loader-runner/download/loader-runner-4.3.0.tgz}
    engines: {node: '>=6.11.5'}

  locate-path@2.0.0:
    resolution: {integrity: sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=, tarball: http://registry.m.jd.com/locate-path/download/locate-path-2.0.0.tgz}
    engines: {node: '>=4'}

  locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=, tarball: http://registry.m.jd.com/locate-path/download/locate-path-6.0.0.tgz}
    engines: {node: '>=10'}

  locate-path@7.2.0:
    resolution: {integrity: sha1-acsXeb2Qs1qx53Hh8viaICwqioo=, tarball: http://registry.m.jd.com/locate-path/download/locate-path-7.2.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  lodash.camelcase@4.3.0:
    resolution: {integrity: sha1-soqmKIorn8ZRA1x3EfZathkDMaY=, tarball: http://registry.m.jd.com/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha1-gteb/zCmfEAF/9XiUVMArZyk168=, tarball: http://registry.m.jd.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=, tarball: http://registry.m.jd.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz}

  lodash.kebabcase@4.1.1:
    resolution: {integrity: sha1-hImxyw0p/4gZXM7KRI/21swpXDY=, tarball: http://registry.m.jd.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz}

  lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=, tarball: http://registry.m.jd.com/lodash.merge/download/lodash.merge-4.6.2.tgz}

  lodash.mergewith@4.6.2:
    resolution: {integrity: sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U=, tarball: http://registry.m.jd.com/lodash.mergewith/download/lodash.mergewith-4.6.2.tgz}

  lodash.snakecase@4.1.1:
    resolution: {integrity: sha1-OdcUo1NXFHg3rv1ktdy7Fr7Nj40=, tarball: http://registry.m.jd.com/lodash.snakecase/download/lodash.snakecase-4.1.1.tgz}

  lodash.startcase@4.4.0:
    resolution: {integrity: sha1-lDbjTtJgk+1/+uGTYUQ1CRXZrdg=, tarball: http://registry.m.jd.com/lodash.startcase/download/lodash.startcase-4.4.0.tgz}

  lodash.uniq@4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=, tarball: http://registry.m.jd.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz}

  lodash.upperfirst@4.3.1:
    resolution: {integrity: sha1-E2Xt9DFIBIHvDRxolXpe2Z1J984=, tarball: http://registry.m.jd.com/lodash.upperfirst/download/lodash.upperfirst-4.3.1.tgz}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=, tarball: http://registry.m.jd.com/lodash/download/lodash-4.17.21.tgz}

  loose-envify@1.4.0:
    resolution: {integrity: sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=, tarball: http://registry.m.jd.com/loose-envify/download/loose-envify-1.4.0.tgz}
    hasBin: true

  lower-case@2.0.2:
    resolution: {integrity: sha1-b6I3xj29xKgsoP2ILkci3F5jTig=, tarball: http://registry.m.jd.com/lower-case/download/lower-case-2.0.2.tgz}

  lru-cache@10.4.3:
    resolution: {integrity: sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=, tarball: http://registry.m.jd.com/lru-cache/download/lru-cache-10.4.3.tgz}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=, tarball: http://registry.m.jd.com/lru-cache/download/lru-cache-5.1.1.tgz}

  magic-string@0.30.17:
    resolution: {integrity: sha1-RQpElnPSRg5bvPupphkWoXFMdFM=, tarball: http://registry.m.jd.com/magic-string/download/magic-string-0.30.17.tgz}

  make-dir@2.1.0:
    resolution: {integrity: sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=, tarball: http://registry.m.jd.com/make-dir/download/make-dir-2.1.0.tgz}
    engines: {node: '>=6'}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=, tarball: http://registry.m.jd.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz}
    engines: {node: '>= 0.4'}

  md5@2.3.0:
    resolution: {integrity: sha1-w9qaaq46MLRreww0m4exENw72k8=, tarball: http://registry.m.jd.com/md5/download/md5-2.3.0.tgz}

  meow@12.1.1:
    resolution: {integrity: sha1-5Vjd26sSR3tpsumicowyfxkbrOY=, tarball: http://registry.m.jd.com/meow/download/meow-12.1.1.tgz}
    engines: {node: '>=16.10'}

  meow@13.2.0:
    resolution: {integrity: sha1-a31j+RP5hAY7PMJhtuiADEzTR08=, tarball: http://registry.m.jd.com/meow/download/meow-13.2.0.tgz}
    engines: {node: '>=18'}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=, tarball: http://registry.m.jd.com/merge-stream/download/merge-stream-2.0.0.tgz}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=, tarball: http://registry.m.jd.com/merge2/download/merge2-1.4.1.tgz}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=, tarball: http://registry.m.jd.com/micromatch/download/micromatch-4.0.8.tgz}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=, tarball: http://registry.m.jd.com/mime-db/download/mime-db-1.52.0.tgz}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=, tarball: http://registry.m.jd.com/mime-types/download/mime-types-2.1.35.tgz}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=, tarball: http://registry.m.jd.com/mime/download/mime-1.6.0.tgz}
    engines: {node: '>=4'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=, tarball: http://registry.m.jd.com/mimic-fn/download/mimic-fn-2.1.0.tgz}
    engines: {node: '>=6'}

  minimalistic-assert@1.0.1:
    resolution: {integrity: sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=, tarball: http://registry.m.jd.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=, tarball: http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz}

  minimatch@5.1.6:
    resolution: {integrity: sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=, tarball: http://registry.m.jd.com/minimatch/download/minimatch-5.1.6.tgz}
    engines: {node: '>=10'}

  minimatch@9.0.5:
    resolution: {integrity: sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=, tarball: http://registry.m.jd.com/minimatch/download/minimatch-9.0.5.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=, tarball: http://registry.m.jd.com/minimist/download/minimist-1.2.8.tgz}

  minipass@7.1.2:
    resolution: {integrity: sha1-k6libOXl5mvU24aEnnUV6SNApwc=, tarball: http://registry.m.jd.com/minipass/download/minipass-7.1.2.tgz}
    engines: {node: '>=16 || 14 >=14.17'}

  ms@2.0.0:
    resolution: {integrity: sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=, tarball: http://registry.m.jd.com/ms/download/ms-2.0.0.tgz}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=, tarball: http://registry.m.jd.com/ms/download/ms-2.1.3.tgz}

  mute-stream@0.0.8:
    resolution: {integrity: sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=, tarball: http://registry.m.jd.com/mute-stream/download/mute-stream-0.0.8.tgz}

  nanoid@3.3.11:
    resolution: {integrity: sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=, tarball: http://registry.m.jd.com/nanoid/download/nanoid-3.3.11.tgz}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=, tarball: http://registry.m.jd.com/natural-compare/download/natural-compare-1.4.0.tgz}

  needle@3.3.1:
    resolution: {integrity: sha1-Y/da7FgMLnfiCfPzJOLN89Kb0Ek=, tarball: http://registry.m.jd.com/needle/download/needle-3.3.1.tgz}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  neo-async@2.6.2:
    resolution: {integrity: sha1-tKr7k+OustgXTKU88WOrfXMIMF8=, tarball: http://registry.m.jd.com/neo-async/download/neo-async-2.6.2.tgz}

  no-case@3.0.4:
    resolution: {integrity: sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=, tarball: http://registry.m.jd.com/no-case/download/no-case-3.0.4.tgz}

  node-addon-api@7.1.1:
    resolution: {integrity: sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=, tarball: http://registry.m.jd.com/node-addon-api/download/node-addon-api-7.1.1.tgz}

  node-html-parser@5.4.2:
    resolution: {integrity: sha1-k+AEA4wXr4AibJQjNpkKDq7YE2o=, tarball: http://registry.m.jd.com/node-html-parser/download/node-html-parser-5.4.2.tgz}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=, tarball: http://registry.m.jd.com/node-releases/download/node-releases-2.0.19.tgz}

  normalize-range@0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=, tarball: http://registry.m.jd.com/normalize-range/download/normalize-range-0.1.2.tgz}
    engines: {node: '>=0.10.0'}

  nth-check@2.1.1:
    resolution: {integrity: sha1-yeq0KO/842zWuSySS9sADvHx7R0=, tarball: http://registry.m.jd.com/nth-check/download/nth-check-2.1.1.tgz}

  oauth-sign@0.9.0:
    resolution: {integrity: sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=, tarball: http://registry.m.jd.com/oauth-sign/download/oauth-sign-0.9.0.tgz}

  object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=, tarball: http://registry.m.jd.com/object-assign/download/object-assign-4.1.1.tgz}
    engines: {node: '>=0.10.0'}

  once@1.4.0:
    resolution: {integrity: sha1-WDsap3WWHUsROsF9nFC6753Xa9E=, tarball: http://registry.m.jd.com/once/download/once-1.4.0.tgz}

  onetime@5.1.2:
    resolution: {integrity: sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=, tarball: http://registry.m.jd.com/onetime/download/onetime-5.1.2.tgz}
    engines: {node: '>=6'}

  optionator@0.9.4:
    resolution: {integrity: sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=, tarball: http://registry.m.jd.com/optionator/download/optionator-0.9.4.tgz}
    engines: {node: '>= 0.8.0'}

  os-tmpdir@1.0.2:
    resolution: {integrity: sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=, tarball: http://registry.m.jd.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz}
    engines: {node: '>=0.10.0'}

  p-limit@1.3.0:
    resolution: {integrity: sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=, tarball: http://registry.m.jd.com/p-limit/download/p-limit-1.3.0.tgz}
    engines: {node: '>=4'}

  p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=, tarball: http://registry.m.jd.com/p-limit/download/p-limit-3.1.0.tgz}
    engines: {node: '>=10'}

  p-limit@4.0.0:
    resolution: {integrity: sha1-kUr2VE7TK/pUZwsGHK/L0EmEtkQ=, tarball: http://registry.m.jd.com/p-limit/download/p-limit-4.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-locate@2.0.0:
    resolution: {integrity: sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=, tarball: http://registry.m.jd.com/p-locate/download/p-locate-2.0.0.tgz}
    engines: {node: '>=4'}

  p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=, tarball: http://registry.m.jd.com/p-locate/download/p-locate-5.0.0.tgz}
    engines: {node: '>=10'}

  p-locate@6.0.0:
    resolution: {integrity: sha1-PamknUk0uQEIncozAvpl3FoFwE8=, tarball: http://registry.m.jd.com/p-locate/download/p-locate-6.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-map@7.0.3:
    resolution: {integrity: sha1-esIQotNvgewotzYTSBD3ukQYzbY=, tarball: http://registry.m.jd.com/p-map/download/p-map-7.0.3.tgz}
    engines: {node: '>=18'}

  p-try@1.0.0:
    resolution: {integrity: sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=, tarball: http://registry.m.jd.com/p-try/download/p-try-1.0.0.tgz}
    engines: {node: '>=4'}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=, tarball: http://registry.m.jd.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz}

  param-case@3.0.4:
    resolution: {integrity: sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=, tarball: http://registry.m.jd.com/param-case/download/param-case-3.0.4.tgz}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=, tarball: http://registry.m.jd.com/parent-module/download/parent-module-1.0.1.tgz}
    engines: {node: '>=6'}

  parse-json@4.0.0:
    resolution: {integrity: sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=, tarball: http://registry.m.jd.com/parse-json/download/parse-json-4.0.0.tgz}
    engines: {node: '>=4'}

  parse-json@5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=, tarball: http://registry.m.jd.com/parse-json/download/parse-json-5.2.0.tgz}
    engines: {node: '>=8'}

  parse-node-version@1.0.1:
    resolution: {integrity: sha1-4rXb7eAOf6m8NjYH9TMn6LBzGJs=, tarball: http://registry.m.jd.com/parse-node-version/download/parse-node-version-1.0.1.tgz}
    engines: {node: '>= 0.10'}

  pascal-case@3.1.2:
    resolution: {integrity: sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=, tarball: http://registry.m.jd.com/pascal-case/download/pascal-case-3.1.2.tgz}

  path-exists@3.0.0:
    resolution: {integrity: sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=, tarball: http://registry.m.jd.com/path-exists/download/path-exists-3.0.0.tgz}
    engines: {node: '>=4'}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=, tarball: http://registry.m.jd.com/path-exists/download/path-exists-4.0.0.tgz}
    engines: {node: '>=8'}

  path-exists@5.0.0:
    resolution: {integrity: sha1-pqrZSJIAsh+rMeSc8JJ35RFvuec=, tarball: http://registry.m.jd.com/path-exists/download/path-exists-5.0.0.tgz}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha1-F0uSaHNVNP+8es5r9TpanhtcX18=, tarball: http://registry.m.jd.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=, tarball: http://registry.m.jd.com/path-key/download/path-key-3.1.1.tgz}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=, tarball: http://registry.m.jd.com/path-parse/download/path-parse-1.0.7.tgz}

  path-scurry@1.11.1:
    resolution: {integrity: sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=, tarball: http://registry.m.jd.com/path-scurry/download/path-scurry-1.11.1.tgz}
    engines: {node: '>=16 || 14 >=14.18'}

  path-type@6.0.0:
    resolution: {integrity: sha1-Lxu2eRqRzpkZTK7eXWxZIO2B61E=, tarball: http://registry.m.jd.com/path-type/download/path-type-6.0.0.tgz}
    engines: {node: '>=18'}

  pathe@0.2.0:
    resolution: {integrity: sha1-MP17vgoNkfDmC65iH10Z6eIlwzk=, tarball: http://registry.m.jd.com/pathe/download/pathe-0.2.0.tgz}

  performance-now@2.1.0:
    resolution: {integrity: sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=, tarball: http://registry.m.jd.com/performance-now/download/performance-now-2.1.0.tgz}

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=, tarball: http://registry.m.jd.com/picocolors/download/picocolors-1.1.1.tgz}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=, tarball: http://registry.m.jd.com/picomatch/download/picomatch-2.3.1.tgz}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=, tarball: http://registry.m.jd.com/picomatch/download/picomatch-4.0.2.tgz}
    engines: {node: '>=12'}

  pify@3.0.0:
    resolution: {integrity: sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=, tarball: http://registry.m.jd.com/pify/download/pify-3.0.0.tgz}
    engines: {node: '>=4'}

  pify@4.0.1:
    resolution: {integrity: sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=, tarball: http://registry.m.jd.com/pify/download/pify-4.0.1.tgz}
    engines: {node: '>=6'}

  pkg-conf@2.1.0:
    resolution: {integrity: sha1-ISZRTKbyq/69FoWW3xi6V4Z/AFg=, tarball: http://registry.m.jd.com/pkg-conf/download/pkg-conf-2.1.0.tgz}
    engines: {node: '>=4'}

  postcss-attribute-case-insensitive@7.0.1:
    resolution: {integrity: sha1-DEUA47yyFBhI6JOCwFtaMcIwM6M=, tarball: http://registry.m.jd.com/postcss-attribute-case-insensitive/download/postcss-attribute-case-insensitive-7.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-clamp@4.1.0:
    resolution: {integrity: sha1-cmPpWrrdjCuhvZEbC1pcnJPgI2M=, tarball: http://registry.m.jd.com/postcss-clamp/download/postcss-clamp-4.1.0.tgz}
    engines: {node: '>=7.6.0'}
    peerDependencies:
      postcss: ^8.4.6

  postcss-color-functional-notation@7.0.10:
    resolution: {integrity: sha1-8enD5DcYidzf6r+oUVRk/YM4ztw=, tarball: http://registry.m.jd.com/postcss-color-functional-notation/download/postcss-color-functional-notation-7.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-color-hex-alpha@10.0.0:
    resolution: {integrity: sha1-XdProfj6y06jBsum4/dxLodrDHY=, tarball: http://registry.m.jd.com/postcss-color-hex-alpha/download/postcss-color-hex-alpha-10.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-color-rebeccapurple@10.0.0:
    resolution: {integrity: sha1-WtooQGrEfgeW3/QFawqdWm7OrZg=, tarball: http://registry.m.jd.com/postcss-color-rebeccapurple/download/postcss-color-rebeccapurple-10.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-custom-media@11.0.6:
    resolution: {integrity: sha1-a0UOW/ognvtzaDAGZoLmVnvQSWc=, tarball: http://registry.m.jd.com/postcss-custom-media/download/postcss-custom-media-11.0.6.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-custom-properties@14.0.6:
    resolution: {integrity: sha1-Gvc6ZQvxFboFLPkVKHyZgoJfyQ4=, tarball: http://registry.m.jd.com/postcss-custom-properties/download/postcss-custom-properties-14.0.6.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-custom-selectors@8.0.5:
    resolution: {integrity: sha1-lEjtN6EicderbLNktvdqRqSjI+g=, tarball: http://registry.m.jd.com/postcss-custom-selectors/download/postcss-custom-selectors-8.0.5.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-dir-pseudo-class@9.0.1:
    resolution: {integrity: sha1-gNnoQsmunSn2v1/Tz5lyiR1swMo=, tarball: http://registry.m.jd.com/postcss-dir-pseudo-class/download/postcss-dir-pseudo-class-9.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-double-position-gradients@6.0.2:
    resolution: {integrity: sha1-GF+Oqy25z040vmm1cGyQWJW7Uq4=, tarball: http://registry.m.jd.com/postcss-double-position-gradients/download/postcss-double-position-gradients-6.0.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-focus-visible@10.0.1:
    resolution: {integrity: sha1-H3kEkENootEYCyIFldd7b4qVeGg=, tarball: http://registry.m.jd.com/postcss-focus-visible/download/postcss-focus-visible-10.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-focus-within@9.0.1:
    resolution: {integrity: sha1-rAHOgNPy6LKz6sT/hPjhXNAFe8c=, tarball: http://registry.m.jd.com/postcss-focus-within/download/postcss-focus-within-9.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-font-variant@5.0.0:
    resolution: {integrity: sha1-79WbS36ouwYSfy0DG/u38k0y+mY=, tarball: http://registry.m.jd.com/postcss-font-variant/download/postcss-font-variant-5.0.0.tgz}
    peerDependencies:
      postcss: ^8.1.0

  postcss-gap-properties@6.0.0:
    resolution: {integrity: sha1-1f8L35I8Bmhkme0rEuEl/mQFT+0=, tarball: http://registry.m.jd.com/postcss-gap-properties/download/postcss-gap-properties-6.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-image-set-function@7.0.0:
    resolution: {integrity: sha1-U46U4WcWvkf53wVztWu6yobh2lM=, tarball: http://registry.m.jd.com/postcss-image-set-function/download/postcss-image-set-function-7.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-lab-function@7.0.10:
    resolution: {integrity: sha1-BTe9ckW5NfwTMpjIiWvL0WBUDK4=, tarball: http://registry.m.jd.com/postcss-lab-function/download/postcss-lab-function-7.0.10.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-loader@8.1.1:
    resolution: {integrity: sha1-KCJYnnUiknNElUrLVbvybosZXf4=, tarball: http://registry.m.jd.com/postcss-loader/download/postcss-loader-8.1.1.tgz}
    engines: {node: '>= 18.12.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true

  postcss-logical@8.1.0:
    resolution: {integrity: sha1-QJKxa0nj7NpwxNiUUlfaQD0Wcig=, tarball: http://registry.m.jd.com/postcss-logical/download/postcss-logical-8.1.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-nesting@13.0.2:
    resolution: {integrity: sha1-/eDU33crdtA7UuzMhDcujRyhQC4=, tarball: http://registry.m.jd.com/postcss-nesting/download/postcss-nesting-13.0.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-opacity-percentage@3.0.0:
    resolution: {integrity: sha1-Cw217V21Zw4GcES4AwuJwhbh6wo=, tarball: http://registry.m.jd.com/postcss-opacity-percentage/download/postcss-opacity-percentage-3.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-overflow-shorthand@6.0.0:
    resolution: {integrity: sha1-9SUrSi7hbGjNipAp7bU3DEqYCK8=, tarball: http://registry.m.jd.com/postcss-overflow-shorthand/download/postcss-overflow-shorthand-6.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-page-break@3.0.4:
    resolution: {integrity: sha1-f790HCM2IWIraNQ1ur+3DdjB7l8=, tarball: http://registry.m.jd.com/postcss-page-break/download/postcss-page-break-3.0.4.tgz}
    peerDependencies:
      postcss: ^8

  postcss-place@10.0.0:
    resolution: {integrity: sha1-ujbuR4bKQBN3ztF6OdkFDtdy5ak=, tarball: http://registry.m.jd.com/postcss-place/download/postcss-place-10.0.0.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-preset-env@10.2.2:
    resolution: {integrity: sha1-S+fwaZ7Sz6abO1kGmGCrhhey7Bk=, tarball: http://registry.m.jd.com/postcss-preset-env/download/postcss-preset-env-10.2.2.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-pseudo-class-any-link@10.0.1:
    resolution: {integrity: sha1-BkVUMRcb9EuE15667un9HAWUZUQ=, tarball: http://registry.m.jd.com/postcss-pseudo-class-any-link/download/postcss-pseudo-class-any-link-10.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-replace-overflow-wrap@4.0.0:
    resolution: {integrity: sha1-0t9r7RC0d7+cUvqyjFaLSynKQxk=, tarball: http://registry.m.jd.com/postcss-replace-overflow-wrap/download/postcss-replace-overflow-wrap-4.0.0.tgz}
    peerDependencies:
      postcss: ^8.0.3

  postcss-selector-not@8.0.1:
    resolution: {integrity: sha1-8t+casn5Xp/kQWykGpV+2hYTAXI=, tarball: http://registry.m.jd.com/postcss-selector-not/download/postcss-selector-not-8.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      postcss: ^8.4

  postcss-selector-parser@7.1.0:
    resolution: {integrity: sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=, tarball: http://registry.m.jd.com/postcss-selector-parser/download/postcss-selector-parser-7.1.0.tgz}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=, tarball: http://registry.m.jd.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz}

  postcss@8.5.4:
    resolution: {integrity: sha1-1hAUrADhHV9YRY7XJH2Jm9ZfmcA=, tarball: http://registry.m.jd.com/postcss/download/postcss-8.5.4.tgz}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=, tarball: http://registry.m.jd.com/prelude-ls/download/prelude-ls-1.2.1.tgz}
    engines: {node: '>= 0.8.0'}

  prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=, tarball: http://registry.m.jd.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz}
    engines: {node: '>=6.0.0'}

  prettier@3.5.3:
    resolution: {integrity: sha1-T8LODWV+egLmAlSfBTsjnLff4bU=, tarball: http://registry.m.jd.com/prettier/download/prettier-3.5.3.tgz}
    engines: {node: '>=14'}
    hasBin: true

  prop-types@15.8.1:
    resolution: {integrity: sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=, tarball: http://registry.m.jd.com/prop-types/download/prop-types-15.8.1.tgz}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=, tarball: http://registry.m.jd.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz}

  prr@1.0.1:
    resolution: {integrity: sha1-0/wRS6BplaRexok/SEzrHXj19HY=, tarball: http://registry.m.jd.com/prr/download/prr-1.0.1.tgz}

  psl@1.15.0:
    resolution: {integrity: sha1-vazjGJbx2XzsannoIkiYzpPZdMY=, tarball: http://registry.m.jd.com/psl/download/psl-1.15.0.tgz}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=, tarball: http://registry.m.jd.com/punycode/download/punycode-2.3.1.tgz}
    engines: {node: '>=6'}

  qs@6.5.3:
    resolution: {integrity: sha1-Ou7/yRln7241wOSI70b7KWq3aq0=, tarball: http://registry.m.jd.com/qs/download/qs-6.5.3.tgz}
    engines: {node: '>=0.6'}

  query-string@9.2.0:
    resolution: {integrity: sha1-v5kJQSaJEXhlqsTgXBBCLEg5go8=, tarball: http://registry.m.jd.com/query-string/download/query-string-9.2.0.tgz}
    engines: {node: '>=18'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=, tarball: http://registry.m.jd.com/queue-microtask/download/queue-microtask-1.2.3.tgz}

  randombytes@2.1.0:
    resolution: {integrity: sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=, tarball: http://registry.m.jd.com/randombytes/download/randombytes-2.1.0.tgz}

  react-dom@18.3.1:
    resolution: {integrity: sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=, tarball: http://registry.m.jd.com/react-dom/download/react-dom-18.3.1.tgz}
    peerDependencies:
      react: ^18.3.1

  react-error-boundary@5.0.0:
    resolution: {integrity: sha1-a2x+B1ySKvsCgxR+WwhO+kTmhXA=, tarball: http://registry.m.jd.com/react-error-boundary/download/react-error-boundary-5.0.0.tgz}
    peerDependencies:
      react: '>=16.13.1'

  react-fast-compare@3.2.2:
    resolution: {integrity: sha1-kpqXpTIwTOn+5LyuRCNPHOLCHUk=, tarball: http://registry.m.jd.com/react-fast-compare/download/react-fast-compare-3.2.2.tgz}

  react-is@16.13.1:
    resolution: {integrity: sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=, tarball: http://registry.m.jd.com/react-is/download/react-is-16.13.1.tgz}

  react-refresh@0.17.0:
    resolution: {integrity: sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=, tarball: http://registry.m.jd.com/react-refresh/download/react-refresh-0.17.0.tgz}
    engines: {node: '>=0.10.0'}

  react@18.3.1:
    resolution: {integrity: sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=, tarball: http://registry.m.jd.com/react/download/react-18.3.1.tgz}
    engines: {node: '>=0.10.0'}

  readdirp@4.1.2:
    resolution: {integrity: sha1-64WAFDX78qfuWPGeCSGwaPxplI0=, tarball: http://registry.m.jd.com/readdirp/download/readdirp-4.1.2.tgz}
    engines: {node: '>= 14.18.0'}

  regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=, tarball: http://registry.m.jd.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=, tarball: http://registry.m.jd.com/regenerate/download/regenerate-1.4.2.tgz}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha1-NWreECY/aF3aElEAzYYsHbiVMn8=, tarball: http://registry.m.jd.com/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz}

  regexpu-core@6.2.0:
    resolution: {integrity: sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=, tarball: http://registry.m.jd.com/regexpu-core/download/regexpu-core-6.2.0.tgz}
    engines: {node: '>=4'}

  regjsgen@0.8.0:
    resolution: {integrity: sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=, tarball: http://registry.m.jd.com/regjsgen/download/regjsgen-0.8.0.tgz}

  regjsparser@0.12.0:
    resolution: {integrity: sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=, tarball: http://registry.m.jd.com/regjsparser/download/regjsparser-0.12.0.tgz}
    hasBin: true

  relateurl@0.2.7:
    resolution: {integrity: sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=, tarball: http://registry.m.jd.com/relateurl/download/relateurl-0.2.7.tgz}
    engines: {node: '>= 0.10'}

  request-promise-core@1.1.4:
    resolution: {integrity: sha1-Pu3UIjII1BmGe3jOgVFn0QWToi8=, tarball: http://registry.m.jd.com/request-promise-core/download/request-promise-core-1.1.4.tgz}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      request: ^2.34

  request-promise@4.2.6:
    resolution: {integrity: sha1-fn5blXhjDm9ZjjgTwPjrNCon8KI=, tarball: http://registry.m.jd.com/request-promise/download/request-promise-4.2.6.tgz}
    engines: {node: '>=0.10.0'}
    deprecated: request-promise has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142
    peerDependencies:
      request: ^2.34

  request@2.88.2:
    resolution: {integrity: sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=, tarball: http://registry.m.jd.com/request/download/request-2.88.2.tgz}
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142

  require-directory@2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=, tarball: http://registry.m.jd.com/require-directory/download/require-directory-2.1.1.tgz}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=, tarball: http://registry.m.jd.com/require-from-string/download/require-from-string-2.0.2.tgz}
    engines: {node: '>=0.10.0'}

  resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=, tarball: http://registry.m.jd.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=, tarball: http://registry.m.jd.com/resolve-from/download/resolve-from-4.0.0.tgz}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=, tarball: http://registry.m.jd.com/resolve-from/download/resolve-from-5.0.0.tgz}
    engines: {node: '>=8'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha1-YWs9wsVwVrVYjDHN9LPWTbEzcg8=, tarball: http://registry.m.jd.com/resolve-pkg-maps/download/resolve-pkg-maps-1.0.0.tgz}

  resolve@1.22.10:
    resolution: {integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=, tarball: http://registry.m.jd.com/resolve/download/resolve-1.22.10.tgz}
    engines: {node: '>= 0.4'}
    hasBin: true

  restore-cursor@3.1.0:
    resolution: {integrity: sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=, tarball: http://registry.m.jd.com/restore-cursor/download/restore-cursor-3.1.0.tgz}
    engines: {node: '>=8'}

  reusify@1.1.0:
    resolution: {integrity: sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=, tarball: http://registry.m.jd.com/reusify/download/reusify-1.1.0.tgz}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rollup-plugin-delete@3.0.1:
    resolution: {integrity: sha1-1bsgB1JjaVbc4aqBeJcWjXs88LI=, tarball: http://registry.m.jd.com/rollup-plugin-delete/download/rollup-plugin-delete-3.0.1.tgz}
    engines: {node: '>=18'}
    peerDependencies:
      rollup: '*'

  rollup@4.42.0:
    resolution: {integrity: sha1-V5kPBZ6Wu8zM79WkHY1a/xWsHLg=, tarball: http://registry.m.jd.com/rollup/download/rollup-4.42.0.tgz}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  run-async@2.4.1:
    resolution: {integrity: sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=, tarball: http://registry.m.jd.com/run-async/download/run-async-2.4.1.tgz}
    engines: {node: '>=0.12.0'}

  run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=, tarball: http://registry.m.jd.com/run-parallel/download/run-parallel-1.2.0.tgz}

  rxjs@6.6.7:
    resolution: {integrity: sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=, tarball: http://registry.m.jd.com/rxjs/download/rxjs-6.6.7.tgz}
    engines: {npm: '>=2.0.0'}

  rxjs@7.8.2:
    resolution: {integrity: sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=, tarball: http://registry.m.jd.com/rxjs/download/rxjs-7.8.2.tgz}

  safe-buffer@5.2.1:
    resolution: {integrity: sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=, tarball: http://registry.m.jd.com/safe-buffer/download/safe-buffer-5.2.1.tgz}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=, tarball: http://registry.m.jd.com/safer-buffer/download/safer-buffer-2.1.2.tgz}

  sass-embedded-android-arm64@1.89.0:
    resolution: {integrity: sha1-T932fVJUg9xJqxfUDPKEsHRtcyE=, tarball: http://registry.m.jd.com/sass-embedded-android-arm64/download/sass-embedded-android-arm64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-android-arm@1.89.0:
    resolution: {integrity: sha1-xZrfMBHPdTSNqIAWSri+pK5YAls=, tarball: http://registry.m.jd.com/sass-embedded-android-arm/download/sass-embedded-android-arm-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-android-ia32@1.89.0:
    resolution: {integrity: sha1-FVQ9uxc+pdiZSo35PO9F4RM1f3Y=, tarball: http://registry.m.jd.com/sass-embedded-android-ia32/download/sass-embedded-android-ia32-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-android-riscv64@1.89.0:
    resolution: {integrity: sha1-StX3C8086OV3gLlfA49O6Ji39Xc=, tarball: http://registry.m.jd.com/sass-embedded-android-riscv64/download/sass-embedded-android-riscv64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-android-x64@1.89.0:
    resolution: {integrity: sha1-o2/pnBQmOmgl4lzi/71pJ0NW1nQ=, tarball: http://registry.m.jd.com/sass-embedded-android-x64/download/sass-embedded-android-x64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-darwin-arm64@1.89.0:
    resolution: {integrity: sha1-/ZcFvxzd3xMX5huNa0kp0Ujm/wI=, tarball: http://registry.m.jd.com/sass-embedded-darwin-arm64/download/sass-embedded-darwin-arm64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-darwin-x64@1.89.0:
    resolution: {integrity: sha1-20Ma73OV5oh3gI0LtdOMFdiBikE=, tarball: http://registry.m.jd.com/sass-embedded-darwin-x64/download/sass-embedded-darwin-x64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-arm64@1.89.0:
    resolution: {integrity: sha1-75gy8IVNdhPKwv2LRWpZ7B+wZVQ=, tarball: http://registry.m.jd.com/sass-embedded-linux-arm64/download/sass-embedded-linux-arm64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-arm@1.89.0:
    resolution: {integrity: sha1-okiaBO4/QfPOBl6pLDVYyKtgGwU=, tarball: http://registry.m.jd.com/sass-embedded-linux-arm/download/sass-embedded-linux-arm-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-ia32@1.89.0:
    resolution: {integrity: sha1-Y70829EB3V393zfg1/lEeO9sVAg=, tarball: http://registry.m.jd.com/sass-embedded-linux-ia32/download/sass-embedded-linux-ia32-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-musl-arm64@1.89.0:
    resolution: {integrity: sha1-64GxEM+dwdC7UmiZ0OyyrRiBYgY=, tarball: http://registry.m.jd.com/sass-embedded-linux-musl-arm64/download/sass-embedded-linux-musl-arm64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-musl-arm@1.89.0:
    resolution: {integrity: sha1-p0HMDwqn+fqVlBNMp+fWo40aOnY=, tarball: http://registry.m.jd.com/sass-embedded-linux-musl-arm/download/sass-embedded-linux-musl-arm-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-musl-ia32@1.89.0:
    resolution: {integrity: sha1-65CYmq3iZRcjUoT/Qv/Qi2LyqS4=, tarball: http://registry.m.jd.com/sass-embedded-linux-musl-ia32/download/sass-embedded-linux-musl-ia32-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-musl-riscv64@1.89.0:
    resolution: {integrity: sha1-OtQlzHhCBTdgunLDk8GMg7e+Tsk=, tarball: http://registry.m.jd.com/sass-embedded-linux-musl-riscv64/download/sass-embedded-linux-musl-riscv64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-musl-x64@1.89.0:
    resolution: {integrity: sha1-Jh0wL+KAgFxwtUd1f0Dll81tzhY=, tarball: http://registry.m.jd.com/sass-embedded-linux-musl-x64/download/sass-embedded-linux-musl-x64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-riscv64@1.89.0:
    resolution: {integrity: sha1-cQe5k6UbYPpTY5nghyRVlS6vyc0=, tarball: http://registry.m.jd.com/sass-embedded-linux-riscv64/download/sass-embedded-linux-riscv64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-linux-x64@1.89.0:
    resolution: {integrity: sha1-P2ISyqDyPZG0Qh9lsxZFKb2PPho=, tarball: http://registry.m.jd.com/sass-embedded-linux-x64/download/sass-embedded-linux-x64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-win32-arm64@1.89.0:
    resolution: {integrity: sha1-pmSN1U8975A+2I6axUQFcmWt2/g=, tarball: http://registry.m.jd.com/sass-embedded-win32-arm64/download/sass-embedded-win32-arm64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-win32-ia32@1.89.0:
    resolution: {integrity: sha1-cNBOizNOUslzuZCwyEWmCvEZgxo=, tarball: http://registry.m.jd.com/sass-embedded-win32-ia32/download/sass-embedded-win32-ia32-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded-win32-x64@1.89.0:
    resolution: {integrity: sha1-Q/lLNBVhNUaEkB2EIKwfLN8cG+w=, tarball: http://registry.m.jd.com/sass-embedded-win32-x64/download/sass-embedded-win32-x64-1.89.0.tgz}
    engines: {node: '>=14.0.0'}

  sass-embedded@1.89.0:
    resolution: {integrity: sha1-BCV1+UNk/0t7FC+OK+AD80i2K4Y=, tarball: http://registry.m.jd.com/sass-embedded/download/sass-embedded-1.89.0.tgz}
    engines: {node: '>=16.0.0'}
    hasBin: true

  sass@1.89.1:
    resolution: {integrity: sha1-koHFLIW0vlQmTTEP72OoEd/Pudk=, tarball: http://registry.m.jd.com/sass/download/sass-1.89.1.tgz}
    engines: {node: '>=14.0.0'}
    hasBin: true

  sax@1.4.1:
    resolution: {integrity: sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=, tarball: http://registry.m.jd.com/sax/download/sax-1.4.1.tgz}

  scheduler@0.23.2:
    resolution: {integrity: sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=, tarball: http://registry.m.jd.com/scheduler/download/scheduler-0.23.2.tgz}

  schema-utils@4.3.2:
    resolution: {integrity: sha1-DBCHi/SnP9Kx39FLlGKyZ4jIBq4=, tarball: http://registry.m.jd.com/schema-utils/download/schema-utils-4.3.2.tgz}
    engines: {node: '>= 10.13.0'}

  screenfull@5.2.0:
    resolution: {integrity: sha1-ZTPVJNMGIfwSg7lpIUbz8TqT0bo=, tarball: http://registry.m.jd.com/screenfull/download/screenfull-5.2.0.tgz}
    engines: {node: '>=0.10.0'}

  semver@5.7.2:
    resolution: {integrity: sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=, tarball: http://registry.m.jd.com/semver/download/semver-5.7.2.tgz}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=, tarball: http://registry.m.jd.com/semver/download/semver-6.3.1.tgz}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=, tarball: http://registry.m.jd.com/semver/download/semver-7.7.2.tgz}
    engines: {node: '>=10'}
    hasBin: true

  serialize-javascript@6.0.2:
    resolution: {integrity: sha1-3voeBVyDv21Z6oBdjahiJU62psI=, tarball: http://registry.m.jd.com/serialize-javascript/download/serialize-javascript-6.0.2.tgz}

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=, tarball: http://registry.m.jd.com/shebang-command/download/shebang-command-2.0.0.tgz}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=, tarball: http://registry.m.jd.com/shebang-regex/download/shebang-regex-3.0.0.tgz}
    engines: {node: '>=8'}

  signal-exit@3.0.7:
    resolution: {integrity: sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=, tarball: http://registry.m.jd.com/signal-exit/download/signal-exit-3.0.7.tgz}

  signal-exit@4.1.0:
    resolution: {integrity: sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=, tarball: http://registry.m.jd.com/signal-exit/download/signal-exit-4.1.0.tgz}
    engines: {node: '>=14'}

  signale@1.4.0:
    resolution: {integrity: sha1-xL5YMC+wJirAD8PYhqfBE3WQQvE=, tarball: http://registry.m.jd.com/signale/download/signale-1.4.0.tgz}
    engines: {node: '>=6'}

  silly-datetime@0.1.2:
    resolution: {integrity: sha1-IZeOjo2EgWFgY6wRL/FGk/BuuFs=, tarball: http://registry.m.jd.com/silly-datetime/download/silly-datetime-0.1.2.tgz}

  slash@5.1.0:
    resolution: {integrity: sha1-vjrd3N8JrDjuvo3Nx7GlenWwlc4=, tarball: http://registry.m.jd.com/slash/download/slash-5.1.0.tgz}
    engines: {node: '>=14.16'}

  source-map-js@1.2.1:
    resolution: {integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=, tarball: http://registry.m.jd.com/source-map-js/download/source-map-js-1.2.1.tgz}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.21:
    resolution: {integrity: sha1-BP58f54e0tZiIzwoyys1ufY/bk8=, tarball: http://registry.m.jd.com/source-map-support/download/source-map-support-0.5.21.tgz}

  source-map@0.6.1:
    resolution: {integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=, tarball: http://registry.m.jd.com/source-map/download/source-map-0.6.1.tgz}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY=, tarball: http://registry.m.jd.com/source-map/download/source-map-0.7.4.tgz}
    engines: {node: '>= 8'}

  split-on-first@3.0.0:
    resolution: {integrity: sha1-8ElZyeqBAbmwu/NaYbnr6nhKI+c=, tarball: http://registry.m.jd.com/split-on-first/download/split-on-first-3.0.0.tgz}
    engines: {node: '>=12'}

  split2@4.2.0:
    resolution: {integrity: sha1-ycWSCQTRSLqwufZxRfJFqGqtv6Q=, tarball: http://registry.m.jd.com/split2/download/split2-4.2.0.tgz}
    engines: {node: '>= 10.x'}

  sshpk@1.18.0:
    resolution: {integrity: sha1-FmPlXN301oi4aka3fw1f42OroCg=, tarball: http://registry.m.jd.com/sshpk/download/sshpk-1.18.0.tgz}
    engines: {node: '>=0.10.0'}
    hasBin: true

  stealthy-require@1.1.1:
    resolution: {integrity: sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=, tarball: http://registry.m.jd.com/stealthy-require/download/stealthy-require-1.1.1.tgz}
    engines: {node: '>=0.10.0'}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=, tarball: http://registry.m.jd.com/string-width/download/string-width-4.2.3.tgz}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=, tarball: http://registry.m.jd.com/string-width/download/string-width-5.1.2.tgz}
    engines: {node: '>=12'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=, tarball: http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=, tarball: http://registry.m.jd.com/strip-ansi/download/strip-ansi-7.1.0.tgz}
    engines: {node: '>=12'}

  strip-bom@3.0.0:
    resolution: {integrity: sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=, tarball: http://registry.m.jd.com/strip-bom/download/strip-bom-3.0.0.tgz}
    engines: {node: '>=4'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=, tarball: http://registry.m.jd.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz}
    engines: {node: '>=8'}

  stylus@0.64.0:
    resolution: {integrity: sha1-r5klPxJUyFFSjETt3DzPH4MZQvE=, tarball: http://registry.m.jd.com/stylus/download/stylus-0.64.0.tgz}
    engines: {node: '>=16'}
    hasBin: true

  sugarss@5.0.0:
    resolution: {integrity: sha1-qX3cG1oVmLooOhC41z2lajhI/jY=, tarball: http://registry.m.jd.com/sugarss/download/sugarss-5.0.0.tgz}
    engines: {node: '>=18.0'}
    peerDependencies:
      postcss: ^8.3.3

  supports-color@5.5.0:
    resolution: {integrity: sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=, tarball: http://registry.m.jd.com/supports-color/download/supports-color-5.5.0.tgz}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=, tarball: http://registry.m.jd.com/supports-color/download/supports-color-7.2.0.tgz}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=, tarball: http://registry.m.jd.com/supports-color/download/supports-color-8.1.1.tgz}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=, tarball: http://registry.m.jd.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz}
    engines: {node: '>= 0.4'}

  sync-child-process@1.0.2:
    resolution: {integrity: sha1-RefHLnVtEkPoC1R+ouF5V6ueNn8=, tarball: http://registry.m.jd.com/sync-child-process/download/sync-child-process-1.0.2.tgz}
    engines: {node: '>=16.0.0'}

  sync-message-port@1.1.3:
    resolution: {integrity: sha1-YFXFZe6MgdL57lqufbdX5tkIjAw=, tarball: http://registry.m.jd.com/sync-message-port/download/sync-message-port-1.1.3.tgz}
    engines: {node: '>=16.0.0'}

  synckit@0.11.8:
    resolution: {integrity: sha1-sqqumYpO9H3tYHc60G58uCH1VFc=, tarball: http://registry.m.jd.com/synckit/download/synckit-0.11.8.tgz}
    engines: {node: ^14.18.0 || >=16.0.0}

  systemjs@6.15.1:
    resolution: {integrity: sha1-dBdbaBDiennhF30h218OMFcRjOo=, tarball: http://registry.m.jd.com/systemjs/download/systemjs-6.15.1.tgz}

  tapable@2.2.2:
    resolution: {integrity: sha1-q0mENA0wy5mJpJADLwhtu4tW2HI=, tarball: http://registry.m.jd.com/tapable/download/tapable-2.2.2.tgz}
    engines: {node: '>=6'}

  terser-webpack-plugin@5.3.14:
    resolution: {integrity: sha1-kDHUjlerJ1Z/AqzoXH1pDbZsPgY=, tarball: http://registry.m.jd.com/terser-webpack-plugin/download/terser-webpack-plugin-5.3.14.tgz}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^5.1.0

  terser@5.41.0:
    resolution: {integrity: sha1-EWRruhHv9y1Qb7r70AhvVxcxQeE=, tarball: http://registry.m.jd.com/terser/download/terser-5.41.0.tgz}
    engines: {node: '>=10'}
    hasBin: true

  text-extensions@2.4.0:
    resolution: {integrity: sha1-oc/MUM802kG/0EfMdE+ATRaA6jQ=, tarball: http://registry.m.jd.com/text-extensions/download/text-extensions-2.4.0.tgz}
    engines: {node: '>=8'}

  through@2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=, tarball: http://registry.m.jd.com/through/download/through-2.3.8.tgz}

  tinyexec@1.0.1:
    resolution: {integrity: sha1-cMMat6u7SuoKJPVdEg5ZkL+h4LE=, tarball: http://registry.m.jd.com/tinyexec/download/tinyexec-1.0.1.tgz}

  tinyglobby@0.2.14:
    resolution: {integrity: sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=, tarball: http://registry.m.jd.com/tinyglobby/download/tinyglobby-0.2.14.tgz}
    engines: {node: '>=12.0.0'}

  tmp@0.0.33:
    resolution: {integrity: sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=, tarball: http://registry.m.jd.com/tmp/download/tmp-0.0.33.tgz}
    engines: {node: '>=0.6.0'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=, tarball: http://registry.m.jd.com/to-regex-range/download/to-regex-range-5.0.1.tgz}
    engines: {node: '>=8.0'}

  tough-cookie@2.5.0:
    resolution: {integrity: sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=, tarball: http://registry.m.jd.com/tough-cookie/download/tough-cookie-2.5.0.tgz}
    engines: {node: '>=0.8'}

  ts-api-utils@2.1.0:
    resolution: {integrity: sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=, tarball: http://registry.m.jd.com/ts-api-utils/download/ts-api-utils-2.1.0.tgz}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  tslib@1.14.1:
    resolution: {integrity: sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=, tarball: http://registry.m.jd.com/tslib/download/tslib-1.14.1.tgz}

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=, tarball: http://registry.m.jd.com/tslib/download/tslib-2.8.1.tgz}

  tsx@4.20.1:
    resolution: {integrity: sha1-9e+aXUg5GE5CFgZONlXMmmV2GYY=, tarball: http://registry.m.jd.com/tsx/download/tsx-4.20.1.tgz}
    engines: {node: '>=18.0.0'}
    hasBin: true

  tunnel-agent@0.6.0:
    resolution: {integrity: sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=, tarball: http://registry.m.jd.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz}

  tweetnacl@0.14.5:
    resolution: {integrity: sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=, tarball: http://registry.m.jd.com/tweetnacl/download/tweetnacl-0.14.5.tgz}

  type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=, tarball: http://registry.m.jd.com/type-check/download/type-check-0.4.0.tgz}
    engines: {node: '>= 0.8.0'}

  type-fest@0.21.3:
    resolution: {integrity: sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=, tarball: http://registry.m.jd.com/type-fest/download/type-fest-0.21.3.tgz}
    engines: {node: '>=10'}

  typescript-eslint@8.34.0:
    resolution: {integrity: sha1-W8fkBc0O1dbyjYYBdRlwC3fKEpg=, tarball: http://registry.m.jd.com/typescript-eslint/download/typescript-eslint-8.34.0.tgz}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  typescript@3.9.10:
    resolution: {integrity: sha1-cPORCselHta+952ngAaQsZv3eLg=, tarball: http://registry.m.jd.com/typescript/download/typescript-3.9.10.tgz}
    engines: {node: '>=4.2.0'}
    hasBin: true

  typescript@5.7.3:
    resolution: {integrity: sha1-kZtEp9u4WDqbhW0WK+JKVL+ABz4=, tarball: http://registry.m.jd.com/typescript/download/typescript-5.7.3.tgz}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@6.21.0:
    resolution: {integrity: sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=, tarball: http://registry.m.jd.com/undici-types/download/undici-types-6.21.0.tgz}

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha1-yzFz/kfKdD4ighbko93EyE1ijMI=, tarball: http://registry.m.jd.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=, tarball: http://registry.m.jd.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=, tarball: http://registry.m.jd.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=, tarball: http://registry.m.jd.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz}
    engines: {node: '>=4'}

  unicorn-magic@0.1.0:
    resolution: {integrity: sha1-G7mlHII6r51zqL/NPRoj3elLDOQ=, tarball: http://registry.m.jd.com/unicorn-magic/download/unicorn-magic-0.1.0.tgz}
    engines: {node: '>=18'}

  unicorn-magic@0.3.0:
    resolution: {integrity: sha1-Tv1FyFpp4N1XbSVTL7+iKqXIoQQ=, tarball: http://registry.m.jd.com/unicorn-magic/download/unicorn-magic-0.3.0.tgz}
    engines: {node: '>=18'}

  universalify@0.1.2:
    resolution: {integrity: sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=, tarball: http://registry.m.jd.com/universalify/download/universalify-0.1.2.tgz}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=, tarball: http://registry.m.jd.com/universalify/download/universalify-2.0.1.tgz}
    engines: {node: '>= 10.0.0'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=, tarball: http://registry.m.jd.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=, tarball: http://registry.m.jd.com/uri-js/download/uri-js-4.4.1.tgz}

  util-deprecate@1.0.2:
    resolution: {integrity: sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=, tarball: http://registry.m.jd.com/util-deprecate/download/util-deprecate-1.0.2.tgz}

  uuid@3.4.0:
    resolution: {integrity: sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=, tarball: http://registry.m.jd.com/uuid/download/uuid-3.4.0.tgz}
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true

  varint@6.0.0:
    resolution: {integrity: sha1-mIHrDOj+rqZRJDnRnd+Ev1UWYdA=, tarball: http://registry.m.jd.com/varint/download/varint-6.0.0.tgz}

  verror@1.10.0:
    resolution: {integrity: sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=, tarball: http://registry.m.jd.com/verror/download/verror-1.10.0.tgz}
    engines: {'0': node >=0.6.0}

  vite-plugin-html@3.2.2:
    resolution: {integrity: sha1-Zhg0+gkBXT/aSLppTbqoCTlvX3o=, tarball: http://registry.m.jd.com/vite-plugin-html/download/vite-plugin-html-3.2.2.tgz}
    peerDependencies:
      vite: '>=2.0.0'

  vite-plugin-require-transform@1.0.21:
    resolution: {integrity: sha1-PWvvSmLnaguXi1R7dndDh8L0cFo=, tarball: http://registry.m.jd.com/vite-plugin-require-transform/download/vite-plugin-require-transform-1.0.21.tgz}

  vite@6.3.5:
    resolution: {integrity: sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=, tarball: http://registry.m.jd.com/vite/download/vite-6.3.5.tgz}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  watchpack@2.4.4:
    resolution: {integrity: sha1-RzvacvCFBFPaZCUIHqRvwNdgKUc=, tarball: http://registry.m.jd.com/watchpack/download/watchpack-2.4.4.tgz}
    engines: {node: '>=10.13.0'}

  webpack-sources@3.3.2:
    resolution: {integrity: sha1-CrVasLOAzlPEXKQMt7M7qzFJ6oU=, tarball: http://registry.m.jd.com/webpack-sources/download/webpack-sources-3.3.2.tgz}
    engines: {node: '>=10.13.0'}

  webpack@5.99.9:
    resolution: {integrity: sha1-1955nsF9DM48g7cHRLSu21N9gkc=, tarball: http://registry.m.jd.com/webpack/download/webpack-5.99.9.tgz}
    engines: {node: '>=10.13.0'}
    hasBin: true

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=, tarball: http://registry.m.jd.com/which/download/which-2.0.2.tgz}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=, tarball: http://registry.m.jd.com/word-wrap/download/word-wrap-1.2.5.tgz}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=, tarball: http://registry.m.jd.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=, tarball: http://registry.m.jd.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=, tarball: http://registry.m.jd.com/wrappy/download/wrappy-1.0.2.tgz}

  y18n@5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=, tarball: http://registry.m.jd.com/y18n/download/y18n-5.0.8.tgz}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=, tarball: http://registry.m.jd.com/yallist/download/yallist-3.1.1.tgz}

  yaml@2.8.0:
    resolution: {integrity: sha1-FfjJhmIRvcLTeBoIkORNT6Gl//Y=, tarball: http://registry.m.jd.com/yaml/download/yaml-2.8.0.tgz}
    engines: {node: '>= 14.6'}
    hasBin: true

  yargs-parser@21.1.1:
    resolution: {integrity: sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=, tarball: http://registry.m.jd.com/yargs-parser/download/yargs-parser-21.1.1.tgz}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha1-mR3zmspnWhkrgW4eA2P5110qomk=, tarball: http://registry.m.jd.com/yargs/download/yargs-17.7.2.tgz}
    engines: {node: '>=12'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=, tarball: http://registry.m.jd.com/yocto-queue/download/yocto-queue-0.1.0.tgz}
    engines: {node: '>=10'}

  yocto-queue@1.2.1:
    resolution: {integrity: sha1-NtfEc593Wzy8KOYTbiGqBXrexBg=, tarball: http://registry.m.jd.com/yocto-queue/download/yocto-queue-1.2.1.tgz}
    engines: {node: '>=12.20'}

snapshots:

  '@adobe/css-tools@4.3.3':
    optional: true

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.27.5': {}

  '@babel/core@7.27.4':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helpers': 7.27.6
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.5':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.27.3':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.0
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/traverse': 7.27.4
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      regexpu-core: 6.2.0
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.4(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      debug: 4.4.1
      lodash.debounce: 4.0.8
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-member-expression-to-functions@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.27.1':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-wrap-function': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-member-expression-to-functions': 7.27.1
      '@babel/helper-optimise-call-expression': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helper-wrap-function@7.27.1':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/helpers@7.27.6':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6

  '@babel/parser@7.27.5':
    dependencies:
      '@babel/types': 7.27.6

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
      '@babel/plugin-transform-optional-chaining': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4

  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-async-generator-functions@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1(@babel/core@7.27.4)
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-remap-async-to-generator': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-block-scoping@7.27.5(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-classes@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.27.4)
      '@babel/traverse': 7.27.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/template': 7.27.2

  '@babel/plugin-transform-destructuring@7.27.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.4
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-object-rest-spread@7.27.3(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-transform-destructuring': 7.27.3(@babel/core@7.27.4)
      '@babel/plugin-transform-parameters': 7.27.1(@babel/core@7.27.4)

  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-replace-supers': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-create-class-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-display-name@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/plugin-syntax-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-react-pure-annotations@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-annotate-as-pure': 7.27.3
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-regenerator@7.27.5(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-runtime@7.27.4(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1
      babel-plugin-polyfill-corejs2: 0.4.13(@babel/core@7.27.4)
      babel-plugin-polyfill-corejs3: 0.11.1(@babel/core@7.27.4)
      babel-plugin-polyfill-regenerator: 0.6.4(@babel/core@7.27.4)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-skip-transparent-expression-wrappers': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-create-regexp-features-plugin': 7.27.1(@babel/core@7.27.4)
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/preset-env@7.27.2(@babel/core@7.27.4)':
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/core': 7.27.4
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.4)
      '@babel/plugin-syntax-import-assertions': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-import-attributes': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.27.4)
      '@babel/plugin-transform-arrow-functions': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-async-generator-functions': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-async-to-generator': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-block-scoped-functions': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-block-scoping': 7.27.5(@babel/core@7.27.4)
      '@babel/plugin-transform-class-properties': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-class-static-block': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-classes': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-computed-properties': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-destructuring': 7.27.3(@babel/core@7.27.4)
      '@babel/plugin-transform-dotall-regex': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-duplicate-keys': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-dynamic-import': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-exponentiation-operator': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-export-namespace-from': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-for-of': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-function-name': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-json-strings': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-literals': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-logical-assignment-operators': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-member-expression-literals': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-modules-amd': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-modules-commonjs': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-modules-systemjs': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-modules-umd': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-new-target': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-numeric-separator': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-object-rest-spread': 7.27.3(@babel/core@7.27.4)
      '@babel/plugin-transform-object-super': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-optional-catch-binding': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-optional-chaining': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-parameters': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-private-methods': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-private-property-in-object': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-property-literals': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-regenerator': 7.27.5(@babel/core@7.27.4)
      '@babel/plugin-transform-regexp-modifiers': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-reserved-words': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-shorthand-properties': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-spread': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-sticky-regex': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-template-literals': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-typeof-symbol': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-unicode-escapes': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-unicode-property-regex': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-unicode-regex': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-unicode-sets-regex': 7.27.1(@babel/core@7.27.4)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.27.4)
      babel-plugin-polyfill-corejs2: 0.4.13(@babel/core@7.27.4)
      babel-plugin-polyfill-corejs3: 0.11.1(@babel/core@7.27.4)
      babel-plugin-polyfill-regenerator: 0.6.4(@babel/core@7.27.4)
      core-js-compat: 3.42.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/types': 7.27.6
      esutils: 2.0.3

  '@babel/preset-react@7.27.1(@babel/core@7.27.4)':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-plugin-utils': 7.27.1
      '@babel/helper-validator-option': 7.27.1
      '@babel/plugin-transform-react-display-name': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-jsx': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-jsx-development': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-pure-annotations': 7.27.1(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime@7.27.6': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6

  '@babel/traverse@7.27.4':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/template': 7.27.2
      '@babel/types': 7.27.6
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.6':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@bufbuild/protobuf@2.4.0': {}

  '@commitlint/cli@19.8.1(@types/node@22.15.31)(typescript@5.7.3)':
    dependencies:
      '@commitlint/format': 19.8.1
      '@commitlint/lint': 19.8.1
      '@commitlint/load': 19.8.1(@types/node@22.15.31)(typescript@5.7.3)
      '@commitlint/read': 19.8.1
      '@commitlint/types': 19.8.1
      tinyexec: 1.0.1
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/config-conventional@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      conventional-changelog-conventionalcommits: 7.0.2

  '@commitlint/config-validator@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      ajv: 8.17.1

  '@commitlint/ensure@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      lodash.camelcase: 4.3.0
      lodash.kebabcase: 4.1.1
      lodash.snakecase: 4.1.1
      lodash.startcase: 4.4.0
      lodash.upperfirst: 4.3.1

  '@commitlint/execute-rule@19.8.1': {}

  '@commitlint/format@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      chalk: 5.4.1

  '@commitlint/is-ignored@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      semver: 7.7.2

  '@commitlint/lint@19.8.1':
    dependencies:
      '@commitlint/is-ignored': 19.8.1
      '@commitlint/parse': 19.8.1
      '@commitlint/rules': 19.8.1
      '@commitlint/types': 19.8.1

  '@commitlint/load@19.8.1(@types/node@22.15.31)(typescript@5.7.3)':
    dependencies:
      '@commitlint/config-validator': 19.8.1
      '@commitlint/execute-rule': 19.8.1
      '@commitlint/resolve-extends': 19.8.1
      '@commitlint/types': 19.8.1
      chalk: 5.4.1
      cosmiconfig: 9.0.0(typescript@5.7.3)
      cosmiconfig-typescript-loader: 6.1.0(@types/node@22.15.31)(cosmiconfig@9.0.0(typescript@5.7.3))(typescript@5.7.3)
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      lodash.uniq: 4.5.0
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/message@19.8.1': {}

  '@commitlint/parse@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      conventional-changelog-angular: 7.0.0
      conventional-commits-parser: 5.0.0

  '@commitlint/read@19.8.1':
    dependencies:
      '@commitlint/top-level': 19.8.1
      '@commitlint/types': 19.8.1
      git-raw-commits: 4.0.0
      minimist: 1.2.8
      tinyexec: 1.0.1

  '@commitlint/resolve-extends@19.8.1':
    dependencies:
      '@commitlint/config-validator': 19.8.1
      '@commitlint/types': 19.8.1
      global-directory: 4.0.1
      import-meta-resolve: 4.1.0
      lodash.mergewith: 4.6.2
      resolve-from: 5.0.0

  '@commitlint/rules@19.8.1':
    dependencies:
      '@commitlint/ensure': 19.8.1
      '@commitlint/message': 19.8.1
      '@commitlint/to-lines': 19.8.1
      '@commitlint/types': 19.8.1

  '@commitlint/to-lines@19.8.1': {}

  '@commitlint/top-level@19.8.1':
    dependencies:
      find-up: 7.0.0

  '@commitlint/types@19.8.1':
    dependencies:
      '@types/conventional-commits-parser': 5.0.1
      chalk: 5.4.1

  '@csstools/cascade-layer-name-parser@2.0.5(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4

  '@csstools/color-helpers@5.0.2': {}

  '@csstools/css-calc@2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4

  '@csstools/css-color-parser@3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    dependencies:
      '@csstools/color-helpers': 5.0.2
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4

  '@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4)':
    dependencies:
      '@csstools/css-tokenizer': 3.0.4

  '@csstools/css-tokenizer@3.0.4': {}

  '@csstools/media-query-list-parser@4.0.3(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4

  '@csstools/postcss-cascade-layers@5.0.1(postcss@8.5.4)':
    dependencies:
      '@csstools/selector-specificity': 5.0.0(postcss-selector-parser@7.1.0)
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  '@csstools/postcss-color-function@4.0.10(postcss@8.5.4)':
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-color-mix-function@3.0.10(postcss@8.5.4)':
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-color-mix-variadic-function-arguments@1.0.0(postcss@8.5.4)':
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-content-alt-text@2.0.6(postcss@8.5.4)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-exponential-functions@2.0.9(postcss@8.5.4)':
    dependencies:
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      postcss: 8.5.4

  '@csstools/postcss-font-format-keywords@4.0.0(postcss@8.5.4)':
    dependencies:
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  '@csstools/postcss-gamut-mapping@2.0.10(postcss@8.5.4)':
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      postcss: 8.5.4

  '@csstools/postcss-gradients-interpolation-method@5.0.10(postcss@8.5.4)':
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-hwb-function@4.0.10(postcss@8.5.4)':
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-ic-unit@4.0.2(postcss@8.5.4)':
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  '@csstools/postcss-initial@2.0.1(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4

  '@csstools/postcss-is-pseudo-class@5.0.2(postcss@8.5.4)':
    dependencies:
      '@csstools/selector-specificity': 5.0.0(postcss-selector-parser@7.1.0)
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  '@csstools/postcss-light-dark-function@2.0.9(postcss@8.5.4)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-logical-float-and-clear@3.0.0(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4

  '@csstools/postcss-logical-overflow@2.0.0(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4

  '@csstools/postcss-logical-overscroll-behavior@2.0.0(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4

  '@csstools/postcss-logical-resize@3.0.0(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  '@csstools/postcss-logical-viewport-units@3.0.4(postcss@8.5.4)':
    dependencies:
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-media-minmax@2.0.9(postcss@8.5.4)':
    dependencies:
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/media-query-list-parser': 4.0.3(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      postcss: 8.5.4

  '@csstools/postcss-media-queries-aspect-ratio-number-values@3.0.5(postcss@8.5.4)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/media-query-list-parser': 4.0.3(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      postcss: 8.5.4

  '@csstools/postcss-nested-calc@4.0.0(postcss@8.5.4)':
    dependencies:
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  '@csstools/postcss-normalize-display-values@4.0.0(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  '@csstools/postcss-oklab-function@4.0.10(postcss@8.5.4)':
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-progressive-custom-properties@4.1.0(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  '@csstools/postcss-random-function@2.0.1(postcss@8.5.4)':
    dependencies:
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      postcss: 8.5.4

  '@csstools/postcss-relative-color-syntax@3.0.10(postcss@8.5.4)':
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  '@csstools/postcss-scope-pseudo-class@4.0.1(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  '@csstools/postcss-sign-functions@1.1.4(postcss@8.5.4)':
    dependencies:
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      postcss: 8.5.4

  '@csstools/postcss-stepped-value-functions@4.0.9(postcss@8.5.4)':
    dependencies:
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      postcss: 8.5.4

  '@csstools/postcss-text-decoration-shorthand@4.0.2(postcss@8.5.4)':
    dependencies:
      '@csstools/color-helpers': 5.0.2
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  '@csstools/postcss-trigonometric-functions@4.0.9(postcss@8.5.4)':
    dependencies:
      '@csstools/css-calc': 2.1.4(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      postcss: 8.5.4

  '@csstools/postcss-unset-value@4.0.0(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4

  '@csstools/selector-resolve-nested@3.1.0(postcss-selector-parser@7.1.0)':
    dependencies:
      postcss-selector-parser: 7.1.0

  '@csstools/selector-specificity@5.0.0(postcss-selector-parser@7.1.0)':
    dependencies:
      postcss-selector-parser: 7.1.0

  '@csstools/utilities@2.0.0(postcss@8.5.4)':
    dependencies:
      postcss: 8.5.4

  '@esbuild/aix-ppc64@0.25.5':
    optional: true

  '@esbuild/android-arm64@0.25.5':
    optional: true

  '@esbuild/android-arm@0.25.5':
    optional: true

  '@esbuild/android-x64@0.25.5':
    optional: true

  '@esbuild/darwin-arm64@0.25.5':
    optional: true

  '@esbuild/darwin-x64@0.25.5':
    optional: true

  '@esbuild/freebsd-arm64@0.25.5':
    optional: true

  '@esbuild/freebsd-x64@0.25.5':
    optional: true

  '@esbuild/linux-arm64@0.25.5':
    optional: true

  '@esbuild/linux-arm@0.25.5':
    optional: true

  '@esbuild/linux-ia32@0.25.5':
    optional: true

  '@esbuild/linux-loong64@0.25.5':
    optional: true

  '@esbuild/linux-mips64el@0.25.5':
    optional: true

  '@esbuild/linux-ppc64@0.25.5':
    optional: true

  '@esbuild/linux-riscv64@0.25.5':
    optional: true

  '@esbuild/linux-s390x@0.25.5':
    optional: true

  '@esbuild/linux-x64@0.25.5':
    optional: true

  '@esbuild/netbsd-arm64@0.25.5':
    optional: true

  '@esbuild/netbsd-x64@0.25.5':
    optional: true

  '@esbuild/openbsd-arm64@0.25.5':
    optional: true

  '@esbuild/openbsd-x64@0.25.5':
    optional: true

  '@esbuild/sunos-x64@0.25.5':
    optional: true

  '@esbuild/win32-arm64@0.25.5':
    optional: true

  '@esbuild/win32-ia32@0.25.5':
    optional: true

  '@esbuild/win32-x64@0.25.5':
    optional: true

  '@eslint-community/eslint-utils@4.7.0(eslint@9.28.0(jiti@2.4.2))':
    dependencies:
      eslint: 9.28.0(jiti@2.4.2)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.20.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.2.2': {}

  '@eslint/core@0.14.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.28.0': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.3.1':
    dependencies:
      '@eslint/core': 0.14.0
      levn: 0.4.1

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.3': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0
    optional: true

  '@jd/jmfe-node-jss@1.2.14':
    dependencies:
      '@types/glob': 7.2.0
      '@types/node': 14.18.63
      '@types/request': 2.48.12
      '@types/request-promise': 4.1.51
      bluebird: 3.7.2
      fs-extra: 8.1.0
      glob: 7.2.3
      lodash: 4.17.21
      mime: 1.6.0
      request: 2.88.2
      request-promise: 4.2.6(request@2.88.2)

  '@jd/upload-oss-tools@1.1.23':
    dependencies:
      '@jd/jmfe-node-jss': 1.2.14
      fs-extra: 9.1.0
      inquirer: 7.3.3
      signale: 1.4.0
      silly-datetime: 0.1.2
      typescript: 3.9.10

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@module-federation/error-codes@0.14.3':
    optional: true

  '@module-federation/runtime-core@0.14.3':
    dependencies:
      '@module-federation/error-codes': 0.14.3
      '@module-federation/sdk': 0.14.3
    optional: true

  '@module-federation/runtime-tools@0.14.3':
    dependencies:
      '@module-federation/runtime': 0.14.3
      '@module-federation/webpack-bundler-runtime': 0.14.3
    optional: true

  '@module-federation/runtime@0.14.3':
    dependencies:
      '@module-federation/error-codes': 0.14.3
      '@module-federation/runtime-core': 0.14.3
      '@module-federation/sdk': 0.14.3
    optional: true

  '@module-federation/sdk@0.14.3':
    optional: true

  '@module-federation/webpack-bundler-runtime@0.14.3':
    dependencies:
      '@module-federation/runtime': 0.14.3
      '@module-federation/sdk': 0.14.3
    optional: true

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@parcel/watcher-android-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.1':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.1':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.1':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.1':
    optional: true

  '@parcel/watcher-win32-x64@2.5.1':
    optional: true

  '@parcel/watcher@2.5.1':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1
    optional: true

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@pkgr/core@0.2.7': {}

  '@plato/rsa@0.0.1': {}

  '@rolldown/pluginutils@1.0.0-beta.11': {}

  '@rollup/pluginutils@4.2.1':
    dependencies:
      estree-walker: 2.0.2
      picomatch: 2.3.1

  '@rollup/rollup-android-arm-eabi@4.42.0':
    optional: true

  '@rollup/rollup-android-arm64@4.42.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.42.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.42.0':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.42.0':
    optional: true

  '@rollup/rollup-freebsd-x64@4.42.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.42.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.42.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.42.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.42.0':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.42.0':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.42.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.42.0':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.42.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.42.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.42.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.42.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.42.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.42.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.42.0':
    optional: true

  '@rspack/binding@1.3.15':
    optional: true

  '@rspack/core@1.3.15(@swc/helpers@0.5.17)':
    dependencies:
      '@module-federation/runtime-tools': 0.14.3
      '@rspack/binding': 1.3.15
      '@rspack/lite-tapable': 1.0.1
      '@swc/helpers': 0.5.17
    optional: true

  '@rspack/lite-tapable@1.0.1':
    optional: true

  '@sindresorhus/merge-streams@2.3.0': {}

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1
    optional: true

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.27.6

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.27.5
      '@babel/types': 7.27.6

  '@types/babel__traverse@7.20.7':
    dependencies:
      '@babel/types': 7.27.6

  '@types/bluebird@3.5.42': {}

  '@types/caseless@0.12.5': {}

  '@types/conventional-commits-parser@5.0.1':
    dependencies:
      '@types/node': 22.15.31

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 9.6.1
      '@types/estree': 1.0.8

  '@types/eslint@9.6.1':
    dependencies:
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.7': {}

  '@types/estree@1.0.8': {}

  '@types/glob@7.2.0':
    dependencies:
      '@types/minimatch': 5.1.2
      '@types/node': 22.15.31

  '@types/json-schema@7.0.15': {}

  '@types/md5@2.3.5': {}

  '@types/minimatch@5.1.2': {}

  '@types/node@14.18.63': {}

  '@types/node@22.15.31':
    dependencies:
      undici-types: 6.21.0

  '@types/prop-types@15.7.15': {}

  '@types/react-dom@18.3.7(@types/react@18.3.23)':
    dependencies:
      '@types/react': 18.3.23

  '@types/react@18.3.23':
    dependencies:
      '@types/prop-types': 15.7.15
      csstype: 3.1.3

  '@types/request-promise@4.1.51':
    dependencies:
      '@types/bluebird': 3.5.42
      '@types/request': 2.48.12

  '@types/request@2.48.12':
    dependencies:
      '@types/caseless': 0.12.5
      '@types/node': 22.15.31
      '@types/tough-cookie': 4.0.5
      form-data: 2.5.3

  '@types/tough-cookie@4.0.5': {}

  '@typescript-eslint/eslint-plugin@8.34.0(@typescript-eslint/parser@8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3))(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)
      '@typescript-eslint/scope-manager': 8.34.0
      '@typescript-eslint/type-utils': 8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)
      '@typescript-eslint/utils': 8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)
      '@typescript-eslint/visitor-keys': 8.34.0
      eslint: 9.28.0(jiti@2.4.2)
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.7.3)
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.34.0
      '@typescript-eslint/types': 8.34.0
      '@typescript-eslint/typescript-estree': 8.34.0(typescript@5.7.3)
      '@typescript-eslint/visitor-keys': 8.34.0
      debug: 4.4.1
      eslint: 9.28.0(jiti@2.4.2)
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/project-service@8.34.0(typescript@5.7.3)':
    dependencies:
      '@typescript-eslint/tsconfig-utils': 8.34.0(typescript@5.7.3)
      '@typescript-eslint/types': 8.34.0
      debug: 4.4.1
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.34.0':
    dependencies:
      '@typescript-eslint/types': 8.34.0
      '@typescript-eslint/visitor-keys': 8.34.0

  '@typescript-eslint/tsconfig-utils@8.34.0(typescript@5.7.3)':
    dependencies:
      typescript: 5.7.3

  '@typescript-eslint/type-utils@8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.34.0(typescript@5.7.3)
      '@typescript-eslint/utils': 8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)
      debug: 4.4.1
      eslint: 9.28.0(jiti@2.4.2)
      ts-api-utils: 2.1.0(typescript@5.7.3)
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.34.0': {}

  '@typescript-eslint/typescript-estree@8.34.0(typescript@5.7.3)':
    dependencies:
      '@typescript-eslint/project-service': 8.34.0(typescript@5.7.3)
      '@typescript-eslint/tsconfig-utils': 8.34.0(typescript@5.7.3)
      '@typescript-eslint/types': 8.34.0
      '@typescript-eslint/visitor-keys': 8.34.0
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.7.3)
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.28.0(jiti@2.4.2))
      '@typescript-eslint/scope-manager': 8.34.0
      '@typescript-eslint/types': 8.34.0
      '@typescript-eslint/typescript-estree': 8.34.0(typescript@5.7.3)
      eslint: 9.28.0(jiti@2.4.2)
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.34.0':
    dependencies:
      '@typescript-eslint/types': 8.34.0
      eslint-visitor-keys: 4.2.1

  '@vitejs/plugin-legacy@6.1.1(terser@5.41.0)(vite@6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0))':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/preset-env': 7.27.2(@babel/core@7.27.4)
      browserslist: 4.25.0
      browserslist-to-esbuild: 2.1.1(browserslist@4.25.0)
      core-js: 3.43.0
      magic-string: 0.30.17
      regenerator-runtime: 0.14.1
      systemjs: 6.15.1
      terser: 5.41.0
      vite: 6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0)
    transitivePeerDependencies:
      - supports-color

  '@vitejs/plugin-react@4.5.2(vite@6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0))':
    dependencies:
      '@babel/core': 7.27.4
      '@babel/plugin-transform-react-jsx-self': 7.27.1(@babel/core@7.27.4)
      '@babel/plugin-transform-react-jsx-source': 7.27.1(@babel/core@7.27.4)
      '@rolldown/pluginutils': 1.0.0-beta.11
      '@types/babel__core': 7.20.5
      react-refresh: 0.17.0
      vite: 6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0)
    transitivePeerDependencies:
      - supports-color

  '@webassemblyjs/ast@1.14.1':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2

  '@webassemblyjs/floating-point-hex-parser@1.13.2': {}

  '@webassemblyjs/helper-api-error@1.13.2': {}

  '@webassemblyjs/helper-buffer@1.14.1': {}

  '@webassemblyjs/helper-numbers@1.13.2':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.13.2': {}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1

  '@webassemblyjs/ieee754@1.13.2':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.13.2':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.13.2': {}

  '@webassemblyjs/wasm-edit@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1

  '@webassemblyjs/wasm-gen@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wasm-opt@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1

  '@webassemblyjs/wasm-parser@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wast-printer@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  JSONStream@1.3.5:
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  ahooks@3.8.5(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.27.6
      dayjs: 1.11.13
      intersection-observer: 0.12.2
      js-cookie: 3.0.5
      lodash: 4.17.21
      react: 18.3.1
      react-fast-compare: 3.2.2
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.8.1

  ajv-formats@2.1.1(ajv@8.17.1):
    dependencies:
      ajv: 8.17.1

  ajv-keywords@5.1.0(ajv@8.17.1):
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0:
    optional: true

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1:
    optional: true

  argparse@2.0.1: {}

  array-ify@1.0.0: {}

  asn1@0.2.6:
    dependencies:
      safer-buffer: 2.1.2

  assert-plus@1.0.0: {}

  async@3.2.6: {}

  asynckit@0.4.0: {}

  at-least-node@1.0.0: {}

  autoprefixer@10.4.21(postcss@8.5.4):
    dependencies:
      browserslist: 4.25.0
      caniuse-lite: 1.0.30001721
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  aws-sign2@0.7.0: {}

  aws4@1.13.2: {}

  axios@1.9.0:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.3
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-loader@10.0.0(@babel/core@7.27.4)(webpack@5.99.9):
    dependencies:
      '@babel/core': 7.27.4
      find-up: 5.0.0
      webpack: 5.99.9

  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.27.4):
    dependencies:
      '@babel/compat-data': 7.27.5
      '@babel/core': 7.27.4
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.27.4)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.27.4):
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.27.4)
      core-js-compat: 3.42.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.27.4):
    dependencies:
      '@babel/core': 7.27.4
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.27.4)
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  bcrypt-pbkdf@1.0.2:
    dependencies:
      tweetnacl: 0.14.5

  bluebird@3.7.2: {}

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist-to-esbuild@2.1.1(browserslist@4.25.0):
    dependencies:
      browserslist: 4.25.0
      meow: 13.2.0

  browserslist@4.25.0:
    dependencies:
      caniuse-lite: 1.0.30001721
      electron-to-chromium: 1.5.165
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.0)

  buffer-builder@0.2.0: {}

  buffer-from@1.1.2: {}

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  callsites@3.1.0: {}

  camel-case@4.1.2:
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.8.1

  caniuse-lite@1.0.30001721: {}

  caseless@0.12.0: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  chardet@0.7.0: {}

  charenc@0.0.2: {}

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2
    optional: true

  chrome-trace-event@1.0.4: {}

  classnames@2.5.1: {}

  clean-css@5.3.3:
    dependencies:
      source-map: 0.6.1

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-width@3.0.0: {}

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colorette@2.0.20: {}

  colorjs.io@0.5.2: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  commander@8.3.0: {}

  compare-func@2.0.0:
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0

  concat-map@0.0.1: {}

  connect-history-api-fallback@1.6.0: {}

  consola@2.15.3: {}

  conventional-changelog-angular@7.0.0:
    dependencies:
      compare-func: 2.0.0

  conventional-changelog-conventionalcommits@7.0.2:
    dependencies:
      compare-func: 2.0.0

  conventional-commits-parser@5.0.0:
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 2.0.0
      meow: 12.1.1
      split2: 4.2.0

  convert-source-map@2.0.0: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1
    optional: true

  core-js-compat@3.42.0:
    dependencies:
      browserslist: 4.25.0

  core-js@3.43.0: {}

  core-util-is@1.0.2: {}

  cosmiconfig-typescript-loader@6.1.0(@types/node@22.15.31)(cosmiconfig@9.0.0(typescript@5.7.3))(typescript@5.7.3):
    dependencies:
      '@types/node': 22.15.31
      cosmiconfig: 9.0.0(typescript@5.7.3)
      jiti: 2.4.2
      typescript: 5.7.3

  cosmiconfig@9.0.0(typescript@5.7.3):
    dependencies:
      env-paths: 2.2.1
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
    optionalDependencies:
      typescript: 5.7.3

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypt@0.0.2: {}

  css-blank-pseudo@7.0.1(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  css-has-pseudo@7.0.2(postcss@8.5.4):
    dependencies:
      '@csstools/selector-specificity': 5.0.0(postcss-selector-parser@7.1.0)
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0

  css-prefers-color-scheme@10.0.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4

  css-select@4.3.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  css-what@6.1.0: {}

  cssdb@8.3.0: {}

  cssesc@3.0.0: {}

  csstype@3.1.3: {}

  dargs@8.1.0: {}

  dashdash@1.14.1:
    dependencies:
      assert-plus: 1.0.0

  dayjs@1.11.13: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  decode-uri-component@0.4.1: {}

  deep-is@0.1.4: {}

  del@8.0.0:
    dependencies:
      globby: 14.1.0
      is-glob: 4.0.3
      is-path-cwd: 3.0.0
      is-path-inside: 4.0.0
      p-map: 7.0.3
      slash: 5.1.0

  delayed-stream@1.0.0: {}

  des.js@1.1.0:
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  detect-libc@1.0.3:
    optional: true

  detect-libc@2.0.4:
    optional: true

  dom-serializer@1.4.1:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  domelementtype@2.3.0: {}

  domhandler@4.3.1:
    dependencies:
      domelementtype: 2.3.0

  domutils@2.8.0:
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  dot-prop@5.3.0:
    dependencies:
      is-obj: 2.0.0

  dotenv-expand@8.0.3: {}

  dotenv@16.5.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0:
    optional: true

  ecc-jsbn@0.1.2:
    dependencies:
      jsbn: 0.1.1
      safer-buffer: 2.1.2

  ejs@3.1.10:
    dependencies:
      jake: 10.9.2

  electron-to-chromium@1.5.165: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2:
    optional: true

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  entities@2.2.0: {}

  env-paths@2.2.1: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-module-lexer@1.7.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  esbuild@0.25.5:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.5
      '@esbuild/android-arm': 0.25.5
      '@esbuild/android-arm64': 0.25.5
      '@esbuild/android-x64': 0.25.5
      '@esbuild/darwin-arm64': 0.25.5
      '@esbuild/darwin-x64': 0.25.5
      '@esbuild/freebsd-arm64': 0.25.5
      '@esbuild/freebsd-x64': 0.25.5
      '@esbuild/linux-arm': 0.25.5
      '@esbuild/linux-arm64': 0.25.5
      '@esbuild/linux-ia32': 0.25.5
      '@esbuild/linux-loong64': 0.25.5
      '@esbuild/linux-mips64el': 0.25.5
      '@esbuild/linux-ppc64': 0.25.5
      '@esbuild/linux-riscv64': 0.25.5
      '@esbuild/linux-s390x': 0.25.5
      '@esbuild/linux-x64': 0.25.5
      '@esbuild/netbsd-arm64': 0.25.5
      '@esbuild/netbsd-x64': 0.25.5
      '@esbuild/openbsd-arm64': 0.25.5
      '@esbuild/openbsd-x64': 0.25.5
      '@esbuild/sunos-x64': 0.25.5
      '@esbuild/win32-arm64': 0.25.5
      '@esbuild/win32-ia32': 0.25.5
      '@esbuild/win32-x64': 0.25.5

  escalade@3.2.0: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-prettier@10.1.5(eslint@9.28.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.28.0(jiti@2.4.2)

  eslint-plugin-prettier@5.4.1(@types/eslint@9.6.1)(eslint-config-prettier@10.1.5(eslint@9.28.0(jiti@2.4.2)))(eslint@9.28.0(jiti@2.4.2))(prettier@3.5.3):
    dependencies:
      eslint: 9.28.0(jiti@2.4.2)
      prettier: 3.5.3
      prettier-linter-helpers: 1.0.0
      synckit: 0.11.8
    optionalDependencies:
      '@types/eslint': 9.6.1
      eslint-config-prettier: 10.1.5(eslint@9.28.0(jiti@2.4.2))

  eslint-plugin-react-hooks@5.2.0(eslint@9.28.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.28.0(jiti@2.4.2)

  eslint-plugin-react-refresh@0.4.20(eslint@9.28.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.28.0(jiti@2.4.2)

  eslint-plugin-simple-import-sort@12.1.1(eslint@9.28.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.28.0(jiti@2.4.2)

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@8.4.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.1: {}

  eslint@9.28.0(jiti@2.4.2):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.28.0(jiti@2.4.2))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.20.0
      '@eslint/config-helpers': 0.2.2
      '@eslint/core': 0.14.0
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.28.0
      '@eslint/plugin-kit': 0.3.1
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.4.2
    transitivePeerDependencies:
      - supports-color

  espree@10.4.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 4.2.1

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  esutils@2.0.3: {}

  events@3.3.0: {}

  extend@3.0.2: {}

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  extsprintf@1.3.0: {}

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fdir@6.4.6(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  figures@2.0.0:
    dependencies:
      escape-string-regexp: 1.0.5

  figures@3.2.0:
    dependencies:
      escape-string-regexp: 1.0.5

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  filter-obj@5.1.0: {}

  find-up@2.1.0:
    dependencies:
      locate-path: 2.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  find-up@7.0.0:
    dependencies:
      locate-path: 7.2.0
      path-exists: 5.0.0
      unicorn-magic: 0.1.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flatted@3.3.3: {}

  follow-redirects@1.15.9: {}

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0
    optional: true

  forever-agent@0.6.1: {}

  form-data@2.3.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@2.5.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35
      safe-buffer: 5.2.1

  form-data@4.0.3:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35

  fraction.js@4.3.7: {}

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-extra@8.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2

  fs-extra@9.1.0:
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-tsconfig@4.10.1:
    dependencies:
      resolve-pkg-maps: 1.0.0
    optional: true

  getpass@0.1.7:
    dependencies:
      assert-plus: 1.0.0

  git-raw-commits@4.0.0:
    dependencies:
      dargs: 8.1.0
      meow: 12.1.1
      split2: 4.2.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1
    optional: true

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  global-directory@4.0.1:
    dependencies:
      ini: 4.1.1

  globals@11.12.0: {}

  globals@14.0.0: {}

  globals@15.15.0: {}

  globby@14.1.0:
    dependencies:
      '@sindresorhus/merge-streams': 2.3.0
      fast-glob: 3.3.3
      ignore: 7.0.5
      path-type: 6.0.0
      slash: 5.1.0
      unicorn-magic: 0.3.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  har-schema@2.0.0: {}

  har-validator@5.1.5:
    dependencies:
      ajv: 6.12.6
      har-schema: 2.0.0

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  html-minifier-terser@6.1.0:
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.3
      commander: 8.3.0
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.41.0

  http-signature@1.2.0:
    dependencies:
      assert-plus: 1.0.0
      jsprim: 1.4.2
      sshpk: 1.18.0

  husky@9.1.7: {}

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2
    optional: true

  ieee754@1.2.1: {}

  ignore@5.3.2: {}

  ignore@7.0.5: {}

  image-size@0.5.5:
    optional: true

  immutable@5.1.2: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-meta-resolve@4.1.0: {}

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@4.1.1: {}

  inquirer@7.3.3:
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8

  intersection-observer@0.12.2: {}

  is-arrayish@0.2.1: {}

  is-buffer@1.1.6: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  is-obj@2.0.0: {}

  is-path-cwd@3.0.0: {}

  is-path-inside@4.0.0: {}

  is-text-path@2.0.0:
    dependencies:
      text-extensions: 2.4.0

  is-typedarray@1.0.0: {}

  is-what@3.14.1:
    optional: true

  isexe@2.0.0: {}

  isstream@0.1.2: {}

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0
    optional: true

  jake@10.9.2:
    dependencies:
      async: 3.2.6
      chalk: 4.1.2
      filelist: 1.0.4
      minimatch: 3.1.2

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 22.15.31
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jiti@1.21.7: {}

  jiti@2.4.2: {}

  jotai@2.12.5(@types/react@18.3.23)(react@18.3.1):
    optionalDependencies:
      '@types/react': 18.3.23
      react: 18.3.1

  js-cookie@3.0.5: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsbn@0.1.1: {}

  jsesc@3.0.2: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-schema@0.4.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-stringify-safe@5.0.1: {}

  json5@2.2.3: {}

  jsonfile@4.0.0:
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonp@0.2.1:
    dependencies:
      debug: 2.6.9
    transitivePeerDependencies:
      - supports-color

  jsonparse@1.3.1: {}

  jsprim@1.4.2:
    dependencies:
      assert-plus: 1.0.0
      extsprintf: 1.3.0
      json-schema: 0.4.0
      verror: 1.10.0

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  less@4.3.0:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.8.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1
    optional: true

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lightningcss-darwin-arm64@1.30.1:
    optional: true

  lightningcss-darwin-x64@1.30.1:
    optional: true

  lightningcss-freebsd-x64@1.30.1:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.30.1:
    optional: true

  lightningcss-linux-arm64-gnu@1.30.1:
    optional: true

  lightningcss-linux-arm64-musl@1.30.1:
    optional: true

  lightningcss-linux-x64-gnu@1.30.1:
    optional: true

  lightningcss-linux-x64-musl@1.30.1:
    optional: true

  lightningcss-win32-arm64-msvc@1.30.1:
    optional: true

  lightningcss-win32-x64-msvc@1.30.1:
    optional: true

  lightningcss@1.30.1:
    dependencies:
      detect-libc: 2.0.4
    optionalDependencies:
      lightningcss-darwin-arm64: 1.30.1
      lightningcss-darwin-x64: 1.30.1
      lightningcss-freebsd-x64: 1.30.1
      lightningcss-linux-arm-gnueabihf: 1.30.1
      lightningcss-linux-arm64-gnu: 1.30.1
      lightningcss-linux-arm64-musl: 1.30.1
      lightningcss-linux-x64-gnu: 1.30.1
      lightningcss-linux-x64-musl: 1.30.1
      lightningcss-win32-arm64-msvc: 1.30.1
      lightningcss-win32-x64-msvc: 1.30.1
    optional: true

  lines-and-columns@1.2.4: {}

  load-json-file@4.0.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0

  loader-runner@4.3.0: {}

  locate-path@2.0.0:
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  locate-path@7.2.0:
    dependencies:
      p-locate: 6.0.0

  lodash.camelcase@4.3.0: {}

  lodash.debounce@4.0.8: {}

  lodash.isplainobject@4.0.6: {}

  lodash.kebabcase@4.1.1: {}

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash.snakecase@4.1.1: {}

  lodash.startcase@4.4.0: {}

  lodash.uniq@4.5.0: {}

  lodash.upperfirst@4.3.1: {}

  lodash@4.17.21: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case@2.0.2:
    dependencies:
      tslib: 2.8.1

  lru-cache@10.4.3:
    optional: true

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  math-intrinsics@1.1.0: {}

  md5@2.3.0:
    dependencies:
      charenc: 0.0.2
      crypt: 0.0.2
      is-buffer: 1.1.6

  meow@12.1.1: {}

  meow@13.2.0: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mimic-fn@2.1.0: {}

  minimalistic-assert@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@7.1.2:
    optional: true

  ms@2.0.0: {}

  ms@2.1.3: {}

  mute-stream@0.0.8: {}

  nanoid@3.3.11: {}

  natural-compare@1.4.0: {}

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    optional: true

  neo-async@2.6.2: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1

  node-addon-api@7.1.1:
    optional: true

  node-html-parser@5.4.2:
    dependencies:
      css-select: 4.3.0
      he: 1.2.0

  node-releases@2.0.19: {}

  normalize-range@0.1.2: {}

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  oauth-sign@0.9.0: {}

  object-assign@4.1.1: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  os-tmpdir@1.0.2: {}

  p-limit@1.3.0:
    dependencies:
      p-try: 1.0.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-limit@4.0.0:
    dependencies:
      yocto-queue: 1.2.1

  p-locate@2.0.0:
    dependencies:
      p-limit: 1.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-locate@6.0.0:
    dependencies:
      p-limit: 4.0.0

  p-map@7.0.3: {}

  p-try@1.0.0: {}

  package-json-from-dist@1.0.1:
    optional: true

  param-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-node-version@1.0.1:
    optional: true

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  path-exists@3.0.0: {}

  path-exists@4.0.0: {}

  path-exists@5.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2
    optional: true

  path-type@6.0.0: {}

  pathe@0.2.0: {}

  performance-now@2.1.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pify@3.0.0: {}

  pify@4.0.1:
    optional: true

  pkg-conf@2.1.0:
    dependencies:
      find-up: 2.1.0
      load-json-file: 4.0.0

  postcss-attribute-case-insensitive@7.0.1(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  postcss-clamp@4.1.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-color-functional-notation@7.0.10(postcss@8.5.4):
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  postcss-color-hex-alpha@10.0.0(postcss@8.5.4):
    dependencies:
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-color-rebeccapurple@10.0.0(postcss@8.5.4):
    dependencies:
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-custom-media@11.0.6(postcss@8.5.4):
    dependencies:
      '@csstools/cascade-layer-name-parser': 2.0.5(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/media-query-list-parser': 4.0.3(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      postcss: 8.5.4

  postcss-custom-properties@14.0.6(postcss@8.5.4):
    dependencies:
      '@csstools/cascade-layer-name-parser': 2.0.5(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-custom-selectors@8.0.5(postcss@8.5.4):
    dependencies:
      '@csstools/cascade-layer-name-parser': 2.0.5(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  postcss-dir-pseudo-class@9.0.1(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  postcss-double-position-gradients@6.0.2(postcss@8.5.4):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-focus-visible@10.0.1(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  postcss-focus-within@9.0.1(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  postcss-font-variant@5.0.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4

  postcss-gap-properties@6.0.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4

  postcss-image-set-function@7.0.0(postcss@8.5.4):
    dependencies:
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-lab-function@7.0.10(postcss@8.5.4):
    dependencies:
      '@csstools/css-color-parser': 3.0.10(@csstools/css-parser-algorithms@3.0.5(@csstools/css-tokenizer@3.0.4))(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-parser-algorithms': 3.0.5(@csstools/css-tokenizer@3.0.4)
      '@csstools/css-tokenizer': 3.0.4
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/utilities': 2.0.0(postcss@8.5.4)
      postcss: 8.5.4

  postcss-loader@8.1.1(@rspack/core@1.3.15(@swc/helpers@0.5.17))(postcss@8.5.4)(typescript@5.7.3)(webpack@5.99.9):
    dependencies:
      cosmiconfig: 9.0.0(typescript@5.7.3)
      jiti: 1.21.7
      postcss: 8.5.4
      semver: 7.7.2
    optionalDependencies:
      '@rspack/core': 1.3.15(@swc/helpers@0.5.17)
      webpack: 5.99.9
    transitivePeerDependencies:
      - typescript

  postcss-logical@8.1.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-nesting@13.0.2(postcss@8.5.4):
    dependencies:
      '@csstools/selector-resolve-nested': 3.1.0(postcss-selector-parser@7.1.0)
      '@csstools/selector-specificity': 5.0.0(postcss-selector-parser@7.1.0)
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  postcss-opacity-percentage@3.0.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4

  postcss-overflow-shorthand@6.0.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-page-break@3.0.4(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4

  postcss-place@10.0.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-value-parser: 4.2.0

  postcss-preset-env@10.2.2(postcss@8.5.4):
    dependencies:
      '@csstools/postcss-cascade-layers': 5.0.1(postcss@8.5.4)
      '@csstools/postcss-color-function': 4.0.10(postcss@8.5.4)
      '@csstools/postcss-color-mix-function': 3.0.10(postcss@8.5.4)
      '@csstools/postcss-color-mix-variadic-function-arguments': 1.0.0(postcss@8.5.4)
      '@csstools/postcss-content-alt-text': 2.0.6(postcss@8.5.4)
      '@csstools/postcss-exponential-functions': 2.0.9(postcss@8.5.4)
      '@csstools/postcss-font-format-keywords': 4.0.0(postcss@8.5.4)
      '@csstools/postcss-gamut-mapping': 2.0.10(postcss@8.5.4)
      '@csstools/postcss-gradients-interpolation-method': 5.0.10(postcss@8.5.4)
      '@csstools/postcss-hwb-function': 4.0.10(postcss@8.5.4)
      '@csstools/postcss-ic-unit': 4.0.2(postcss@8.5.4)
      '@csstools/postcss-initial': 2.0.1(postcss@8.5.4)
      '@csstools/postcss-is-pseudo-class': 5.0.2(postcss@8.5.4)
      '@csstools/postcss-light-dark-function': 2.0.9(postcss@8.5.4)
      '@csstools/postcss-logical-float-and-clear': 3.0.0(postcss@8.5.4)
      '@csstools/postcss-logical-overflow': 2.0.0(postcss@8.5.4)
      '@csstools/postcss-logical-overscroll-behavior': 2.0.0(postcss@8.5.4)
      '@csstools/postcss-logical-resize': 3.0.0(postcss@8.5.4)
      '@csstools/postcss-logical-viewport-units': 3.0.4(postcss@8.5.4)
      '@csstools/postcss-media-minmax': 2.0.9(postcss@8.5.4)
      '@csstools/postcss-media-queries-aspect-ratio-number-values': 3.0.5(postcss@8.5.4)
      '@csstools/postcss-nested-calc': 4.0.0(postcss@8.5.4)
      '@csstools/postcss-normalize-display-values': 4.0.0(postcss@8.5.4)
      '@csstools/postcss-oklab-function': 4.0.10(postcss@8.5.4)
      '@csstools/postcss-progressive-custom-properties': 4.1.0(postcss@8.5.4)
      '@csstools/postcss-random-function': 2.0.1(postcss@8.5.4)
      '@csstools/postcss-relative-color-syntax': 3.0.10(postcss@8.5.4)
      '@csstools/postcss-scope-pseudo-class': 4.0.1(postcss@8.5.4)
      '@csstools/postcss-sign-functions': 1.1.4(postcss@8.5.4)
      '@csstools/postcss-stepped-value-functions': 4.0.9(postcss@8.5.4)
      '@csstools/postcss-text-decoration-shorthand': 4.0.2(postcss@8.5.4)
      '@csstools/postcss-trigonometric-functions': 4.0.9(postcss@8.5.4)
      '@csstools/postcss-unset-value': 4.0.0(postcss@8.5.4)
      autoprefixer: 10.4.21(postcss@8.5.4)
      browserslist: 4.25.0
      css-blank-pseudo: 7.0.1(postcss@8.5.4)
      css-has-pseudo: 7.0.2(postcss@8.5.4)
      css-prefers-color-scheme: 10.0.0(postcss@8.5.4)
      cssdb: 8.3.0
      postcss: 8.5.4
      postcss-attribute-case-insensitive: 7.0.1(postcss@8.5.4)
      postcss-clamp: 4.1.0(postcss@8.5.4)
      postcss-color-functional-notation: 7.0.10(postcss@8.5.4)
      postcss-color-hex-alpha: 10.0.0(postcss@8.5.4)
      postcss-color-rebeccapurple: 10.0.0(postcss@8.5.4)
      postcss-custom-media: 11.0.6(postcss@8.5.4)
      postcss-custom-properties: 14.0.6(postcss@8.5.4)
      postcss-custom-selectors: 8.0.5(postcss@8.5.4)
      postcss-dir-pseudo-class: 9.0.1(postcss@8.5.4)
      postcss-double-position-gradients: 6.0.2(postcss@8.5.4)
      postcss-focus-visible: 10.0.1(postcss@8.5.4)
      postcss-focus-within: 9.0.1(postcss@8.5.4)
      postcss-font-variant: 5.0.0(postcss@8.5.4)
      postcss-gap-properties: 6.0.0(postcss@8.5.4)
      postcss-image-set-function: 7.0.0(postcss@8.5.4)
      postcss-lab-function: 7.0.10(postcss@8.5.4)
      postcss-logical: 8.1.0(postcss@8.5.4)
      postcss-nesting: 13.0.2(postcss@8.5.4)
      postcss-opacity-percentage: 3.0.0(postcss@8.5.4)
      postcss-overflow-shorthand: 6.0.0(postcss@8.5.4)
      postcss-page-break: 3.0.4(postcss@8.5.4)
      postcss-place: 10.0.0(postcss@8.5.4)
      postcss-pseudo-class-any-link: 10.0.1(postcss@8.5.4)
      postcss-replace-overflow-wrap: 4.0.0(postcss@8.5.4)
      postcss-selector-not: 8.0.1(postcss@8.5.4)

  postcss-pseudo-class-any-link@10.0.1(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  postcss-replace-overflow-wrap@4.0.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4

  postcss-selector-not@8.0.1(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
      postcss-selector-parser: 7.1.0

  postcss-selector-parser@7.1.0:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.5.4:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@3.5.3: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-from-env@1.1.0: {}

  prr@1.0.1:
    optional: true

  psl@1.15.0:
    dependencies:
      punycode: 2.3.1

  punycode@2.3.1: {}

  qs@6.5.3: {}

  query-string@9.2.0:
    dependencies:
      decode-uri-component: 0.4.1
      filter-obj: 5.1.0
      split-on-first: 3.0.0

  queue-microtask@1.2.3: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-error-boundary@5.0.0(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.27.6
      react: 18.3.1

  react-fast-compare@3.2.2: {}

  react-is@16.13.1: {}

  react-refresh@0.17.0: {}

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  readdirp@4.1.2:
    optional: true

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.14.1: {}

  regexpu-core@6.2.0:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.8.0: {}

  regjsparser@0.12.0:
    dependencies:
      jsesc: 3.0.2

  relateurl@0.2.7: {}

  request-promise-core@1.1.4(request@2.88.2):
    dependencies:
      lodash: 4.17.21
      request: 2.88.2

  request-promise@4.2.6(request@2.88.2):
    dependencies:
      bluebird: 3.7.2
      request: 2.88.2
      request-promise-core: 1.1.4(request@2.88.2)
      stealthy-require: 1.1.1
      tough-cookie: 2.5.0

  request@2.88.2:
    dependencies:
      aws-sign2: 0.7.0
      aws4: 1.13.2
      caseless: 0.12.0
      combined-stream: 1.0.8
      extend: 3.0.2
      forever-agent: 0.6.1
      form-data: 2.3.3
      har-validator: 5.1.5
      http-signature: 1.2.0
      is-typedarray: 1.0.0
      isstream: 0.1.2
      json-stringify-safe: 5.0.1
      mime-types: 2.1.35
      oauth-sign: 0.9.0
      performance-now: 2.1.0
      qs: 6.5.3
      safe-buffer: 5.2.1
      tough-cookie: 2.5.0
      tunnel-agent: 0.6.0
      uuid: 3.4.0

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  resize-observer-polyfill@1.5.1: {}

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-pkg-maps@1.0.0:
    optional: true

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  reusify@1.1.0: {}

  rollup-plugin-delete@3.0.1(rollup@4.42.0):
    dependencies:
      del: 8.0.0
      rollup: 4.42.0

  rollup@4.42.0:
    dependencies:
      '@types/estree': 1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.42.0
      '@rollup/rollup-android-arm64': 4.42.0
      '@rollup/rollup-darwin-arm64': 4.42.0
      '@rollup/rollup-darwin-x64': 4.42.0
      '@rollup/rollup-freebsd-arm64': 4.42.0
      '@rollup/rollup-freebsd-x64': 4.42.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.42.0
      '@rollup/rollup-linux-arm-musleabihf': 4.42.0
      '@rollup/rollup-linux-arm64-gnu': 4.42.0
      '@rollup/rollup-linux-arm64-musl': 4.42.0
      '@rollup/rollup-linux-loongarch64-gnu': 4.42.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.42.0
      '@rollup/rollup-linux-riscv64-gnu': 4.42.0
      '@rollup/rollup-linux-riscv64-musl': 4.42.0
      '@rollup/rollup-linux-s390x-gnu': 4.42.0
      '@rollup/rollup-linux-x64-gnu': 4.42.0
      '@rollup/rollup-linux-x64-musl': 4.42.0
      '@rollup/rollup-win32-arm64-msvc': 4.42.0
      '@rollup/rollup-win32-ia32-msvc': 4.42.0
      '@rollup/rollup-win32-x64-msvc': 4.42.0
      fsevents: 2.3.3

  run-async@2.4.1: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@6.6.7:
    dependencies:
      tslib: 1.14.1

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  sass-embedded-android-arm64@1.89.0:
    optional: true

  sass-embedded-android-arm@1.89.0:
    optional: true

  sass-embedded-android-ia32@1.89.0:
    optional: true

  sass-embedded-android-riscv64@1.89.0:
    optional: true

  sass-embedded-android-x64@1.89.0:
    optional: true

  sass-embedded-darwin-arm64@1.89.0:
    optional: true

  sass-embedded-darwin-x64@1.89.0:
    optional: true

  sass-embedded-linux-arm64@1.89.0:
    optional: true

  sass-embedded-linux-arm@1.89.0:
    optional: true

  sass-embedded-linux-ia32@1.89.0:
    optional: true

  sass-embedded-linux-musl-arm64@1.89.0:
    optional: true

  sass-embedded-linux-musl-arm@1.89.0:
    optional: true

  sass-embedded-linux-musl-ia32@1.89.0:
    optional: true

  sass-embedded-linux-musl-riscv64@1.89.0:
    optional: true

  sass-embedded-linux-musl-x64@1.89.0:
    optional: true

  sass-embedded-linux-riscv64@1.89.0:
    optional: true

  sass-embedded-linux-x64@1.89.0:
    optional: true

  sass-embedded-win32-arm64@1.89.0:
    optional: true

  sass-embedded-win32-ia32@1.89.0:
    optional: true

  sass-embedded-win32-x64@1.89.0:
    optional: true

  sass-embedded@1.89.0:
    dependencies:
      '@bufbuild/protobuf': 2.4.0
      buffer-builder: 0.2.0
      colorjs.io: 0.5.2
      immutable: 5.1.2
      rxjs: 7.8.2
      supports-color: 8.1.1
      sync-child-process: 1.0.2
      varint: 6.0.0
    optionalDependencies:
      sass-embedded-android-arm: 1.89.0
      sass-embedded-android-arm64: 1.89.0
      sass-embedded-android-ia32: 1.89.0
      sass-embedded-android-riscv64: 1.89.0
      sass-embedded-android-x64: 1.89.0
      sass-embedded-darwin-arm64: 1.89.0
      sass-embedded-darwin-x64: 1.89.0
      sass-embedded-linux-arm: 1.89.0
      sass-embedded-linux-arm64: 1.89.0
      sass-embedded-linux-ia32: 1.89.0
      sass-embedded-linux-musl-arm: 1.89.0
      sass-embedded-linux-musl-arm64: 1.89.0
      sass-embedded-linux-musl-ia32: 1.89.0
      sass-embedded-linux-musl-riscv64: 1.89.0
      sass-embedded-linux-musl-x64: 1.89.0
      sass-embedded-linux-riscv64: 1.89.0
      sass-embedded-linux-x64: 1.89.0
      sass-embedded-win32-arm64: 1.89.0
      sass-embedded-win32-ia32: 1.89.0
      sass-embedded-win32-x64: 1.89.0

  sass@1.89.1:
    dependencies:
      chokidar: 4.0.3
      immutable: 5.1.2
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.1
    optional: true

  sax@1.4.1:
    optional: true

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  schema-utils@4.3.2:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)

  screenfull@5.2.0: {}

  semver@5.7.2:
    optional: true

  semver@6.3.1: {}

  semver@7.7.2: {}

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  signal-exit@3.0.7: {}

  signal-exit@4.1.0:
    optional: true

  signale@1.4.0:
    dependencies:
      chalk: 2.4.2
      figures: 2.0.0
      pkg-conf: 2.1.0

  silly-datetime@0.1.2: {}

  slash@5.1.0: {}

  source-map-js@1.2.1: {}

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.7.4:
    optional: true

  split-on-first@3.0.0: {}

  split2@4.2.0: {}

  sshpk@1.18.0:
    dependencies:
      asn1: 0.2.6
      assert-plus: 1.0.0
      bcrypt-pbkdf: 1.0.2
      dashdash: 1.14.1
      ecc-jsbn: 0.1.2
      getpass: 0.1.7
      jsbn: 0.1.1
      safer-buffer: 2.1.2
      tweetnacl: 0.14.5

  stealthy-require@1.1.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0
    optional: true

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0
    optional: true

  strip-bom@3.0.0: {}

  strip-json-comments@3.1.1: {}

  stylus@0.64.0:
    dependencies:
      '@adobe/css-tools': 4.3.3
      debug: 4.4.1
      glob: 10.4.5
      sax: 1.4.1
      source-map: 0.7.4
    transitivePeerDependencies:
      - supports-color
    optional: true

  sugarss@5.0.0(postcss@8.5.4):
    dependencies:
      postcss: 8.5.4
    optional: true

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  sync-child-process@1.0.2:
    dependencies:
      sync-message-port: 1.1.3

  sync-message-port@1.1.3: {}

  synckit@0.11.8:
    dependencies:
      '@pkgr/core': 0.2.7

  systemjs@6.15.1: {}

  tapable@2.2.2: {}

  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      jest-worker: 27.5.1
      schema-utils: 4.3.2
      serialize-javascript: 6.0.2
      terser: 5.41.0
      webpack: 5.99.9

  terser@5.41.0:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.15.0
      commander: 2.20.3
      source-map-support: 0.5.21

  text-extensions@2.4.0: {}

  through@2.3.8: {}

  tinyexec@1.0.1: {}

  tinyglobby@0.2.14:
    dependencies:
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  tough-cookie@2.5.0:
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1

  ts-api-utils@2.1.0(typescript@5.7.3):
    dependencies:
      typescript: 5.7.3

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsx@4.20.1:
    dependencies:
      esbuild: 0.25.5
      get-tsconfig: 4.10.1
    optionalDependencies:
      fsevents: 2.3.3
    optional: true

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  tweetnacl@0.14.5: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.21.3: {}

  typescript-eslint@8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.34.0(@typescript-eslint/parser@8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3))(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)
      '@typescript-eslint/parser': 8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)
      '@typescript-eslint/utils': 8.34.0(eslint@9.28.0(jiti@2.4.2))(typescript@5.7.3)
      eslint: 9.28.0(jiti@2.4.2)
      typescript: 5.7.3
    transitivePeerDependencies:
      - supports-color

  typescript@3.9.10: {}

  typescript@5.7.3: {}

  undici-types@6.21.0: {}

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  unicorn-magic@0.1.0: {}

  unicorn-magic@0.3.0: {}

  universalify@0.1.2: {}

  universalify@2.0.1: {}

  update-browserslist-db@1.1.3(browserslist@4.25.0):
    dependencies:
      browserslist: 4.25.0
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  util-deprecate@1.0.2: {}

  uuid@3.4.0: {}

  varint@6.0.0: {}

  verror@1.10.0:
    dependencies:
      assert-plus: 1.0.0
      core-util-is: 1.0.2
      extsprintf: 1.3.0

  vite-plugin-html@3.2.2(vite@6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0)):
    dependencies:
      '@rollup/pluginutils': 4.2.1
      colorette: 2.0.20
      connect-history-api-fallback: 1.6.0
      consola: 2.15.3
      dotenv: 16.5.0
      dotenv-expand: 8.0.3
      ejs: 3.1.10
      fast-glob: 3.3.3
      fs-extra: 10.1.0
      html-minifier-terser: 6.1.0
      node-html-parser: 5.4.2
      pathe: 0.2.0
      vite: 6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0)

  vite-plugin-require-transform@1.0.21:
    dependencies:
      '@babel/generator': 7.27.5
      '@babel/parser': 7.27.5
      '@babel/traverse': 7.27.4
      '@babel/types': 7.27.6
    transitivePeerDependencies:
      - supports-color

  vite@6.3.5(@types/node@22.15.31)(jiti@2.4.2)(less@4.3.0)(lightningcss@1.30.1)(sass-embedded@1.89.0)(sass@1.89.1)(stylus@0.64.0)(sugarss@5.0.0(postcss@8.5.4))(terser@5.41.0)(tsx@4.20.1)(yaml@2.8.0):
    dependencies:
      esbuild: 0.25.5
      fdir: 6.4.6(picomatch@4.0.2)
      picomatch: 4.0.2
      postcss: 8.5.4
      rollup: 4.42.0
      tinyglobby: 0.2.14
    optionalDependencies:
      '@types/node': 22.15.31
      fsevents: 2.3.3
      jiti: 2.4.2
      less: 4.3.0
      lightningcss: 1.30.1
      sass: 1.89.1
      sass-embedded: 1.89.0
      stylus: 0.64.0
      sugarss: 5.0.0(postcss@8.5.4)
      terser: 5.41.0
      tsx: 4.20.1
      yaml: 2.8.0

  watchpack@2.4.4:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  webpack-sources@3.3.2: {}

  webpack@5.99.9:
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.15.0
      browserslist: 4.25.0
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.1
      es-module-lexer: 1.7.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.2
      tapable: 2.2.2
      terser-webpack-plugin: 5.3.14(webpack@5.99.9)
      watchpack: 2.4.4
      webpack-sources: 3.3.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0
    optional: true

  wrappy@1.0.2: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yaml@2.8.0:
    optional: true

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  yocto-queue@1.2.1: {}
