/**
 * getUrlFilename 提取URL中的文件名
 * @param {string} url
 * @param {boolean} [includeExt=false]
 * @returns {string}
 */
export function getUrlFilename(url: string, includeExt = false): string {
  if (!url || typeof url !== 'string') {
    return ''
  }

  let path = ''
  let inQuery = false
  let inHash = false
  let lastSlashIndex = -1

  for (let i = 0; i < url.length; i++) {
    const char = url[i]
    if (char === '?') {
      inQuery = true
    } else if (char === '#') {
      inHash = true
    }

    if (!inQuery && !inHash) {
      path += char
      if (char === '/') {
        lastSlashIndex = path.length - 1
      }
    } else {
      break
    }
  }

  // 如果路径以斜杠结尾，返回空字符串
  if (lastSlashIndex === path.length - 1) {
    return ''
  }

  let filename
  if (lastSlashIndex === -1) {
    filename = path // 如果没有斜杠，整个路径就是文件名
  } else {
    filename = path.substring(lastSlashIndex + 1)
  }

  if (!includeExt) {
    const dotIndex = filename.indexOf('.')
    if (dotIndex !== -1) {
      return filename.substring(0, dotIndex)
    }
  }

  return filename
}
