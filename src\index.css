:root {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 默认样式 */
#root {
  display: flex;
  /* justify-content: center; */
  width: 100%;
}

.layout {
  width: 1280px;
  margin: 0 auto;
  padding-bottom: 24px;
}

.main {
  position: relative;
  z-index: 1;
  width: 828px;
  float: left;
}

.main>div {
  margin-bottom: 16px;
}

.main>div:last-child {
  margin-bottom: 0;
}

.aside {
  width: 436px;
  padding-left: 844px;
}

#shortcut-2024 .w {
  width: 1280px;
}

.icon-info {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-image: url('//img30.360buyimg.com/ling/jfs/t1/281753/14/10244/773/67e4c142F97ddafa0/ba8fa566f48a15c4.png');
  background-size: 12px 12px;
  cursor: pointer;
}

/* 窗口宽度在 1660px 到无穷大之间 */
@media (min-width: 1660px) {
  .layout {
    width: 1552px;
  }

  .main {
    width: 1100px;
    float: left;
  }

  .aside {
    position: sticky;
    top: 10px;
    width: 436px;
    padding-left: 1116px;
  }

  .payment-action-toggle {
    display: none;
  }

  .payment-summary-inner {
    max-height: calc(100vh - 300px - var(--payment-state-subsidy-height, 0px));
  }
  
  .aside.is-sticky .payment-summary-inner {
    max-height: calc(100vh - 200px - var(--payment-state-subsidy-height, 0px));
  }

  #shortcut-2024 .w {
    width: 1552px;
  }
  #footer-2024 .mod_footer {
    min-width: 1230px;
  }
}

/* 窗口宽度在 1440px 到 1660px 之间 */
@media (max-width: 1659px) and (min-width: 1440px) {
  .layout {
    width: 1280px;
  }

  .main {
    width: 828px;
    float: left;
  }

  .aside {
    position: sticky;
    top: 10px;
    width: 436px;
    padding-left: 844px;
  }

  .payment-action-toggle {
    display: none;
  }

  .payment-summary-inner {
    max-height: calc(100vh - 300px - var(--payment-state-subsidy-height, 0px));
  }
  
  .aside.is-sticky .payment-summary-inner {
    max-height: calc(100vh - 200px - var(--payment-state-subsidy-height, 0px));
  }
}

/* 窗口宽度在 1240px 到 1440px 之间 */
@media (max-width: 1439px) and (min-width: 1240px) {
  .layout {
    width: 1100px;
  }

  .main {
    width: 1100px;
  }

  .aside {
    z-index: 30;
    position: fixed;
    bottom: 24px;
    right: calc((100% - 1100px) / 2);
    padding-left: 0;
  }
  
  .payment {
    box-shadow: 0 12px 24px rgba(0, 0, 0, .1);
  }

  .payment-summary {
    display: none;
  }

  .payment-summary-inner {
    max-height: calc(100vh - 300px);
  }
}

/* 窗口宽度小于 1240px */
@media (max-width: 1239px) {
  .layout {
    width: 828px;
  }

  .main {
    width: 828px;
  }

  .aside {
    z-index: 30;
    position: fixed;
    bottom: 24px;
    right: calc((100% - 828px) / 2);
    padding-left: 0;
  }
  
  .payment {
    box-shadow: 0 12px 24px rgba(0, 0, 0, .1);
  }

  .payment-summary {
    display: none;
  }

  .payment-summary-inner {
    max-height: calc(100vh - 300px);
  }
}

/* 窄屏触底时“付款详情”模块楼层样式设置 */
.main.nstb {
  float: none !important;
}

.aside.nstb {
  position: static;
  width: 100%;
  padding-left: 0;
  margin-top: 16px;
}

.aside.nstb .payment {
  box-shadow: none;
}

.aside.nstb .payment .payment-summary-inner {
  max-height: none;
}

.aside.nstb .payment .payment__bg {
  display: none;
}

.aside.nstb .payment .payment-summary {
  display: block;
}

.aside.nstb .payment .payment-action-toggle {
  display: none;
}
