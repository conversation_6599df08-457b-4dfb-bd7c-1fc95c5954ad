import * as React from 'react'
import showMsg from './showMsg'

interface TypeProps {
  type: 'success' | 'warn' | 'wrong' | 'info'
}

const showType = (title?: React.ReactNode, description?: React.ReactNode, duration?: number, type?: TypeProps['type']) => {
  showMsg({
    type,
    title,
    description,
    duration,
    showIcon: true,
    autohide: true,
  })
}
const info = (title?: React.ReactNode, description?: React.ReactNode, duration?: number) => showType(title, description, duration, 'info')
const success = (title?: React.ReactNode, description?: React.ReactNode, duration?: number) =>
  showType(title, description, duration, 'success')
const warn = (title?: React.ReactNode, description?: React.ReactNode, duration?: number) => showType(title, description, duration, 'warn')
const wrong = (title?: React.ReactNode, description?: React.ReactNode, duration?: number) => showType(title, description, duration, 'wrong')

export { info, success, warn, wrong }
