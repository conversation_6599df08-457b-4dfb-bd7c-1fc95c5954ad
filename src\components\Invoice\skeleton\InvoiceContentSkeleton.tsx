import classNames from 'classnames'
import './InvoiceContentSkeleton.scss' // 导入样式文件
import ReactDOM from 'react-dom'

/**
 * 发票弹窗骨架屏组件
 */
interface InvoiceSkeletonProps {
  onClose?: () => void
  width?: number
  height?: number
}

function InvoiceContentSkeleton({ onClose, width, height }: InvoiceSkeletonProps) {
  const viewportHeight = window.innerHeight
  const viewportWidth = window.innerWidth

  return ReactDOM.createPortal(
    <div className="invoice-dialog-wrapper">
      {/* 模拟蒙层 */}
      <div className="invoice-dialog-mask" />

      {/* 模拟弹窗主体 */}
      <div
        className={classNames('invoice-dialog', 'invoice-skeleton')}
        style={{
          width: (width || 724) + 'px',
          height: (height || 808) + 'px',
          marginLeft: (viewportWidth - (width || 724)) / 2 + 'px',
          marginTop: (viewportHeight - (height || 808)) / 2 + 'px',
        }}
      >
        {/* 模拟头部 */}
        <div className={classNames('invoice-dialog-head', 'invoice-skeleton-header', 'is-flex', 'justify-between', 'items-center')}>
          <div className="invoice-skeleton-title jd-skeleton"></div>
          {/* 真实的关闭按钮 */}
          {onClose && <button className="invoice-dialog-close" onClick={onClose} />}
        </div>
        {/* 模拟主体内容 */}
        <div className={classNames('invoice-skeleton-content')}>
          {/* 发票商品列表骨架屏 */}
          <div className="invoice-skeleton-products mt-16">
            <div className="products-header jd-skeleton h-14 w-120"></div>
            <div className="products-list">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="product-item jd-skeleton is-flex mb-12" style={{ height: '54px', width: '54px' }}></div>
              ))}
            </div>
          </div>

          {/* 发票类型选项 */}
          <div className={classNames('invoice-skeleton-options', 'is-flex')}>
            {[...Array(3)].map((_, i) => (
              <div key={i} className={classNames('invoice-skeleton-option', 'is-flex', 'items-center')}></div>
            ))}
          </div>

          {/* 表单字段 */}
          <div className="invoice-skeleton-form">
            {[...Array(2)].map((_, i) => (
              <div key={i} className={classNames('invoice-skeleton-form-item', 'mb-12')}>
                <div className="form-label jd-skeleton"></div>
                {[...Array(3)].map((_, j) => (
                  <div key={j} className="form-radio jd-skeleton"></div>
                ))}
              </div>
            ))}
            {[...Array(6)].map((_, i) => (
              <div key={i} className={classNames('invoice-skeleton-form-item', 'mb-16')}>
                <div className="form-label jd-skeleton"></div>
                <div className="form-input jd-skeleton"></div>
              </div>
            ))}
          </div>
        </div>

        {/* 模拟底部按钮 */}
        <div className={classNames('invoice-skeleton-footer', 'is-flex', 'justify-end')}>
          <div className="is-flex justify-end">
            <span className="cancel-btn jd-skeleton mr-12"></span>
            <span className="confirm-btn jd-skeleton"></span>
          </div>
        </div>
      </div>
    </div>,
    document.body,
  )
}

export default InvoiceContentSkeleton
