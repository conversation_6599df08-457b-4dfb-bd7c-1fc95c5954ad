const path = require('path')
const WebPackUploadOssPlugin = require('@jd/upload-oss-tools')

const pathResolve = (pathUrl) => path.join(__dirname, pathUrl)
const version = require(pathResolve('../package.json')).version
//TODO 两种方案，一种是带哈希不带版本号，一种是带版本号不带哈希
//const OSS_BASE = "//storage.360buyimg.com/mall_fe";
//const folderName = `JDC_m_auction_skygem_user`;
const folderName = `pc_settlement/pro/${version}` //TODO change to your project folder name

new WebPackUploadOssPlugin({
  localFullPath: path.resolve(__dirname, '../dist/'), // 被上传的本地绝对路径，自行配置
  access: 'IFivSqVWg5slcY4B', // http://oss.jd.com/user/glist 生成的 access key
  secret: '0wNUw59r0oSkyJaK8BhuonNfDNzSjW5rJxLdDbWZ', // http://oss.jd.com/user/glist 生成的 secret key
  site: '', // 远程 oss 路径 非必填 默认 storage.jd.com 可选(中国:storage.jd.com 印尼:storage.jd.id 泰国:storage.jd.co.th 测试:test.oss.jd.com)
  useHttps: true, // 是否启用https通信 默认true
  bucket: 'retail-mall', // 空间名字 仅能由小写字母、数字、点号(.)、中划线(-)组成
  folder: folderName, // 空间文件名称 非必填 (建议填写，否则将清空对应bucket下所有内容)
  ignoreRegexp: '', // 排除的文件规则 正则字符串
  cover: false, // 是否覆盖远程空间文件 默认true
}).upload()
