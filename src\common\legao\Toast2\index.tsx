import React, { useState, useEffect, useRef } from 'react'
import { createRoot } from 'react-dom/client'
import classNames from 'classnames'
import styles from './index.module.scss'

export interface ToastProps {
  className?: string
  customStyle?: React.CSSProperties
  /**
   * 是否展示元素
   * @defaultValue false
   */
  isOpened: boolean
  /**
   * 元素的内容
   */
  text?: string
  /**
   * 元素持续的事件（设置为 0 将不会自动消失）
   * @defaultValue 3000
   */
  duration?: number
  /**
   * 是否存在底部遮罩层(无法点击底部的内容区)
   */
  hasMask?: boolean
  /**
   * 元素被点击之后触发的事件
   */
  onClick?: (event: React.TouchEvent) => void
  /**
   * 元素被关闭之后触发的事件
   */
  onClose?: () => void
  /**
   * 是否聚焦
   */
  isFocus?: boolean
  type?: 'fail' | 'success' | 'warning'
}

interface CompoundedComponent {
  (props: React.PropsWithChildren<ToastProps>): React.ReactElement | null
  create: (props: Omit<ToastProps, 'isOpened'>) => { destroy: () => void } | undefined
}
const Toast: CompoundedComponent = ({
  isOpened = false,
  customStyle,
  className,
  text,
  hasMask = true,
  duration = 2000,
  onClick,
  onClose,
  isFocus = true,
  type,
}) => {
  const callback = useRef<{ onClose?: typeof onClose }>({})
  const [open, setOpen] = useState<boolean>(false)
  const toastDivRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    callback.current.onClose = onClose
  }, [onClose])

  useEffect(() => {
    if (isOpened) {
      setOpen(true)
    }
  }, [isOpened, duration])

  useEffect(() => {
    if (!open || duration <= 0) {
      return
    }
    const timeout = setTimeout(() => {
      setOpen(false)
    }, duration)

    return () => clearTimeout(timeout)
  }, [open, duration])

  useEffect(() => {
    if (open) {
      // 无障碍提示
      if (toastDivRef.current && isFocus) {
        toastDivRef.current.focus()
      }

      const current = callback.current
      return () => current.onClose?.()
    }
  }, [open, isFocus])

  if (!open) {
    return null
  }

  return (
    <div className={classNames(styles.toast, className)}>
      {hasMask && <div className={styles.overlay} onClick={() => setOpen(false)} />}
      <div
        className={styles.body}
        style={customStyle}
        onClick={(e) => {
          if (onClick) {
            return onClick(e as any)
          }
          setOpen(false)
        }}
      >
        <div className={styles.info} tabIndex={0} ref={toastDivRef} aria-label={`提示，${text}`}>
          {styles[type || ''] && <i className={styles[type || '']}></i>}
          <span>{text}</span>
        </div>
      </div>
    </div>
  )
}

Toast.create = (props) => {
  const root = document.body

  if (!root) {
    throw new Error('create modal error: root element does not exists')
  }

  const node = document.createElement('div')
  root.appendChild(node)

  const reactRoot = createRoot(node)

  const destroy = () => {
    Promise.resolve().then(() => {
      reactRoot.unmount()
    })
    node.remove?.()
    props.onClose?.()
  }
  const render = () => {
    /**
     * Sync render blocks React event. Let's make this async.
     */
    reactRoot.render(<Toast isOpened {...props} onClose={destroy} />)
  }
  render()
  return {
    destroy,
  }
}

export default Toast
