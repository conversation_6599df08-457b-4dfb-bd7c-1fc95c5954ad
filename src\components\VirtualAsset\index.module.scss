.flex {
	display: flex
}
.virtualAsset {
	width: 100%;
	box-sizing: border-box;
	margin: 0 auto;
	padding: 0 16px 16px;
	margin-bottom: 16px;
	border: 1px solid #FFFFFF;
	border-radius: 8px;
	background-color: #FFFFFF;
	font-family: PingFang SC;
	position: relative;
	.title {
		// height: 56px;
		// line-height: 68px;
		padding-top: 20px;
		padding-bottom: 16px;
		display: flex;
		justify-content: space-between;

		h5 {
			color: #1A1A1A;
			font-size: 16px;
			font-weight: 600;
		}

		.discount {
			color: var(--custom-color-red);
			font-size: 16px;
			i,
			span {
				font-weight: 700;
			}
		}
	}

	.tips {
		color: var(--custom-color-grey1);
		font-size: 14px;
		font-weight: 400;
		span {
			margin: 0 4px;
			color: var(--custom-color-red);
			cursor: pointer;
		}
		span:last-child {
			margin-right: 0;
		}


	}


	.tabs {
		display: flex;
		flex-direction: row;
		gap: 20px;

	}
}

.skeletonTop {
	width: 100%;
	height: 20px;
	padding-top: 24px;
	padding-bottom: 24px;
	justify-content: space-between;
	>div {
		border-radius: 4px;
	}
	&Left {
		width: 110px;
	}
	&Right {
		width: 72px;
		height: 14px;
	}
}
.skeletonCneter {
	border-bottom: 1px solid rgba(0, 0, 0, 0.06);
	padding-top: 17px;
	padding-bottom: 17px;
	padding-left: 16px;
	>div {
		width: 48px;
		height: 14px;
		margin-right: 44px;
		border-radius: 4px;
		&:nth-child(2) {
			width: 32px;
		}
	}
}
.skeletonBottom {
	padding-top: 20px;
	padding-bottom: 84px;
	>div { 
		width: 100px;
		height: 36px;
		border-radius: 4px;
		margin-right: 8px;
	}
}

