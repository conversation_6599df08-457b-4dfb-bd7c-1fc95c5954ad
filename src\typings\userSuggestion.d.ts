/**
 * 历史收货人推荐列表
 */
declare namespace AddressStandard {
    export interface RecommendList {
        /**
         * 手机号
         */
        mobile?: string;
        /**
         * 姓名
         */
        name?: string;
        /**
         * 区号
         */
        areaCode?: string;
        /** 省 */
        provinceId?: number;
        parentId: number;
        /** 市 */
        cityId?: number;
        addrId: number;
        /** 县 */
        countyId?: number;
        /** 镇 */
        townId?: number;
    }
    export interface UserSuggestionListTypes {
        recommendMobileList?: Array<RecommendList>, // 手机号推荐列表
        recommendNameMobileList?: Array<RecommendList>, // 手机号。姓名推荐列表
    }
}
