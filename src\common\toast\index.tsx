import React, { useState, useEffect, useCallback } from 'react'
import ReactDOM from 'react-dom'
import styles from './index.module.scss'

export interface ToastProps {
  /** 提示内容 */
  content: string
  /** 图标类型：success, error, warning, info, 或自定义图标组件 */
  icon?: 'success' | 'error' | 'warning' | 'info' | React.ReactNode
  /** 自动消失时间，单位毫秒，默认2000ms */
  duration?: number
  /** 消失后的回调 */
  onClose?: () => void
  /** 是否显示 */
  visible?: boolean
}

const Toast: React.FC<ToastProps> = ({ content, icon = 'info', duration = 2000, onClose, visible = true }) => {
  const [show, setShow] = useState(visible)

  const handleClose = useCallback(() => {
    setShow(false)
    onClose?.()
  }, [onClose])

  useEffect(() => {
    if (visible && duration > 0) {
      const timer = setTimeout(() => {
        handleClose()
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [visible, duration, handleClose])

  useEffect(() => {
    setShow(visible)
  }, [visible])

  // 渲染图标
  const renderIcon = () => {
    if (React.isValidElement(icon)) {
      return icon
    }

    const iconMap = {
      success: (
        <img
          width={24}
          height={24}
          src={'https://img12.360buyimg.com/img/jfs/t1/311064/31/14356/1503/6866682fFf6efee67/6d5b4a20488fc76a.png'}
          alt="error"
        />
      ),
      error: (
        <img
          width={24}
          height={24}
          src={'https://img12.360buyimg.com/img/jfs/t1/311517/12/14248/1319/686666b1Fa1a73115/9c9a1d2508b6e065.png'}
          alt="error"
        />
      ),
      warning: (
        <svg className={styles.toastIcon} viewBox="0 0 16 16" fill="none">
          <circle cx="8" cy="8" r="8" fill="#FAAD14" />
          <path d="M8 5v3M8 11h.01" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      ),
      info: (
        <svg className={styles.toastIcon} viewBox="0 0 16 16" fill="none">
          <circle cx="8" cy="8" r="8" fill="#1890FF" />
          <path d="M8 5v6M8 11h.01" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      ),
    }

    return iconMap[icon as keyof typeof iconMap] || iconMap.info
  }

  if (!show) {
    return null
  }

  return (
    <div className={styles.toastOverlay}>
      <div className={styles.toastContainer}>
        <div className={styles.toastContent}>
          {renderIcon()}
          <span className={styles.toastText}>{content}</span>
        </div>
      </div>
    </div>
  )
}

// Toast方法调用接口
interface ToastOptions extends Omit<ToastProps, 'visible'> {
  /** 挂载的容器，默认document.body */
  container?: HTMLElement
}

let toastContainer: HTMLDivElement | null = null

const showToast = (options: ToastOptions) => {
  const { container = document.body, ...props } = options

  // 清除之前的toast
  if (toastContainer) {
    ReactDOM.unmountComponentAtNode(toastContainer)
    container.removeChild(toastContainer)
  }

  // 创建新的容器
  toastContainer = document.createElement('div')
  toastContainer.className = styles.toastWrapper
  container.appendChild(toastContainer)

  const handleClose = () => {
    if (toastContainer) {
      ReactDOM.unmountComponentAtNode(toastContainer)
      container.removeChild(toastContainer)
      toastContainer = null
    }
    props.onClose?.()
  }

  ReactDOM.render(<Toast {...props} visible={true} onClose={handleClose} />, toastContainer)
}

// 便捷方法
const toast = {
  show: showToast,
  success: (content: string, options?: Omit<ToastOptions, 'content' | 'icon'>) => showToast({ content, icon: 'success', ...options }),
  error: (content: string, options?: Omit<ToastOptions, 'content' | 'icon'>) => showToast({ content, icon: 'error', ...options }),
  warning: (content: string, options?: Omit<ToastOptions, 'content' | 'icon'>) => showToast({ content, icon: 'warning', ...options }),
  info: (content: string, options?: Omit<ToastOptions, 'content' | 'icon'>) => showToast({ content, icon: 'info', ...options }),
}

export default toast
export { Toast }
