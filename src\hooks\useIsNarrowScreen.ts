import { useState, useEffect } from 'react'

const mediaQuery = window?.matchMedia(`screen and (max-width: 1440px)`)

function useIsNarrowScreen() {
  const [matches, setMatches] = useState<boolean>(mediaQuery.matches)

  useEffect(() => {
    const handleChange = () => {
      setMatches(mediaQuery.matches)
    }
    mediaQuery.addEventListener('change', handleChange)
    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }, [])

  return matches
}

export default useIsNarrowScreen
