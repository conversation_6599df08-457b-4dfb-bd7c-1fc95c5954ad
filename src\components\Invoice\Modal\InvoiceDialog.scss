// 定义变量
$font-family: PingFang SC;
$primary-color: #1a1a1a;
$border-color: #f0f0f0;
$mask-color: rgba(0, 0, 0, 0.5);
$shadow-color: rgba(0, 0, 0, 0.15);

.invoice-dialog {
  z-index: 999;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
  position: relative;

  &-wrapper {
    position: fixed;
    z-index: 999;
    top: 0;
    left: 0;
    // width: 100vw;
    // height: 100vh;
    // display: flex;
    // align-items: center;
    // justify-content: center;
  }

  &-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 998;
    background-color: $mask-color;
  }

  &-head {
    position: relative;
    padding: 0 24px;
    height: 58px;
    line-height: 58px;
    color: $primary-color;
    font-family: $font-family;
    font-size: 18px;
    font-weight: 600;

    &-notitle {
      background: #fff;
    }
  }

  &-close {
    padding: 0;
    outline: 0;
    border: 0;
    cursor: pointer;
    display: block;
    position: absolute;
    z-index: 1000;
    top: 24px;
    right: 24px;
    width: 16px;
    height: 16px;
    background: url(https://img12.360buyimg.com/imagetools/jfs/t1/275638/17/11200/598/67e669f0F62ee6ede/bf00226f8543b19b.png) center / 100%
      100% no-repeat;

    &-notitle {
      width: 16px;
      height: 16px;
      background: url(//misc.360buyimg.com/user/cart/css/i/commontips/common-tips-close.png) no-repeat;
    }
  }

  &-body {
    padding: 10px 20px 5px;
    font-size: 14px;
    color: #666;
    overflow: auto;
  }
}

.modal {
  &-content {
    position: relative;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px $shadow-color;
    overflow: visible;
    display: flex;
    flex-direction: column;
  }

  &-body {
    padding: 0 20px;
    font-size: 14px;
    color: #666;
    overflow: auto;
    max-height: 80vh;
  }

  &-footer {
    position: absolute;
    z-index: 1000;
    width: 100%;
    bottom: 0;
    background-color: #fff;

    &-content {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      padding: 16px 24px 24px 24px;
    }
  }

  &-header-with-tabs {
    display: flex;
    height: 100%;
    align-items: center;
  }
}

.invoice-modal,
.invoice-notice-modal {
  max-height: 624px;
  .invoice-dialog-head {
    align-items: center;
  }

  .invoice-dialog-body {
    padding: 0 24px;
  }
}

.invoice-btn {
  padding-left: 30px;
  padding-right: 30px;
}
