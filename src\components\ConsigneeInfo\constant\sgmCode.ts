// 自定义sgm编码收集
const sgmCode = {
  LLV: 'latLongVertify', //  经纬度校验为空以及是否在合理的范围内
  ADDRESS_CMPNT_QUERYADDRESS: 'pc_address_cmpnt_queryAddress', // 常用地址列表
  // 编辑页面开始----
  CREATE_ADDRESS_TOO_LONG: 'create_address_too_long', // 创建地址过长
  CASCADE_COMPLETE: 'cascade_complete', // 地市/区县信息是否完整
  MAP_COMPLETE: 'map_complete', // 地市/区县信息是否完整
  NOPASS_NAME: 'nopass_name', // 名称校验失败
  NOPASS_MOBILE: 'nopass_mobile', // 手机号校验失败
  NOPASS_PHONE: 'nopass_phone', // 手机号校验失败
  NOPASS_EMAIL: 'nopass_email', // 邮箱校验失败
  NOPASS_POSTCODE: 'nopass_postcode', // 邮编校验失败
  NOPASS_HOUSENUMBER: 'nopass_housenumber', // 门牌号校验失败
  NOPASS_ADDRESSDETAIL: 'nopass_addressdetail', // 详细地址校验失败
  NOPASS_FRAME_TIPS: 'frameTips', // 服务端下发的frameTips
  SHOWBOXNOCHOOSE: 'showBoxNoChoose', // 四级地址末级地址选择暂不知道:点击否，提示请选择地址所在地区，展示地区选择弹窗
  SHOWBOXNOCHOOSEFAIL: 'showBoxNoChooseFail', // 系统识别失败toast曝光-识别不成功的时候
  SHOWBOX: 'showbox', // 根据详细地址匹配，接口返回相关信息，弹窗提示用户
  SHOWBOXNOAUTOCOMP: 'showBoxNoAutoComp', // 详细地址与四级地址不匹配（不在一个范围）
  SHOWBOXFORCESAVE: 'showBoxForceSave', // 简化三级强制保存
  FEEDBACKTIPS: 'feedbackTips', // 觉得地址无问题，点击反馈问题
  ERRORTOAST: 'errorToast', // 网络异常，请稍后重试~
  SHOWBOXRECOMMENDPOI: 'showBoxRecommendPoi', // 精细化地图弹窗-推荐poi
  CATCHTOAST: 'catchToast', // 网络异常，请稍后重试~
  // 编辑页面结束----
  POI: 'poiRefinement', // 地图选址精细化poi需求，弹窗没有当前地址或者推荐地址
  HAS_LOCATION_PERMISSION_WITH_SCENE: 'hasLocationPermissionWithScene', // app是否授权
  MANUAL_REQ_LOC_PREM_WITH_SECNE: 'manualReqLocPermWithScene', // 授权弹窗
  REQUEST_LOCATION_PERMISSION_WITH_SCENE: 'requestLocationPermissionWithScene', // 限制频次授权弹窗
  GET_LAT_LNG: 'getLatLng', // 获取实时经纬度
  APP_NETWORK_PARAMS: 'app_network_params', // 原生网络库入参
  APP_NETWORK_SUCCESS: 'app_network_success', // 原生网络请求出参
  APP_NETWORK_FAIL: 'app_network_fail', // 原生网络请求失败
  APP_NETWORK_TIMEOUT: 'app_network_timeout', // 原生网络请求失败
}
export default sgmCode
