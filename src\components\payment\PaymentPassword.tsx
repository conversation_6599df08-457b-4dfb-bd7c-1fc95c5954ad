import React, { useRef, useImperativeHandle } from 'react'
import OTPInput from './components/OTPInput'
import type { OTPRef } from './components/OTPInput'
import Tooltip from '@app/common/Tooltip'

type LPInputRef = {
  focus: VoidFunction
  blur: VoidFunction
  reset: VoidFunction
  readonly value: string
}

const LPInput = React.forwardRef<LPInputRef>((_props, ref) => {
  const inputRef = useRef<HTMLInputElement>(null)

  useImperativeHandle(ref, () => {
    return {
      focus: () => {
        inputRef.current?.focus()
      },
      blur: () => {
        inputRef.current?.blur()
      },
      reset: () => {
        inputRef.current!.value = ''
      },

      get value() {
        return inputRef.current!.value
      },
    }
  }, [])

  return (
    <div className="long-passwd">
      <input className="otp-input" type="password" ref={inputRef} />
    </div>
  )
})

LPInput.displayName = 'LPInput'

type PaymentPasswordProps = {
  mode?: 'short' | 'long'
  info?: string
}

export type PaymentPasswordRef = {
  focus: VoidFunction
  blur: VoidFunction
  reset: VoidFunction
  readonly value: [boolean, string]
}

const PaymentPassword = React.forwardRef<PaymentPasswordRef, PaymentPasswordProps>((props, ref) => {
  const { mode = 'short', info } = props
  const otpInputRef = useRef<OTPRef>(null)
  const lpInputRef = useRef<LPInputRef>(null)

  const getCurrentInputRef = (): OTPRef | LPInputRef | null => {
    return mode === 'short' ? otpInputRef.current : lpInputRef.current
  }

  useImperativeHandle(ref, () => {
    return {
      focus: () => {
        const currentInput = getCurrentInputRef()
        currentInput?.focus()
      },
      blur: () => {
        const currentInput = getCurrentInputRef()
        currentInput?.blur()
      },
      reset: () => {
        const currentInput = getCurrentInputRef()
        currentInput?.reset()
      },
      get value(): [boolean, string] {
        const currentInput = getCurrentInputRef()
        const value = currentInput?.value
        if (value == undefined || value === '') {
          return [false, '']
        }
        if (typeof value === 'string') {
          return [true, value]
        }
        return [value.filter(Boolean).length === 6, value.join('')]
      },
    }
  }, [])

  return (
    <div className="payment-summary-password">
      <div className="payment-summary-password__title">
        <em>
          支付密码
          {info && (
            <Tooltip
              content={<div dangerouslySetInnerHTML={{ __html: info }} />}
              placement="top"
              width={280}
              padding="12px"
              arrow
            >
              <i className="icon-info ml-4" />
            </Tooltip>
          )}
        </em>
        <span>
          <a href="//idt.jd.com/password-auth/#/passwordAuthCheck" target="_blank">
            忘记密码
          </a>
        </span>
      </div>
      <div className="payment-summary-password__input">
        {mode === 'short' ? (
          <OTPInput
            ref={otpInputRef}
            type="password"
            onInput={(value) => {
              console.log(value)
            }}
            onComplete={(value) => {
              console.log(value)
            }}
          />
        ) : (
          <LPInput ref={lpInputRef} />
        )}
      </div>
    </div>
  )
})

PaymentPassword.displayName = 'PaymentPassword'

export default PaymentPassword
