/**
 * @file: useGlobalZero.ts
 * @description: 税费击穿逻辑
 */
import { useMemo } from 'react'
import { useStore } from 'jotai'
import useMasterData from '@app/hooks/useMasterData'
import { commonConfigAtom } from '@app/atoms/commonConfigAtom'
import { confirm } from '../components/confirm_dialog'
const CONFIG_TYPE = 'TAX_AND_FEE_BREAKDOWN_RULES'

function useGlobalZero() {
  const store = useStore()
  const masterData = useMasterData()?.body
  const total = masterData?.balanceTotal
  const allWasteAmount = total?.allWasteAmount
  const isGlobalZero = allWasteAmount != null 
    && +allWasteAmount === +allWasteAmount
    && +allWasteAmount > 0
    && store.get(commonConfigAtom)?.[CONFIG_TYPE]

  const action = useMemo(() => isGlobalZero ? () => {
    let content = store.get(commonConfigAtom)?.[CONFIG_TYPE]
    content = content?.replace("xx", +allWasteAmount * 100);
    confirm({ okText : '我知道了', content})
  }: undefined, [isGlobalZero])

  return [isGlobalZero, action] as const
}

export default useGlobalZero
