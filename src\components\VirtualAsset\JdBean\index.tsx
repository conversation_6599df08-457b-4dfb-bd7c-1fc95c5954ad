/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-10 15:17:33
 * @LastEditors: ext.wangchao120
 * @Description: 京豆
 * @FilePath: /pc_settlement/src/components/VirtualAsset/JdBean/index.tsx
 */
import { ReactNode, useRef, useState, FC } from 'react'
import Input from '../components/Input'
import classNames from 'classnames'
import styles from './index.module.scss'
import Tooltip, { type TooltipActions } from '@app/common/Tooltip'
import useMasterData from '@app/hooks/useMasterData'
import type { BalanceVirtualAssetsVO, BalanceTotal, jdBean } from '@app/typings/master_api_response'
import { api_useJdBean } from '@app/services/api'
import type { BalanceUser } from '@app/typings/master_api_response'

import useUpdateMasterData from '@app/hooks/useUpdateMasterData'
import RightTip from '../components/RightTip'
import showToast from '@app/components/payment'
import { ExposeBuriedPoints } from '@app/utils/dataSet'
import { _batchExposeDom } from '../batchExpose'
import { reportClick } from '@app/utils/event_tracking'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import { useAtom } from 'jotai'
import { virtualAssetLoading } from '@app/components/VirtualAsset/atoms'
const JdBean: FC = () => {
  const observer = useRef<ExposeBuriedPoints | null>(null)
  const masterData = useMasterData()?.body ?? {}
  const balanceVirtualAssetsVO = (masterData?.balanceVirtualAssetsVO as BalanceVirtualAssetsVO) ?? {}
  const balanceTotal = (masterData?.balanceTotal as BalanceTotal) ?? {}
  const balanceUser = (useMasterData()?.body?.balanceUser as BalanceUser) ?? {}

  const updateMasterData = useUpdateMasterData()

  const dropDownRef = useRef<TooltipActions | null>(null)
  // 京豆自定义输入数量
  const [value, setValue] = useState<number | string>('')
  // 京豆输入框是否编辑
  const [isEdit, setIsEdit] = useState<boolean>(false)
  // 选中的京豆
  const [jdBeanNum, setJdBeanNum] = useState<number>(0)

  const [, setLoading] = useAtom(virtualAssetLoading)

  // 京豆输入框输入
  const inputChange = (value: string) => {
    // 不允许输入小数点
    value = value.replace(/[^0-9]/g, '')
    setValue(value)
  }

  const onChange = (visible: boolean) => {
    observer.current?.destory()
    // 批量曝光
    if (visible) {
      observer.current = _batchExposeDom('jdbeanEXPO', `jdbean`, 'jdbeannum')
    } else {
      observer.current = null
    }
  }
  const inputBlur = (value: number) => {
    // 最小使用京东豆数量
    if (Number(value) <= balanceVirtualAssetsVO?.lowerLimitAvailablePoints) {
      setValue(balanceVirtualAssetsVO?.lowerLimitAvailablePoints)
      setJdBeanNum(balanceVirtualAssetsVO?.lowerLimitAvailablePoints)
      // 最大使用京东豆数量
    } else if (Number(value) > balanceVirtualAssetsVO?.upperLimitAvailablePoints) {
      setValue(balanceVirtualAssetsVO?.upperLimitAvailablePoints)
      setJdBeanNum(balanceVirtualAssetsVO?.upperLimitAvailablePoints)
    }
  }

  /**
   * 使用京豆
   * @param num 使用京豆数量
   */
  const handleJdBean = (item: jdBean) => {
    setLoading(true)
    api_useJdBean({
      jdBeanNum: item?.num || 0,
      pointSelectFlag: item?.pointSelectFlag || '',
    })
      .then((res) => {
        if (res?.code === '0') {
          // console.log('京豆使用成功', res)
          // 设置京豆数量
          setJdBeanNum(Number(item?.num))
          // 关闭下拉框
          dropDownRef.current?.hide()
          // 更新融合数据
          updateMasterData(undefined, 'virtualAsset')
          // 自定义输入京豆
          if (item?.pointSelectFlag === '4') {
            setValue('')
            setIsEdit(false)
          }
          // 点击埋点
          reportClick('jdbean', {
            clickPos: item?.pointSelectFlag === '4' ? 1 : 0, // 0: 下拉框选择京豆，1: 自定义输入京豆
            jdbeannum: item?.num, // 京豆数量
          })
        } else {
          showToast({ title: res?.message || '请稍后再试' })
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'balance_useCancelEditJdBean_pc',
            error_type_txt: 'balance_useCancelEditJdBean_pc接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 京豆输入框确认点击
  const confirm = () => {
    if (isEdit) {
      handleJdBean({
        num: Number(value),
        pointSelectFlag: '4',
      })
    } else {
      setIsEdit(true)
    }
  }

  // 京豆下拉框内容
  const dropDownCont = (
    <div className={styles.jdBeanDropDownCont}>
      <div className={styles.jdBeanDropItem}>
        {masterData?.jdBeanVO?.jdBeanMaps &&
          masterData?.jdBeanVO?.jdBeanMaps.map((item, index) => {
            return (
              <div
                key={index}
                data-point-id={item?.num}
                className={classNames(
                  item?.num && item?.num > 0 && 'jdbean',
                  styles.jdBeanItem,
                  Number(item?.num) && jdBeanNum == item?.num ? styles.jdBeanItemActive : '',
                )}
                onClick={() => handleJdBean(item)}
              >
                {item.text}
              </div>
            )
          })}
      </div>
      <div className={styles.fixedBottom}>
        <div className={classNames(styles.jdBeanCustomize, isEdit && styles.jdBeanCustomizeEdit)}>
          <Input
            type="number"
            size="large"
            value={value}
            placeholder="自定义输入京豆数量"
            className={styles.jdBeanInput}
            disabled={!isEdit}
            onBlur={(value) => inputBlur(Number(value))}
            onChange={(value) => inputChange(value + '')}
          ></Input>
          <div className={styles.btn} onClick={confirm}>
            {isEdit ? '确认' : '编辑'}
          </div>
        </div>
      </div>
    </div>
  ) as ReactNode

  return (
    <>
      <div className={styles.jdBean}>
        <div className={styles.jdBeanPrompt}>
          <div className={styles.jdBeanPromptText}>
            {Number(balanceTotal?.itemTotalPrice) >= Number(masterData?.jdBeanVO?.minPayableAmount)
              ? `您当前订单最低可用${balanceVirtualAssetsVO?.lowerLimitAvailablePoints}京豆，京豆支付不得超过每笔结算应付金额的${balanceVirtualAssetsVO.pointsDeductionRatio}%，实际支付金额以页面显示为准`
              : '不符合京豆使用规则，无可用京豆'}
            <Tooltip
              arrow={true}
              maxWidth={360}
              padding={'12px'}
              content={`【使用条件】
                    订单金额大于20元（含）；京豆数量大于500个（含）；具体以页面实际可用京豆量为准；
                    您当前的京享值${balanceUser?.userScore}，京豆支付不得超过每笔订单应付金额的${balanceVirtualAssetsVO.pointsDeductionRatio}% ；实际支付金额以页面显示为准。
                    【使用数量】
                    京豆数量大于500个（含）；500京豆抵5元。`}
            >
              <div className={classNames('icon-info', styles.icon)}></div>
            </Tooltip>
          </div>
          <RightTip type="jingDouDescLink" />
        </div>
        <div className={styles.jdBeanSelect}>
          <div>账户共{balanceVirtualAssetsVO?.totalJdBeanCount}个京豆，本次使用</div>
          <div className={styles.jdBeanDropDown}>
            <Tooltip
              ref={dropDownRef}
              type="popover"
              width={280}
              padding={'4px 4px 12px'}
              borderRadius={'4px'}
              height={268}
              maxHeight={268}
              borderColor="rgba(0, 0, 0, 0.1)"
              content={dropDownCont}
              trigger="click"
              className={styles.jdBeanDrop}
              onChange={onChange}
              disabled={!balanceVirtualAssetsVO?.disableJdBeanShow || !balanceVirtualAssetsVO?.lowerLimitAvailablePoints}
            >
              <div
                className={classNames(
                  styles.jdBeanTitle,
                  (!balanceVirtualAssetsVO?.disableJdBeanShow || !balanceVirtualAssetsVO?.lowerLimitAvailablePoints) && styles.disabled,
                )}
              >
                {Number(jdBeanNum) > 0 ? (
                  <span>
                    {jdBeanNum}个，抵&yen;{Number(jdBeanNum) / 100}元
                  </span>
                ) : (
                  '暂不使用'
                )}
                <i className={classNames(styles.arrow)}></i>
              </div>
            </Tooltip>
          </div>
        </div>
      </div>
    </>
  )
}

export default JdBean
