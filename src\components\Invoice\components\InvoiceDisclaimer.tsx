import { FC } from 'react'
import { Checkbox } from '@app/common/legao'
import { useAtomValue } from 'jotai'
import { currentInvoiceAtom, selectedInvoiceTitleAtom, selectedInvoiceTypeAtom } from '@app/components/Invoice/atom/invoiceAtom'
import { InvoiceTitle, InvoiceType } from '@app/typings/invoice.d'
interface InvoiceDisclaimerProps {
  disclaimerChecked: boolean
  onDisclaimerChange: (checked: boolean) => void
}

/**
 * 发票药品免责声明组件
 */
const InvoiceDisclaimer: FC<InvoiceDisclaimerProps> = ({ disclaimerChecked, onDisclaimerChange }) => {
  const currentInvoice = useAtomValue(currentInvoiceAtom)
  const selectedInvoiceTitle = useAtomValue(selectedInvoiceTitleAtom)
  const selectedInvoiceType = useAtomValue(selectedInvoiceTypeAtom)
  // 选中发票类型为普通发票或电子专票时，不显示免责声明
  if (selectedInvoiceType === InvoiceType.NORMAL || selectedInvoiceType === InvoiceType.E_VAT) return null
  // 选中抬头为个人时，不显示免责声明·
  if (selectedInvoiceTitle !== InvoiceTitle.COMPANY && selectedInvoiceType !== InvoiceType.VAT) return null
  // 需要合规提示且合规提示不为1时，不显示免责声明
  if (currentInvoice.needConformanceTips && currentInvoice.needConformanceTips !== 1) return null
  if (!currentInvoice.isNeedConformanceTips) return null // 不需要合规提示时，不显示免责声明

  return (
    <div className="invoice-disclaimer" style={{ marginTop: '5px' }}>
      <Checkbox checked={disclaimerChecked} onChange={onDisclaimerChange}>
        <span style={{ color: '#1A1A1A' }}> 依照相关法律法规要求，该订单含药品，需承诺仅用于个人自用，不进行二次销售。</span>
      </Checkbox>
    </div>
  )
}

export default InvoiceDisclaimer
