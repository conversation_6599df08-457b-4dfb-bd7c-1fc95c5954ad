/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-02 17:46:37
 * @LastEditors: ext.wangchao120
 * @Description: 批量曝光
 * @FilePath: /pc_settlement/src/components/VirtualAsset/batchExpose.ts
 */

import { batchExposeDom } from '@app/utils/event_tracking'
import { ExposeBuriedPoints } from '@app/utils/dataSet'

/**
 * 批量曝光
 * @param funcId 功能标识
 * @param className dom类名
 * @param key  埋点key
 * @param params  埋点参数
 */
const _batchExposeDom = (funcId: string, className: string, key: string, params: any = {}): ExposeBuriedPoints | null => {
  // 获取dom列表
  const domList = Array.from(document.querySelectorAll(`.${className}`))
  const domInfoList: IExposeDomInfo[] = domList.map((dom, i) => {
    const domInfo: IExposeDomInfo = {
      key: i,
      dom: dom as HTMLElement,
      params: dom?.getAttribute('data-point-id'),
    }
    // 领货码额外参数
    if (dom?.getAttribute('data-point-clerk')) {
      domInfo.extParams = dom?.getAttribute('data-point-clerk')
    }
    return domInfo
  })
  return batchExposeDom(funcId, params, domInfoList, key) as ExposeBuriedPoints
}

/** 曝光列表 */
const domMap = new Map<string, string>()

export { _batchExposeDom, domMap }
