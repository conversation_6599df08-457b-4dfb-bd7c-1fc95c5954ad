/**
 * @file: InstallModule.tsx
 * @description: 安装模块，用于展示安装等逻辑
 */
import { useEffect, useState } from 'react'
import SelectItemV2 from '../selector/SelectItemV2'
import Delivery from '../deliverys/domestic/delivery2'
import Skeleton from '../../ui/skeleton'
import { api_GetBundleShipmentList } from '@app/services/api'
import { DeliveryDataIniter } from '../initDeliveryData'
import { selectDataProcess } from '../selector/initSelectData'
import { useDeliveryHooks } from './hooks'
import { getDefaultTsx } from './utils'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import showToast from '@app/components/payment/components/toast'

export default function InstallModule() {
  const { wrapperRef, isOpen, setIsOpen, initState, deliveryLogger, defErrMsg, setErrorMessage, errorMessage } = useDeliveryHooks()

  const { installItem, transportCtrl } = selectDataProcess(initState.bundle)
  const [installDataIniter, setInstallDataIniter] = useState<DeliveryDataIniter | null>(null)

  useEffect(() => {
    setErrorMessage('')
    if (!isOpen) {
      setInstallDataIniter(null)
      return
    }
    const apiData = api_GetBundleShipmentList({
      bundleUuid: initState.bundle.bundleId,
      venderId: initState.venderId,
      transportCtrl: transportCtrl,
    })
    apiData
      .then((res) => {
        if (transportCtrl) {
          setInstallDataIniter(null)
          return
        } else {
          const initer = new DeliveryDataIniter(initState, res, true)
          setInstallDataIniter(initer)
          if (initer.getErrorMessage()) {
            setErrorMessage(defErrMsg)
          }
        }
      })
      .catch((error) => {
        setErrorMessage(defErrMsg)
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.Delivery,
          msg: {
            type: '配送模块',
            error: error,
            data: initState.bundle,
            info: '请求安装信息失败',
          },
        })
      })
  }, [isOpen, transportCtrl, initState])

  const has = !!installItem
  useEffect(() => {
    if (!has) {
      return
    }
    deliveryLogger.InstalltimeEXPO()
  }, [has])

  const defaultInstallString =
    installItem?.items.slice(2).reduce((prev, curr) => {
      return prev + curr.text
    }, '') || ''

  const defaultTsx = () => getDefaultTsx(defaultInstallString, { installDataIniter }, () => setIsOpen(false))

  const getInstallTsx = () => {
    if (!isOpen) {
      return null
    }
    if (errorMessage) {
      return defaultTsx()
    }
    if (!installDataIniter) {
      return <Skeleton onClick={() => setIsOpen(false)} />
    }
    return <Delivery defaultTsx={() => defaultTsx()} onCancel={() => setIsOpen(false)} deliveryDataIniter={installDataIniter} />
  }

  if (!installItem) return null
  return (
    <div ref={wrapperRef}>
      <SelectItemV2
        isOpen={isOpen}
        canOpen={installItem.canOpen}
        items={installItem.items}
        onClick={() => {
          setIsOpen(!isOpen)
          deliveryLogger.InstalltimeClick()
          deliveryLogger.orddetail('1')
        }}
      >
        {getInstallTsx()}
      </SelectItemV2>
    </div>
  )
}
