import { FC } from 'react'
import { Checkbox } from '@app/common/legao'
import {
  currentInvoiceAtom,
  selectedInvoiceStatusAtom,
  selectedInvoiceTypeAtom,
  selfInvoiceAtom,
  specialInvoiceStepAtom,
} from '@app/components/Invoice/atom/invoiceAtom'
import { useAtomValue, useSetAtom } from 'jotai'
import './DefaultInvoiceCheckbox.scss'
import { InvoiceType } from '@app/typings/invoice.d'

/**
 * 设置默认发票选项组件
 */
const DefaultInvoiceCheckbox: FC = () => {
  const selectedInvoiceType = useAtomValue(selectedInvoiceTypeAtom)
  const currentInvoice = useAtomValue(currentInvoiceAtom)
  const selectedInvoiceStatus = useAtomValue(selectedInvoiceStatusAtom)
  const specialInvoiceStep = useAtomValue(specialInvoiceStepAtom)
  const setSelfInvoiceAtom = useSetAtom(selfInvoiceAtom)
  // 只有自营商品(selectedInvoiceStatus === '0')才显示默认发票选项
  if (selectedInvoiceStatus !== 0) return null
  let defaultChecked = false
  if (selectedInvoiceType === InvoiceType.NORMAL) {
    defaultChecked = currentInvoice.normalInvoice?.defaultFlag || false
  } else if (selectedInvoiceType === InvoiceType.VAT || selectedInvoiceType === InvoiceType.E_VAT) {
    defaultChecked = currentInvoice.vat?.defaultFlag || false
  } else if (selectedInvoiceType === InvoiceType.ELECTRONIC) {
    defaultChecked = currentInvoice.electroInvoice?.defaultFlag || false
  }
  /**
   * 处理默认发票选项变化
   */
  const handleDefaultChange = (flag: boolean) => {
    if (selectedInvoiceType === InvoiceType.NORMAL) {
      setSelfInvoiceAtom({ ...currentInvoice, normalInvoice: { ...currentInvoice.normalInvoice, defaultFlag: flag } })
    } else if (selectedInvoiceType === InvoiceType.VAT || selectedInvoiceType === InvoiceType.E_VAT) {
      setSelfInvoiceAtom({ ...currentInvoice, vat: { ...currentInvoice.vat, defaultFlag: flag } })
    } else if (selectedInvoiceType === InvoiceType.ELECTRONIC) {
      setSelfInvoiceAtom({ ...currentInvoice, electroInvoice: { ...currentInvoice.electroInvoice, defaultFlag: flag } })
    }
  }
  // 专用发票场景下，第一步不展示
  if (selectedInvoiceType === InvoiceType.VAT && specialInvoiceStep === 1) return null
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div style={{ width: '110px' }}></div>
      <div className="default-invoice-option">
        <Checkbox checked={defaultChecked} onChange={handleDefaultChange}>
          <span style={{ color: '#1A1A1A' }}>设为默认发票</span>
        </Checkbox>
        <div style={{ marginTop: '5px', display: 'flex', alignItems: 'center', gap: '5px' }}>
          <img
            src="https://img13.360buyimg.com/imagetools/jfs/t1/255112/1/12753/663/67877438F9304b67f/ed3f7c64aed852ff.png"
            alt=""
            className="default-invoice-icon"
          />
          <span className="default-invoice-tips">勾选后，自营订单后续下单优先使用以上发票信息，若订单不支持会为您自动切换</span>
        </div>
      </div>
    </div>
  )
}

export default DefaultInvoiceCheckbox
