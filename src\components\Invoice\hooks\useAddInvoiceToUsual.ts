import { InvoiceToUsualForm, InvoiceResult, UsualInvoiceItem } from '@app/typings/invoice'
import { addInvoiceToUsual as addInvoiceToUsualApi } from '@app/services/api'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'

interface UseAddInvoiceToUsualParams {
  selectInvoiceTitle: number | null | undefined
  updateUsualInvoice: (newInvoice: UsualInvoiceItem) => void
}

/**
 * 添加发票到常用发票的Hook
 */
export const useAddInvoiceToUsual = ({ selectInvoiceTitle, updateUsualInvoice }: UseAddInvoiceToUsualParams) => {
  return async (newItem: Partial<UsualInvoiceItem>, invoiceCode?: string) => {
    if (!newItem?.content?.trim()) {
      return { success: false, message: '发票抬头内容不能为空' }
    }

    try {
      // 调用保存接口
      const body: InvoiceToUsualForm = {
        selectInvoiceTitle: selectInvoiceTitle?.toString() || '',
        norCompanyName: newItem.content || '',
        invoiceCode: invoiceCode || newItem.invoiceCode || '',
        norCode: invoiceCode || newItem.invoiceCode || '',
        norRegAddr: newItem.regAddress || '',
        norRegPhone: newItem.regTel || '',
        norRegBank: newItem.regBank || '',
        norRegBankAccount: newItem.regAccount || '',
      }

      const response = (await addInvoiceToUsualApi(body)) as InvoiceResult<number>

      if (response && response.code === '0') {
        // 构建新的发票对象
        const newInvoiceItem: UsualInvoiceItem = {
          id: response.body || 0,
          content: newItem.content || '',
          invoiceCode: invoiceCode || newItem.invoiceCode || '',
          regTel: newItem.regTel || '',
          regBank: newItem.regBank || '',
          regAccount: newItem.regAccount || '',
          regAddress: newItem.regAddress || '',
          selected: true,
        }

        // 更新发票列表
        updateUsualInvoice(newInvoiceItem)

        return {
          success: true,
          data: newInvoiceItem,
        }
      } else {
        const errorMsg = response?.message
        const errorCode = response?.code
        console.error('添加常用发票失败:', response)
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.Invoice,
          msg: {
            type: '发票',
            action: '添加常用发票',
            error: errorMsg,
            code: errorCode,
            traceId: response?.traceId,
            requestId: response?.requestId,
          },
        })
        return {
          success: false,
          message: errorMsg,
        }
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误'
      console.error('添加常用发票异常:', error)
      monitoring({
        name: monitorName.Settlement,
        code: monitorCode.Invoice,
        msg: {
          type: '发票',
          action: '添加常用发票',
          error: '添加常用发票异常:' + errorMsg,
        },
      })
      return {
        success: false,
        message: errorMsg,
      }
    }
  }
}

export default useAddInvoiceToUsual
