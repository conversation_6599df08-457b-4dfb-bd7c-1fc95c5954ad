/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-30 15:47:48
 * @LastEditTime: 2025-07-04 17:59:44
 * @LastEditors: ext.wangchao120
 * @Description: 优惠券
 * @FilePath: /pc_settlement/src/components/VirtualAsset/Coupons/index.tsx
 */
import React, { useEffect, useState, useRef, useMemo } from 'react'
import { ExposeBuriedPoints } from '@app/utils/dataSet'

import styles from './index.module.scss'
import Card from './Card'
import Tabs from '../components/Tabs'
import RedeemCode from '../components/RedeemCode'
import Combination from './Combination'
import NoData from '../components/NoData'
import monitoring, { monitorCode, monitorName } from '@app/utils/monitoring'
import useMasterData from '@app/hooks/useMasterData'
import { api_exchangeCoupons, api_getPageCouponList, api_selectOrderCoupon } from '@app/services/api'
import useUpdateMasterData from '@app/hooks/useUpdateMasterData'
import type { CouponItem } from '@app/typings/virtualAsset'
import RightTip from '../components/RightTip'
import showToast from '@app/components/payment'
import { COUPONS_TABS } from '../constants'
import { _batchExposeDom, domMap } from '../batchExpose'
import { reportClick } from '@app/utils/event_tracking'
import { useAtom } from 'jotai'
import { virtualAssetLoading } from '@app/components/VirtualAsset/atoms'
interface Props {
  tabKey: string
}

const Coupons: React.FC<Props> = ({ tabKey }) => {
  const masterData = useMasterData()?.body
  const useBestCoupon = masterData?.balanceVirtualAssetsVO?.useBestCoupon
  const observer = useRef<ExposeBuriedPoints | null>(null)
  const updateMasterData = useUpdateMasterData()
  const [activeKey, seActiveKey] = useState<string>('available')
  const [, setLoading] = useAtom(virtualAssetLoading)

  // 可用优惠券
  const [availableCoupon, setAvailableCoupon] = useState<Array<CouponItem>>([])
  // 不可用优惠券
  const [unAvailableCoupon, setUnAvailableCoupon] = useState<Array<CouponItem>>([])

  // 当没有最优组合推荐只请求1次优惠劵标识
  const requestedRef = useRef<boolean>(false)

  // 点击使用最优组合推荐时的计数
  const combinationClickCount = useRef<number>(0)

  // 优惠组合推荐是否选中
  const [checked, setChecked] = useState<boolean>(false)

  // 是否显示优惠劵列表
  const showCouponList = useMemo(() => {
    return !checked && (!masterData?.balanceVirtualAssetsVO?.disableCouponShow || !masterData?.balanceVirtualAssetsVO?.useBestCoupon)
  }, [checked, masterData?.balanceVirtualAssetsVO?.disableCouponShow, masterData?.balanceVirtualAssetsVO?.useBestCoupon])

  // 选择优惠券
  const cardSelect = (item: CouponItem) => {
    // 当有不可叠加的劵不调用接口
    if (item?.readOnly) return
    const params = {
      couponKey: item?.key,
      couponId: item?.id,
      selectCoupon: !item?.selected,
    }
    setLoading(true)
    api_selectOrderCoupon(params)
      .then((res) => {
        // console.log('res', res)
        try {
          if (res?.code == '0') {
            setAvailableCoupon(res?.body?.couponList)
            // 更新融合接口
            updateMasterData(
              {
                balanceCommonOrderForm: {
                  useBestCoupon: false,
                },
              },
              'virtualAsset',
            )
            // 点击埋点
            reportClick('virtualasset_CouponClick', {
              second_tab_name: COUPONS_TABS,
              couponid: item?.id,
              coupon_info: item,
            })
          } else {
            showToast({ title: res?.message || '请稍后再试' })
          }
        } catch (error) {
          console.log('error', error)
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'balance_exchangeCoupons_pc',
            error_type_txt: 'balance_exchangeCoupons_pc接口异常',
            request: JSON.stringify(params),
            error_msg: JSON.stringify(error),
          },
        })
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 优惠码兑换
  const redeemCode = (code: string, valid: boolean, setError: any) => {
    // console.log(code, valid)
    if (valid) {
      const params = {
        couponKey: code,
      }
      setLoading(true)
      api_exchangeCoupons(params)
        .then((res) => {
          // console.log('兑换优惠卷', res)
          // 兑换失败
          if (res?.code !== '0') {
            setError(res?.message || '请稍后再试')
          } else {
            // 兑换成功
            // 跳转到可用优惠券，并刷新列表
            tabChange('available')
          }
        })
        .catch((error) => {
          monitoring({
            name: monitorName.Settlement,
            code: monitorCode.VirtualAsset,
            msg: {
              functionId: 'balance_exchangeCoupons_pc',
              error_type_txt: 'balance_exchangeCoupons_pc接口异常',
              request: JSON.stringify(params),
              error_msg: JSON.stringify(error),
            },
          })
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }

  /**
   * tab切换
   */
  const tabChange = (key: string) => {
    seActiveKey(key)
    if (key == 'available') {
      getCouponList()
    } else if (key == 'unAvailable') {
      getCouponList('unAvailable')
    }
  }
  /**
   * 获取优惠券列表
   */
  const getCouponList = (type = 'available') => {
    setLoading(true)
    api_getPageCouponList()
      .then((res) => {
        // console.log('res', res)
        if (res?.code == '0') {
          // 可用优惠券列表
          if (type === 'available') {
            setAvailableCoupon(res?.body?.couponList ?? [])
            // 不可用优惠券列表
          } else if (type === 'unAvailable') {
            setUnAvailableCoupon(res.body?.notCanUsedCouponList ?? [])
            // console.log('不可用优惠券列表', res.body?.notCanUsedCouponList)
          }
          // 设置为true，避免重复请求
          requestedRef.current = true
        } else {
          showToast({ title: res?.message || '请稍后再试' })
        }
      })
      .catch((error) => {
        monitoring({
          name: monitorName.Settlement,
          code: monitorCode.VirtualAsset,
          msg: {
            functionId: 'balance_getPageCouponList_pc',
            error_type_txt: 'balance_getPageCouponList_pc接口异常',
            error_msg: JSON.stringify(error),
          },
        })
      })
      .finally(() => {
        setLoading(false)
      })
  }

  /**
   * 最优组合优惠券切换
   */
  const combinationChange = (_checked: boolean) => {
    // 切换到优惠券列表时获取普通优惠券列表
    if (!_checked) {
      // 更新融合接口
      updateMasterData(
        {
          balanceCommonOrderForm: {
            useBestCoupon: false,
          },
        },
        'virtualAsset',
      )
      getCouponList()
    }
    setChecked(_checked)
    combinationClickCount.current += 1
  }

  useEffect(() => {
    // 为true时，代表已经请求过普通优惠劵列表
    if (requestedRef.current) return
    // 融合接口没有下发最优优惠券列表时请求普通优惠券列表
    if (!masterData?.couponVOList || !masterData?.couponVOList?.length) {
      getCouponList()
    }
  }, [masterData?.couponVOList])

  // 批量曝光
  useEffect(() => {
    // 清空map
    domMap.clear()
  }, [activeKey, tabKey, showCouponList])

  /**
   * 已加载完的dom上报
   * @param id id
   */
  const loadingDom = (id: string) => {
    // 已加载domid存起来
    domMap.set(id, id)
    // 可用优惠劵
    if (activeKey === 'available') {
      // domInfoMap长度与availableCoupon.length相等时说明已加载完所有dom
      if (domMap.size === availableCoupon.length) {
        observer.current?.destory()
        // 曝光
        observer.current = _batchExposeDom('virtualasset_CouponExpo', `coupon`, 'couponid', { second_tab_name: COUPONS_TABS })
      }
      // 不可用优惠劵
    } else if (activeKey === 'unAvailable') {
      if (domMap.size === unAvailableCoupon.length) {
        observer.current?.destory()
        // 曝光
        observer.current = _batchExposeDom('virtualasset_CouponExpo', `uncoupon`, 'couponid', { second_tab_name: COUPONS_TABS })
      }
    }
  }
  return (
    <>
      {/* 最优组合优惠券 */}
      {/* 融合接口下发disableCouponShow 或者有优惠劵列表显示最优组合切换*/}
      {(masterData?.balanceVirtualAssetsVO?.disableCouponShow || availableCoupon.length > 0) && (
        <Combination
          tabsName={COUPONS_TABS}
          tabKey={tabKey}
          onChange={combinationChange}
          checked={checked || !!useBestCoupon}
          combinationClickCount={combinationClickCount?.current}
        ></Combination>
      )}
      {/* 优惠券列表 */}
      {showCouponList && (
        <Tabs
          activeKey={activeKey}
          onChange={tabChange}
          type="button"
          maxHeight="300"
          rightTip={<RightTip type="couponDescLink" />}
          tabClass={styles.tabsContent}
        >
          <Tabs.Panel tabKey="available" tab="可用优惠劵">
            <NoData arr={availableCoupon} text="无可用优惠券">
              <div className={styles.cardList}>
                {availableCoupon.map((item: CouponItem, index: number) => (
                  <Card
                    key={index}
                    onClick={() => cardSelect(item)}
                    item={item}
                    loadingDom={loadingDom}
                    activeKey={activeKey}
                    tabKey={tabKey}
                  />
                ))}
              </div>
            </NoData>
          </Tabs.Panel>
          <Tabs.Panel tabKey="unAvailable" tab="不可用优惠劵">
            <NoData arr={unAvailableCoupon}>
              <div className={styles.cardList}>
                {unAvailableCoupon.map((item: CouponItem, index: number) => (
                  <Card key={index} item={item} disabled loadingDom={loadingDom} activeKey={activeKey} tabKey={tabKey} />
                ))}
              </div>
            </NoData>
          </Tabs.Panel>
          <Tabs.Panel tabKey="redeemCode" tab="优惠码兑换" panelClass={styles.tabPanel}>
            <RedeemCode onClick={redeemCode}>兑换</RedeemCode>
          </Tabs.Panel>
        </Tabs>
      )}
    </>
  )
}

export default Coupons
