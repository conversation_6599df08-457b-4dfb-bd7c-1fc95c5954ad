import { atom } from 'jotai'
import type { MasterApiResponse } from '@app/typings/master_api_response'
/** 页面主数据 */
export const masterAtom = atom<MasterApiResponse | null>(null)

type MethodCollection = Record<string, () => void>
export const methodsAtom = atom<MethodCollection>({})

/** 店铺留言 */
export const noteBundleIdMap = atom<Map<string, { remark: string; venderId: number; storeId?: number; jdcombineStoreId?: number }>>()
export const venderRemarksAtom = atom((get) => {
  const venderRemarks = get(noteBundleIdMap)
  return venderRemarks
    ? Array.from(venderRemarks.values()).map((item) => {
        const { remark, venderId, jdcombineStoreId } = item
        return {
          remark,
          venderId,
          jdcombineStoreId,
        }
      })
    : []
})
