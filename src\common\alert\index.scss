.alert {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #FFF5CC;
  border: 1px solid #ffbf00;
  border-radius: 8px;
  font-size: 14px;
  color: #C47600;

  .alert-icon {
    width: 14px;
    height: 14px;
    background-image: url('//img10.360buyimg.com/ling/jfs/t1/310657/30/12635/640/685dee2dF0b02b128/18fc81b5191b2180.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 14px 14px;
    margin-right: 10px;
  }

  .alert-content {
    flex: 1;
    line-height: 20px;

    a {
      color: #D97716;
    }

    a:hover {
      text-decoration: underline;
    }
  }

  .alert-close {
    width: 12px;
    height: 12px;
    margin-left: 12px;
    cursor: pointer;

    &::after {
      content: "\e600";
      display: inline-block;
      width: 12px;
      height: 12px;
      -webkit-text-stroke-width: 0.2px;
      -moz-osx-font-smoothing: grayscale;
      font-family: iconfont, sans-serif;
      font-size: 12px;
      vertical-align: top;
    }
  }
}