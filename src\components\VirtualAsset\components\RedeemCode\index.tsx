/*
 * @Author: ext.wangchao120
 * @Date: 2025-06-27 12:55:06
 * @LastEditTime: 2025-07-02 17:51:02
 * @LastEditors: ext.wangchao120
 * @Description: 兑换输入框
 * @FilePath: /pc_settlement/src/components/VirtualAsset/components/RedeemCode/index.tsx
 */

import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react'
import styles from './index.module.scss'
import Input from '../Input'
import Button from '../Button'
import classNames from 'classnames'

interface PropsType {
  children?: React.ReactNode
  onClick: (value: string, valid: boolean, fa: any) => void
}
interface RefsType {
  code1: React.RefObject<HTMLInputElement | null>
  code2: React.RefObject<HTMLInputElement | null>
  code3: React.RefObject<HTMLInputElement | null>
  code4: React.RefObject<HTMLInputElement | null>
}

interface CouponType {
  code1: string
  code2: string
  code3: string
  code4: string
}
export type codeRef = {
  clearInput: () => void
}

const RedeemCode = forwardRef<codeRef, PropsType>((props, ref) => {
  const { children, onClick } = props
  const refs: RefsType = {
    code1: useRef<HTMLInputElement | null>(null),
    code2: useRef<HTMLInputElement | null>(null),
    code3: useRef<HTMLInputElement | null>(null),
    code4: useRef<HTMLInputElement | null>(null),
  }

  const [coupon, setCoupon] = useState<CouponType>({
    code1: '',
    code2: '',
    code3: '',
    code4: '',
  })

  const [errorMsg, setErrorMsg] = useState<string>('')

  /**
   * 输入框变化时的回调函数，过滤非字母数字字符并转换为大写，替换'O'为'0'
   * @param value - 输入框的值
   * @param type  - 输入框的类型，用于确定输入框的顺序
   */
  const inputChange = (value: string, type: string) => {
    // 只能输入字母和数字, 小写转大写
    const filteredValue = value
      .replace(/[^a-zA-Z0-9]/g, '')
      .toUpperCase()
      .replace('O', '0')
    if (filteredValue.length == 4) {
      // 前3个输入框跳到下一个输入框并聚焦
      if (Number(type) < 4) {
        refs[`code${Number(type) + 1}` as keyof CouponType]?.current?.focus()
      }
    }
    setCoupon({
      ...coupon,
      [`code${type}`]: filteredValue,
    })
  }

  // 优惠券兑换码点击
  const handleSubmit = () => {
    const valid = Object.values(coupon).every((item: any) => item.length >= 4)
    if (!valid) {
      setError('请输入密码')
      return
    }
    const result = Object.values(coupon).join('')
    // 父组件事件回调
    onClick(result, valid, setError)
  }

  /**
   * 处理粘贴事件，过滤非字母数字字符并转换为大写，替换'O'为'0'
   * @param {string} value - 粘贴的字符串值
   * @param {string} type - 类型参数，用于确定输入框的起始位置
   */
  const handlePaste = (value: string, type: string) => {
    // 只保留字母和数字，转换为大写，并将字母'O'替换为数字'0'
    const filteredValue = value
      .replace(/[^a-zA-Z0-9]/g, '')
      .toUpperCase()
      .replace('O', '0')

    const obj: Partial<CouponType> = {}

    for (let i = 0; i < 5 - Number(type); i++) {
      // 将过滤后的值按4个字符一组分割，并存储到obj对象中
      const key = `code${Number(type) + i}` as keyof CouponType
      obj[key] = filteredValue.slice(0 + i * 4, 4 + i * 4)

      // 当前输入框长度达到4，则将焦点移动到下一个输入框
      if (obj[key]?.length >= 4) {
        refs[`code${Number(type) + i + 1}` as keyof RefsType]?.current?.focus()
      }
    }
    setCoupon({
      ...coupon,
      ...obj,
    })
  }
  // 清空输入框
  const clearInput = () => {
    setCoupon({
      code1: '',
      code2: '',
      code3: '',
      code4: '',
    })
    setErrorMsg('')
  }

  // 给error赋值后，两秒后清空errorMsg
  const setError = (msg: string) => {
    setErrorMsg(msg)
    setTimeout(() => {
      setErrorMsg('')
    }, 2000)
  }

  useImperativeHandle(ref, () => ({
    clearInput,
  }))
  return (
    <>
      <div className={styles.code}>
        <Input
          size="large"
          className={styles.codeInput}
          onPaste={(value) => handlePaste(String(value), '1')}
          onChange={(value) => inputChange(value as string, '1')}
          value={coupon.code1}
          maxLength={4}
          ref={refs.code1}
        />
        <Input
          size="large"
          className={styles.codeInput}
          onPaste={(value) => handlePaste(String(value), '2')}
          onChange={(value) => inputChange(value as string, '2')}
          value={coupon.code2}
          maxLength={4}
          ref={refs.code2}
        />
        <Input
          size="large"
          className={styles.codeInput}
          onPaste={(value) => handlePaste(String(value), '3')}
          onChange={(value) => inputChange(value as string, '3')}
          value={coupon.code3}
          maxLength={4}
          ref={refs.code3}
        />
        <Input
          size="large"
          className={styles.codeInput}
          onPaste={(value) => handlePaste(String(value), '4')}
          onChange={(value) => inputChange(value as string, '4')}
          value={coupon.code4}
          maxLength={4}
          ref={refs.code4}
        />

        {React.isValidElement(children) ? (
          React.cloneElement(children, {
            onClick: () => {
              handleSubmit()
            },
          } as React.DOMAttributes<HTMLDivElement>)
        ) : (
          <Button onClick={handleSubmit} className={styles.btn}>
            {children}
          </Button>
        )}
      </div>
      {/* 错误提示 */}
      {errorMsg ? (
        <div className={classNames(styles.errorMsg, styles.msg)}>{errorMsg}</div>
      ) : (
        // 默认提示
        <div className={styles.msg}>密码只包含数字 0-9，大写字母 A-F</div>
      )}
    </>
  )
})
export default RedeemCode
