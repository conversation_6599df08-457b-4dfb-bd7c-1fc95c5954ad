import JDTooltip from '@app/common/Tooltip'
import { useEffect, useState, useRef, RefObject, FC, useImperativeHandle, forwardRef } from 'react'
import useClickOutside from '@app/hooks/useClickOutside'
import style from './area.module.scss'

interface TooltipProps {
  children?: React.ReactNode
  content?: React.ReactNode | string
  width?: number
}
type TooltipRefType = {
  hide: () => void
  show: () => void
  triggerRef: RefObject<any>
}

const GBAreaNestTip: FC<TooltipProps> = forwardRef(({ children, content = <span>
      当前详细地址不在省市对应范围内
    </span>, width = 258 }, ref: any) => {
  const tooltipRef = useRef<TooltipRefType>(null)
  const tooltipEl = useRef(null)
  const [closed, setClosed] = useState(false)
  useEffect(() => {
    tooltipEl.current = tooltipRef.current?.triggerRef?.current
    tooltipRef.current?.show()
  }, [])
  useClickOutside(tooltipEl, () => !closed && tooltipRef.current?.show())
  const onClose = () => {
    tooltipRef.current?.hide()
    setClosed(true)
  }
  const onShow = () => {
    tooltipRef.current?.show()
    setClosed(false)
  }
  useImperativeHandle(ref, () => {
    if (tooltipRef.current) {
      return { ...(tooltipRef.current || {}), onClose, onShow }
    }
  })
  return (
    <JDTooltip
      ref={tooltipRef}
      content={
        <div className="leading-20 is-flex items-center justify-between">
          {content}
          <i className={`h-12 w-12 is-inline-block cursor-pointer ${style['tip-close']}`} onClick={onClose} />
        </div>
      }
      distance={12}
      placement="top"
      trigger="click"
      width={width}
      padding="10px 10px 10px 12px"
      className="is-block"
      arrow
    >
      {children}
    </JDTooltip>
  )
})

export default GBAreaNestTip
